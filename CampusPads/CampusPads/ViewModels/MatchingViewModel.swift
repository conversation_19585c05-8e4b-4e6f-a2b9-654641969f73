import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift
import FirebaseAuth
import Combine
import CoreLocation

enum SwipeDirection {
    case left, right, up
}

@MainActor
class MatchingViewModel: ObservableObject {
    static let shared = MatchingViewModel()

    @Published var potentialMatches: [UserModel] = []
    @Published var errorMessage: String?
    @Published var lastSwipedCandidate: UserModel?
    @Published var rightSwipesCount: Int = 0
    @Published var mutualMatchesCount: Int = 0
    @Published var superLikedUserIDs: Set<String> = []
    @Published var matchedUserIDs: Set<String> = []
    @Published var isLoading: Bool = false

    // MARK: - Match Celebration State
    @Published var showMatchCelebration = false
    @Published var celebrationMatch: (currentUser: UserModel, matchedUser: UserModel, matchType: MatchType)?

    // Track when matches are created to prevent rewind
    @Published var lastMatchCreatedForUserID: String?

    /// Convenience to avoid repeated Auth lookups
    private var currentUID: String? {
        Auth.auth().currentUser?.uid
    }

    /// Sync local counts with Firebase data for accurate stats
    func syncStatsWithFirebase() {
        guard let uid = currentUID else {
            Logger.shared.warning("Cannot sync stats - no current user")
            return
        }

        Logger.shared.info("Syncing stats with Firebase...")

        // Sync right swipes count
        db.collection("swipes")
            .whereField("from", isEqualTo: uid)
            .whereField("liked", isEqualTo: true)
            .getDocuments { [weak self] snapshot, error in
                if let error = error {
                    Logger.shared.error("Error syncing right swipes: \(error.localizedDescription)")
                    return
                }

                let firebaseCount = snapshot?.documents.count ?? 0
                DispatchQueue.main.async {
                    self?.rightSwipesCount = firebaseCount
                    Logger.shared.debug("Synced right swipes count: \(firebaseCount)")
                }
            }

        // Sync mutual matches count
        db.collection("matches")
            .whereField("participants", arrayContains: uid)
            .getDocuments { [weak self] snapshot, error in
                if let error = error {
                    print("❌ MatchingViewModel: Error syncing matches: \(error.localizedDescription)")
                    return
                }

                let firebaseCount = snapshot?.documents.count ?? 0
                DispatchQueue.main.async {
                    self?.mutualMatchesCount = firebaseCount
                    print("✅ MatchingViewModel: Synced mutual matches count: \(firebaseCount)")
                }
            }
    }

    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()

    init() {
        // OPTIMIZATION: Removed automatic refresh on profile changes to prevent unnecessary reloads
        // Refresh will only happen on:
        // 1. Manual refresh button tap
        // 2. SwipeDeckView onAppear (first time)
        // 3. Filter settings changes

        loadSuperLikes()
    }

    private func loadSuperLikes() {
        guard let currentUserID = self.currentUID else { return }

        db.collection("swipes")
            .whereField("from", isEqualTo: currentUserID)
            .whereField("superLiked", isEqualTo: true)
            .getDocuments { [weak self] snapshot, _ in
                guard let docs = snapshot?.documents else { return }
                let ids = docs.compactMap { $0.data()["to"] as? String }
                DispatchQueue.main.async {
                    self?.superLikedUserIDs = Set(ids)
                }
            }
    }

  var currentUser: UserModel? {
    ProfileViewModel.shared.userProfile
  }

    /// Optimized force refresh method for manual refresh only
    func forceRefresh() {
        print("🔄 MatchingViewModel: Manual force refresh called")

        // OPTIMIZATION: Reset rate limiting for manual refresh
        isFetching = false
        lastFetchTime = nil

        DispatchQueue.main.async {
            self.potentialMatches = []
            self.errorMessage = nil
            self.isLoading = true
        }

        // Simply fetch potential matches - no complex retry logic
        fetchPotentialMatches()
    }

    /// Initial load method for SwipeDeckView onAppear
    func initialLoad() {
        print("🔄 MatchingViewModel: Initial load called")

        // Only load if we don't have matches already
        if potentialMatches.isEmpty {
            DispatchQueue.main.async {
                self.isLoading = true
                self.errorMessage = nil
            }
            fetchPotentialMatches()
        } else {
            print("🔄 MatchingViewModel: Matches already loaded, skipping initial load")
        }
    }

    // OPTIMIZATION: Add request deduplication to prevent multiple simultaneous requests
    private var isFetching = false
    private var lastFetchTime: Date?
    private let minimumFetchInterval: TimeInterval = 5.0 // Reduced for debugging - Prevent requests more frequent than 5 seconds

    func fetchPotentialMatches() {
        print("🔄 MatchingViewModel: fetchPotentialMatches called")

        // OPTIMIZATION: Prevent duplicate requests
        guard !isFetching else {
            print("⚠️ MatchingViewModel: Fetch already in progress, skipping")
            return
        }

        // OPTIMIZATION: Rate limiting to prevent excessive requests
        if let lastFetch = lastFetchTime {
            let timeSinceLastFetch = Date().timeIntervalSince(lastFetch)
            if timeSinceLastFetch < minimumFetchInterval {
                print("⚠️ MatchingViewModel: Rate limited - last fetch was \(timeSinceLastFetch)s ago (minimum: \(minimumFetchInterval)s)")
                return
            } else {
                print("✅ MatchingViewModel: Rate limit passed - last fetch was \(timeSinceLastFetch)s ago")
            }
        } else {
            print("✅ MatchingViewModel: No previous fetch - proceeding")
        }

        guard let uid = currentUID else {
            DispatchQueue.main.async {
                self.errorMessage = "User not authenticated"
                self.isLoading = false
            }
            print("❌ MatchingViewModel: User not authenticated")
            return
        }

        guard let me = ProfileViewModel.shared.userProfile else {
            DispatchQueue.main.async {
                self.errorMessage = "Profile not available. Please complete your profile setup."
                self.isLoading = false
            }
            print("❌ MatchingViewModel: User profile not available - manual refresh required")
            return
        }

        // Set fetching state
        isFetching = true
        lastFetchTime = Date()

        print("🔍 MatchingViewModel: Starting to fetch potential matches for user: \(me.firstName ?? "Unknown")")

        // Clear previous state
        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
            // Don't clear potentialMatches here to avoid flickering
        }

        // 1) Get users I've already swiped on
        print("📊 MatchingViewModel: Loading existing swipes for user \(uid)")
        db.collection("swipes")
            .whereField("from", isEqualTo: uid)
            .getDocuments { [weak self] swipeSnapshot, swipeError in
                guard let self = self else { return }

                if let swipeError = swipeError {
                    let errorMsg = "Failed to load swipes: \(swipeError.localizedDescription)"
                    DispatchQueue.main.async {
                        self.errorMessage = errorMsg
                        self.isLoading = false
                    }
                    print("❌ MatchingViewModel: \(errorMsg)")
                    print("   - Error code: \((swipeError as NSError).code)")
                    print("   - Error domain: \((swipeError as NSError).domain)")
                    return
                }

                let swipedIDs = Set(swipeSnapshot?.documents.compactMap { $0.data()["to"] as? String } ?? [])
                print("📊 MatchingViewModel: Found \(swipedIDs.count) previously swiped users")

                // 2) CRITICAL FIX: Enhanced discovery query with better error handling and higher limit
                print("🔍 MatchingViewModel: Building discovery query...")
                print("   - Query criteria: isEmailVerified = true, hideFromDiscovery = false")
                print("   - Limit: 200 users (increased for better coverage)")

                self.db.collection("users")
                    .whereField("isEmailVerified", isEqualTo: true)
                    .whereField("hideFromDiscovery", isEqualTo: false)
                    .limit(to: 200) // CRITICAL FIX: Increased limit for better profile coverage
                    .getDocuments { userSnapshot, userError in
                        // CRITICAL FIX: Always reset fetching state
                        defer {
                            DispatchQueue.main.async {
                                self.isFetching = false
                                self.isLoading = false
                            }
                        }

                        DispatchQueue.main.async {
                            if let userError = userError {
                                // OPTIMIZATION: Simplified error handling without auto-retry
                                let errorMessage = "Failed to load users: \(userError.localizedDescription)"
                                self.errorMessage = errorMessage
                                print("❌ MatchingViewModel: Failed to load users - \(userError.localizedDescription)")
                                print("🔄 MatchingViewModel: Use manual refresh to retry")
                                return
                            }

                            guard let userDocs = userSnapshot?.documents else {
                                self.errorMessage = "No users found in database"
                                print("❌ MatchingViewModel: No user documents returned from query")
                                return
                            }

                            print("📊 MatchingViewModel: Retrieved \(userDocs.count) users from Firestore")

                            // 3) Simple filtering and processing
                            let allUsers = userDocs.compactMap { doc -> UserModel? in
                                do {
                                    var user = try doc.data(as: UserModel.self)

                                    // CRITICAL FIX: Manually set the ID from document ID if it's nil
                                    if user.id == nil {
                                        user.id = doc.documentID
                                        print("🔧 MatchingViewModel: Fixed missing ID - set to \(doc.documentID)")
                                    }

                                    print("🔍 MatchingViewModel: Loaded user \(user.firstName ?? "Unknown")")
                                    print("   - Document ID: \(doc.documentID)")
                                    print("   - User ID: \(user.id ?? "nil")")
                                    print("   - Email: \(user.email)")
                                    print("   - Email Domain: \(user.emailDomain ?? "nil")")
                                    print("   - Email Verified: \(user.isEmailVerified)")
                                    print("   - Hidden from Discovery: \(user.hideFromDiscovery ?? false)")
                                    print("   - Profile Image URL: \(user.profileImageUrl ?? "nil")")
                                    print("   - Profile Image URLs: \(user.profileImageUrls?.count ?? 0) images")
                                    print("   - College: \(user.collegeName ?? "nil")")
                                    print("   - Housing Status: \(user.housingStatus ?? "nil")")
                                    return user
                                } catch {
                                    print("❌ MatchingViewModel: Failed to decode user from document \(doc.documentID)")
                                    print("   - Error: \(error.localizedDescription)")
                                    print("   - Error Type: \(type(of: error))")
                                    if let decodingError = error as? DecodingError {
                                        print("   - Decoding Error Details: \(decodingError)")
                                    }
                                    return nil
                                }
                            }

                            print("📊 MatchingViewModel: Successfully parsed \(allUsers.count) user models")

                            let eligibleUsers = allUsers.filter { user in
                                guard let userID = user.id else {
                                    print("⚠️ MatchingViewModel: Skipping user with no ID - \(user.firstName ?? "Unknown") (\(user.email))")
                                    return false
                                }

                                print("🔍 MatchingViewModel: Evaluating eligibility for \(user.firstName ?? "Unknown") (\(userID))")
                                print("   - Email: \(user.email)")
                                print("   - Email Domain: \(user.emailDomain ?? "nil")")

                                // Basic eligibility checks including profile image requirement
                                let isNotSelf = userID != uid
                                let notAlreadySwiped = !swipedIDs.contains(userID)
                                let hasBasicInfo = user.firstName != nil && !user.firstName!.isEmpty
                                let hasProfileImage = self.hasValidProfileImage(user)

                                print("   - Is Not Self: \(isNotSelf ? "✅" : "❌") (current user: \(uid))")
                                print("   - Not Already Swiped: \(notAlreadySwiped ? "✅" : "❌")")
                                print("   - Has Basic Info: \(hasBasicInfo ? "✅" : "❌") (firstName: \(user.firstName ?? "nil"))")
                                print("   - Has Profile Image: \(hasProfileImage ? "✅" : "❌")")

                                // Detailed profile image analysis
                                let hasProfileImageUrl = user.profileImageUrl != nil && !user.profileImageUrl!.isEmpty
                                let profileImageUrlsCount = user.profileImageUrls?.filter { !$0.isEmpty }.count ?? 0
                                print("     * Single Image URL: \(hasProfileImageUrl ? "✅" : "❌") (\(user.profileImageUrl ?? "nil"))")
                                print("     * Multiple Images: \(profileImageUrlsCount) images")

                                let isEligible = isNotSelf && notAlreadySwiped && hasBasicInfo && hasProfileImage

                                if isEligible {
                                    print("✅ MatchingViewModel: \(user.firstName ?? "Unknown") (\(userID)) is ELIGIBLE for discovery")
                                } else {
                                    let reasons = [
                                        !isNotSelf ? "is current user" : nil,
                                        !notAlreadySwiped ? "already swiped" : nil,
                                        !hasBasicInfo ? "missing basic info" : nil,
                                        !hasProfileImage ? "no profile image" : nil
                                    ].compactMap { $0 }.joined(separator: ", ")
                                    print("❌ MatchingViewModel: EXCLUDING \(user.firstName ?? "Unknown") (\(userID)): \(reasons)")
                                }

                                return isEligible
                            }

                            print("✅ MatchingViewModel: Found \(eligibleUsers.count) eligible matches")

                            // Apply SmartMatchingEngine filtering if user has filter settings
                            let filteredMatches: [UserModel]
                            if let filterSettings = me.filterSettings {
                                print("🔍 MatchingViewModel: Applying SmartMatchingEngine with filter settings")
                                print("   - Filter mode: \(filterSettings.mode ?? "none")")
                                print("   - College filter: \(filterSettings.collegeName ?? "none")")
                                print("   - Housing status filter: \(filterSettings.housingStatus ?? "none")")
                                print("   - Show All Profiles: \(filterSettings.showAllProfiles ?? false)")
                                print("   - Eligible users before SmartMatchingEngine: \(eligibleUsers.count)")

                                // Log each eligible user before SmartMatchingEngine
                                for (index, user) in eligibleUsers.enumerated() {
                                    print("     \(index + 1). \(user.firstName ?? "Unknown") - College: \(user.collegeName ?? "none") - Housing: \(user.housingStatus ?? "none")")
                                }

                                filteredMatches = SmartMatchingEngine.generateSortedMatches(
                                    from: eligibleUsers,
                                    currentUser: me,
                                    using: filterSettings
                                )
                                print("✅ MatchingViewModel: SmartMatchingEngine returned \(filteredMatches.count) filtered matches")

                                // Log each filtered user after SmartMatchingEngine
                                for (index, user) in filteredMatches.enumerated() {
                                    print("     \(index + 1). \(user.firstName ?? "Unknown") - College: \(user.collegeName ?? "none") - Housing: \(user.housingStatus ?? "none")")
                                }

                                if filteredMatches.count < eligibleUsers.count {
                                    print("⚠️ MatchingViewModel: SmartMatchingEngine filtered out \(eligibleUsers.count - filteredMatches.count) users")
                                }

                                // Enhanced logging for "Show All Profiles" mode
                                if filterSettings.showAllProfiles == true {
                                    print("🎯 MatchingViewModel: 'Show All Profiles' mode analysis:")
                                    print("   - Input users: \(eligibleUsers.count)")
                                    print("   - Output matches: \(filteredMatches.count)")
                                    print("   - Filtered out: \(eligibleUsers.count - filteredMatches.count)")
                                    if filteredMatches.count < eligibleUsers.count {
                                        print("   ⚠️ WARNING: 'Show All Profiles' mode should not filter out users!")
                                    }
                                }
                            } else {
                                print("🔍 MatchingViewModel: No filter settings found, creating default FilterSettings for compatibility sorting")

                                // CRITICAL FIX: Create default FilterSettings to use the enhanced SmartMatchingEngine
                                let defaultFilterSettings = FilterSettings(
                                    // No mandatory filters specified - will use base compatibility scoring
                                    showAllProfiles: false // Default to normal compatibility sorting
                                )

                                filteredMatches = SmartMatchingEngine.generateSortedMatches(
                                    from: eligibleUsers,
                                    currentUser: me,
                                    using: defaultFilterSettings
                                )
                                print("✅ MatchingViewModel: SmartMatchingEngine returned \(filteredMatches.count) compatibility-sorted matches")
                            }

                            print("✅ MatchingViewModel: Filtered match count: \(filteredMatches.count)")

                            // Apply psychological distribution for engagement optimization
                            Task {
                                print("🧠 MatchingViewModel: Applying psychological distribution...")
                                let finalMatches = await PsychologicalMatchingEngine.shared.distributeMatches(
                                    for: uid,
                                    allMatches: filteredMatches
                                )

                                print("✅ MatchingViewModel: Psychological distribution complete: \(finalMatches.count) matches")

                                // Track analytics for psychological optimization
                                AnalyticsManager.shared.trackEvent("matches_loaded_with_psychology", parameters: [
                                    "user_id": uid,
                                    "original_count": filteredMatches.count,
                                    "optimized_count": finalMatches.count
                                ])

                                // Update potential matches on main thread
                                DispatchQueue.main.async {
                                    self.potentialMatches = finalMatches
                                    self.isLoading = false // Stop loading indicator
                                    print("✅ MatchingViewModel: Updated potentialMatches with \(finalMatches.count) psychologically optimized users")

                                    // Enhanced logging for debugging card display issues
                                    if finalMatches.isEmpty {
                                        print("❌ MatchingViewModel: No final matches - this will show empty state")
                                    } else {
                                        print("📋 MatchingViewModel: Final matches ranking:")
                                        for (index, user) in finalMatches.prefix(5).enumerated() {
                                            let userID = user.id ?? "no-id"
                                            let firstName = user.firstName ?? "Unknown"
                                            let housingStatus = user.housingStatus ?? "None"
                                            let isVerified = user.isEmailVerified
                                            let isHidden = user.hideFromDiscovery ?? false

                                            print("   \(index + 1). \(firstName) (\(userID))")
                                            print("      - Housing: \(housingStatus)")
                                            print("      - Verified: \(isVerified), Hidden: \(isHidden)")

                                            if index == 0 {
                                                print("      ⭐ RANKED #1 - This should be the first card displayed")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
            }
    }




    /// Simple swipe recording with enhanced logging and validation
    private func recordSwipe(
        on user: UserModel,
        liked: Bool,
        superLiked: Bool = false
    ) {
        guard let currentUID = self.currentUID,
              let matchUID = user.id else {
            let errorMsg = "Unable to record swipe: currentUID=\(currentUID?.description ?? "nil"), userID=\(user.id?.description ?? "nil")"
            DispatchQueue.main.async {
                self.errorMessage = errorMsg
            }
            print("❌ MatchingViewModel: \(errorMsg)")
            return
        }

        // Validate user email exists
        guard !user.email.isEmpty else {
            let errorMsg = "Unable to record swipe: user email is empty"
            DispatchQueue.main.async {
                self.errorMessage = errorMsg
            }
            print("❌ MatchingViewModel: \(errorMsg)")
            return
        }

        let swipeType = superLiked ? "super like" : (liked ? "like" : "pass")
        print("👆 MatchingViewModel: Recording \(swipeType) on \(user.firstName ?? "Unknown") (\(matchUID))")
        print("   - From: \(currentUID)")
        print("   - To: \(matchUID)")
        print("   - Email: \(user.email)")
        print("   - Liked: \(liked)")
        print("   - SuperLiked: \(superLiked)")

        // Enhanced Firestore write with validation
        let swipeData: [String: Any] = [
            "from": currentUID,
            "to": matchUID,
            "liked": liked,
            "superLiked": superLiked,
            "timestamp": FieldValue.serverTimestamp(),
            "userEmail": user.email,
            "swipeType": swipeType // Add explicit swipe type for easier querying
        ]

        print("📝 MatchingViewModel: Writing swipe data to Firestore...")

        // Perform Firebase operations on background queue for better performance
        Task {
            do {
                try await db.collection("swipes").addDocument(data: swipeData)
                print("✅ MatchingViewModel: Successfully recorded \(swipeType) in Firestore")

                // Update UI on main thread
                await MainActor.run {
                    self.updateLocalStateAfterSwipe(user: user, liked: liked, superLiked: superLiked, matchUID: matchUID)
                }

                // 🔔 SEND SUPER LIKE NOTIFICATION (background task)
                if superLiked {
                    await self.sendSuperLikeNotificationAsync(to: matchUID, user: user)
                }

            } catch {
                let errorMsg = "Failed to record swipe: \(error.localizedDescription)"
                print("❌ MatchingViewModel: \(errorMsg)")
                print("   - Error code: \((error as NSError).code)")
                print("   - Error domain: \((error as NSError).domain)")

                await MainActor.run {
                    self.errorMessage = errorMsg
                }
            }
        }

        // Check for mutual match if liked
        if liked {
            print("💕 MatchingViewModel: Checking for mutual match with \(matchUID)")
            checkForMutualMatch(with: matchUID, currentUserSuperLiked: superLiked)
        }
    }

    /// Updates local state after a successful swipe
    private func updateLocalStateAfterSwipe(user: UserModel, liked: Bool, superLiked: Bool, matchUID: String) {
        DispatchQueue.main.async {
            // Update swipe counts
            if liked {
                self.rightSwipesCount += 1
            }

            // Update last swiped candidate
            self.lastSwipedCandidate = user

            // Update super liked users
            if superLiked {
                self.superLikedUserIDs.insert(matchUID)
            }

            // Don't remove from potentialMatches array to maintain indexing
            // The SwipeDeckView will handle showing/hiding based on currentIndex

            // Clear any error messages on successful swipe
            self.errorMessage = nil
        }
    }

    func swipeRight(on user: UserModel) {
      recordSwipe(on: user, liked: true)
      // Track analytics
      AnalyticsManager.shared.trackSwipe(action: .like)
    }

    func swipeLeft(on user: UserModel) {
      recordSwipe(on: user, liked: false)
      // Track analytics
      AnalyticsManager.shared.trackSwipe(action: .pass)
    }

    func superLike(on user: UserModel) {
      // skip duplicates
      guard let matchUID = user.id,
            !superLikedUserIDs.contains(matchUID)
      else { return }
      recordSwipe(on: user, liked: true, superLiked: true)
      // Track analytics
      AnalyticsManager.shared.trackSwipe(action: .like)
    }

    /// Adds a user to the discovery stack (for like-from-search functionality)
    /// - Parameter userID: The ID of the user to add to discovery stack
    func addUserToDiscoveryStack(userID: String) {
        print("🔄 MatchingViewModel: Adding user \(userID) to discovery stack from search")

        // Load the user from Firestore and add to potential matches
        db.collection("users").document(userID).getDocument { [weak self] document, error in
            guard let self = self else { return }

            if let error = error {
                print("❌ MatchingViewModel: Failed to load user for discovery stack: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to add user to discovery: \(error.localizedDescription)"
                }
                return
            }

            guard let document = document, document.exists else {
                print("❌ MatchingViewModel: User document not found: \(userID)")
                return
            }

            do {
                var user = try document.data(as: UserModel.self)

                // CRITICAL FIX: Manually set the ID from document ID if it's nil
                if user.id == nil {
                    user.id = document.documentID
                    print("🔧 MatchingViewModel: Fixed missing ID for discovery stack - set to \(document.documentID)")
                }

                print("✅ MatchingViewModel: Successfully loaded user \(user.firstName ?? "Unknown") for discovery stack")

                DispatchQueue.main.async {
                    // Add to the beginning of potential matches so they appear next
                    if !self.potentialMatches.contains(where: { $0.id == userID }) {
                        self.potentialMatches.insert(user, at: 0)
                        print("✅ MatchingViewModel: Added user to discovery stack. Total matches: \(self.potentialMatches.count)")
                    } else {
                        print("ℹ️ MatchingViewModel: User already in discovery stack")
                    }
                }
            } catch {
                print("❌ MatchingViewModel: Failed to decode user: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to decode user data: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Enhanced mutual match detection with proper match storage and celebration
    private func checkForMutualMatch(with otherUserID: String, currentUserSuperLiked: Bool = false) {
        guard let currentUserID = self.currentUID else {
            print("❌ MatchingViewModel: Current user ID not available for mutual match check")
            return
        }

        print("🔍 MatchingViewModel: Checking for mutual match with \(otherUserID)")

        let query = db.collection("swipes")
            .whereField("from", isEqualTo: otherUserID)
            .whereField("to", isEqualTo: currentUserID)
            .whereField("liked", isEqualTo: true)
            .limit(to: 1) // Only need to know if one exists

        query.getDocuments { [weak self] snapshot, error in
            if let error = error {
                print("❌ MatchingViewModel: Error checking mutual match: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self?.errorMessage = "Failed to check for mutual match"
                }
                return
            }

            guard let snapshot = snapshot, !snapshot.documents.isEmpty else {
                print("👀 MatchingViewModel: No mutual match yet with \(otherUserID)")
                return
            }

            guard let self = self else { return }

            // Check if it was a super like from the other user
            let otherUserSuperLiked = snapshot.documents.first?.data()["superLiked"] as? Bool ?? false
            let matchType: MatchType = (currentUserSuperLiked || otherUserSuperLiked) ? .superLike : .regular

            print("💕 MatchingViewModel: MUTUAL MATCH FOUND with \(otherUserID)! Type: \(matchType.displayName)")

            // Mutual match found!
            if !self.matchedUserIDs.contains(otherUserID) {
                self.matchedUserIDs.insert(otherUserID)
                DispatchQueue.main.async {
                    self.mutualMatchesCount += 1
                    print("🎉 MatchingViewModel: New mutual match found with user: \(otherUserID)")
                }

                // Track match creation in analytics
                AnalyticsManager.shared.trackSwipe(action: .like, matchCreated: true)

                // Create the match document in Firestore
                self.createMatch(with: otherUserID, matchType: matchType, initiatedBy: currentUserID)
            }
        }
    }

    /// Create a match document in Firestore and trigger celebration
    private func createMatch(with otherUserID: String, matchType: MatchType, initiatedBy: String) {
        guard let currentUserID = self.currentUID else {
            print("❌ MatchingViewModel: Cannot create match - no current user")
            return
        }

        print("💕 MatchingViewModel: Creating match document for \(currentUserID) and \(otherUserID)")

        // Create match model
        let match = MatchModel(
            participants: [currentUserID, otherUserID].sorted(), // Sort for consistency
            createdAt: Date(),
            hasConversationStarted: false,
            isNewMatch: true,
            initiatedBy: initiatedBy,
            matchType: matchType
        )

        // Store in Firestore
        db.collection("matches").addDocument(data: match.toFirestoreData()) { [weak self] error in
            guard let self = self else { return }

            // Track match creation for rewind prevention
            DispatchQueue.main.async {
                self.lastMatchCreatedForUserID = otherUserID
                print("🚫 MatchingViewModel: Marked user \(otherUserID) as matched - rewind disabled")
            }

            if let error = error {
                print("❌ MatchingViewModel: Failed to create match document: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to create match: \(error.localizedDescription)"
                }
                return
            }

            print("✅ MatchingViewModel: Successfully created match document")

            // 🔔 SEND PUSH NOTIFICATION TO MATCHED USER
            self.sendMatchNotifications(currentUserID: currentUserID, otherUserID: otherUserID, matchType: matchType)

            print("✅ MatchingViewModel: Successfully created match document")

            // DEBUGGING: Verify the match was actually created and visible in console
            self.verifyMatchCreated(with: otherUserID)
            self.debugMatchesCollectionVisibility()

            // Trigger match celebration on main thread
            DispatchQueue.main.async {
                self.triggerMatchCelebration(with: otherUserID, matchType: matchType)
            }
        }
    }

    /// Trigger match celebration UI
    private func triggerMatchCelebration(with otherUserID: String, matchType: MatchType) {
        guard let currentUser = ProfileViewModel.shared.userProfile else {
            print("❌ MatchingViewModel: Cannot show celebration - no current user profile")
            return
        }

        // Find the matched user in potential matches
        guard let matchedUser = potentialMatches.first(where: { $0.id == otherUserID }) else {
            print("❌ MatchingViewModel: Cannot show celebration - matched user not found in potential matches")
            return
        }

        print("🎉 MatchingViewModel: Triggering match celebration for \(matchedUser.firstName ?? "Unknown")")

        // Set celebration data and show
        celebrationMatch = (currentUser: currentUser, matchedUser: matchedUser, matchType: matchType)
        showMatchCelebration = true

        // Trigger haptic feedback
        HapticFeedbackManager.shared.match()
    }

    /// Dismiss match celebration
    func dismissMatchCelebration() {
        showMatchCelebration = false
        celebrationMatch = nil
    }

    // MARK: - Push Notifications

    /// Send push notifications to both users when a match is created
    private func sendMatchNotifications(currentUserID: String, otherUserID: String, matchType: MatchType) {
        print("🔔 MatchingViewModel: Sending match notifications")

        // Get current user's name for notification
        db.collection("users").document(currentUserID).getDocument { [weak self] snapshot, error in
            guard let data = snapshot?.data(),
                  let currentUserName = data["firstName"] as? String else {
                print("❌ MatchingViewModel: Could not get current user name for notification")
                return
            }

            // Send notification to the other user
            PushNotificationService.shared.sendMatchNotification(
                to: otherUserID,
                matchedUserName: currentUserName,
                matchType: matchType
            )

            print("✅ MatchingViewModel: Match notification sent to \(otherUserID)")
        }

        // Get other user's name and send notification to current user
        db.collection("users").document(otherUserID).getDocument { [weak self] snapshot, error in
            guard let data = snapshot?.data(),
                  let otherUserName = data["firstName"] as? String else {
                print("❌ MatchingViewModel: Could not get other user name for notification")
                return
            }

            // Send notification to current user
            PushNotificationService.shared.sendMatchNotification(
                to: currentUserID,
                matchedUserName: otherUserName,
                matchType: matchType
            )

            print("✅ MatchingViewModel: Match notification sent to \(currentUserID)")
        }
    }

    /// Send super like notification to the liked user
    private func sendSuperLikeNotification(to userID: String, user: UserModel) {
        guard let currentUserID = self.currentUID else { return }

        print("⭐ MatchingViewModel: Sending super like notification to \(userID)")

        // Get current user's name for notification
        db.collection("users").document(currentUserID).getDocument { snapshot, error in
            guard let data = snapshot?.data(),
                  let currentUserName = data["firstName"] as? String else {
                print("❌ MatchingViewModel: Could not get current user name for super like notification")
                return
            }

            // Send super like notification
            PushNotificationService.shared.sendSuperLikeNotification(
                to: userID,
                from: currentUserName
            )

            print("✅ MatchingViewModel: Super like notification sent to \(userID)")
        }
    }

    /// Async version for better performance during swipes
    private func sendSuperLikeNotificationAsync(to userID: String, user: UserModel) async {
        guard let currentUserID = self.currentUID else { return }

        print("⭐ MatchingViewModel: Sending super like notification to \(userID) (async)")

        do {
            let snapshot = try await db.collection("users").document(currentUserID).getDocument()
            guard let data = snapshot.data(),
                  let currentUserName = data["firstName"] as? String else {
                print("❌ MatchingViewModel: Could not get current user name for super like notification")
                return
            }

            // Send super like notification on background thread
            Task.detached {
                PushNotificationService.shared.sendSuperLikeNotification(
                    to: userID,
                    from: currentUserName
                )
            }

            print("✅ MatchingViewModel: Super like notification sent to \(userID) (async)")
        } catch {
            print("❌ MatchingViewModel: Error sending super like notification: \(error.localizedDescription)")
        }
    }

    /// Debug function to verify matches collection
    func verifyMatchesCollection() {
        guard let currentUserID = self.currentUID else {
            print("❌ MatchingViewModel: Cannot verify matches - no current user")
            return
        }

        print("🔍 MatchingViewModel: Verifying matches collection for user: \(currentUserID)")

        db.collection("matches")
            .whereField("participants", arrayContains: currentUserID)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ MatchingViewModel: Error verifying matches: \(error.localizedDescription)")
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ MatchingViewModel: No snapshot returned for matches")
                    return
                }

                print("✅ MatchingViewModel: Found \(snapshot.documents.count) matches in Firestore")

                for (index, doc) in snapshot.documents.enumerated() {
                    let data = doc.data()
                    print("   Match \(index + 1):")
                    print("     - ID: \(doc.documentID)")
                    print("     - Participants: \(data["participants"] ?? "unknown")")
                    print("     - Created: \(data["createdAt"] ?? "unknown")")
                    print("     - Type: \(data["matchType"] ?? "unknown")")
                    print("     - New: \(data["isNewMatch"] ?? "unknown")")
                }
            }
    }

    /// Debug function to verify a specific match was created
    private func verifyMatchCreated(with otherUserID: String) {
        guard let currentUserID = self.currentUID else {
            print("❌ MatchingViewModel: Cannot verify match creation - no current user")
            return
        }

        let participants = [currentUserID, otherUserID].sorted()
        print("🔍 MatchingViewModel: Verifying match creation between \(participants)")

        db.collection("matches")
            .whereField("participants", isEqualTo: participants)
            .limit(to: 1)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ MatchingViewModel: Error verifying match creation: \(error.localizedDescription)")
                    return
                }

                guard let snapshot = snapshot, !snapshot.documents.isEmpty else {
                    print("❌ MatchingViewModel: CRITICAL - Match document was NOT created despite success message!")
                    print("   - Expected participants: \(participants)")
                    print("   - This indicates a Firestore rules or data structure issue")
                    return
                }

                let doc = snapshot.documents.first!
                let data = doc.data()
                print("✅ MatchingViewModel: Match document verified successfully!")
                print("   - Document ID: \(doc.documentID)")
                print("   - Participants: \(data["participants"] ?? "unknown")")
                print("   - Created: \(data["createdAt"] ?? "unknown")")
                print("   - Match Type: \(data["matchType"] ?? "unknown")")
            }
    }

    /// Debug function to verify matches collection visibility in Firebase Console
    private func debugMatchesCollectionVisibility() {
        print("🔍 MatchingViewModel: Debugging matches collection visibility...")

        // Query all matches to verify collection exists and is accessible
        db.collection("matches")
            .limit(to: 5)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ MatchingViewModel: Error accessing matches collection: \(error.localizedDescription)")
                    print("   - This may indicate a Firestore rules or indexing issue")
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ MatchingViewModel: No snapshot returned from matches collection")
                    return
                }

                print("✅ MatchingViewModel: Matches collection is accessible")
                print("   - Total documents found: \(snapshot.documents.count)")
                print("   - Collection should now be visible in Firebase Console")

                // Log sample document structure for debugging
                if let firstDoc = snapshot.documents.first {
                    print("   - Sample document ID: \(firstDoc.documentID)")
                    print("   - Sample document data: \(firstDoc.data())")
                }

                // Verify collection indexing
                self.verifyCollectionIndexing()
            }
    }

    /// Verify that the matches collection has proper indexing for Firebase Console visibility
    private func verifyCollectionIndexing() {
        print("🔍 MatchingViewModel: Verifying collection indexing...")

        // Test common queries that Firebase Console might use
        let queries = [
            db.collection("matches").order(by: "createdAt", descending: true).limit(to: 1),
            db.collection("matches").whereField("participants", arrayContains: "test").limit(to: 1)
        ]

        for (index, query) in queries.enumerated() {
            query.getDocuments { snapshot, error in
                if let error = error {
                    print("⚠️ MatchingViewModel: Query \(index + 1) failed: \(error.localizedDescription)")
                    if error.localizedDescription.contains("index") {
                        print("   - This indicates missing Firestore indexes")
                        print("   - Collection may not be fully visible in Firebase Console until indexes are created")
                    }
                } else {
                    print("✅ MatchingViewModel: Query \(index + 1) successful - indexing appears correct")
                }
            }
        }
    }

    func createChatIfNotExists(userA: String, userB: String) {
        let chatsRef = db.collection("chats")

        // More efficient query - check if chat already exists
        chatsRef
            .whereField("participants", arrayContains: userA)
            .limit(to: 50) // Limit for performance
            .getDocuments { [weak self] snapshot, error in
                if let error = error {
                    print("Error searching for existing chat: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        self?.errorMessage = "Failed to create chat"
                    }
                    return
                }

                // Check if chat already exists
                if let snapshot = snapshot {
                    for doc in snapshot.documents {
                        let participants = doc.data()["participants"] as? [String] ?? []
                        if participants.contains(userB) {
                            print("Chat already exists between \(userA) and \(userB)")
                            return // Chat already exists
                        }
                    }
                }

                // Create new chat
                let chatData: [String: Any] = [
                    "participants": [userA, userB],
                    "createdAt": FieldValue.serverTimestamp(),
                    "lastMessageAt": FieldValue.serverTimestamp(),
                    "isTyping": false,
                    "lastMessage": "",
                    "unreadCount": [userA: 0, userB: 0]
                ]

                chatsRef.addDocument(data: chatData) { [weak self] error in
                    if let error = error {
                        print("Error creating chat: \(error.localizedDescription)")
                        DispatchQueue.main.async {
                            self?.errorMessage = "Failed to create chat: \(error.localizedDescription)"
                        }
                    } else {
                        print("Successfully created chat between \(userA) and \(userB)")
                    }
                }
            }
    }



    #if DEBUG
    /// Debug method to clear swipe history (for testing only)
    func clearSwipeHistory() {
        guard let uid = currentUID else { return }

        print("🧹 MatchingViewModel: Clearing swipe history for debugging")

        db.collection("swipes")
            .whereField("from", isEqualTo: uid)
            .getDocuments { [weak self] snapshot, error in
                if let error = error {
                    print("❌ Error fetching swipes to clear: \(error)")
                    return
                }

                guard let documents = snapshot?.documents else { return }

                let batch = self?.db.batch()
                for document in documents {
                    batch?.deleteDocument(document.reference)
                }

                batch?.commit { error in
                    if let error = error {
                        print("❌ Error clearing swipe history: \(error)")
                    } else {
                        print("✅ Cleared \(documents.count) swipe records")
                        DispatchQueue.main.async {
                            self?.forceRefresh()
                        }
                    }
                }
            }
    }
    #endif

    /// Simplified profile image validation - check if user has at least 1 image total
    private func hasValidProfileImage(_ user: UserModel) -> Bool {
        var imageCount = 0

        // Count legacy single image
        if let imageUrl = user.profileImageUrl, !imageUrl.isEmpty {
            imageCount += 1
        }

        // Count multiple images array
        if let imageUrls = user.profileImageUrls {
            imageCount += imageUrls.filter { !$0.isEmpty }.count
        }

        return imageCount >= 1
    }

    /// Cleanup method for memory management






    #if DEBUG
    /// Quick debug method for console testing (DEBUG ONLY)
    func quickDebugDiscovery() {
        Logger.shared.debug("Starting comprehensive discovery debug...")
        debugDiscoveryPipeline { report in
            Logger.shared.debug("Debug completed")
        }
    }

    /// Force fetch potential matches bypassing rate limiting (DEBUG ONLY)
    func forceFetchPotentialMatches() {
        Logger.shared.debug("Forcing fresh fetch (bypassing rate limit)")

        // Reset rate limiting
        lastFetchTime = nil
        isFetching = false

        // Clear current matches
        DispatchQueue.main.async {
            self.potentialMatches = []
            self.isLoading = true
        }

        // Force fetch
        fetchPotentialMatches()
    }
    #endif

    #if DEBUG
    /// Debug method to check and fix user ID issues in the database (DEBUG ONLY)
    func debugUserIDIssues() {
        Logger.shared.debug("Checking for users with missing IDs...")

        db.collection("users").limit(to: 10).getDocuments { snapshot, error in
            if let error = error {
                Logger.shared.error("Error fetching users for ID debug: \(error.localizedDescription)")
                return
            }

            guard let documents = snapshot?.documents else {
                Logger.shared.warning("No documents found for ID debug")
                return
            }

            Logger.shared.debug("Checking \(documents.count) users for ID issues...")

            for doc in documents {
                do {
                    var user = try doc.data(as: UserModel.self)
                    let docID = doc.documentID

                    if user.id == nil {
                        Logger.shared.warning("Found user with missing ID: \(user.firstName ?? "Unknown") (\(user.email))")
                        Logger.shared.debug("Document ID: \(docID) - Will be fixed automatically on next fetch")
                    } else {
                        Logger.shared.debug("User ID OK: \(user.firstName ?? "Unknown") - ID: \(user.id!)")
                    }
                } catch {
                    Logger.shared.error("Failed to decode user \(doc.documentID): \(error.localizedDescription)")
                }
            }
        }
    }
    #endif

    #if DEBUG
    /// Debug SmartMatchingEngine scoring for a specific user (DEBUG ONLY)
    func debugSmartMatchingScore(targetUserID: String) {
        guard let currentUser = ProfileViewModel.shared.userProfile else {
            print("❌ No current user profile loaded")
            return
        }

        guard let filterSettings = currentUser.filterSettings else {
            print("❌ No filter settings found")
            return
        }

        print("🎯 MatchingViewModel.debugSmartMatchingScore: Testing scoring for \(targetUserID)")
        print("   - Current user: \(currentUser.firstName ?? "Unknown")")
        print("   - Filter mode: \(filterSettings.mode ?? "none")")
        print("   - College filter: \(filterSettings.collegeName ?? "none")")

        // Load the target user
        ProfileLoaderService.shared.loadUserProfile(userID: targetUserID) { result in
            switch result {
            case .success(let targetUser):
                print("✅ Target user loaded: \(targetUser.firstName ?? "Unknown")")

                // Test the scoring
                let score = SmartMatchingEngine.calculateFilterMatchScore(
                    filterSettings: filterSettings,
                    otherUser: targetUser,
                    currentUser: currentUser
                )

                print("🎯 FINAL SCORE: \(score)")
                print("   - Will be included: \(score > 0 ? "✅ YES" : "❌ NO")")

            case .failure(let error):
                print("❌ Failed to load target user: \(error.localizedDescription)")
            }
        }
    }

    /// Helper method to get swiped user IDs for debugging
    private func getSwipedUserIDs(for userID: String, completion: @escaping ([String]) -> Void) {
        db.collection("swipes")
            .whereField("swiperId", isEqualTo: userID)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ Error fetching swipe history: \(error.localizedDescription)")
                    completion([])
                    return
                }

                let swipedIDs = snapshot?.documents.compactMap { doc in
                    doc.data()["swipedUserId"] as? String
                } ?? []

                completion(swipedIDs)
            }
    }

    private func debugSwipeHistory(currentUID: String, completion: @escaping (String) -> Void) {
        var report = "🔍 Swipe History Debug:\n"

        // Get swipe history from MatchingViewModel
        getSwipedUserIDs(for: currentUID) { swipedIDs in
            report += "✅ Retrieved swipe history: \(swipedIDs.count) users\n"

            if swipedIDs.isEmpty {
                report += "   📝 No previous swipes found\n"
            } else {
                report += "   📝 Previously swiped users:\n"
                for (index, userID) in swipedIDs.prefix(10).enumerated() {
                    report += "      \(index + 1). \(userID)\n"
                }
                if swipedIDs.count > 10 {
                    report += "      ... and \(swipedIDs.count - 10) more\n"
                }
            }

            completion(report)
        }
    }

    /// Comprehensive debug method to analyze discovery issues
    func debugDiscoveryPipeline(completion: @escaping (String) -> Void) {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            completion("❌ No authenticated user")
            return
        }

        var debugReport = "🔍 DISCOVERY PIPELINE DEBUG REPORT\n"
        debugReport += String(repeating: "=", count: 60) + "\n"
        debugReport += "Current User: \(currentUID)\n"
        debugReport += "Timestamp: \(Date())\n\n"

        // Step 1: Check current user's profile
        debugReport += "📋 STEP 1: CURRENT USER PROFILE ANALYSIS\n"
        debugReport += String(repeating: "-", count: 40) + "\n"

        ProfileLoaderService.shared.debugProfileEligibility(userID: currentUID) { currentUserReport in
            debugReport += currentUserReport + "\n"

            // Step 2: Check Firestore query
            debugReport += "📊 STEP 2: FIRESTORE DISCOVERY QUERY ANALYSIS\n"
            debugReport += String(repeating: "-", count: 40) + "\n"

            self.debugFirestoreQuery(currentUID: currentUID) { queryReport in
                debugReport += queryReport + "\n"

                // Step 3: Check swipe history
                debugReport += "📝 STEP 3: SWIPE HISTORY ANALYSIS\n"
                debugReport += String(repeating: "-", count: 40) + "\n"

                self.debugSwipeHistory(currentUID: currentUID) { swipeReport in
                    debugReport += swipeReport + "\n"

                    // Step 4: Check matching algorithm
                    debugReport += "🎯 STEP 4: MATCHING ALGORITHM ANALYSIS\n"
                    debugReport += String(repeating: "-", count: 40) + "\n"

                    self.debugMatchingAlgorithm(currentUID: currentUID) { algorithmReport in
                        debugReport += algorithmReport + "\n"

                        debugReport += String(repeating: "=", count: 60) + "\n"
                        debugReport += "🏁 DEBUG REPORT COMPLETE\n"

                        print(debugReport)
                        completion(debugReport)
                    }
                }
            }
        }
    }

    private func debugFirestoreQuery(currentUID: String, completion: @escaping (String) -> Void) {
        var report = "🔍 Firestore Query Debug:\n"

        // Test the exact query used in loadPotentialMatches
        db.collection("users")
            .whereField("isEmailVerified", isEqualTo: true)
            .whereField("hideFromDiscovery", isEqualTo: false)
            .limit(to: 100)
            .getDocuments { snapshot, error in
                if let error = error {
                    report += "❌ Query Error: \(error.localizedDescription)\n"
                    completion(report)
                    return
                }

                guard let documents = snapshot?.documents else {
                    report += "❌ No documents returned\n"
                    completion(report)
                    return
                }

                report += "✅ Query returned \(documents.count) documents\n"
                report += "📊 Document Analysis:\n"

                var validProfiles = 0
                var invalidProfiles = 0
                var currentUserFound = false

                for doc in documents {
                    let _ = doc.data()
                    let docUID = doc.documentID

                    if docUID == currentUID {
                        currentUserFound = true
                        report += "   🔍 Current user found in query results\n"
                    }

                    // Try to decode each profile
                    do {
                        let user = try doc.data(as: UserModel.self)
                        validProfiles += 1

                        // Check basic eligibility
                        let hasFirstName = user.firstName != nil && !user.firstName!.isEmpty
                        let hasProfileImage = (user.profileImageUrl != nil && !user.profileImageUrl!.isEmpty) ||
                                            (user.profileImageUrls?.first != nil && !user.profileImageUrls!.first!.isEmpty)

                        if !hasFirstName || !hasProfileImage {
                            report += "   ⚠️ \(user.firstName ?? "Unknown") (\(docUID)): Missing requirements\n"
                            report += "      - Has firstName: \(hasFirstName)\n"
                            report += "      - Has profile image: \(hasProfileImage)\n"
                        }
                    } catch {
                        invalidProfiles += 1
                        report += "   ❌ Failed to decode \(docUID): \(error.localizedDescription)\n"
                    }
                }

                report += "\n📈 Query Summary:\n"
                report += "   - Total documents: \(documents.count)\n"
                report += "   - Valid profiles: \(validProfiles)\n"
                report += "   - Invalid profiles: \(invalidProfiles)\n"
                report += "   - Current user in results: \(currentUserFound ? "✅ YES" : "❌ NO")\n"

                completion(report)
            }
    }

    private func debugMatchingAlgorithm(currentUID: String, completion: @escaping (String) -> Void) {
        var report = "🔍 Matching Algorithm Debug:\n"

        // Get current user's filter settings
        if let currentUser = ProfileViewModel.shared.userProfile {
            report += "✅ Current user profile loaded\n"
            report += "   - Filter settings: \(currentUser.filterSettings != nil ? "✅ Present" : "❌ Missing")\n"

            if let filters = currentUser.filterSettings {
                report += "   - Budget range: \(filters.budgetMin ?? 0) - \(filters.budgetMax ?? 2000)\n"
                report += "   - Preferred gender: \(filters.preferredGender ?? "Any")\n"
                report += "   - Housing status: \(filters.housingStatus ?? "Any")\n"
                report += "   - Max distance: \(filters.maxDistance ?? 50) miles\n"
            }

            // Test SmartMatchingEngine
            report += "\n🎯 SmartMatchingEngine Test:\n"

            // Create a test user to see if filtering works
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            let testDate = dateFormatter.date(from: "1995-01-01") ?? Date()

            let testUser = UserModel(
                email: "<EMAIL>",
                isEmailVerified: true,
                firstName: "Test",
                lastName: "User",
                dateOfBirth: testDate,
                gender: "Male",
                collegeName: "Test University",
                housingStatus: "Looking for roommate"
            )

            let compatibilityScore = SmartMatchingEngine.calculateSmartMatchScore(between: currentUser, and: testUser)
            report += "   - Test compatibility score: \(compatibilityScore)\n"

        } else {
            report += "❌ Current user profile not loaded\n"
        }

        // Check current state
        report += "\n📊 Current MatchingViewModel State:\n"
        report += "   - Is loading: \(self.isLoading)\n"
        report += "   - Error message: \(self.errorMessage ?? "None")\n"
        report += "   - Potential matches count: \(self.potentialMatches.count)\n"
        report += "   - Potential matches loaded: \(self.potentialMatches.count > 0 ? "✅ YES" : "❌ NO")\n"

        completion(report)
    }

    /// Debug why current user might not appear in other users' discovery
    func debugCurrentUserVisibility(completion: @escaping (String) -> Void) {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            completion("❌ No authenticated user")
            return
        }

        var report = "🔍 CURRENT USER VISIBILITY DEBUG\n"
        report += String(repeating: "=", count: 50) + "\n"
        report += "Checking why \(currentUID) might not appear in others' discovery\n\n"

        // Check if current user meets basic discovery requirements
        ProfileLoaderService.shared.debugProfileEligibility(userID: currentUID) { eligibilityReport in
            report += "📋 ELIGIBILITY CHECK:\n"
            report += eligibilityReport + "\n"

            // Check if current user appears in the discovery query
            self.db.collection("users")
                .whereField("isEmailVerified", isEqualTo: true)
                .whereField("hideFromDiscovery", isEqualTo: false)
                .getDocuments { snapshot, error in
                    if let error = error {
                        report += "❌ Query Error: \(error.localizedDescription)\n"
                        completion(report)
                        return
                    }

                    guard let documents = snapshot?.documents else {
                        report += "❌ No documents in discovery query\n"
                        completion(report)
                        return
                    }

                    let currentUserInQuery = documents.contains { $0.documentID == currentUID }

                    report += "🔍 DISCOVERY QUERY CHECK:\n"
                    report += "   - Total users in discovery: \(documents.count)\n"
                    report += "   - Current user in query: \(currentUserInQuery ? "✅ YES" : "❌ NO")\n"

                    if !currentUserInQuery {
                        report += "\n❌ ISSUE FOUND: Current user not in discovery query\n"
                        report += "   Possible reasons:\n"
                        report += "   - isEmailVerified = false\n"
                        report += "   - hideFromDiscovery = true\n"
                        report += "   - Profile document doesn't exist\n"
                    }

                    // Check specific field values
                    self.db.collection("users").document(currentUID).getDocument { snapshot, error in
                        if let error = error {
                            report += "\n❌ Error fetching current user doc: \(error.localizedDescription)\n"
                            completion(report)
                            return
                        }

                        guard let snapshot = snapshot, snapshot.exists else {
                            report += "\n❌ CRITICAL: Current user document doesn't exist!\n"
                            completion(report)
                            return
                        }

                        let data = snapshot.data() ?? [:]
                        report += "\n📊 CURRENT USER DOCUMENT FIELDS:\n"
                        report += "   - isEmailVerified: \(data["isEmailVerified"] ?? "missing")\n"
                        report += "   - hideFromDiscovery: \(data["hideFromDiscovery"] ?? "missing")\n"
                        report += "   - firstName: \(data["firstName"] ?? "missing")\n"
                        report += "   - profileImageUrl: \(data["profileImageUrl"] ?? "missing")\n"
                        report += "   - profileImageUrls: \(data["profileImageUrls"] ?? "missing")\n"

                        print(report)
                        completion(report)
                    }
                }
        }
    }

    /// Test if a specific user would see the current user in their discovery
    func debugMutualVisibility(otherUserID: String, completion: @escaping (String) -> Void) {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            completion("❌ No authenticated user")
            return
        }

        var report = "🔍 MUTUAL VISIBILITY TEST\n"
        report += String(repeating: "=", count: 40) + "\n"
        report += "Testing if \(otherUserID) would see \(currentUID)\n\n"

        // Get both users' profiles
        let group = DispatchGroup()
        var currentUser: UserModel?
        var otherUser: UserModel?
        var errors: [String] = []

        // Load current user
        group.enter()
        ProfileLoaderService.shared.loadUserProfile(userID: currentUID) { result in
            switch result {
            case .success(let user):
                currentUser = user
            case .failure(let error):
                errors.append("Current user: \(error.localizedDescription)")
            }
            group.leave()
        }

        // Load other user
        group.enter()
        ProfileLoaderService.shared.loadUserProfile(userID: otherUserID) { result in
            switch result {
            case .success(let user):
                otherUser = user
            case .failure(let error):
                errors.append("Other user: \(error.localizedDescription)")
            }
            group.leave()
        }

        group.notify(queue: .main) {
            if !errors.isEmpty {
                report += "❌ Errors loading profiles:\n"
                for error in errors {
                    report += "   - \(error)\n"
                }
                completion(report)
                return
            }

            guard let current = currentUser, let other = otherUser else {
                report += "❌ Failed to load one or both profiles\n"
                completion(report)
                return
            }

            report += "✅ Both profiles loaded successfully\n\n"

            // Test compatibility from other user's perspective
            let compatibilityScore = SmartMatchingEngine.calculateSmartMatchScore(between: other, and: current)
            report += "🎯 COMPATIBILITY TEST:\n"
            report += "   - Compatibility score from \(other.firstName ?? "Other") to \(current.firstName ?? "Current"): \(compatibilityScore)\n"

            // Test basic eligibility
            let hasFirstName = current.firstName != nil && !current.firstName!.isEmpty
            let hasProfileImage = (current.profileImageUrl != nil && !current.profileImageUrl!.isEmpty) ||
                                (current.profileImageUrls?.first != nil && !current.profileImageUrls!.first!.isEmpty)

            report += "\n📋 BASIC ELIGIBILITY:\n"
            report += "   - Has firstName: \(hasFirstName ? "✅" : "❌")\n"
            report += "   - Has profile image: \(hasProfileImage ? "✅" : "❌")\n"
            report += "   - Email verified: \(current.isEmailVerified ? "✅" : "❌")\n"
            report += "   - Hidden from discovery: \(current.hideFromDiscovery == true ? "❌ YES" : "✅ NO")\n"

            print(report)
            completion(report)
        }
    }

    /// Debug method to check if current user appears in discovery for other users
    func debugCurrentUserInDiscovery(completion: @escaping (String) -> Void) {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            completion("❌ No authenticated user")
            return
        }

        var report = "🔍 CHECKING IF CURRENT USER APPEARS IN DISCOVERY\n"
        report += String(repeating: "=", count: 50) + "\n"

        // Check if current user meets discovery criteria
        db.collection("users")
            .whereField("isEmailVerified", isEqualTo: true)
            .whereField("hideFromDiscovery", isEqualTo: false)
            .getDocuments { snapshot, error in
                if let error = error {
                    report += "❌ Query Error: \(error.localizedDescription)\n"
                    completion(report)
                    return
                }

                guard let documents = snapshot?.documents else {
                    report += "❌ No documents found\n"
                    completion(report)
                    return
                }

                let currentUserInQuery = documents.contains { $0.documentID == currentUID }

                report += "📊 DISCOVERY QUERY RESULTS:\n"
                report += "   - Total users in discovery: \(documents.count)\n"
                report += "   - Current user included: \(currentUserInQuery ? "✅ YES" : "❌ NO")\n\n"

                if currentUserInQuery {
                    // Check if current user has valid profile data
                    if let currentUserDoc = documents.first(where: { $0.documentID == currentUID }) {
                        do {
                            let currentUser = try currentUserDoc.data(as: UserModel.self)

                            report += "✅ CURRENT USER PROFILE ANALYSIS:\n"
                            report += "   - Name: \(currentUser.firstName ?? "nil") \(currentUser.lastName ?? "nil")\n"
                            report += "   - Email: \(currentUser.email)\n"
                            report += "   - Email Verified: \(currentUser.isEmailVerified)\n"
                            report += "   - Hidden: \(currentUser.hideFromDiscovery ?? false)\n"

                            let hasProfileImage = (currentUser.profileImageUrl != nil && !currentUser.profileImageUrl!.isEmpty) ||
                                                (currentUser.profileImageUrls?.first != nil && !currentUser.profileImageUrls!.first!.isEmpty)
                            report += "   - Has Profile Image: \(hasProfileImage ? "✅" : "❌")\n"

                            if hasProfileImage {
                                report += "     * profileImageUrl: \(currentUser.profileImageUrl ?? "nil")\n"
                                report += "     * profileImageUrls: \(currentUser.profileImageUrls?.count ?? 0) images\n"
                            }

                            report += "\n🎯 DISCOVERY ELIGIBILITY: \(hasProfileImage ? "✅ ELIGIBLE" : "❌ NOT ELIGIBLE")\n"

                        } catch {
                            report += "❌ Failed to decode current user: \(error.localizedDescription)\n"
                        }
                    }
                } else {
                    report += "❌ ISSUE: Current user not in discovery query\n"
                    report += "   This means other users will NOT see you in their discovery\n"
                    report += "   Check: isEmailVerified and hideFromDiscovery fields\n"
                }

                print(report)
                completion(report)
            }
    }

    /// Test method to simulate what another user would see
    func simulateOtherUserDiscovery(completion: @escaping (String) -> Void) {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            completion("❌ No authenticated user")
            return
        }

        var report = "🎮 SIMULATING OTHER USER'S DISCOVERY VIEW\n"
        report += String(repeating: "=", count: 45) + "\n"

        // Get all users that would appear in discovery (excluding current user)
        db.collection("users")
            .whereField("isEmailVerified", isEqualTo: true)
            .whereField("hideFromDiscovery", isEqualTo: false)
            .limit(to: 20)
            .getDocuments { snapshot, error in
                if let error = error {
                    report += "❌ Query Error: \(error.localizedDescription)\n"
                    completion(report)
                    return
                }

                guard let documents = snapshot?.documents else {
                    report += "❌ No documents found\n"
                    completion(report)
                    return
                }

                let otherUsers = documents.compactMap { doc -> UserModel? in
                    guard doc.documentID != currentUID else { return nil }
                    return try? doc.data(as: UserModel.self)
                }

                report += "📊 SIMULATION RESULTS:\n"
                report += "   - Total users in discovery: \(documents.count)\n"
                report += "   - Other users (excluding you): \(otherUsers.count)\n"
                report += "   - Current user in results: \(documents.contains { $0.documentID == currentUID } ? "✅ YES" : "❌ NO")\n\n"

                if otherUsers.isEmpty {
                    report += "❌ NO OTHER USERS FOUND\n"
                    report += "   This explains why discovery is empty\n"
                } else {
                    report += "✅ OTHER USERS FOUND:\n"
                    for (index, user) in otherUsers.prefix(5).enumerated() {
                        let hasImage = (user.profileImageUrl != nil && !user.profileImageUrl!.isEmpty) ||
                                     (user.profileImageUrls?.first != nil && !user.profileImageUrls!.first!.isEmpty)
                        report += "   \(index + 1). \(user.firstName ?? "Unknown") - Image: \(hasImage ? "✅" : "❌")\n"
                    }
                    if otherUsers.count > 5 {
                        report += "   ... and \(otherUsers.count - 5) more\n"
                    }
                }

                Logger.shared.debug(report)
                completion(report)
            }
    }
    #endif

    deinit {
        cancellables.forEach { $0.cancel() }
    }
}
