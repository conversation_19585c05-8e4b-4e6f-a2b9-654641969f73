//
//  PrivacyPolicyView.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                    // Header
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("Privacy Policy")
                            .font(.custom("AvenirNext-Bold", size: 28))
                            .foregroundStyle(AppTheme.sexyGradient)
                        
                        Text("Effective Date: January 2025")
                            .font(.custom("AvenirNext-Medium", size: 14))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                    .padding(.bottom, AppTheme.spacing16)
                    
                    // Introduction
                    PolicySection(
                        title: "1. Introduction",
                        content: "Welcome to CampusPads. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services. Your privacy is important to us, and we are committed to protecting your personal information."
                    )
                    
                    // Information We Collect
                    PolicySection(
                        title: "2. Information We Collect",
                        content: """
                        **Personal Information You Provide:**
                        • Account Information: Email, name, date of birth, gender, college
                        • Profile Information: Photos, bio, academic details, housing preferences
                        • Communication Data: Messages and match interactions
                        • Housing Information: Property details and roommate criteria
                        
                        **Automatically Collected Information:**
                        • Location Data: Approximate location for matching (with permission)
                        • Device Information: Device type, OS, unique identifiers
                        • Usage Data: App interactions and time spent
                        • Technical Data: IP address, app version
                        """
                    )
                    
                    // How We Use Information
                    PolicySection(
                        title: "3. How We Use Your Information",
                        content: """
                        **Primary Purposes:**
                        • Connect you with compatible roommates and housing
                        • Enable messaging between matched users
                        • Verify profiles and ensure user safety
                        • Enhance app functionality and user experience
                        
                        **Secondary Purposes:**
                        • Provide customer support
                        • Comply with legal requirements
                        • Conduct analytics and research
                        """
                    )
                    
                    // Information Sharing
                    PolicySection(
                        title: "4. Information Sharing",
                        content: """
                        **With Other Users:**
                        • Profile information visible to potential matches
                        • Approximate location only (never exact address)
                        • Messages shared only with matched users
                        
                        **With Third Parties:**
                        • Service providers (cloud hosting, analytics)
                        • Legal requirements when required by law
                        • Business transfers (merger, acquisition)
                        
                        **We Never Sell Your Data** - We do not sell, rent, or trade your personal information.
                        """
                    )
                    
                    // Data Security
                    PolicySection(
                        title: "5. Data Security",
                        content: """
                        **Our Security Measures:**
                        • Data encrypted in transit and at rest
                        • Limited access to authorized personnel only
                        • Regular security audits and testing
                        • Industry-standard cloud security practices
                        
                        **Your Responsibilities:**
                        • Keep login credentials secure
                        • Report security concerns to us
                        • Don't share sensitive information in messages
                        """
                    )
                    
                    // Your Rights
                    PolicySection(
                        title: "6. Your Privacy Rights",
                        content: """
                        **Access and Control:**
                        • View and access your personal information
                        • Update or correct your data
                        • Request account deletion
                        • Export your data in common formats
                        
                        **Communication Preferences:**
                        • Control push notifications and emails
                        • Opt out of promotional communications
                        • Enable/disable location services
                        """
                    )
                    
                    // Contact Information
                    PolicySection(
                        title: "7. Contact Us",
                        content: """
                        If you have questions about this Privacy Policy:
                        
                        • Email: <EMAIL>
                        • In-App: Use the "Report a Problem" feature
                        • We respond to privacy inquiries within 48 hours
                        
                        For California residents (CCPA) and EU residents (GDPR), you have additional rights regarding your personal information.
                        """
                    )
                    
                    // Footer
                    VStack(spacing: AppTheme.spacing8) {
                        Text("Last Updated: January 2025")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(AppTheme.textSecondary)
                        
                        Text("This Privacy Policy is designed to be transparent about our data practices while protecting your privacy and enabling our roommate matching services.")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, AppTheme.spacing24)
                }
                .padding(AppTheme.spacing20)
            }
            .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
            .navigationBarHidden(true)
            .overlay(
                // Custom close button
                VStack {
                    HStack {
                        Spacer()
                        Button(action: { dismiss() }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(AppTheme.textSecondary)
                                .background(Color.black.opacity(0.1))
                                .clipShape(Circle())
                        }
                        .padding(.trailing, AppTheme.spacing20)
                        .padding(.top, AppTheme.spacing20)
                    }
                    Spacer()
                }
            )
        }
    }
}

struct PolicySection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            Text(title)
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            Text(content)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)
                .lineSpacing(4)
        }
        .padding(AppTheme.spacing16)
        .background(AppTheme.glassEffect)
        .cornerRadius(AppTheme.radiusMedium)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }
}

#Preview {
    PrivacyPolicyView()
}
