//
//  FirebaseAuthService.swift
//  CampusPads
//
//  Created by [Your Name] on [Date].
//

import Foundation
import Firebase
import FirebaseAuth
import FirebaseFirestore
import FirebaseFirestoreCombineSwift // Enables setData(from:) and @DocumentID

class FirebaseAuthService {

    private let db = Firestore.firestore()

    /// Signs up a user with any valid email address, sends a verification email,
    /// and creates a user document in Firestore if successful.
    /// - Parameters:
    ///   - email: The email entered by the user
    ///   - password: The password
    ///   - completion: Escaping closure that returns a result: success or error
    func signUpWithEmail(
        email: String,
        password: String,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        // Check network connectivity
        if !NetworkMonitor.shared.isConnected {
            let networkError = NSError(
                domain: "NetworkError",
                code: 1003,
                userInfo: [NSLocalizedDescriptionKey: "No internet connection. Please check your network and try again."]
            )
            completion(.failure(networkError))
            return
        }

        Logger.shared.info("Network check passed, proceeding with signup")
        // 1. Validate email format (any valid email now accepted)
        guard isValidEmail(email) else {
            let domainError = NSError(
                domain: "EmailValidation",
                code: 1001,
                userInfo: [NSLocalizedDescriptionKey: "Please use a valid email address."]
            )
            completion(.failure(domainError))
            return
        }

        // 2. Validate password strength
        let passwordValidation = isValidPassword(password)
        guard passwordValidation.isValid else {
            let passwordError = NSError(
                domain: "PasswordValidation",
                code: 1002,
                userInfo: [NSLocalizedDescriptionKey: passwordValidation.message ?? "Invalid password"]
            )
            completion(.failure(passwordError))
            return
        }

        // 3. Check if user actually exists in our database before attempting signup
        checkUserExistsInDatabase(email: email) { [weak self] userExists in
            if userExists {
                Logger.shared.warning("User exists in database but not in Auth - attempting cleanup")
                // User exists in our database but not in Firebase Auth (orphaned data)
                // This can happen if account was deleted from Firebase Auth but not from Firestore
                self?.cleanupOrphanedUserData(email: email) { cleanupResult in
                    switch cleanupResult {
                    case .success:
                        Logger.shared.info("Orphaned data cleaned up, proceeding with signup")
                        self?.createUserInFirebaseAuth(email: email, password: password, completion: completion)
                    case .failure(let cleanupError):
                        Logger.shared.error("Failed to cleanup orphaned data: \(cleanupError.localizedDescription)")
                        completion(.failure(cleanupError))
                    }
                }
            } else {
                // No existing user data, proceed with normal signup
                self?.createUserInFirebaseAuth(email: email, password: password, completion: completion)
            }
        }
    }

    /// Signs up a user with email, password, and age verification
    /// - Parameters:
    ///   - email: The email entered by the user
    ///   - password: The password
    ///   - dateOfBirth: The user's date of birth for age verification
    ///   - completion: Escaping closure that returns a result: success or error
    func signUpWithEmailAndAge(
        email: String,
        password: String,
        dateOfBirth: Date,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        // Check network connectivity
        if !NetworkMonitor.shared.isConnected {
            let networkError = NSError(
                domain: "NetworkError",
                code: 1003,
                userInfo: [NSLocalizedDescriptionKey: "No internet connection. Please check your network and try again."]
            )
            completion(.failure(networkError))
            return
        }

        print("🔄 FirebaseAuthService: Network check passed, proceeding with signup with age verification")
        // 1. Validate email format
        guard isValidEmail(email) else {
            let domainError = NSError(
                domain: "EmailValidation",
                code: 1001,
                userInfo: [NSLocalizedDescriptionKey: "Please use a valid email address."]
            )
            completion(.failure(domainError))
            return
        }

        // 2. Validate password strength
        let passwordValidation = isValidPassword(password)
        guard passwordValidation.isValid else {
            let passwordError = NSError(
                domain: "PasswordValidation",
                code: 1002,
                userInfo: [NSLocalizedDescriptionKey: passwordValidation.message ?? "Invalid password"]
            )
            completion(.failure(passwordError))
            return
        }

        // 3. Check if user exists in database before attempting signup
        checkUserExistsInDatabase(email: email) { [weak self] userExists in
            if userExists {
                print("⚠️ FirebaseAuthService: User exists in database but not in Auth - attempting cleanup")
                self?.cleanupOrphanedUserData(email: email) { cleanupResult in
                    switch cleanupResult {
                    case .success:
                        print("✅ FirebaseAuthService: Orphaned data cleaned up, proceeding with signup")
                        self?.createUserInFirebaseAuthWithAge(email: email, password: password, dateOfBirth: dateOfBirth, completion: completion)
                    case .failure(let cleanupError):
                        print("❌ FirebaseAuthService: Failed to cleanup orphaned data: \(cleanupError.localizedDescription)")
                        completion(.failure(cleanupError))
                    }
                }
            } else {
                // No existing user data, proceed with normal signup
                self?.createUserInFirebaseAuthWithAge(email: email, password: password, dateOfBirth: dateOfBirth, completion: completion)
            }
        }
    }

    // 4. Create user in FirebaseAuth (extracted to separate method)
    private func createUserInFirebaseAuth(email: String, password: String, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🔄 FirebaseAuthService: Attempting to create user with email: \(email)")
        Auth.auth().createUser(withEmail: email, password: password) { [weak self] authResult, error in
            if let error = error {
                print("❌ FirebaseAuthService: User creation failed: \(error.localizedDescription)")
                print("❌ Error details: \(error)")
                completion(.failure(error))
                return
            }

            guard let user = authResult?.user else {
                let nilUserError = NSError(
                    domain: "AuthResult",
                    code: 0,
                    userInfo: [NSLocalizedDescriptionKey: "User not found after sign-up."]
                )
                completion(.failure(nilUserError))
                return
            }

            // 4. Send verification email
            user.sendEmailVerification { verificationError in
                if let verificationError = verificationError {
                    completion(.failure(verificationError))
                    return
                }

                // 5. Create user document in Firestore with enhanced security
                self?.createUserDocument(for: user) { firestoreResult in
                    switch firestoreResult {
                    case .success:
                        completion(.success(()))
                    case .failure(let err):
                        completion(.failure(err))
                    }
                }
            }
        }
    }

    // Create user in FirebaseAuth with age verification data
    private func createUserInFirebaseAuthWithAge(email: String, password: String, dateOfBirth: Date, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🔄 FirebaseAuthService: Attempting to create user with email and age verification: \(email)")
        Auth.auth().createUser(withEmail: email, password: password) { [weak self] authResult, error in
            if let error = error {
                print("❌ FirebaseAuthService: User creation failed: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }

            guard let user = authResult?.user else {
                let nilUserError = NSError(
                    domain: "AuthResult",
                    code: 0,
                    userInfo: [NSLocalizedDescriptionKey: "User not found after sign-up."]
                )
                completion(.failure(nilUserError))
                return
            }

            // Send verification email
            user.sendEmailVerification { verificationError in
                if let verificationError = verificationError {
                    completion(.failure(verificationError))
                    return
                }

                // Create user document in Firestore with age verification
                self?.createUserDocument(for: user, dateOfBirth: dateOfBirth) { firestoreResult in
                    switch firestoreResult {
                    case .success:
                        completion(.success(()))
                    case .failure(let err):
                        completion(.failure(err))
                    }
                }
            }
        }
    }

    /// Validates email format (accepts any valid email address)
    private func isValidEmail(_ email: String) -> Bool {
        // Standard email validation - accepts any valid email format
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    /// Validates password strength
    private func isValidPassword(_ password: String) -> (isValid: Bool, message: String?) {
        guard password.count >= 8 else {
            return (false, "Password must be at least 8 characters long")
        }

        let hasUppercase = password.range(of: "[A-Z]", options: .regularExpression) != nil
        let hasLowercase = password.range(of: "[a-z]", options: .regularExpression) != nil
        let hasNumber = password.range(of: "[0-9]", options: .regularExpression) != nil
        let hasSpecialChar = password.range(of: "[!@#$%^&*(),.?\":{}|<>]", options: .regularExpression) != nil

        guard hasUppercase && hasLowercase && hasNumber && hasSpecialChar else {
            return (false, "Password must contain uppercase, lowercase, number, and special character")
        }

        return (true, nil)
    }

    /// CRITICAL FIX: Creates a corresponding user document in Firestore with unified verification fields
    /// - Parameter user: The authenticated Firebase user
    private func createUserDocument(for user: User, completion: @escaping (Result<Void, Error>) -> Void) {
        createUserDocument(for: user, dateOfBirth: nil, completion: completion)
    }

    /// Creates a user document with optional age verification data
    /// - Parameters:
    ///   - user: The authenticated Firebase user
    ///   - dateOfBirth: Optional date of birth for age verification
    ///   - completion: Completion handler
    private func createUserDocument(for user: User, dateOfBirth: Date?, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🔄 FirebaseAuthService: Creating user document with unified verification fields")
        print("   - Email: \(user.email ?? "unknown")")
        print("   - Email verified: \(user.isEmailVerified)")
        print("   - Age verified: \(dateOfBirth != nil)")

        // Construct a new UserModel with discovery-friendly defaults
        let userModel = UserModel(
            email: user.email ?? "",
            isEmailVerified: user.isEmailVerified,
            createdAt: Date(),
            lastActiveAt: Date(),
            hideFromDiscovery: false, // Default to visible in discovery
            dateOfBirth: dateOfBirth,
            ageVerified: dateOfBirth != nil
        )

        // Use Firestore's setData(from:) to automatically encode userModel
        do {
            try db.collection("users")
                .document(user.uid) // doc name = auth user's UID
                .setData(from: userModel) { error in
                    if let error = error {
                        print("❌ FirebaseAuthService: Failed to create user document: \(error.localizedDescription)")
                        let firestoreError = NSError(
                            domain: "FirestoreError",
                            code: 1004,
                            userInfo: [
                                NSLocalizedDescriptionKey: "Failed to create user profile: \(error.localizedDescription)"
                            ]
                        )
                        completion(.failure(firestoreError))
                    } else {
                        print("✅ FirebaseAuthService: Successfully created user document with unified verification fields")
                        completion(.success(()))
                    }
                }
        } catch {
            // setData(from:) can throw if encoding fails
            let encodingError = NSError(
                domain: "EncodingError",
                code: 1005,
                userInfo: [
                    NSLocalizedDescriptionKey: "Failed to encode user data: \(error.localizedDescription)"
                ]
            )
            completion(.failure(encodingError))
        }
    }

    // MARK: - Database Checking Methods

    /// Check if a user exists in our Firestore database by email
    private func checkUserExistsInDatabase(email: String, completion: @escaping (Bool) -> Void) {
        print("🔍 FirebaseAuthService: Checking if user exists in database for email: \(email)")

        let db = Firestore.firestore()
        db.collection("users")
            .whereField("email", isEqualTo: email)
            .limit(to: 1)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ FirebaseAuthService: Error checking user existence: \(error.localizedDescription)")
                    // On error, assume user doesn't exist to allow signup attempt
                    completion(false)
                    return
                }

                let userExists = !(snapshot?.documents.isEmpty ?? true)
                print("🔍 FirebaseAuthService: User exists in database: \(userExists)")
                completion(userExists)
            }
    }

    /// Clean up orphaned user data (user exists in Firestore but not in Firebase Auth)
    private func cleanupOrphanedUserData(email: String, completion: @escaping (Result<Void, Error>) -> Void) {
        print("🧹 FirebaseAuthService: Cleaning up orphaned data for email: \(email)")

        let db = Firestore.firestore()

        // Find and delete the orphaned user document
        db.collection("users")
            .whereField("email", isEqualTo: email)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ FirebaseAuthService: Error finding orphaned user data: \(error.localizedDescription)")
                    completion(.failure(error))
                    return
                }

                guard let documents = snapshot?.documents, !documents.isEmpty else {
                    print("✅ FirebaseAuthService: No orphaned data found")
                    completion(.success(()))
                    return
                }

                // Delete all orphaned documents for this email
                let batch = db.batch()
                for document in documents {
                    batch.deleteDocument(document.reference)
                    print("🗑️ FirebaseAuthService: Marking orphaned document for deletion: \(document.documentID)")
                }

                batch.commit { error in
                    if let error = error {
                        print("❌ FirebaseAuthService: Failed to delete orphaned data: \(error.localizedDescription)")
                        completion(.failure(error))
                    } else {
                        print("✅ FirebaseAuthService: Successfully cleaned up orphaned data")
                        completion(.success(()))
                    }
                }
            }
    }
}
