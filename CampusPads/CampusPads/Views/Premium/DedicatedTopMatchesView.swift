import SwiftUI
import FirebaseFirestore

struct DedicatedTopMatchesView: View {
    @StateObject private var topMatchesManager = TopMatchesManager.shared
    @StateObject private var profileVM = ProfileViewModel.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showPremiumUpgrade = false
    @State private var selectedMatch: UserModel?
    @State private var animateCards = false
    @State private var showPaywall = false
    @State private var showingPurchaseAlert = false

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // Premium Background
                    premiumBackground

                    // Content area - only show for premium users, completely hide for non-premium
                    if profileVM.userProfile?.isPremium == true {
                        ScrollView {
                            AdaptiveContainer {
                                VStack(spacing: ResponsiveSpacing.lg) {
                                    // Premium Header
                                    premiumHeaderView

                                    // Content - full access for premium users
                                    premiumContentView

                                    Spacer(minLength: ResponsiveSpacing.xxl)
                                }
                                .responsivePadding()
                            }
                        }
                    } else {
                        // Show placeholder content that gets completely covered by paywall
                        ScrollView {
                            AdaptiveContainer {
                                VStack(spacing: ResponsiveSpacing.lg) {
                                    // Empty placeholder to maintain layout structure
                                    Rectangle()
                                        .fill(Color.clear)
                                        .frame(height: DeviceInfo.isIPad ? 150 : 100)

                                    Rectangle()
                                        .fill(Color.clear)
                                        .frame(maxWidth: .infinity, maxHeight: .infinity)

                                    Spacer(minLength: ResponsiveSpacing.xxl)
                                }
                                .responsivePadding()
                            }
                        }
                    }

                    // Paywall overlay for non-premium users - covers entire screen
                    if profileVM.userProfile?.isPremium != true {
                        PremiumPaywallOverlay(
                            title: "Premium Top Matches",
                            subtitle: "Get access to daily curated matches with 90%+ compatibility",
                            onUpgrade: {
                                showPremiumUpgrade = true
                            },
                            onDismiss: {
                                dismiss()
                            }
                        )
                    }
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .navigationTitle("Top Matches")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            loadTopMatches()
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3)) {
                animateCards = true
            }
        }
        .sheet(item: $selectedMatch) { match in
            TopMatchDetailView(match: match)
        }
        .sheet(isPresented: $showPremiumUpgrade) {
            PremiumUpgradeView()
        }
    }

    // MARK: - Premium Background
    private var premiumBackground: some View {
        ZStack {
            // Base gradient
            AppTheme.backgroundGradient
                .ignoresSafeArea()

            // Premium overlay
            LinearGradient(
                colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    Color.clear,
                    Color.yellow.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        }
    }

    // MARK: - Premium Header
    private var premiumHeaderView: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            // Crown Icon with Animation
            Image(systemName: "crown.fill")
                .font(.system(size: DeviceInfo.isIPad ? 64 : 48, weight: .bold))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.yellow, Color.orange, Color.yellow],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
                .scaleEffect(animateCards ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: animateCards)

            VStack(spacing: ResponsiveSpacing.xs) {
                Text("Premium Top Matches")
                    .font(.system(size: ResponsiveTypography.fontSize(28), weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Your daily selection of highest compatibility matches")
                    .font(ResponsiveTypography.subheadline)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .responsivePadding()
        .background(premiumCardBackground)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(premiumBorder)
        .shadow(color: Color.yellow.opacity(0.2), radius: 16, x: 0, y: 8)
    }

    // MARK: - Premium Content
    private var premiumContentView: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Usage Stats
            usageStatsView

            // Top Matches Grid
            if topMatchesManager.isLoading {
                loadingView
            } else if topMatchesManager.topMatches.isEmpty {
                emptyStateView
            } else {
                topMatchesGridView
            }
        }
    }

    // MARK: - Usage Stats
    private var usageStatsView: some View {
        Group {
            if DeviceInfo.isIPad {
                HStack(spacing: ResponsiveSpacing.lg) {
                    StatCard(
                        title: "Views Left",
                        value: "\(topMatchesManager.maxDailyViews - topMatchesManager.viewedCount)",
                        icon: "eye.fill",
                        color: AppTheme.primaryColor,
                        isPremium: true
                    )

                    StatCard(
                        title: "Likes Left",
                        value: "\(topMatchesManager.maxDailyLikes - topMatchesManager.likedCount)",
                        icon: "heart.fill",
                        color: .red,
                        isPremium: true
                    )
                }
            } else {
                VStack(spacing: ResponsiveSpacing.md) {
                    StatCard(
                        title: "Views Left",
                        value: "\(topMatchesManager.maxDailyViews - topMatchesManager.viewedCount)",
                        icon: "eye.fill",
                        color: AppTheme.primaryColor,
                        isPremium: true
                    )

                    StatCard(
                        title: "Likes Left",
                        value: "\(topMatchesManager.maxDailyLikes - topMatchesManager.likedCount)",
                        icon: "heart.fill",
                        color: .red,
                        isPremium: true
                    )
                }
            }
        }
    }

    // MARK: - Top Matches Grid
    private var topMatchesGridView: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            HStack {
                Text("Today's Top Matches")
                    .font(.system(size: ResponsiveTypography.fontSize(20), weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()

                Text("Resets at midnight")
                    .font(ResponsiveTypography.caption)
                    .foregroundColor(AppTheme.textTertiary)
            }

            let columns = DeviceInfo.isIPad ?
                Array(repeating: GridItem(.flexible(), spacing: ResponsiveSpacing.md), count: 3) :
                Array(repeating: GridItem(.flexible(), spacing: ResponsiveSpacing.sm), count: 2)

            LazyVGrid(columns: columns, spacing: ResponsiveSpacing.md) {
                ForEach(Array(topMatchesManager.topMatches.enumerated()), id: \.offset) { index, match in
                    TopMatchCard(
                        match: match,
                        index: index,
                        canView: topMatchesManager.canViewProfile(at: index),
                        canLike: topMatchesManager.canLikeProfile(),
                        onTap: {
                            handleCardTap(match: match, index: index)
                        },
                        onLike: {
                            handleCardLike(match: match)
                        }
                    )
                    .scaleEffect(animateCards ? 1.0 : 0.8)
                    .opacity(animateCards ? 1.0 : 0.0)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                        .delay(Double(index) * 0.1),
                        value: animateCards
                    )
                }
            }
        }
        .responsivePadding()
        .background(premiumCardBackground)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(premiumBorder)
        .shadow(color: Color.yellow.opacity(0.2), radius: 16, x: 0, y: 8)
    }

    // MARK: - Paywall View
    private var paywallView: some View {
        VStack(spacing: AppTheme.spacing24) {
            VStack(spacing: AppTheme.spacing16) {
                Image(systemName: "lock.fill")
                    .font(.system(size: 64, weight: .bold))
                    .foregroundColor(AppTheme.textTertiary)

                Text("Premium Feature")
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Unlock daily top matches with premium subscription")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: { showPremiumUpgrade = true }) {
                HStack {
                    Image(systemName: "crown.fill")
                        .font(.system(size: 18, weight: .bold))

                    Text("Upgrade to Premium")
                        .font(.system(size: 18, weight: .bold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing24)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    LinearGradient(
                        colors: [Color.yellow, Color.orange],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(AppTheme.radiusLarge)
                .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
            }
        }
        .padding(AppTheme.spacing32)
        .background(AppTheme.modernCardGradient)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .stroke(AppTheme.textTertiary.opacity(0.2), lineWidth: 1)
        )
        .shadow(color: .black.opacity(0.1), radius: 16, x: 0, y: 8)
    }

    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(Color.yellow)

            Text("Loading your top matches...")
                .font(AppTheme.subheadline)
                .foregroundColor(AppTheme.textSecondary)
        }
        .padding(AppTheme.spacing32)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
    }

    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "heart.slash")
                .font(.system(size: 48, weight: .bold))
                .foregroundColor(AppTheme.textTertiary)

            Text("No Top Matches Yet")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(AppTheme.textPrimary)

            Text("Check back tomorrow for fresh matches!")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(AppTheme.spacing32)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
    }

    // MARK: - Premium Styling Components
    private var premiumCardBackground: some View {
        ZStack {
            AppTheme.modernCardGradient

            LinearGradient(
                colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: ResponsiveRadius.large)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.yellow.opacity(0.6),
                        Color.orange.opacity(0.4),
                        Color.yellow.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: DeviceInfo.isIPad ? 3 : 2
            )
    }

    // MARK: - Actions
    private func loadTopMatches() {
        guard let userID = profileVM.userProfile?.id else { return }

        Task {
            await topMatchesManager.loadTopMatches(for: userID)
        }
    }

    private func handleCardTap(match: UserModel, index: Int) {
        topMatchesManager.viewProfile(
            at: index,
            userID: profileVM.userProfile?.id ?? "",
            viewedUserID: match.id ?? ""
        )
        selectedMatch = match
    }

    private func handleCardLike(match: UserModel) {
        if topMatchesManager.likeProfile(userID: profileVM.userProfile?.id ?? "") {
            // Create premium match
            createPremiumMatch(with: match)
            HapticFeedbackManager.shared.generateImpact(style: .medium)
        } else {
            showPremiumUpgrade = true
        }
    }

    private func createPremiumMatch(with user: UserModel) {
        Task {
            do {
                try await MatchingService.shared.createMatch(
                    user1ID: profileVM.userProfile?.id ?? "",
                    user2ID: user.id ?? "",
                    isPremiumMatch: true
                )

                AnalyticsManager.shared.trackEvent("premium_match_created_dedicated", parameters: [
                    "match_user_id": user.id ?? "",
                    "compatibility_score": user.compatibilityScore ?? 0.0
                ])

            } catch {
                print("❌ Failed to create premium match: \(error)")
            }
        }
    }
}

// MARK: - Premium Upgrade View
struct PremiumUpgradeView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var profileVM = ProfileViewModel.shared
    @StateObject private var purchaseManager = PurchaseManager.shared
    @State private var animateFeatures = false
    @State private var selectedPlan: PremiumPlan = .lifetime
    @State private var showingPurchaseAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    enum PremiumPlan: String, CaseIterable {
        case lifetime = "Lifetime"

        var price: String {
            return "$4.99"
        }

        var savings: String? {
            return "One-time payment"
        }

        var description: String {
            return "Unlock all features forever"
        }
    }

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // Premium background
                    AppTheme.backgroundGradient
                        .ignoresSafeArea()

                    ScrollView(.vertical, showsIndicators: false) {
                        AdaptiveContainer(maxWidth: optimalContentWidth) {
                            VStack(spacing: responsiveMainSpacing) {
                                // Top spacing for better visual balance
                                Spacer()
                                    .frame(height: responsiveTopSpacing)

                                // Header
                                premiumHeaderView

                                // Features list
                                premiumFeaturesView

                                // Pricing plans
                                pricingPlansView

                                // Purchase button
                                purchaseButtonView

                                // Terms and restore
                                termsAndRestoreView

                                // Bottom spacing for safe area
                                Spacer()
                                    .frame(height: responsiveBottomSpacing)
                            }
                            .responsivePadding()
                        }
                    }
                }
            }
            .navigationTitle("Premium")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundColor(AppTheme.textPrimary)
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.3)) {
                animateFeatures = true
            }
        }
        .alert(alertTitle, isPresented: $showingPurchaseAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - Premium Header
    private var premiumHeaderView: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            // Crown with glow - enhanced responsive sizing
            ZStack {
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                Color.yellow.opacity(0.4),
                                Color.orange.opacity(0.2),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: crownGlowInnerRadius,
                            endRadius: crownGlowOuterRadius
                        )
                    )
                    .frame(width: crownContainerSize, height: crownContainerSize)

                Image(systemName: "crown.fill")
                    .font(.system(size: crownIconSize, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.yellow, Color.orange, Color.yellow],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(color: Color.yellow.opacity(0.6), radius: 8, x: 0, y: 4)
            }

            VStack(spacing: ResponsiveSpacing.xs) {
                Text("Unlock CampusPads Premium")
                    .font(.system(size: headerTitleSize, weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("We know you won't always need to find a roommate. That's why we offer a one-time payment to unlock all premium features for life.")
                    .font(.system(size: headerSubtitleSize, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(headerSubtitleLineLimit)
            }
        }
        .responsivePadding()
        .background(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: Color.yellow.opacity(0.2), radius: 16, x: 0, y: 8)
    }

    // MARK: - Premium Features
    private var premiumFeaturesView: some View {
        Group {
            if DeviceInfo.isIPad {
                // iPad: 2-column grid layout
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: ResponsiveSpacing.md),
                    GridItem(.flexible(), spacing: ResponsiveSpacing.md)
                ], spacing: ResponsiveSpacing.md) {
                    ForEach(Array(premiumFeatures.enumerated()), id: \.offset) { index, feature in
                        premiumFeatureRow(
                            icon: feature.icon,
                            title: feature.title,
                            description: feature.description,
                            index: index
                        )
                        .scaleEffect(animateFeatures ? 1.0 : 0.8)
                        .opacity(animateFeatures ? 1.0 : 0.0)
                        .animation(
                            .spring(response: 0.6, dampingFraction: 0.8)
                            .delay(Double(index) * 0.1),
                            value: animateFeatures
                        )
                    }
                }
            } else {
                // iPhone: Vertical layout
                VStack(spacing: ResponsiveSpacing.sm) {
                    ForEach(Array(premiumFeatures.enumerated()), id: \.offset) { index, feature in
                        premiumFeatureRow(
                            icon: feature.icon,
                            title: feature.title,
                            description: feature.description,
                            index: index
                        )
                        .scaleEffect(animateFeatures ? 1.0 : 0.8)
                        .opacity(animateFeatures ? 1.0 : 0.0)
                        .animation(
                            .spring(response: 0.6, dampingFraction: 0.8)
                            .delay(Double(index) * 0.1),
                            value: animateFeatures
                        )
                    }
                }
            }
        }
    }

    private func premiumFeatureRow(icon: String, title: String, description: String, index: Int) -> some View {
        VStack(spacing: ResponsiveSpacing.sm) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: ResponsiveSizing.iconSize, weight: .bold))
                .foregroundColor(.yellow)
                .frame(width: ResponsiveSizing.buttonHeight * 0.6, height: ResponsiveSizing.buttonHeight * 0.6)
                .background(
                    Circle()
                        .fill(Color.yellow.opacity(0.2))
                        .overlay(
                            Circle()
                                .stroke(Color.yellow.opacity(0.4), lineWidth: 1)
                        )
                )

            // Content
            VStack(alignment: .center, spacing: ResponsiveSpacing.xs) {
                Text(title)
                    .font(ResponsiveTypography.headline.weight(.bold))
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(ResponsiveTypography.caption.weight(.medium))
                    .foregroundColor(AppTheme.textSecondary)
                    .lineLimit(DeviceInfo.isIPad ? 3 : 2)
                    .multilineTextAlignment(.center)
            }
        }
        .responsivePadding()
        .frame(maxWidth: .infinity, minHeight: DeviceInfo.isIPad ? 120 : 100)
        .background(
            RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Pricing Plans
    private var pricingPlansView: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            Text("Simple, One-Time Payment")
                .font(.system(size: ResponsiveTypography.fontSize(20), weight: .bold, design: .rounded))
                .foregroundColor(AppTheme.textPrimary)

            pricingPlanCard(plan: .lifetime)
        }
    }

    private func pricingPlanCard(plan: PremiumPlan) -> some View {
        let isSelected = selectedPlan == plan

        return Button(action: {
            selectedPlan = plan
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            VStack(spacing: ResponsiveSpacing.md) {
                // One-time payment badge
                Text("ONE-TIME PAYMENT")
                    .font(.system(size: ResponsiveTypography.fontSize(11), weight: .bold))
                    .foregroundColor(.white)
                    .responsivePadding(.horizontal)
                    .padding(.vertical, ResponsiveSpacing.xs)
                    .background(Color.green)
                    .cornerRadius(ResponsiveRadius.small)

                // Plan name
                Text("Lifetime Premium")
                    .font(.system(size: ResponsiveTypography.fontSize(20), weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                // Price
                VStack(spacing: ResponsiveSpacing.xs) {
                    Text("$4.99")
                        .font(.system(size: ResponsiveTypography.fontSize(32), weight: .black, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.yellow, Color.orange],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Pay once, use forever")
                        .font(.system(size: ResponsiveTypography.fontSize(13), weight: .medium))
                        .foregroundColor(AppTheme.textSecondary)
                }

                // Description
                Text("No monthly fees, no subscriptions")
                    .font(.system(size: ResponsiveTypography.fontSize(14), weight: .medium))
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)
            }
            .responsivePadding()
            .frame(maxWidth: DeviceInfo.isIPad ? 500 : .infinity)
            .background(
                RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                            .stroke(
                                isSelected ? Color.yellow : Color.gray.opacity(0.3),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
    }

    // MARK: - Purchase Button
    private var purchaseButtonView: some View {
        Button(action: {
            purchasePremium()
        }) {
            HStack(spacing: ResponsiveSpacing.sm) {
                if purchaseManager.isPurchasing {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(DeviceInfo.isIPad ? 1.2 : 0.8)
                } else {
                    Image(systemName: "crown.fill")
                        .font(.system(size: ResponsiveTypography.fontSize(18), weight: .bold))
                }

                Text(purchaseButtonText)
                    .font(.system(size: ResponsiveTypography.fontSize(18), weight: .bold, design: .rounded))
            }
            .foregroundColor(.white)
            .responsivePadding(.horizontal)
            .padding(.vertical, ResponsiveSpacing.md)
            .frame(maxWidth: DeviceInfo.isIPad ? 400 : .infinity)
            .background(
                LinearGradient(
                    colors: purchaseManager.isPurchasing ?
                        [Color.gray, Color.gray.opacity(0.8)] :
                        [Color.yellow, Color.orange],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(ResponsiveRadius.large)
            .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
        }
        .disabled(purchaseManager.isPurchasing)
    }

    private var purchaseButtonText: String {
        if purchaseManager.isPurchasing {
            return "Processing Purchase..."
        } else if let product = purchaseManager.getLifetimePremiumProduct() {
            return "Get Lifetime Premium - \(product.displayPrice)"
        } else {
            return "Get Lifetime Premium - $4.99"
        }
    }

    // MARK: - Terms and Restore
    private var termsAndRestoreView: some View {
        VStack(spacing: ResponsiveSpacing.sm) {
            Button(action: {
                restorePurchases()
            }) {
                HStack(spacing: ResponsiveSpacing.xs) {
                    if purchaseManager.isRestoring {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryColor))
                            .scaleEffect(DeviceInfo.isIPad ? 1.0 : 0.7)
                    }

                    Text(purchaseManager.isRestoring ? "Restoring..." : "Restore Purchases")
                }
            }
            .font(.system(size: ResponsiveTypography.fontSize(15), weight: .medium))
            .foregroundColor(AppTheme.primaryColor)
            .disabled(purchaseManager.isRestoring)

            Text("Terms of Service • Privacy Policy")
                .font(.system(size: ResponsiveTypography.fontSize(13), weight: .medium))
                .foregroundColor(AppTheme.textTertiary)
        }
        .responsivePadding()
    }

    // MARK: - Premium Features Data
    private var premiumFeatures: [(icon: String, title: String, description: String)] {
        [
            ("infinity", "Unlimited Daily Swipes", "No more waiting - swipe as much as you want"),
            ("crown.fill", "Premium Top Matches", "8 daily curated matches with 90%+ compatibility"),
            ("star.fill", "Premium Super Likes", "3 weekly super likes to stand out and get noticed"),
            ("magnifyingglass.circle.fill", "Advanced User Search", "Find users by name, major, interests"),
            ("eye.fill", "Profile Views", "See who viewed your profile and connect with them"),
            ("heart.fill", "Unlimited Likes", "Like as many profiles as you want, forever"),
            ("sparkles", "Lifetime Access", "Pay once, use forever - no subscriptions")
        ]
    }

    // MARK: - Actions
    private func purchasePremium() {
        guard let product = purchaseManager.getLifetimePremiumProduct() else {
            showAlert(title: "Error", message: "Product not available. Please try again later.")
            return
        }

        Logger.shared.info("Purchase Premium: \(selectedPlan.rawValue)")

        Task {
            let success = await purchaseManager.purchase(product)

            DispatchQueue.main.async {
                if success {
                    // Purchase successful - dismiss the view
                    self.dismiss()
                } else if let errorMessage = self.purchaseManager.errorMessage {
                    // Show error alert
                    self.showAlert(title: "Purchase Failed", message: errorMessage)
                }
            }
        }
    }

    private func restorePurchases() {
        Logger.shared.info("Restore Purchases")

        Task {
            let success = await purchaseManager.restorePurchases()

            DispatchQueue.main.async {
                if success {
                    // Restore successful - dismiss the view
                    self.dismiss()
                } else if let errorMessage = self.purchaseManager.errorMessage {
                    // Show error or no purchases found message
                    self.showAlert(title: "Restore Purchases", message: errorMessage)
                }
            }
        }
    }

    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingPurchaseAlert = true
    }

    // MARK: - Responsive Design Helpers

    /// Optimal content width based on device and screen size
    private var optimalContentWidth: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return 900
        case .iPadPro11, .iPadAir: return 800
        case .iPadMini: return 700
        default: return .infinity
        }
    }

    /// Responsive main spacing between sections
    private var responsiveMainSpacing: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9, .iPadPro11, .iPadAir: return ResponsiveSpacing.xxl
        case .iPadMini: return ResponsiveSpacing.xl
        default: return ResponsiveSpacing.lg
        }
    }

    /// Responsive top spacing
    private var responsiveTopSpacing: CGFloat {
        DeviceInfo.isIPad ? ResponsiveSpacing.xl : ResponsiveSpacing.md
    }

    /// Responsive bottom spacing
    private var responsiveBottomSpacing: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9, .iPadPro11, .iPadAir: return ResponsiveSpacing.xxl * 1.5
        case .iPadMini: return ResponsiveSpacing.xxl
        default: return ResponsiveSpacing.xl
        }
    }

    /// Crown container size
    private var crownContainerSize: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return 200
        case .iPadPro11, .iPadAir: return 180
        case .iPadMini: return 160
        default: return 120
        }
    }

    /// Crown icon size
    private var crownIconSize: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return 80
        case .iPadPro11, .iPadAir: return 72
        case .iPadMini: return 64
        default: return 48
        }
    }

    /// Crown glow inner radius
    private var crownGlowInnerRadius: CGFloat {
        crownContainerSize * 0.22
    }

    /// Crown glow outer radius
    private var crownGlowOuterRadius: CGFloat {
        crownContainerSize * 0.5
    }

    /// Header title size
    private var headerTitleSize: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return ResponsiveTypography.fontSize(32)
        case .iPadPro11, .iPadAir: return ResponsiveTypography.fontSize(30)
        case .iPadMini: return ResponsiveTypography.fontSize(28)
        default: return ResponsiveTypography.fontSize(26)
        }
    }

    /// Header subtitle size
    private var headerSubtitleSize: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9, .iPadPro11, .iPadAir: return ResponsiveTypography.fontSize(18)
        case .iPadMini: return ResponsiveTypography.fontSize(17)
        default: return ResponsiveTypography.fontSize(16)
        }
    }

    /// Header subtitle line limit
    private var headerSubtitleLineLimit: Int {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9, .iPadPro11, .iPadAir: return 8
        case .iPadMini: return 6
        default: return 4
        }
    }
}

// MARK: - Preview
struct DedicatedTopMatchesView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            DedicatedTopMatchesView()
        }
    }
}
