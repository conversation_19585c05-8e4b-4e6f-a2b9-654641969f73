//
//  GenericAlertError.swift
//  CampusPads
//
//  Enhanced error handling system for production-ready error management.
//

import Foundation

// MARK: - Error Types

enum AppErrorType {
    case network
    case authentication
    case validation
    case storage
    case location
    case general
}

enum AppErrorSeverity {
    case low
    case medium
    case high
    case critical
}

// MARK: - Enhanced Error Structure

struct AppError: Error, Identifiable {
    let id = UUID()
    let type: AppErrorType
    let severity: AppErrorSeverity
    let title: String
    let message: String
    let technicalDetails: String?
    let recoveryAction: String?
    let timestamp: Date

    init(
        type: AppErrorType,
        severity: AppErrorSeverity = .medium,
        title: String,
        message: String,
        technicalDetails: String? = nil,
        recoveryAction: String? = nil
    ) {
        self.type = type
        self.severity = severity
        self.title = title
        self.message = message
        self.technicalDetails = technicalDetails
        self.recoveryAction = recoveryAction
        self.timestamp = Date()
    }
}

// MARK: - Legacy Support

struct GenericAlertError: Identifiable {
    let id = UUID()
    let message: String

    init(message: String) {
        self.message = message
    }

    init(from appError: AppError) {
        self.message = appError.message
    }
}

// MARK: - Error Factory

struct ErrorFactory {
    static func networkError(
        message: String = "Network connection failed",
        technicalDetails: String? = nil
    ) -> AppError {
        AppError(
            type: .network,
            severity: .high,
            title: "Connection Error",
            message: message,
            technicalDetails: technicalDetails,
            recoveryAction: "Check your internet connection and try again"
        )
    }

    static func authenticationError(
        message: String = "Authentication failed",
        technicalDetails: String? = nil
    ) -> AppError {
        AppError(
            type: .authentication,
            severity: .high,
            title: "Authentication Error",
            message: message,
            technicalDetails: technicalDetails,
            recoveryAction: "Please sign in again"
        )
    }

    static func validationError(
        message: String,
        technicalDetails: String? = nil
    ) -> AppError {
        AppError(
            type: .validation,
            severity: .medium,
            title: "Validation Error",
            message: message,
            technicalDetails: technicalDetails,
            recoveryAction: "Please check your input and try again"
        )
    }

    static func storageError(
        message: String = "Failed to save data",
        technicalDetails: String? = nil
    ) -> AppError {
        AppError(
            type: .storage,
            severity: .high,
            title: "Storage Error",
            message: message,
            technicalDetails: technicalDetails,
            recoveryAction: "Please try again later"
        )
    }

    static func locationError(
        message: String = "Location access failed",
        technicalDetails: String? = nil
    ) -> AppError {
        AppError(
            type: .location,
            severity: .medium,
            title: "Location Error",
            message: message,
            technicalDetails: technicalDetails,
            recoveryAction: "Please enable location access in Settings"
        )
    }
}
