//
//  ProfileLoaderService.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseFirestore
import Combine

/// Shared service for loading user profiles across the app
class ProfileLoaderService: ObservableObject {
    static let shared = ProfileLoaderService()

    @Published var user: UserModel?
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()

    // Cache for loaded profiles to avoid repeated network requests
    private var profileCache: [String: UserModel] = [:]
    private var loadingProfiles: Set<String> = []

    private init() {} // Singleton pattern

    /// Load a user profile by ID with comprehensive debugging logs
    func loadProfile(candidateID: String) {
        guard !candidateID.isEmpty else {
            print("❌ ProfileLoader.loadProfile: Empty candidate ID provided")
            return
        }

        print("🔍 ProfileLoader.loadProfile: Starting load for candidate: \(candidateID)")

        // Cache check with logging
        if let cachedProfile = profileCache[candidateID] {
            print("✅ ProfileLoader.loadProfile: Found cached profile for \(candidateID) - \(cachedProfile.firstName ?? "Unknown") (\(cachedProfile.email))")
            return
        }

        // Duplicate check with logging
        if loadingProfiles.contains(candidateID) {
            print("⏳ ProfileLoader.loadProfile: Already loading profile for \(candidateID)")
            return
        }

        // Mark as loading with logging
        loadingProfiles.insert(candidateID)
        print("🔄 ProfileLoader.loadProfile: Starting background load for \(candidateID)")

        // Load profile with comprehensive error tracking
        Task {
            do {
                print("📡 ProfileLoader.loadProfile: Fetching document from Firestore for \(candidateID)")
                let snapshot = try await db.collection("users").document(candidateID).getDocument()

                // Remove from loading set
                loadingProfiles.remove(candidateID)

                guard snapshot.exists else {
                    print("❌ ProfileLoader.loadProfile: Document does not exist for \(candidateID)")
                    return
                }

                print("📄 ProfileLoader.loadProfile: Document exists for \(candidateID), attempting to decode...")
                let profile = try snapshot.data(as: UserModel.self)

                // Log successful decode with detailed profile info
                print("✅ ProfileLoader.loadProfile: Successfully decoded profile for \(candidateID)")
                print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                print("   - Email: \(profile.email)")
                print("   - Email Domain: \(profile.emailDomain ?? "nil")")
                print("   - Email Verified: \(profile.isEmailVerified)")
                print("   - Hidden from Discovery: \(profile.hideFromDiscovery ?? false)")
                print("   - Profile Image URL: \(profile.profileImageUrl ?? "nil")")
                print("   - Profile Image URLs: \(profile.profileImageUrls?.count ?? 0) images")
                print("   - College: \(profile.collegeName ?? "nil")")
                print("   - Housing Status: \(profile.housingStatus ?? "nil")")

                // Cache the profile
                profileCache[candidateID] = profile
                print("💾 ProfileLoader.loadProfile: Cached profile for \(candidateID)")

            } catch {
                // Detailed error logging
                print("❌ ProfileLoader.loadProfile: Error loading profile for \(candidateID)")
                print("   - Error: \(error.localizedDescription)")
                print("   - Error Type: \(type(of: error))")
                if let decodingError = error as? DecodingError {
                    print("   - Decoding Error Details: \(decodingError)")
                }
                loadingProfiles.remove(candidateID)
            }
        }
    }

    /// Load profile with UI updates (use this when NOT called from view updates)
    func loadProfileWithUIUpdates(candidateID: String) {
        guard !candidateID.isEmpty else {
            print("❌ ProfileLoader: Empty candidate ID provided")
            return
        }

        // Check cache first
        if let cachedProfile = profileCache[candidateID] {
            Task { @MainActor in
                self.user = cachedProfile
            }
            print("✅ ProfileLoader: Using cached profile for \(cachedProfile.firstName ?? "Unknown")")
            return
        }

        // Check if already loading
        if loadingProfiles.contains(candidateID) {
            return
        }

        // Mark as loading and update UI state
        loadingProfiles.insert(candidateID)
        Task { @MainActor in
            self.isLoading = true
            self.errorMessage = nil
        }

        print("🔄 ProfileLoader: Loading profile with UI updates for candidate: \(candidateID)")

        Task {
            do {
                print("📡 ProfileLoader.loadProfileWithUIUpdates: Fetching document from Firestore for \(candidateID)")
                let snapshot = try await db.collection("users").document(candidateID).getDocument()

                await MainActor.run {
                    self.loadingProfiles.remove(candidateID)
                    self.isLoading = false
                }

                guard snapshot.exists else {
                    print("❌ ProfileLoader.loadProfileWithUIUpdates: Profile document does not exist for: \(candidateID)")
                    await MainActor.run {
                        self.errorMessage = "Profile not found"
                    }
                    return
                }

                print("📄 ProfileLoader.loadProfileWithUIUpdates: Document exists for \(candidateID), attempting to decode...")
                let profile = try snapshot.data(as: UserModel.self)

                print("✅ ProfileLoader.loadProfileWithUIUpdates: Successfully loaded profile for \(candidateID)")
                print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                print("   - Email: \(profile.email)")
                print("   - Email Domain: \(profile.emailDomain ?? "nil")")
                print("   - Email Verified: \(profile.isEmailVerified)")
                print("   - Hidden from Discovery: \(profile.hideFromDiscovery ?? false)")
                print("   - Profile Image URL: \(profile.profileImageUrl ?? "nil")")
                print("   - Profile Image URLs: \(profile.profileImageUrls?.count ?? 0) images")

                // Cache the profile
                profileCache[candidateID] = profile

                // Update UI state
                await MainActor.run {
                    self.user = profile
                    self.errorMessage = nil
                }

            } catch {
                print("❌ ProfileLoader.loadProfileWithUIUpdates: Error loading profile for \(candidateID)")
                print("   - Error: \(error.localizedDescription)")
                print("   - Error Type: \(type(of: error))")
                if let decodingError = error as? DecodingError {
                    print("   - Decoding Error Details: \(decodingError)")
                }
                await MainActor.run {
                    self.loadingProfiles.remove(candidateID)
                    self.isLoading = false
                    self.errorMessage = "Failed to load profile: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Clear the current profile data
    func clearProfile() {
        Task { @MainActor in
            self.user = nil
            self.errorMessage = nil
            self.isLoading = false
        }
    }

    /// OPTIMIZED: Fast profile loading with simple caching (no retry logic for speed)
    func loadUserProfile(userID: String, completion: @escaping (Result<UserModel, Error>) -> Void) {
        guard !userID.isEmpty else {
            completion(.failure(NSError(domain: "ProfileLoaderService", code: 400,
                                      userInfo: [NSLocalizedDescriptionKey: "Empty user ID provided"])))
            return
        }

        // Check cache first - instant return
        if let cachedProfile = profileCache[userID] {
            print("⚡ ProfileLoaderService: INSTANT cached profile for \(userID)")
            completion(.success(cachedProfile))
            return
        }

        // Prevent duplicate requests
        if loadingProfiles.contains(userID) {
            print("⏳ ProfileLoaderService: Already loading profile for \(userID)")
            return
        }

        loadingProfiles.insert(userID)
        print("🚀 ProfileLoaderService: FAST loading profile for user: \(userID)")

        // OPTIMIZED: Single request, no retry for speed
        db.collection("users").document(userID).getDocument { [weak self] snapshot, error in
            defer {
                self?.loadingProfiles.remove(userID)
            }

            if let error = error {
                print("❌ ProfileLoaderService: Fast load failed for \(userID): \(error.localizedDescription)")
                completion(.failure(error))
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                let error = NSError(domain: "ProfileLoaderService", code: 404,
                                  userInfo: [NSLocalizedDescriptionKey: "Profile not found"])
                completion(.failure(error))
                return
            }

            do {
                let profile = try snapshot.data(as: UserModel.self)
                print("⚡ ProfileLoaderService: FAST loaded profile for \(userID) - \(profile.firstName ?? "Unknown")")

                // Cache immediately
                self?.profileCache[userID] = profile
                completion(.success(profile))
            } catch {
                print("❌ ProfileLoaderService: Fast decode failed for \(userID): \(error.localizedDescription)")
                completion(.failure(error))
            }
        }
    }

    /// Load profile with retry logic for network resilience
    private func loadProfileWithRetry(userID: String, attempt: Int, completion: @escaping (Result<UserModel, Error>) -> Void) {
        let maxRetries = 3

        db.collection("users").document(userID).getDocument { [weak self] snapshot, error in
            if let error = error {
                print("❌ ProfileLoaderService: Load attempt \(attempt) failed for \(userID): \(error.localizedDescription)")

                // Check if it's a retryable error
                if attempt < maxRetries && self?.isRetryableError(error) == true {
                    let delay = pow(2.0, Double(attempt - 1)) // Exponential backoff
                    DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                        self?.loadProfileWithRetry(userID: userID, attempt: attempt + 1, completion: completion)
                    }
                } else {
                    completion(.failure(error))
                }
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                let error = NSError(domain: "ProfileLoaderService", code: 404,
                                  userInfo: [NSLocalizedDescriptionKey: "Profile not found for user: \(userID)"])
                completion(.failure(error))
                return
            }

            do {
                print("📄 ProfileLoaderService.loadProfileWithRetry: Document exists for \(userID), attempting to decode (attempt \(attempt))...")
                let profile = try snapshot.data(as: UserModel.self)

                print("✅ ProfileLoaderService.loadProfileWithRetry: Successfully loaded profile for \(userID) (attempt \(attempt))")
                print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                print("   - Email: \(profile.email)")
                print("   - Email Domain: \(profile.emailDomain ?? "nil")")
                print("   - Email Verified: \(profile.isEmailVerified)")
                print("   - Hidden from Discovery: \(profile.hideFromDiscovery ?? false)")
                print("   - Profile Image URL: \(profile.profileImageUrl ?? "nil")")
                print("   - Profile Image URLs: \(profile.profileImageUrls?.count ?? 0) images")
                print("   - College: \(profile.collegeName ?? "nil")")
                print("   - Housing Status: \(profile.housingStatus ?? "nil")")

                completion(.success(profile))
            } catch {
                print("❌ ProfileLoaderService.loadProfileWithRetry: Failed to decode profile for \(userID) (attempt \(attempt))")
                print("   - Error: \(error.localizedDescription)")
                print("   - Error Type: \(type(of: error))")
                if let decodingError = error as? DecodingError {
                    print("   - Decoding Error Details: \(decodingError)")
                    switch decodingError {
                    case .typeMismatch(let type, let context):
                        print("   - Type Mismatch: Expected \(type), Context: \(context)")
                    case .valueNotFound(let type, let context):
                        print("   - Value Not Found: \(type), Context: \(context)")
                    case .keyNotFound(let key, let context):
                        print("   - Key Not Found: \(key), Context: \(context)")
                    case .dataCorrupted(let context):
                        print("   - Data Corrupted: \(context)")
                    @unknown default:
                        print("   - Unknown Decoding Error")
                    }
                }
                completion(.failure(error))
            }
        }
    }

    /// Check if an error is retryable
    private func isRetryableError(_ error: Error) -> Bool {
        if let nsError = error as NSError? {
            // Network-related errors that are retryable
            switch nsError.code {
            case -1001, -1005, -1009, -1017: // Timeout, connection lost, offline, cannot parse response
                return true
            default:
                return false
            }
        }
        return false
    }

    /// OPTIMIZED: Batch load multiple profiles for matches/chat dashboards
    func batchLoadProfiles(userIDs: [String], completion: @escaping ([String: UserModel]) -> Void) {
        guard !userIDs.isEmpty else {
            completion([:])
            return
        }

        print("🚀 ProfileLoaderService: BATCH loading \(userIDs.count) profiles")

        var results: [String: UserModel] = [:]
        var uncachedIDs: [String] = []

        // First, collect all cached profiles
        for userID in userIDs {
            if let cachedProfile = profileCache[userID] {
                results[userID] = cachedProfile
                print("⚡ ProfileLoaderService: BATCH using cached profile for \(userID)")
            } else {
                uncachedIDs.append(userID)
            }
        }

        // If all profiles are cached, return immediately
        guard !uncachedIDs.isEmpty else {
            print("⚡ ProfileLoaderService: BATCH all profiles cached - instant return")
            completion(results)
            return
        }

        print("🔄 ProfileLoaderService: BATCH loading \(uncachedIDs.count) uncached profiles")

        let group = DispatchGroup()

        // Load uncached profiles in parallel
        for userID in uncachedIDs {
            group.enter()

            db.collection("users").document(userID).getDocument { [weak self] snapshot, error in
                defer { group.leave() }

                if let error = error {
                    print("❌ ProfileLoaderService: BATCH failed for \(userID): \(error.localizedDescription)")
                    return
                }

                guard let snapshot = snapshot, snapshot.exists else {
                    print("❌ ProfileLoaderService: BATCH profile not found for \(userID)")
                    return
                }

                do {
                    let profile = try snapshot.data(as: UserModel.self)
                    print("✅ ProfileLoaderService: BATCH loaded \(userID) - \(profile.firstName ?? "Unknown")")

                    // Cache and add to results
                    self?.profileCache[userID] = profile
                    results[userID] = profile
                } catch {
                    print("❌ ProfileLoaderService: BATCH decode failed for \(userID): \(error.localizedDescription)")
                }
            }
        }

        // Return results when all requests complete
        group.notify(queue: .main) {
            print("🎉 ProfileLoaderService: BATCH completed - loaded \(results.count)/\(userIDs.count) profiles")
            completion(results)
        }
    }

    /// Get cached profile without loading (for search functionality)
    func getCachedProfile(userID: String) -> UserModel? {
        return profileCache[userID]
    }

    /// Clear profile cache
    func clearCache() {
        profileCache.removeAll()
        loadingProfiles.removeAll()
        print("🧹 ProfileLoaderService: Cache cleared")
    }

    /// Load profile with real-time updates (for active conversations)
    func loadProfileWithRealTimeUpdates(candidateID: String) {
        guard !candidateID.isEmpty else {
            print("❌ ProfileLoaderService: Empty candidate ID provided for real-time updates")
            return
        }

        Task { @MainActor in
            self.isLoading = true
            self.errorMessage = nil
        }

        print("🔄 ProfileLoaderService: Setting up real-time profile updates for: \(candidateID)")

        db.collection("users").document(candidateID)
            .snapshotPublisher()
            .tryMap { snapshot -> UserModel in
                guard snapshot.exists else {
                    throw NSError(domain: "ProfileLoaderService", code: 404,
                                userInfo: [NSLocalizedDescriptionKey: "Profile document does not exist"])
                }
                return try snapshot.data(as: UserModel.self)
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                Task { @MainActor in
                    self?.isLoading = false
                    if case let .failure(error) = completion {
                        print("❌ ProfileLoader: Real-time profile loading failed: \(error.localizedDescription)")
                        self?.errorMessage = "Failed to load profile: \(error.localizedDescription)"
                    }
                }
            } receiveValue: { [weak self] profile in
                print("✅ ProfileLoader: Real-time profile update for \(profile.firstName ?? "Unknown")")
                Task { @MainActor in
                    self?.user = profile
                    self?.errorMessage = nil
                    self?.isLoading = false
                }
            }
            .store(in: &cancellables)
    }

    /// Debug method to analyze profile eligibility for discovery
    func debugProfileEligibility(userID: String, completion: @escaping (String) -> Void) {
        print("🔍 ProfileLoader.debugProfileEligibility: Starting eligibility analysis for \(userID)")

        db.collection("users").document(userID).getDocument { snapshot, error in
            var debugReport = "🔍 PROFILE ELIGIBILITY DEBUG REPORT for \(userID)\n"
            debugReport += String(repeating: "=", count: 60) + "\n"

            if let error = error {
                debugReport += "❌ FIRESTORE ERROR: \(error.localizedDescription)\n"
                completion(debugReport)
                return
            }

            guard let snapshot = snapshot else {
                debugReport += "❌ SNAPSHOT ERROR: Snapshot is nil\n"
                completion(debugReport)
                return
            }

            if !snapshot.exists {
                debugReport += "❌ DOCUMENT MISSING: Profile document does not exist in Firestore\n"
                completion(debugReport)
                return
            }

            debugReport += "✅ DOCUMENT EXISTS: Profile document found in Firestore\n"

            do {
                let profile = try snapshot.data(as: UserModel.self)
                debugReport += "✅ DECODING SUCCESS: Profile successfully decoded\n\n"

                // Basic Info Analysis
                debugReport += "📋 BASIC PROFILE INFO:\n"
                debugReport += "   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")\n"
                debugReport += "   - Email: \(profile.email)\n"
                debugReport += "   - Email Domain: \(profile.emailDomain ?? "nil")\n"
                debugReport += "   - User ID: \(profile.id ?? "nil")\n\n"

                // Discovery Eligibility Analysis
                debugReport += "🎯 DISCOVERY ELIGIBILITY ANALYSIS:\n"

                // 1. Email Verification Check
                let isEmailVerified = profile.isEmailVerified
                debugReport += "   1. Email Verified: \(isEmailVerified ? "✅ YES" : "❌ NO")\n"

                // 2. Hidden from Discovery Check
                let isHidden = profile.hideFromDiscovery ?? false
                debugReport += "   2. Hidden from Discovery: \(isHidden ? "❌ YES (HIDDEN)" : "✅ NO (VISIBLE)")\n"

                // 3. Profile Image Check
                let hasProfileImageUrl = profile.profileImageUrl != nil && !profile.profileImageUrl!.isEmpty
                let profileImageUrlsCount = profile.profileImageUrls?.filter { !$0.isEmpty }.count ?? 0
                let hasProfileImages = hasProfileImageUrl || profileImageUrlsCount > 0
                debugReport += "   3. Profile Images:\n"
                debugReport += "      - Single Image URL: \(hasProfileImageUrl ? "✅ YES" : "❌ NO") (\(profile.profileImageUrl ?? "nil"))\n"
                debugReport += "      - Multiple Images: \(profileImageUrlsCount) images\n"
                debugReport += "      - Has Valid Images: \(hasProfileImages ? "✅ YES" : "❌ NO")\n"

                // 4. Basic Info Check
                let hasFirstName = profile.firstName != nil && !profile.firstName!.isEmpty
                debugReport += "   4. Has First Name: \(hasFirstName ? "✅ YES" : "❌ NO")\n"

                // 5. Overall Eligibility
                let isEligible = isEmailVerified && !isHidden && hasProfileImages && hasFirstName
                debugReport += "\n🎯 OVERALL DISCOVERY ELIGIBILITY: \(isEligible ? "✅ ELIGIBLE" : "❌ NOT ELIGIBLE")\n"

                if !isEligible {
                    debugReport += "\n❌ EXCLUSION REASONS:\n"
                    if !isEmailVerified { debugReport += "   - Email not verified\n" }
                    if isHidden { debugReport += "   - Hidden from discovery\n" }
                    if !hasProfileImages { debugReport += "   - No profile images\n" }
                    if !hasFirstName { debugReport += "   - Missing first name\n" }
                }

                // Additional Profile Details
                debugReport += "\n📊 ADDITIONAL PROFILE DETAILS:\n"
                debugReport += "   - College: \(profile.collegeName ?? "nil")\n"
                debugReport += "   - Housing Status: \(profile.housingStatus ?? "nil")\n"
                debugReport += "   - Gender: \(profile.gender ?? "nil")\n"
                debugReport += "   - Date of Birth: \(profile.dateOfBirth?.description ?? "nil")\n"
                debugReport += "   - About Me: \(profile.aboutMe?.prefix(50) ?? "nil")\n"
                debugReport += "   - Created At: \(profile.createdAt?.description ?? "nil")\n"
                debugReport += "   - Last Active: \(profile.lastActiveAt?.description ?? "nil")\n"

            } catch {
                debugReport += "❌ DECODING ERROR: Failed to decode profile\n"
                debugReport += "   - Error: \(error.localizedDescription)\n"
                debugReport += "   - Error Type: \(type(of: error))\n"

                if let decodingError = error as? DecodingError {
                    debugReport += "   - Decoding Error Details:\n"
                    switch decodingError {
                    case .typeMismatch(let type, let context):
                        debugReport += "     * Type Mismatch: Expected \(type)\n"
                        debugReport += "     * Context: \(context)\n"
                    case .valueNotFound(let type, let context):
                        debugReport += "     * Value Not Found: \(type)\n"
                        debugReport += "     * Context: \(context)\n"
                    case .keyNotFound(let key, let context):
                        debugReport += "     * Key Not Found: \(key)\n"
                        debugReport += "     * Context: \(context)\n"
                    case .dataCorrupted(let context):
                        debugReport += "     * Data Corrupted: \(context)\n"
                    @unknown default:
                        debugReport += "     * Unknown Decoding Error\n"
                    }
                }
            }

            debugReport += "\n" + String(repeating: "=", count: 60) + "\n"
            print(debugReport)
            completion(debugReport)
        }
    }

    /// Quick debug method for testing in console
    func quickDebugProfile(_ userID: String) {
        debugProfileEligibility(userID: userID) { report in
            // Report is already printed in the method, this is just for completion
        }
    }

    /// Debug all users in discovery query to find issues
    func debugDiscoveryQuery(completion: @escaping ([String]) -> Void) {
        print("🔍 ProfileLoader.debugDiscoveryQuery: Starting comprehensive discovery debug")

        db.collection("users")
            .whereField("isEmailVerified", isEqualTo: true)
            .whereField("hideFromDiscovery", isEqualTo: false)
            .limit(to: 50) // Limit for debugging
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ ProfileLoader.debugDiscoveryQuery: Error fetching users: \(error.localizedDescription)")
                    completion([])
                    return
                }

                guard let documents = snapshot?.documents else {
                    print("❌ ProfileLoader.debugDiscoveryQuery: No documents found")
                    completion([])
                    return
                }

                print("📊 ProfileLoader.debugDiscoveryQuery: Found \(documents.count) users in discovery query")

                var debugReports: [String] = []
                let group = DispatchGroup()

                for doc in documents {
                    group.enter()
                    self.debugProfileEligibility(userID: doc.documentID) { report in
                        debugReports.append(report)
                        group.leave()
                    }
                }

                group.notify(queue: .main) {
                    print("✅ ProfileLoader.debugDiscoveryQuery: Completed debugging \(debugReports.count) profiles")
                    completion(debugReports)
                }
            }
    }

    deinit {
        cancellables.forEach { $0.cancel() }
    }
}
