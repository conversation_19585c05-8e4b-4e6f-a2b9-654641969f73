//
//  MatchCelebrationEffect.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

/// Enhanced match celebration effect with particles and animations
struct MatchCelebrationEffect: View {
    @State private var particles: [Particle] = []
    @State private var showEffect = false
    
    let isActive: Bool
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // Particle system
            ForEach(particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
            }
            
            // Central burst effect
            if showEffect {
                ZStack {
                    // Outer ring
                    Circle()
                        .stroke(AppTheme.primaryColor, lineWidth: 3)
                        .frame(width: 100, height: 100)
                        .scaleEffect(showEffect ? 2.0 : 0.1)
                        .opacity(showEffect ? 0 : 1)
                        .animation(.easeOut(duration: 0.8), value: showEffect)
                    
                    // Inner ring
                    Circle()
                        .stroke(AppTheme.accentColor, lineWidth: 2)
                        .frame(width: 60, height: 60)
                        .scaleEffect(showEffect ? 1.5 : 0.1)
                        .opacity(showEffect ? 0 : 1)
                        .animation(.easeOut(duration: 0.6).delay(0.1), value: showEffect)
                    
                    // Heart icon
                    Image(systemName: "heart.fill")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(AppTheme.primaryColor)
                        .scaleEffect(showEffect ? 1.2 : 0.8)
                        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: showEffect)
                }
            }
        }
        .onChange(of: isActive) { _, active in
            if active {
                startCelebration()
            }
        }
    }
    
    private func startCelebration() {
        showEffect = true
        generateParticles()
        
        // Complete after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            onComplete()
            showEffect = false
            particles.removeAll()
        }
    }
    
    private func generateParticles() {
        particles.removeAll()
        
        let colors = [
            AppTheme.primaryColor,
            AppTheme.accentColor,
            AppTheme.successColor,
            Color.pink,
            Color.orange
        ]
        
        for i in 0..<20 {
            let angle = Double(i) * (360.0 / 20.0) * .pi / 180.0
            let distance = Double.random(in: 50...120)
            
            let particle = Particle(
                id: i,
                position: CGPoint(
                    x: 150 + cos(angle) * distance,
                    y: 150 + sin(angle) * distance
                ),
                color: colors.randomElement() ?? AppTheme.primaryColor,
                size: Double.random(in: 4...12),
                opacity: 1.0,
                scale: 1.0
            )
            
            particles.append(particle)
            
            // Animate particle
            withAnimation(.easeOut(duration: 1.0).delay(Double(i) * 0.05)) {
                if let index = particles.firstIndex(where: { $0.id == i }) {
                    particles[index].opacity = 0
                    particles[index].scale = 0.5
                    particles[index].position = CGPoint(
                        x: particle.position.x + cos(angle) * 50,
                        y: particle.position.y + sin(angle) * 50
                    )
                }
            }
        }
    }
}

// MARK: - Particle Model
struct Particle: Identifiable {
    let id: Int
    var position: CGPoint
    let color: Color
    let size: Double
    var opacity: Double
    var scale: Double
}

// MARK: - Match Celebration Overlay
struct MatchCelebrationOverlay: ViewModifier {
    @Binding var showCelebration: Bool
    let onComplete: () -> Void
    
    func body(content: Content) -> some View {
        content
            .overlay(
                MatchCelebrationEffect(
                    isActive: showCelebration,
                    onComplete: {
                        showCelebration = false
                        onComplete()
                    }
                )
                .allowsHitTesting(false)
                .opacity(showCelebration ? 1 : 0)
            )
    }
}

extension View {
    func matchCelebration(
        isActive: Binding<Bool>,
        onComplete: @escaping () -> Void = {}
    ) -> some View {
        modifier(MatchCelebrationOverlay(showCelebration: isActive, onComplete: onComplete))
    }
}

// MARK: - Enhanced Button Styles
struct TinderStyleButton: ButtonStyle {
    let isPressed: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct PulsingButton: ButtonStyle {
    @State private var isPulsing = false
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(isPulsing ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isPulsing)
            .onAppear {
                isPulsing = true
            }
    }
}
