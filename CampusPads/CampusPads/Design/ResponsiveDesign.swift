//
//  ResponsiveDesign.swift
//  CampusPads
//
//  Comprehensive responsive design system for iPhone and iPad
//

import SwiftUI

// MARK: - Device Detection
struct DeviceInfo {
    static let isIPad = UIDevice.current.userInterfaceIdiom == .pad
    static let isIPhone = UIDevice.current.userInterfaceIdiom == .phone
    
    // Screen size categories
    static var screenSize: ScreenSize {
        let width = UIScreen.main.bounds.width
        let height = UIScreen.main.bounds.height
        let maxDimension = max(width, height)
        
        if DeviceInfo.isIPad {
            if maxDimension >= 1366 { return .iPadPro12_9 }
            if maxDimension >= 1194 { return .iPadPro11 }
            if maxDimension >= 1080 { return .iPadAir }
            return .iPadMini
        } else {
            if maxDimension >= 932 { return .iPhoneProMax }
            if maxDimension >= 844 { return .iPhonePro }
            if maxDimension >= 812 { return .iPhoneStandard }
            return .iPhoneCompact
        }
    }
    
    // Orientation detection
    static var isLandscape: Bool {
        UIScreen.main.bounds.width > UIScreen.main.bounds.height
    }
    
    // Safe area detection
    static var safeAreaInsets: UIEdgeInsets {
        UIApplication.shared.windows.first?.safeAreaInsets ?? UIEdgeInsets()
    }
}

enum ScreenSize {
    case iPhoneCompact      // iPhone SE, iPhone 8
    case iPhoneStandard     // iPhone 12, 13, 14
    case iPhonePro          // iPhone 12 Pro, 13 Pro, 14 Pro
    case iPhoneProMax       // iPhone 12 Pro Max, 13 Pro Max, 14 Pro Max
    case iPadMini           // iPad Mini
    case iPadAir            // iPad Air, iPad 10.9"
    case iPadPro11          // iPad Pro 11"
    case iPadPro12_9        // iPad Pro 12.9"
}

// MARK: - Responsive Spacing System
struct ResponsiveSpacing {
    // Base spacing values that scale with device
    static func spacing(_ base: CGFloat) -> CGFloat {
        let multiplier = DeviceInfo.isIPad ? 1.5 : 1.0
        return base * multiplier
    }
    
    // Predefined responsive spacing
    static var xs: CGFloat { spacing(4) }      // 4pt iPhone, 6pt iPad
    static var sm: CGFloat { spacing(8) }      // 8pt iPhone, 12pt iPad
    static var md: CGFloat { spacing(16) }     // 16pt iPhone, 24pt iPad
    static var lg: CGFloat { spacing(24) }     // 24pt iPhone, 36pt iPad
    static var xl: CGFloat { spacing(32) }     // 32pt iPhone, 48pt iPad
    static var xxl: CGFloat { spacing(48) }    // 48pt iPhone, 72pt iPad
    
    // Content-specific spacing
    static var cardPadding: CGFloat { DeviceInfo.isIPad ? 24 : 16 }
    static var sectionSpacing: CGFloat { DeviceInfo.isIPad ? 40 : 24 }
    static var buttonHeight: CGFloat { DeviceInfo.isIPad ? 56 : 44 }
    
    // Layout margins
    static var horizontalMargin: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return 80
        case .iPadPro11, .iPadAir: return 60
        case .iPadMini: return 40
        default: return 20
        }
    }
    
    // Grid spacing
    static var gridSpacing: CGFloat { DeviceInfo.isIPad ? 20 : 12 }
}

// MARK: - Responsive Typography
struct ResponsiveTypography {
    // Scale factor for iPad
    private static let iPadScale: CGFloat = 1.2
    
    static func fontSize(_ base: CGFloat) -> CGFloat {
        DeviceInfo.isIPad ? base * iPadScale : base
    }
    
    // Responsive font sizes
    static var largeTitle: Font { .custom("AvenirNext-Bold", size: fontSize(34)) }
    static var title1: Font { .custom("AvenirNext-Bold", size: fontSize(28)) }
    static var title2: Font { .custom("AvenirNext-Bold", size: fontSize(22)) }
    static var title3: Font { .custom("AvenirNext-Medium", size: fontSize(20)) }
    static var headline: Font { .custom("AvenirNext-Medium", size: fontSize(17)) }
    static var body: Font { .custom("AvenirNext-Regular", size: fontSize(17)) }
    static var callout: Font { .custom("AvenirNext-Regular", size: fontSize(16)) }
    static var subheadline: Font { .custom("AvenirNext-Regular", size: fontSize(15)) }
    static var footnote: Font { .custom("AvenirNext-Regular", size: fontSize(13)) }
    static var caption: Font { .custom("AvenirNext-Regular", size: fontSize(12)) }
}

// MARK: - Responsive Sizing
struct ResponsiveSizing {
    // Card dimensions
    static var cardWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let margin = ResponsiveSpacing.horizontalMargin * 2
        
        if DeviceInfo.isIPad {
            // iPad: Use columns for better layout
            let availableWidth = screenWidth - margin
            let columns = DeviceInfo.isLandscape ? 3 : 2
            let spacing = ResponsiveSpacing.gridSpacing * CGFloat(columns - 1)
            return (availableWidth - spacing) / CGFloat(columns)
        } else {
            // iPhone: Full width minus margins
            return screenWidth - margin
        }
    }
    
    // Profile image sizes
    static var profileImageSmall: CGFloat { DeviceInfo.isIPad ? 60 : 40 }
    static var profileImageMedium: CGFloat { DeviceInfo.isIPad ? 120 : 80 }
    static var profileImageLarge: CGFloat { DeviceInfo.isIPad ? 180 : 120 }
    
    // Button dimensions
    static var buttonHeight: CGFloat { ResponsiveSpacing.buttonHeight }
    static var iconSize: CGFloat { DeviceInfo.isIPad ? 28 : 20 }
    
    // Tab bar height
    static var tabBarHeight: CGFloat { DeviceInfo.isIPad ? 80 : 60 }
}

// MARK: - Responsive Corner Radius
struct ResponsiveRadius {
    static func radius(_ base: CGFloat) -> CGFloat {
        DeviceInfo.isIPad ? base * 1.2 : base
    }
    
    static var small: CGFloat { radius(8) }
    static var medium: CGFloat { radius(12) }
    static var large: CGFloat { radius(16) }
    static var xlarge: CGFloat { radius(24) }
}

// MARK: - Layout Helpers
struct LayoutHelpers {
    // Grid columns based on device and orientation
    static var gridColumns: Int {
        if DeviceInfo.isIPad {
            return DeviceInfo.isLandscape ? 3 : 2
        } else {
            return DeviceInfo.isLandscape ? 2 : 1
        }
    }
    
    // Maximum content width for readability
    static var maxContentWidth: CGFloat {
        switch DeviceInfo.screenSize {
        case .iPadPro12_9: return 800
        case .iPadPro11, .iPadAir: return 700
        case .iPadMini: return 600
        default: return .infinity
        }
    }
    
    // Chat bubble max width
    static var chatBubbleMaxWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        if DeviceInfo.isIPad {
            return min(400, screenWidth * 0.6)
        } else {
            return screenWidth * 0.75
        }
    }
}

// MARK: - Responsive View Modifiers
extension View {
    // Apply responsive padding
    func responsivePadding(_ edges: Edge.Set = .all, _ value: CGFloat? = nil) -> some View {
        let padding = value ?? ResponsiveSpacing.md
        return self.padding(edges, padding)
    }

    // Apply responsive horizontal margins
    func responsiveHorizontalPadding() -> some View {
        self.padding(.horizontal, ResponsiveSpacing.horizontalMargin)
    }

    // Limit content width for readability on iPad
    func limitContentWidth() -> some View {
        self.frame(maxWidth: LayoutHelpers.maxContentWidth)
    }

    // Responsive card style
    func responsiveCardStyle() -> some View {
        self
            .padding(ResponsiveSpacing.cardPadding)
            .background(AppTheme.modernCardGradient)
            .cornerRadius(ResponsiveRadius.large)
            .shadow(
                color: .black.opacity(0.1),
                radius: DeviceInfo.isIPad ? 12 : 8,
                x: 0,
                y: DeviceInfo.isIPad ? 6 : 4
            )
    }

    // Responsive grid layout
    func responsiveGrid<Content: View>(
        spacing: CGFloat = ResponsiveSpacing.gridSpacing,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        let columns = Array(repeating: GridItem(.flexible(), spacing: spacing), count: LayoutHelpers.gridColumns)

        return LazyVGrid(columns: columns, spacing: spacing) {
            content()
        }
    }
}

// MARK: - Adaptive Layout Containers
struct AdaptiveContainer<Content: View>: View {
    let content: Content
    let maxWidth: CGFloat?

    init(maxWidth: CGFloat? = nil, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.maxWidth = maxWidth
    }

    var body: some View {
        content
            .frame(maxWidth: maxWidth ?? LayoutHelpers.maxContentWidth)
            .responsiveHorizontalPadding()
    }
}

struct AdaptiveStack<Content: View>: View {
    let content: Content
    let spacing: CGFloat

    init(spacing: CGFloat = ResponsiveSpacing.md, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.spacing = spacing
    }

    var body: some View {
        if DeviceInfo.isIPad && DeviceInfo.isLandscape {
            HStack(spacing: spacing) {
                content
            }
        } else {
            VStack(spacing: spacing) {
                content
            }
        }
    }
}

// MARK: - Responsive Button Styles
struct ResponsivePrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(ResponsiveTypography.headline.weight(.semibold))
            .foregroundColor(.white)
            .frame(height: ResponsiveSizing.buttonHeight)
            .frame(maxWidth: .infinity)
            .background(AppTheme.sexyGradient)
            .cornerRadius(ResponsiveRadius.medium)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .shadow(
                color: AppTheme.primaryColor.opacity(0.3),
                radius: DeviceInfo.isIPad ? 8 : 6,
                x: 0,
                y: DeviceInfo.isIPad ? 4 : 3
            )
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct ResponsiveSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(ResponsiveTypography.headline.weight(.medium))
            .foregroundColor(AppTheme.primaryColor)
            .frame(height: ResponsiveSizing.buttonHeight)
            .frame(maxWidth: .infinity)
            .background(Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                    .stroke(AppTheme.primaryColor, lineWidth: DeviceInfo.isIPad ? 2 : 1.5)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
