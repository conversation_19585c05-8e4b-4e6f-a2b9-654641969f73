import Foundation
import FirebaseFirestore
import FirebaseAuth
import UIKit

class OnlineStatusManager: ObservableObject {
    static let shared = OnlineStatusManager()

    private let db = Firestore.firestore()
    private var currentUserID: String?
    private var heartbeatTimer: Timer?
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid

    // Constants
    private let heartbeatInterval: TimeInterval = 30.0 // Update every 30 seconds
    private let offlineThreshold: TimeInterval = 60.0  // Consider offline after 60 seconds

    private init() {
        setupNotificationObservers()
    }

    // MARK: - Public Methods

    /// Start online status tracking for the current user
    @MainActor func startTracking() {
        guard let userID = Auth.auth().currentUser?.uid else {
            print("❌ OnlineStatusManager: No authenticated user")
            return
        }

        currentUserID = userID
        setOnlineStatus(true)
        startHeartbeat()

        print("✅ OnlineStatusManager: Started tracking for user: \(userID)")
    }

    /// Stop online status tracking
    @MainActor func stopTracking() {
        guard let userID = currentUserID else { return }

        setOnlineStatus(false)
        stopHeartbeat()
        currentUserID = nil

        print("🛑 OnlineStatusManager: Stopped tracking for user: \(userID)")
    }

    /// Check if a user is currently online
    func isUserOnline(userID: String, completion: @escaping (Bool) -> Void) {
        db.collection("users").document(userID).getDocument { snapshot, error in
            if let error = error {
                print("❌ OnlineStatusManager: Error checking online status: \(error)")
                completion(false)
                return
            }

            guard let data = snapshot?.data(),
                  let lastSeen = (data["lastSeen"] as? Timestamp)?.dateValue() else {
                completion(false)
                return
            }

            let timeSinceLastSeen = Date().timeIntervalSince(lastSeen)
            let isOnline = timeSinceLastSeen < self.offlineThreshold

            completion(isOnline)
        }
    }

    // MARK: - Private Methods

    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
    }

    @objc private func appDidEnterBackground() {
        print("📱 OnlineStatusManager: App entered background")

        // Start background task to update status
        backgroundTask = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }

        // Set offline status after a delay to allow for quick app switches
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            if UIApplication.shared.applicationState == .background {
                self.setOnlineStatus(false)
                self.stopHeartbeat()
            }
            self.endBackgroundTask()
        }
    }

    @objc private func appWillEnterForeground() {
        print("📱 OnlineStatusManager: App entering foreground")
        endBackgroundTask()

        if currentUserID != nil {
            setOnlineStatus(true)
            startHeartbeat()
        }
    }

    @objc private func appWillTerminate() {
        print("📱 OnlineStatusManager: App terminating")
        setOnlineStatus(false)
        stopHeartbeat()
    }

    private func endBackgroundTask() {
        if backgroundTask != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTask)
            backgroundTask = .invalid
        }
    }

    private func startHeartbeat() {
        stopHeartbeat() // Stop any existing timer

        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: heartbeatInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateLastSeen()
            }
        }

        print("💓 OnlineStatusManager: Heartbeat started")
    }

    private func stopHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
        print("💓 OnlineStatusManager: Heartbeat stopped")
    }

    private func setOnlineStatus(_ isOnline: Bool) {
        guard let userID = currentUserID else { return }

        let updateData: [String: Any] = [
            "isOnline": isOnline,
            "lastSeen": FieldValue.serverTimestamp()
        ]

        db.collection("users").document(userID).updateData(updateData) { error in
            if let error = error {
                print("❌ OnlineStatusManager: Failed to update online status: \(error)")
            } else {
                print("✅ OnlineStatusManager: Updated online status to: \(isOnline)")
            }
        }
    }

    @MainActor private func updateLastSeen() {
        guard let userID = currentUserID else { return }

        let updateData: [String: Any] = [
            "lastSeen": FieldValue.serverTimestamp(),
            "isOnline": true
        ]

        db.collection("users").document(userID).updateData(updateData) { error in
            if let error = error {
                print("❌ OnlineStatusManager: Failed to update last seen: \(error)")
            } else {
                print("💓 OnlineStatusManager: Updated last seen timestamp")
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        Task { @MainActor in
            stopTracking()
        }
    }
}

// MARK: - Online Status Extensions

extension OnlineStatusManager {
    /// Get online status with caching for better performance
    func getOnlineStatus(for userID: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            isUserOnline(userID: userID) { isOnline in
                continuation.resume(returning: isOnline)
            }
        }
    }

    /// Batch check online status for multiple users
    func getOnlineStatus(for userIDs: [String], completion: @escaping ([String: Bool]) -> Void) {
        var results: [String: Bool] = [:]
        let group = DispatchGroup()

        for userID in userIDs {
            group.enter()
            isUserOnline(userID: userID) { isOnline in
                results[userID] = isOnline
                group.leave()
            }
        }

        group.notify(queue: .main) {
            completion(results)
        }
    }
}
