import Foundation
import FirebaseFirestore
import Combine

@MainActor
class DailySwipeLimitManager: ObservableObject {
    static let shared = DailySwipeLimitManager()
    private let db = Firestore.firestore()
    
    @Published var dailySwipeCount: Int = 0
    @Published var hasReachedLimit: Bool = false
    @Published var isLoading: Bool = false
    
    // Constants
    let maxDailySwipes = 25
    private var currentUserID: String?
    
    private init() {
        setupDailyResetTimer()
    }
    
    // MARK: - Public Methods
    
    func initialize(for userID: String) async {
        currentUserID = userID
        await loadDailySwipeCount()
    }
    
    func canSwipe(isPremium: Bool) -> Bool {
        if isPremium {
            return true // Premium users have unlimited swipes
        }
        return dailySwipeCount < maxDailySwipes
    }
    
    func recordSwipe(userID: String, isPremium: Bool) async {
        print("🎯 DailySwipeLimitManager.recordSwipe called:")
        print("   - UserID: \(userID)")
        print("   - isPremium: \(isPremium)")
        print("   - Current count: \(dailySwipeCount)")
        print("   - Max swipes: \(maxDailySwipes)")

        guard !isPremium else {
            print("✅ Premium user - swipe not counted towards limit")
            return
        }

        guard dailySwipeCount < maxDailySwipes else {
            print("⚠️ User has reached daily swipe limit (\(dailySwipeCount)/\(maxDailySwipes))")
            return
        }

        let previousCount = dailySwipeCount

        // Increment local count on main thread
        await MainActor.run {
            dailySwipeCount += 1
            hasReachedLimit = dailySwipeCount >= maxDailySwipes
        }

        print("📊 Swipe recorded: \(previousCount) → \(dailySwipeCount)/\(maxDailySwipes)")

        // Update Firebase
        await updateFirebaseSwipeCount(userID: userID)
    }
    
    func getRemainingSwipes(isPremium: Bool) -> Int {
        if isPremium {
            return Int.max // Unlimited for premium
        }
        return max(0, maxDailySwipes - dailySwipeCount)
    }
    
    func getSwipeCountText(isPremium: Bool) -> String {
        if isPremium {
            return "Unlimited"
        }
        let remaining = getRemainingSwipes(isPremium: false)
        return "\(remaining) left today"
    }
    
    // MARK: - Private Methods
    
    private func loadDailySwipeCount() async {
        guard let userID = currentUserID else { return }
        
        isLoading = true
        
        do {
            let today = getTodayDateString()
            let doc = try await db.collection("daily_swipe_limits")
                .document(userID)
                .collection("daily_counts")
                .document(today)
                .getDocument()
            
            if let data = doc.data() {
                let count = data["swipeCount"] as? Int ?? 0
                
                DispatchQueue.main.async {
                    self.dailySwipeCount = count
                    self.hasReachedLimit = count >= self.maxDailySwipes
                }
                
                print("✅ Loaded daily swipe count: \(count)")
            } else {
                // No document exists, user hasn't swiped today
                DispatchQueue.main.async {
                    self.dailySwipeCount = 0
                    self.hasReachedLimit = false
                }
                
                print("✅ No swipes recorded for today")
            }
            
        } catch {
            print("❌ Failed to load daily swipe count: \(error)")
        }
        
        isLoading = false
    }
    
    private func updateFirebaseSwipeCount(userID: String) async {
        do {
            let today = getTodayDateString()
            let docRef = db.collection("daily_swipe_limits")
                .document(userID)
                .collection("daily_counts")
                .document(today)
            
            let updateData: [String: Any] = [
                "swipeCount": dailySwipeCount,
                "lastUpdated": FieldValue.serverTimestamp(),
                "date": today,
                "userID": userID
            ]
            
            try await docRef.setData(updateData, merge: true)
            print("✅ Updated Firebase swipe count: \(dailySwipeCount)")
            
        } catch {
            print("❌ Failed to update Firebase swipe count: \(error)")
        }
    }
    
    private func getTodayDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: Date())
    }
    
    // MARK: - Daily Reset Timer
    
    private func setupDailyResetTimer() {
        // Calculate time until next midnight
        let calendar = Calendar.current
        let now = Date()
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now)!
        let midnight = calendar.startOfDay(for: tomorrow)
        
        let timeUntilMidnight = midnight.timeIntervalSince(now)
        
        // Schedule timer for midnight
        Timer.scheduledTimer(withTimeInterval: timeUntilMidnight, repeats: false) { _ in
            Task {
                await self.performDailyReset()
            }
        }
        
        print("🕐 Daily reset timer scheduled for: \(midnight)")
    }
    
    private func performDailyReset() async {
        print("🌅 Performing daily swipe limit reset...")
        
        // Reset local state
        DispatchQueue.main.async {
            self.dailySwipeCount = 0
            self.hasReachedLimit = false
        }
        
        // Schedule next reset
        setupDailyResetTimer()
        
        print("✅ Daily swipe limit reset completed")
    }
    
    // MARK: - Analytics Integration
    
    func trackSwipeLimitReached(userID: String) {
        AnalyticsManager.shared.trackEvent("daily_swipe_limit_reached", parameters: [
            "user_id": userID,
            "swipe_count": dailySwipeCount,
            "limit": maxDailySwipes,
            "date": getTodayDateString()
        ])
        
        print("📊 Tracked swipe limit reached for user: \(userID)")
    }
    
    func trackPremiumUpgradeFromSwipeLimit(userID: String) {
        AnalyticsManager.shared.trackEvent("premium_upgrade_from_swipe_limit", parameters: [
            "user_id": userID,
            "swipe_count_when_prompted": dailySwipeCount,
            "conversion_source": "swipe_limit"
        ])
        
        print("📊 Tracked premium upgrade from swipe limit for user: \(userID)")
    }
}

// MARK: - Swipe Limit Data Model
struct DailySwipeData: Codable {
    let userID: String
    let date: String
    let swipeCount: Int
    let lastUpdated: Date
    
    init(userID: String, date: String, swipeCount: Int) {
        self.userID = userID
        self.date = date
        self.swipeCount = swipeCount
        self.lastUpdated = Date()
    }
}
