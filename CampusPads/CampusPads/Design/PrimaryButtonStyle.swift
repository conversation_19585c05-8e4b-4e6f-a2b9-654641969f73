import SwiftUI

// MARK: - Enhanced Primary Button Style
struct PrimaryButtonStyle: ButtonStyle {
    var backgroundColor: Color = AppTheme.primaryColor
    var isLoading: Bool = false

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTheme.body.weight(.semibold))
            .foregroundColor(.white)
            .padding(.vertical, AppTheme.spacing16)
            .padding(.horizontal, AppTheme.spacing24)
            .frame(maxWidth: .infinity)
            .background(
                ZStack {
                    if isLoading {
                        // Animated loading gradient
                        LinearGradient(
                            colors: [
                                backgroundColor.opacity(0.6),
                                backgroundColor.opacity(0.8),
                                backgroundColor.opacity(0.6)
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isLoading)
                    } else {
                        // Enhanced gradient with press effect
                        LinearGradient(
                            colors: [
                                backgroundColor,
                                backgroundColor.opacity(0.8),
                                backgroundColor
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        .opacity(configuration.isPressed ? 0.9 : 1.0)
                        .brightness(configuration.isPressed ? -0.1 : 0)
                    }
                }
            )
            .cornerRadius(AppTheme.radiusLarge)
            .scaleEffect(configuration.isPressed ? 0.97 : 1.0)
            .rotation3DEffect(
                .degrees(configuration.isPressed ? 1 : 0),
                axis: (x: 1, y: 1, z: 0)
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
            .shadow(color: backgroundColor.opacity(0.4), radius: configuration.isPressed ? 8 : 12, x: 0, y: configuration.isPressed ? 4 : 6)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    HapticFeedbackManager.shared.generateImpact(style: .medium)
                }
            }
    }
}

// MARK: - Secondary Button Style
struct SecondaryButtonStyle: ButtonStyle {
    var borderColor: Color = AppTheme.primaryColor
    var textColor: Color = AppTheme.primaryColor

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTheme.body.weight(.semibold))
            .foregroundColor(textColor.opacity(configuration.isPressed ? 0.7 : 1.0))
            .padding(.vertical, AppTheme.spacing16)
            .padding(.horizontal, AppTheme.spacing24)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .stroke(borderColor.opacity(configuration.isPressed ? 0.7 : 1.0), lineWidth: 2)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .fill(configuration.isPressed ? borderColor.opacity(0.1) : Color.clear)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: AppTheme.animationFast), value: configuration.isPressed)
    }
}

// MARK: - Tertiary Button Style
struct TertiaryButtonStyle: ButtonStyle {
    var textColor: Color = AppTheme.primaryColor

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTheme.body.weight(.medium))
            .foregroundColor(textColor.opacity(configuration.isPressed ? 0.7 : 1.0))
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusSmall)
                    .fill(configuration.isPressed ? textColor.opacity(0.1) : Color.clear)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: AppTheme.animationFast), value: configuration.isPressed)
    }
}

// MARK: - Icon Button Style
struct IconButtonStyle: ButtonStyle {
    var backgroundColor: Color = AppTheme.surfacePrimary
    var iconColor: Color = AppTheme.primaryColor
    var size: CGFloat = 50

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 20, weight: .semibold))
            .foregroundColor(iconColor.opacity(configuration.isPressed ? 0.7 : 1.0))
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(backgroundColor.opacity(configuration.isPressed ? 0.8 : 1.0))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: AppTheme.animationFast), value: configuration.isPressed)
            .lightShadow()
    }
}

// MARK: - Floating Action Button Style
struct FloatingActionButtonStyle: ButtonStyle {
    var backgroundColor: Color = AppTheme.primaryColor
    var size: CGFloat = 60

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 24, weight: .bold))
            .foregroundColor(.white)
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(backgroundColor.opacity(configuration.isPressed ? 0.8 : 1.0))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: AppTheme.animationFast), value: configuration.isPressed)
            .heavyShadow()
    }
}

// MARK: - Previews
struct ButtonStyles_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(spacing: AppTheme.spacing20) {
                Group {
                    Text("Button Styles")
                        .font(AppTheme.title2)
                        .padding(.top)

                    Button("Primary Button") { }
                        .buttonStyle(PrimaryButtonStyle())

                    Button("Secondary Button") { }
                        .buttonStyle(SecondaryButtonStyle())

                    Button("Tertiary Button") { }
                        .buttonStyle(TertiaryButtonStyle())

                    Button("Custom Primary") { }
                        .buttonStyle(PrimaryButtonStyle(backgroundColor: AppTheme.accentColor))
                }

                Group {
                    HStack(spacing: AppTheme.spacing16) {
                        Button(action: {}) {
                            Image(systemName: "heart.fill")
                        }
                        .buttonStyle(IconButtonStyle(iconColor: .red))

                        Button(action: {}) {
                            Image(systemName: "star.fill")
                        }
                        .buttonStyle(IconButtonStyle(iconColor: .yellow))

                        Button(action: {}) {
                            Image(systemName: "plus")
                        }
                        .buttonStyle(FloatingActionButtonStyle())
                    }
                    .padding(.top)
                }
            }
            .padding(AppTheme.spacing20)
        }
        .background(AppTheme.backgroundGradient)
        .previewLayout(.sizeThatFits)
    }
}
