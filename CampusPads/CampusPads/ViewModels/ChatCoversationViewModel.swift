import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift
import Combine
import FirebaseAuth
import UIKit

/// A combined view model for a single chat conversation.
class ChatConversationViewModel: ObservableObject {
    @Published var messages: [MessageModel] = []
    @Published var errorMessage: String?
    @Published var isTyping: Bool = false  // Indicates if the other user is typing

    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()
    private var typingTimer: Timer?

    var chatID: String
    var currentUserID: String? {
        Auth.auth().currentUser?.uid
    }

    init(chatID: String) {
        self.chatID = chatID
        observeMessages()
        observeTypingStatus()
    }

    deinit {
        cancellables.forEach { $0.cancel() }
        typingTimer?.invalidate()
    }

    /// Observes messages in real time for this chat.
    func observeMessages() {
        db.collection("chats")
            .document(chatID)
            .collection("messages")
            .order(by: "timestamp", descending: false)
            .snapshotPublisher()
            .map { querySnapshot -> [MessageModel] in
                querySnapshot.documents.compactMap { doc in
                    do {
                        var message = try doc.data(as: MessageModel.self)
                        if message.id == nil {
                            message.id = doc.documentID
                        }
                        return message
                    } catch {
                        print("Error decoding message doc: \(error)")
                        return nil
                    }
                }
            }
            .sink { [weak self] completion in
                if case let .failure(error) = completion {
                    DispatchQueue.main.async {
                        self?.errorMessage = error.localizedDescription
                    }
                }
            } receiveValue: { [weak self] fetchedMessages in
                DispatchQueue.main.async {
                    self?.messages = fetchedMessages
                    self?.markMessagesAsRead()
                }
            }
            .store(in: &cancellables)
    }

    /// Observes the typing status from the chat document.
    func observeTypingStatus() {
        db.collection("chats")
            .document(chatID)
            .addSnapshotListener { [weak self] snapshot, error in
                if let error = error {
                    print("Error observing typing status: \(error.localizedDescription)")
                    return
                }
                if let data = snapshot?.data(), let typing = data["isTyping"] as? Bool {
                    DispatchQueue.main.async {
                        self?.isTyping = typing
                    }
                }
            }
    }

    /// Sends a new message with enhanced error handling and chat creation
    func sendMessage(text: String, participants: [String]? = nil) {
        guard let userID = currentUserID else {
            DispatchQueue.main.async {
                self.errorMessage = "User not authenticated."
            }
            return
        }

        // Validate message content
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else {
            DispatchQueue.main.async {
                self.errorMessage = "Message cannot be empty."
            }
            return
        }

        guard trimmedText.count <= 1000 else {
            DispatchQueue.main.async {
                self.errorMessage = "Message is too long. Please keep it under 1000 characters."
            }
            return
        }

        // CRITICAL: Content moderation before sending
        let moderationResult = ContentModerationService.shared.validateMessageContent(trimmedText)

        if !moderationResult.isApproved {
            Logger.shared.warning("Message blocked by content moderation")
            DispatchQueue.main.async {
                self.errorMessage = "Message contains inappropriate content and cannot be sent."
            }

            // Record violation for user
            Task {
                if let violationType = moderationResult.violationType {
                    let violation = ContentViolation(field: "message", type: violationType)
                    await ContentModerationService.shared.recordViolation(userID: userID, violation: violation)
                }
            }
            return
        }

        // Use filtered text (with profanity censored if any)
        let messageText = moderationResult.filteredText

        Logger.shared.info("Sending message to chat: \(chatID)")

        // If this is the first message and we have participants, ensure chat exists
        if messages.isEmpty, let participants = participants {
            Logger.shared.debug("First message - ensuring chat exists")
            ensureChatExists(participants: participants) { [weak self] success in
                if success {
                    self?.sendMessageToExistingChat(text: messageText, userID: userID, participants: participants)
                } else {
                    DispatchQueue.main.async {
                        self?.errorMessage = "Failed to create chat conversation"
                    }
                }
            }
        } else {
            sendMessageToExistingChat(text: messageText, userID: userID, participants: participants)
        }
    }

    /// Ensure chat document exists before sending message
    private func ensureChatExists(participants: [String], completion: @escaping (Bool) -> Void) {
        // Check if chat document exists
        db.collection("chats").document(chatID).getDocument { [weak self] document, error in
            if let error = error {
                print("❌ ChatConversationViewModel: Error checking chat existence: \(error.localizedDescription)")
                completion(false)
                return
            }

            if let document = document, document.exists {
                print("✅ ChatConversationViewModel: Chat already exists")
                completion(true)
            } else {
                print("📝 ChatConversationViewModel: Creating new chat document")
                self?.createChatDocument(participants: participants, completion: completion)
            }
        }
    }

    /// Create chat document if it doesn't exist
    private func createChatDocument(participants: [String], completion: @escaping (Bool) -> Void) {
        let chatData: [String: Any] = [
            "participants": participants.sorted(),
            "createdAt": FieldValue.serverTimestamp(),
            "lastMessageAt": FieldValue.serverTimestamp(),
            "lastMessage": "",
            "lastMessageSenderID": "",
            "unreadCount": participants.reduce(into: [String: Int]()) { result, participant in
                result[participant] = 0
            },
            "isActive": true
        ]

        db.collection("chats").document(chatID).setData(chatData) { error in
            if let error = error {
                print("❌ ChatConversationViewModel: Failed to create chat: \(error.localizedDescription)")
                completion(false)
            } else {
                print("✅ ChatConversationViewModel: Successfully created chat")
                completion(true)
            }
        }
    }

    /// Send message to existing chat
    private func sendMessageToExistingChat(text: String, userID: String, participants: [String]?) {
        let newMessage = MessageModel(
            id: nil,
            senderID: userID,
            text: text,
            timestamp: Date(),
            isRead: false
        )

        // Handle offline scenario
        if !NetworkMonitor.shared.isConnected {
            // CRITICAL FIX: Safe encoding instead of force unwrapping
            do {
                let messageData = try JSONEncoder().encode(newMessage)
                OfflineManager.shared.addPendingOperation(
                    OfflineManager.PendingOperation(
                        type: .messageSend,
                        data: messageData,
                        timestamp: Date()
                    )
                )

                // Add message to local state immediately for better UX
                DispatchQueue.main.async {
                    self.messages.append(newMessage)
                    self.errorMessage = nil
                }
            } catch {
                print("❌ ChatCoversationViewModel: Failed to encode message for offline storage: \(error)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to save message for offline sending"
                }
            }
            return
        }

        do {
            try db.collection("chats")
                .document(chatID)
                .collection("messages")
                .document()
                .setData(from: newMessage) { [weak self] error in
                    DispatchQueue.main.async {
                        if let error = error {
                            self?.errorMessage = "Failed to send message: \(error.localizedDescription)"
                        } else {
                            self?.errorMessage = nil
                            print("✅ ChatConversationViewModel: Message sent successfully")

                            // Update chat metadata
                            self?.updateChatMetadata(text: text, senderID: userID)

                            // 🔔 SEND MESSAGE NOTIFICATION
                            self?.sendMessageNotification(text: text, senderID: userID, participants: participants)

                            // Mark conversation as started if this is first message
                            if let participants = participants, self?.messages.count == 1 {
                                ChatManager.shared.markConversationStarted(for: participants, chatID: self?.chatID ?? "")
                            }
                        }
                    }
                }
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to encode message: \(error.localizedDescription)"
            }
        }

        // Reset typing status after sending
        setTypingStatus(isTyping: false)
    }

    /// Updates the chat document with the last message info
    private func updateChatMetadata(text: String, senderID: String) {
        ChatManager.shared.updateChatMetadata(chatID: chatID, lastMessage: text, senderID: senderID) { error in
            if let error = error {
                print("❌ ChatConversationViewModel: Failed to update chat metadata: \(error.localizedDescription)")
            }
        }
    }

    /// Marks unread messages (not sent by the current user) as read.
    func markMessagesAsRead() {
        guard let currentUserID = currentUserID else { return }
        for message in messages where message.senderID != currentUserID && (message.isRead == nil || message.isRead == false) {
            guard let messageID = message.id else { continue }
            let messageRef = db.collection("chats").document(chatID).collection("messages").document(messageID)
            messageRef.updateData(["isRead": true]) { error in
                if let error = error {
                    print("Error marking message as read: \(error.localizedDescription)")
                }
            }
        }
    }

    /// Updates the typing status in the chat document.
    func setTypingStatus(isTyping: Bool) {
        db.collection("chats").document(chatID).updateData(["isTyping": isTyping]) { error in
            if let error = error {
                print("Error updating typing status: \(error.localizedDescription)")
            }
        }
    }

    /// Call this when the user is actively typing.
    func userIsTyping() {
        // Immediately set typing status to true.
        setTypingStatus(isTyping: true)
        // Reset the timer.
        typingTimer?.invalidate()
        typingTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false, block: { [weak self] _ in
            self?.setTypingStatus(isTyping: false)
        })
    }

    // MARK: - Push Notifications

    /// Send push notification for new message
    private func sendMessageNotification(text: String, senderID: String, participants: [String]?) {
        guard let participants = participants,
              let recipientID = participants.first(where: { $0 != senderID }) else {
            print("❌ ChatConversationViewModel: Could not determine message recipient")
            return
        }

        print("🔔 ChatConversationViewModel: Sending message notification to \(recipientID)")

        // Get sender's name for notification
        Firestore.firestore().collection("users").document(senderID).getDocument { snapshot, error in
            guard let data = snapshot?.data(),
                  let senderName = data["firstName"] as? String else {
                print("❌ ChatConversationViewModel: Could not get sender name for notification")
                return
            }

            // Send message notification
            PushNotificationService.shared.sendMessageNotification(
                to: recipientID,
                from: senderName,
                message: text,
                chatID: self.chatID
            )

            print("✅ ChatConversationViewModel: Message notification sent to \(recipientID)")
        }
    }

    // MARK: - Media Message Sending

    /// Send an image message
    func sendImageMessage(_ image: UIImage, participants: [String]? = nil) {
        guard let userID = currentUserID else {
            DispatchQueue.main.async {
                self.errorMessage = "User not authenticated."
            }
            return
        }

        let messageID = UUID().uuidString

        // Create placeholder message with upload progress
        let placeholderMessage = MessageModel(
            id: messageID,
            senderID: userID,
            text: "📸 Sending image...",
            timestamp: Date(),
            isRead: false,
            messageType: .image,
            uploadProgress: 0.0
        )

        // Add placeholder to UI immediately
        DispatchQueue.main.async {
            self.messages.append(placeholderMessage)
        }

        // Upload image
        Task {
            do {
                let (imageURL, thumbnailURL) = try await MediaUploadService.shared.uploadImage(image, chatID: chatID, messageID: messageID)

                // Create final message
                let finalMessage = MessageModel(
                    id: messageID,
                    senderID: userID,
                    text: "",
                    timestamp: Date(),
                    isRead: false,
                    messageType: .image,
                    mediaUrl: imageURL,
                    thumbnailUrl: thumbnailURL
                )

                // Save to Firebase
                try await saveMediaMessage(finalMessage, participants: participants)

                // Update local message
                DispatchQueue.main.async {
                    if let index = self.messages.firstIndex(where: { $0.id == messageID }) {
                        self.messages[index] = finalMessage
                    }
                }

            } catch {
                print("❌ Failed to upload image: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    // Remove placeholder message on error
                    self.messages.removeAll { $0.id == messageID }
                    self.errorMessage = "Failed to send image: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Send a video message
    func sendVideoMessage(from url: URL, participants: [String]? = nil) {
        guard let userID = currentUserID else {
            DispatchQueue.main.async {
                self.errorMessage = "User not authenticated."
            }
            return
        }

        let messageID = UUID().uuidString

        // Create placeholder message
        let placeholderMessage = MessageModel(
            id: messageID,
            senderID: userID,
            text: "🎥 Sending video...",
            timestamp: Date(),
            isRead: false,
            messageType: .video,
            uploadProgress: 0.0
        )

        // Add placeholder to UI immediately
        DispatchQueue.main.async {
            self.messages.append(placeholderMessage)
        }

        // Upload video
        Task {
            do {
                let (videoURL, thumbnailURL) = try await MediaUploadService.shared.uploadVideo(from: url, chatID: chatID, messageID: messageID)

                // Create final message
                let finalMessage = MessageModel(
                    id: messageID,
                    senderID: userID,
                    text: "",
                    timestamp: Date(),
                    isRead: false,
                    messageType: .video,
                    mediaUrl: videoURL,
                    thumbnailUrl: thumbnailURL
                )

                // Save to Firebase
                try await saveMediaMessage(finalMessage, participants: participants)

                // Update local message
                DispatchQueue.main.async {
                    if let index = self.messages.firstIndex(where: { $0.id == messageID }) {
                        self.messages[index] = finalMessage
                    }
                }

            } catch {
                print("❌ Failed to upload video: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    // Remove placeholder message on error
                    self.messages.removeAll { $0.id == messageID }
                    self.errorMessage = "Failed to send video: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Save media message to Firebase
    private func saveMediaMessage(_ message: MessageModel, participants: [String]?) async throws {
        // Ensure chat exists if this is the first message
        if messages.count == 1, let participants = participants {
            try await ensureChatExistsAsync(participants: participants)
        }

        // Save message to Firebase
        try db.collection("chats")
            .document(chatID)
            .collection("messages")
            .document(message.id ?? UUID().uuidString)
            .setData(from: message)

        // Update chat metadata
        let messageText = message.messageType == .image ? "📸 Image" : "🎥 Video"
        updateChatMetadata(text: messageText, senderID: message.senderID)

        print("✅ Media message saved successfully")
    }

    /// Async version of ensureChatExists
    private func ensureChatExistsAsync(participants: [String]) async throws {
        let document = try await db.collection("chats").document(chatID).getDocument()

        if !document.exists {
            let chatData: [String: Any] = [
                "participants": participants.sorted(),
                "createdAt": FieldValue.serverTimestamp(),
                "lastMessageAt": FieldValue.serverTimestamp(),
                "lastMessage": "",
                "lastMessageSenderID": "",
                "unreadCount": participants.reduce(into: [String: Int]()) { result, participant in
                    result[participant] = 0
                },
                "isActive": true
            ]

            try await db.collection("chats").document(chatID).setData(chatData)
        }
    }

    /// Add emoji reaction to a message with improved persistence
    func addReaction(to messageID: String, emoji: String) {
        guard let userID = currentUserID else {
            print("❌ ChatConversationViewModel: Cannot add reaction - user not authenticated")
            return
        }

        print("💬 ChatConversationViewModel: Adding reaction \(emoji) to message \(messageID)")

        let messageRef = db.collection("chats")
            .document(chatID)
            .collection("messages")
            .document(messageID)

        // Update Firebase FIRST, then let the real-time listener update the UI
        Task {
            do {
                // Use transaction for atomic updates to prevent race conditions
                try await db.runTransaction { transaction, errorPointer in
                    let messageDoc: DocumentSnapshot
                    do {
                        messageDoc = try transaction.getDocument(messageRef)
                    } catch let fetchError as NSError {
                        errorPointer?.pointee = fetchError
                        return nil
                    }

                    let currentData = messageDoc.data() ?? [:]
                    var reactions = currentData["reactions"] as? [String: Int] ?? [:]
                    reactions[emoji] = (reactions[emoji] ?? 0) + 1

                    transaction.updateData(["reactions": reactions], forDocument: messageRef)
                    return nil
                }

                print("✅ ChatConversationViewModel: Successfully saved reaction \(emoji) to Firebase")

            } catch {
                print("❌ ChatConversationViewModel: Error saving reaction: \(error)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to add reaction"
                }
            }
        }
    }
}
