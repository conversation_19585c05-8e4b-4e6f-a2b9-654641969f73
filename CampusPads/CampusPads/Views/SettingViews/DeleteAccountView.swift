import SwiftUI

struct DeleteAccountView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.presentationMode) var presentationMode
    @State private var showConfirmation: Bool = false
    @State private var isDeleting: Bool = false
    @State private var deletionError: String?
    
    var body: some View {
        ZStack {
            // Global background gradient.
            AppTheme.backgroundGradient.ignoresSafeArea()
            
            // Remove inner NavigationView; assume this view is embedded in a navigation container.
            Form {
                Section(header: Text("Warning")
                            .font(AppTheme.subtitleFont)) {
                    Text("Deleting your account is irreversible. All your data will be permanently removed. This action cannot be undone.")
                        .font(AppTheme.bodyFont)
                        .foregroundColor(.red)
                }

                Section(header: Text("What Will Be Deleted")
                            .font(AppTheme.subtitleFont)) {
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        deletionItem(icon: "person.circle", text: "Your profile and personal information")
                        deletionItem(icon: "photo", text: "All uploaded photos and images")
                        deletionItem(icon: "heart", text: "All matches and connections")
                        deletionItem(icon: "message", text: "All chat conversations and messages")
                        deletionItem(icon: "hand.thumbsup", text: "Your swipe history and preferences")
                        deletionItem(icon: "exclamationmark.triangle", text: "Any reports you've made or received")
                        deletionItem(icon: "person.badge.minus", text: "Your account from other users' blocked lists")
                        deletionItem(icon: "key", text: "Your login credentials and authentication")
                    }
                    .padding(.vertical, AppTheme.spacing8)
                }
                
                Section {
                    if isDeleting {
                        ProgressView("Deleting account...")
                            .font(AppTheme.bodyFont)
                    } else {
                        Button("Delete Account") {
                            showConfirmation = true
                        }
                        .font(AppTheme.bodyFont)
                        .foregroundColor(.red)
                    }
                }
            }
            .scrollContentBackground(.hidden)
            .font(AppTheme.bodyFont)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("Delete Account")
                        .font(AppTheme.titleFont)
                        .foregroundColor(.primary)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .font(AppTheme.bodyFont)
                    .foregroundColor(.primary)
                }
            }
            .alert(isPresented: $showConfirmation) {
                Alert(title: Text("Confirm Deletion"),
                      message: Text("Are you sure you want to delete your account? This action cannot be undone."),
                      primaryButton: .destructive(Text("Delete")) {
                        deleteAccount()
                      },
                      secondaryButton: .cancel())
            }
            .alert(item: Binding(get: {
                deletionError.map { GenericAlertError(message: $0) }
            }, set: { _ in deletionError = nil })) { alertError in
                Alert(title: Text("Error"), message: Text(alertError.message), dismissButton: .default(Text("OK")))
            }
        }
    }

    // MARK: - Helper Views

    private func deletionItem(icon: String, text: String) -> some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.red)
                .frame(width: 20)

            Text(text)
                .font(AppTheme.bodyFont)
                .foregroundColor(AppTheme.textPrimary)

            Spacer()
        }
    }

    private func deleteAccount() {
        isDeleting = true

        // Use the comprehensive data deletion service
        DataDeletionService.shared.deleteAllUserData { result in
            DispatchQueue.main.async {
                isDeleting = false
                switch result {
                case .success:
                    Logger.shared.info("Account deletion successful")

                    // Clear the user session in AuthViewModel
                    authViewModel.userSession = nil
                    authViewModel.email = ""
                    authViewModel.password = ""
                    authViewModel.errorMessage = nil
                    authViewModel.passwordResetMessage = nil

                    // Clear ProfileViewModel data
                    ProfileViewModel.shared.clearUserData()

                    // Clear ProfileLoaderService cache
                    ProfileLoaderService.shared.clearCache()
                    ProfileLoaderService.shared.clearProfile()

                    // Dismiss the view - user will be automatically taken to auth screen
                    presentationMode.wrappedValue.dismiss()

                case .failure(let error):
                    Logger.shared.error("Account deletion failed: \(error.localizedDescription)")
                    deletionError = error.localizedDescription
                }
            }
        }
    }
}

struct DeleteAccountView_Previews: PreviewProvider {
    static var previews: some View {
        DeleteAccountView().environmentObject(AuthViewModel())
    }
}
