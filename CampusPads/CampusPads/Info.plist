<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>FirebaseAnalyticsCollectionEnabled</key>
	<false/>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<true/>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>FirebaseInAppMessagingAutomaticDataCollectionEnabled</key>
	<false/>
	<key>ITSAppContentRating</key>
	<string>17+</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>mailto</string>
		<string>tel</string>
	</array>
	<key>MinimumOSVersion</key>
	<string>17.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>campuspads-43e5e.appspot.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>collegepads-1.firebasestorage.app</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>firebase.googleapis.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>firebaselogging-pa.googleapis.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>firebasestorage.googleapis.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>firestore.googleapis.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>NSContactURL</key>
	<string>https://campuspadsapp.com/support</string>
	<key>NSPrivacyPolicyURL</key>
	<string>https://campuspadsapp.com/privacy-policy</string>
	<key>NSSupportsSecureCoding</key>
	<true/>
	<key>NSTermsOfServiceURL</key>
	<string>https://campuspadsapp.com/terms-of-service</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>CampusPads sends notifications for new matches, messages, and important safety updates.</string>
	<key>UIAccessibilityEnabled</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>remote-notification</string>
		<string>processing</string>
	</array>
</dict>
</plist>
