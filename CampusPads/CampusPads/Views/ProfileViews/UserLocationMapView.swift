import SwiftUI
import MapKit

/// A SwiftUI view that displays a secure, non-interactive map with a “location bubble” at the coordinate.
struct UserLocationMapView: View {
    let coordinate: CLLocationCoordinate2D
    let regionSpan: CLLocationDegrees = 0.08  // Increased for more general area view (security)

    var body: some View {
        let region = MKCoordinateRegion(center: coordinate,
                                        span: MKCoordinateSpan(latitudeDelta: regionSpan, longitudeDelta: regionSpan))

        Map(initialPosition: .region(region), interactionModes: []) {
            Annotation("Location", coordinate: coordinate) {
                // Enhanced location indicator with privacy-focused design
                ZStack {
                    // Outer glow for better visibility
                    Circle()
                        .fill(AppTheme.primaryColor.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .blur(radius: 4)

                    // Main location bubble
                    Circle()
                        .fill(AppTheme.primaryColor.opacity(0.4))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Circle()
                                .stroke(AppTheme.primaryColor, lineWidth: 3)
                        )
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)

                    // Center dot
                    Circle()
                        .fill(AppTheme.primaryColor)
                        .frame(width: 12, height: 12)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .cornerRadius(AppTheme.radiusXLarge)
        .clipped()
        .allowsHitTesting(false) // SECURITY FIX: Disable all touch interactions
        .overlay(
            // Privacy indicator overlay
            VStack {
                HStack {
                    Spacer()
                    Image(systemName: "location.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(6)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.6))
                        )
                        .padding(8)
                }
                Spacer()
            }
        )
    }
}

struct UserLocationMapView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleCoordinate = CLLocationCoordinate2D(latitude: 44.98, longitude: -93.27)
        UserLocationMapView(coordinate: sampleCoordinate)
    }
}
