<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CampusPads - Find Your Perfect College Roommate</title>
    <meta name="description" content="Find your perfect college roommate with CampusPads. Smart compatibility matching, housing status logic, and 25 free daily swipes. Premium features for $4.99 one-time.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Meta Tags for Social Sharing -->
    <meta property="og:title" content="CampusPads - Find Your Perfect College Roommate">
    <meta property="og:description" content="Smart roommate matching for college students. 25 free daily swipes, housing compatibility logic, premium features for $4.99 one-time.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://getcollegepads.com">
    <meta property="og:image" content="https://getcollegepads.com/og-image.png">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="CampusPads - Find Your Perfect College Roommate">
    <meta name="twitter:description" content="Smart roommate matching for college students. 25 free daily swipes, housing compatibility logic, premium features for $4.99 one-time.">
    <meta name="twitter:image" content="https://getcollegepads.com/og-image.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-home"></i>
                <span>CampusPads</span>
            </div>
            <div class="nav-menu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#how-it-works" class="nav-link">How It Works</a>
                <a href="#safety" class="nav-link">Safety</a>
                <a href="#download" class="nav-link">Download</a>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-background">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
        </div>
        
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-heart"></i>
                    <span>Smart Roommate Matching • Find Your Perfect Match</span>
                </div>
                
                <h1 class="hero-title">
                    Find Your Perfect
                    <span class="gradient-text">College Roommate</span>
                </h1>

                <p class="hero-description">
                    Swipe through college students, match with compatible roommates, and find your perfect housing situation.
                    Smart compatibility matching with housing status logic for perfect roommate pairs.
                </p>



                <div class="hero-buttons">
                    <div class="btn btn-primary coming-soon-hero">
                        <i class="fab fa-apple"></i>
                        Coming Soon to App Store
                    </div>
                    <a href="#how-it-works" class="btn btn-secondary">
                        <i class="fas fa-play"></i>
                        Watch Demo
                    </a>
                </div>
                
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">25</span>
                        <span class="stat-label">Daily Free Swipes</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">$4.99</span>
                        <span class="stat-label">One-Time Premium</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">8</span>
                        <span class="stat-label">Daily Top Matches</span>
                    </div>
                </div>
            </div>

            <!-- Desktop Carousel (Hidden on Mobile) -->
            <div class="hero-visual">
                <div class="app-showcase">
                    <div class="phone-mockup main-phone">
                        <div class="phone-frame">
                            <img src="images/deckview-screen.png" alt="CampusPads Swipe Interface" class="app-screenshot deck-screen">
                            <img src="images/home-screen.png" alt="CampusPads Home Dashboard" class="app-screenshot home-screen">
                            <img src="images/chat-screen.png" alt="CampusPads Messages" class="app-screenshot chat-screen">
                            <img src="images/match-screen.png" alt="CampusPads Match Celebration" class="app-screenshot match-screen">
                        </div>
                    </div>
                </div>

                <div class="floating-cards">
                    <div class="floating-card card-1">
                        <i class="fas fa-home"></i>
                        <span>Perfect Match Found!</span>
                    </div>
                    <div class="floating-card card-2">
                        <i class="fas fa-shield-alt"></i>
                        <span>Verified Student</span>
                    </div>
                    <div class="floating-card card-3">
                        <i class="fas fa-comments"></i>
                        <span>New Message</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- App Showcase Section -->
    <section class="app-showcase-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    Beautiful, Modern Design
                    <span class="gradient-text">Built for College Students</span>
                </h2>
                <p class="section-description">
                    Experience the most elegant roommate finder app with stunning visuals,
                    smooth animations, and intuitive user interface designed specifically for college life.
                </p>
            </div>

            <!-- Loading Screen Showcase -->
            <div class="loading-showcase-section">
                <div class="loading-screen-frame">
                    <img src="images/loading-screen.png" alt="CampusPads Loading Screen" class="loading-screen-image">
                </div>
                <div class="loading-screen-info">
                    <h3>Stunning First Impression</h3>
                    <p>Beautiful loading screen with brand colors, smooth animations, and anticipation-building design that sets the perfect tone for finding your roommate.</p>
                    <div class="screen-features">
                        <span class="feature-tag">🎨 Brand Identity</span>
                        <span class="feature-tag">⚡ Fast Loading</span>
                        <span class="feature-tag">💫 Smooth Animations</span>
                    </div>
                </div>
            </div>

            <div class="app-screens-grid">
                <div class="screen-showcase deck-showcase">
                    <div class="screen-frame">
                        <img src="images/deckview-screen.png" alt="CampusPads Swipe Interface" class="screen-image">
                    </div>
                    <div class="screen-info">
                        <h3>Tinder-Style Swiping</h3>
                        <p>Beautiful full-screen cards with smooth swipe animations. Discover compatible roommates with our smart matching algorithm and intuitive interface.</p>
                        <div class="screen-features">
                            <span class="feature-tag">💕 Smart Matching</span>
                            <span class="feature-tag">🎯 Full-Screen Cards</span>
                            <span class="feature-tag">✨ Smooth Animations</span>
                        </div>
                    </div>
                </div>

                <div class="screen-showcase home-showcase">
                    <div class="screen-frame">
                        <img src="images/home-screen.png" alt="CampusPads Home Dashboard" class="screen-image">
                    </div>
                    <div class="screen-info">
                        <h3>Elegant Home Dashboard</h3>
                        <p>Clean, modern navigation with easy access to all features. Beautiful tab bar design that makes finding roommates effortless and enjoyable.</p>
                        <div class="screen-features">
                            <span class="feature-tag">🏠 Clean Navigation</span>
                            <span class="feature-tag">🎨 Modern Design</span>
                            <span class="feature-tag">⚡ Fast Access</span>
                        </div>
                    </div>
                </div>

                <div class="screen-showcase chat-showcase">
                    <div class="screen-frame">
                        <img src="images/chat-screen.png" alt="CampusPads Messages Interface" class="screen-image">
                    </div>
                    <div class="screen-info">
                        <h3>Smart Messaging Experience</h3>
                        <p>Intuitive chat interface with real-time messaging, emoji reactions, and smart conversation starters. Connect with your matches effortlessly.</p>
                        <div class="screen-features">
                            <span class="feature-tag">💬 Real-time Chat</span>
                            <span class="feature-tag">😊 Emoji Reactions</span>
                            <span class="feature-tag">🎯 Smart UX</span>
                        </div>
                    </div>
                </div>

                <div class="screen-showcase match-showcase">
                    <div class="screen-frame">
                        <img src="images/match-screen.png" alt="CampusPads Match Celebration" class="screen-image">
                    </div>
                    <div class="screen-info">
                        <h3>Exciting Match Celebrations</h3>
                        <p>Beautiful match animations that create excitement when you find your perfect roommate. Celebrate connections with style and start conversations instantly.</p>
                        <div class="screen-features">
                            <span class="feature-tag">🎉 Match Animation</span>
                            <span class="feature-tag">💫 Celebration Effects</span>
                            <span class="feature-tag">🚀 Instant Chat</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Why Choose CampusPads?</h2>
                <p>Everything you need to find the perfect roommate and housing situation</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Smart Compatibility Matching</h3>
                    <p>Advanced algorithm analyzes lifestyle, study habits, cleanliness, sleep schedules, and housing preferences to find your perfect roommate match.</p>
                    <div class="feature-details">
                        <span>• Lifestyle compatibility scoring</span>
                        <span>• Housing status matching logic</span>
                        <span>• Academic & social preferences</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>College Community</h3>
                    <p>Connect with college students from your university and nearby schools. Build a trusted community of potential roommates.</p>
                    <div class="feature-details">
                        <span>• University-based matching</span>
                        <span>• College student focused</span>
                        <span>• Safe, trusted community</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <h3>Housing Status Matching</h3>
                    <p>Smart housing compatibility - match "Looking for Roommate" with "Looking for Lease" users for perfect housing arrangements.</p>
                    <div class="feature-details">
                        <span>• Looking for Roommate matching</span>
                        <span>• Looking for Lease compatibility</span>
                        <span>• Find Together partnerships</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>Real-Time Chat & Media</h3>
                    <p>Secure messaging with photo sharing, emoji reactions, and typing indicators. Plan your housing together seamlessly.</p>
                    <div class="feature-details">
                        <span>• Real-time messaging</span>
                        <span>• Photo & media sharing</span>
                        <span>• Emoji reactions & typing indicators</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3>Premium Features ($4.99)</h3>
                    <p>One-time payment for unlimited swipes, 8 daily top matches, 3 weekly super likes, and advanced search capabilities.</p>
                    <div class="feature-details">
                        <span>• Unlimited daily swipes</span>
                        <span>• 8 daily premium top matches</span>
                        <span>• Advanced user search by name</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <h3>Advanced Filtering</h3>
                    <p>Filter by housing status, college, gender preference, age, lifestyle habits, cleanliness, and more for precise matching.</p>
                    <div class="feature-details">
                        <span>• Housing & college filters</span>
                        <span>• Lifestyle preference matching</span>
                        <span>• Age & gender preferences</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2>How CampusPads Works</h2>
                <p>Your complete journey from app launch to finding your perfect roommate</p>
            </div>

            <div class="modern-steps-container">
                <!-- Step 1: Create Your Profile -->
                <div class="modern-step">
                    <div class="step-content">
                        <div class="step-number">1</div>
                        <h3>Create Your Profile</h3>
                        <p>Tell us about yourself, your lifestyle, study habits, and housing preferences. Upload photos and verify your student status to join our trusted college community.</p>
                        <div class="step-features">
                            <span class="step-feature">📝 Profile creation</span>
                            <span class="step-feature">🎓 Student verification</span>
                            <span class="step-feature">🏠 Housing preferences</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Smart Matching -->
                <div class="modern-step">
                    <div class="step-content">
                        <div class="step-number">2</div>
                        <h3>Smart Roommate Discovery</h3>
                        <p>Our AI analyzes your preferences and finds compatible roommates. Swipe through potential matches with our beautiful Tinder-style interface and see compatibility scores.</p>
                        <div class="step-features">
                            <span class="step-feature">🧠 AI-powered matching</span>
                            <span class="step-feature">💕 Tinder-style swiping</span>
                            <span class="step-feature">📊 Compatibility scores</span>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Match & Connect -->
                <div class="modern-step">
                    <div class="step-content">
                        <div class="step-number">3</div>
                        <h3>Match & Start Chatting</h3>
                        <p>When you both swipe right, it's a match! Enjoy beautiful celebration animations and start meaningful conversations about your housing journey.</p>
                        <div class="step-features">
                            <span class="step-feature">🎉 Match celebrations</span>
                            <span class="step-feature">💬 Real-time messaging</span>
                            <span class="step-feature">🚀 Instant connections</span>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Plan Together -->
                <div class="modern-step">
                    <div class="step-content">
                        <div class="step-number">4</div>
                        <h3>Find Housing Together</h3>
                        <p>Coordinate with your matches to find the perfect living situation. Share preferences, plan viewings, and make decisions as a team with confidence.</p>
                        <div class="step-features">
                            <span class="step-feature">🏡 Housing coordination</span>
                            <span class="step-feature">📅 Viewing planning</span>
                            <span class="step-feature">🤝 Team decisions</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Safety & Trust Section -->
    <section id="safety" class="safety">
        <div class="container">
            <div class="section-header">
                <h2>Your Safety is Our Priority</h2>
                <p>Built with comprehensive safety features and verification systems</p>
            </div>

            <div class="safety-grid">
                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>Student Verification</h3>
                    <p>Every user must verify their student status with a valid .edu email address and student ID confirmation.</p>
                </div>

                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Profile Moderation</h3>
                    <p>All profiles and photos are reviewed by our moderation team to ensure authenticity and appropriate content.</p>
                </div>

                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h3>Privacy Controls</h3>
                    <p>Comprehensive privacy settings let you control who can see your profile and contact you.</p>
                </div>

                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <h3>Block & Report</h3>
                    <p>Easy-to-use blocking and reporting tools help maintain a safe and respectful community.</p>
                </div>

                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3>Secure Messaging</h3>
                    <p>All communications are encrypted and monitored for inappropriate content or behavior.</p>
                </div>

                <div class="safety-feature">
                    <div class="safety-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3>24/7 Support</h3>
                    <p>Our support team is available around the clock to help with any safety concerns or issues.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <div class="section-header">
                <h2>What Students Are Saying</h2>
                <p>Real stories from students who found their perfect roommates</p>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"The smart matching algorithm really works! I love how it matches housing status - I was looking for a roommate and found someone who needed a place. Perfect fit!"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                        <div class="author-info">
                            <h4>Sarah Chen</h4>
                            <span>University of Minnesota</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Love the college student community - knowing everyone is a real student makes me feel so much safer. The 25 daily swipes are perfect for finding quality matches."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></div>
                        <div class="author-info">
                            <h4>Marcus Johnson</h4>
                            <span>University of Wisconsin</span>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"The premium features are worth it! $4.99 one-time for unlimited swipes and top matches is such a good deal compared to other apps with monthly fees."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar" style="background: linear-gradient(135deg, #4facfe, #00f2fe);"></div>
                        <div class="author-info">
                            <h4>Emily Rodriguez</h4>
                            <span>Iowa State University</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="download-content">
                <div class="download-text">
                    <h2>Ready to Find Your Perfect Roommate?</h2>
                    <p>Join thousands of students who have already found their ideal living situation through CampusPads.</p>

                    <div class="download-features">
                        <div class="download-feature">
                            <i class="fas fa-check"></i>
                            <span>25 free daily swipes</span>
                        </div>
                        <div class="download-feature">
                            <i class="fas fa-check"></i>
                            <span>College student community</span>
                        </div>
                        <div class="download-feature">
                            <i class="fas fa-check"></i>
                            <span>Smart compatibility matching</span>
                        </div>
                        <div class="download-feature">
                            <i class="fas fa-check"></i>
                            <span>Premium for $4.99 one-time</span>
                        </div>
                    </div>

                    <div class="download-buttons">
                        <div class="coming-soon">
                            <div class="btn-icon">
                                <i class="fab fa-apple"></i>
                            </div>
                            <div class="btn-text">
                                <span>Coming Soon to</span>
                                <strong>App Store</strong>
                            </div>
                        </div>

                        <div class="coming-soon">
                            <div class="btn-icon">
                                <i class="fab fa-google-play"></i>
                            </div>
                            <div class="btn-text">
                                <span>Coming Soon to</span>
                                <strong>Google Play</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="download-visual">
                    <div class="real-phones-showcase">
                        <div class="phone-showcase primary-phone">
                            <div class="phone-frame">
                                <img src="images/deckview-screen.png" alt="CampusPads Swipe Interface" class="phone-screenshot">
                            </div>
                            <div class="phone-label">Swipe to Find Matches</div>
                        </div>

                        <div class="phone-showcase secondary-phone">
                            <div class="phone-frame">
                                <img src="images/home-screen.png" alt="CampusPads Home Dashboard" class="phone-screenshot">
                            </div>
                            <div class="phone-label">Beautiful Home Dashboard</div>
                        </div>

                        <div class="phone-showcase tertiary-phone">
                            <div class="phone-frame">
                                <img src="images/chat-screen.png" alt="CampusPads Messages" class="phone-screenshot">
                            </div>
                            <div class="phone-label">Smart Messaging</div>
                        </div>

                        <div class="phone-showcase quaternary-phone">
                            <div class="phone-frame">
                                <img src="images/match-screen.png" alt="CampusPads Match Celebration" class="phone-screenshot">
                            </div>
                            <div class="phone-label">Match Celebrations</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-home"></i>
                        <span>CampusPads</span>
                    </div>
                    <p>The ultimate college roommate finder app. Connect with compatible students and find your perfect living situation.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Product</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#how-it-works">How It Works</a></li>
                        <li><a href="#safety">Safety</a></li>
                        <li><a href="#download">Download</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Press</a></li>
                        <li><a href="support.html">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="support.html">Help Center</a></li>
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="community-guidelines.html">Community Guidelines</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 CampusPads. All rights reserved.</p>
                <p>Made with ❤️ for college students everywhere</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>

    <!-- ========================================
         MOBILE NAVIGATION JS - ADDED 2024
         ========================================
         Last Updated: June 5, 2024
         Purpose: Fix hamburger menu functionality
         ======================================== -->
    <!-- Mobile Navigation JavaScript -->
    <script>
        // Mobile menu toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navToggle = document.querySelector('.nav-toggle');
            const navMenu = document.querySelector('.nav-menu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', function() {
                    navMenu.classList.toggle('active');

                    // Update hamburger icon
                    const icon = navToggle.querySelector('i');
                    if (navMenu.classList.contains('active')) {
                        icon.classList.remove('fa-bars');
                        icon.classList.add('fa-times');
                    } else {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                });

                // Close menu when clicking on a link
                const navLinks = navMenu.querySelectorAll('a');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        navMenu.classList.remove('active');
                        const icon = navToggle.querySelector('i');
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    if (!navToggle.contains(event.target) && !navMenu.contains(event.target)) {
                        navMenu.classList.remove('active');
                        const icon = navToggle.querySelector('i');
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                });
            }
        });
    </script>
</body>
</html>
