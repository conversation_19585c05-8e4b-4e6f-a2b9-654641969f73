// RealtimeDatabaseService.swift
// Service for managing majors and interests in Firebase Realtime Database

import Foundation
import FirebaseDatabase
import Combine

class RealtimeDatabaseService: ObservableObject {
    static let shared = RealtimeDatabaseService()
    
    private let database = Database.database()
    
    @Published var majors: [String] = []
    @Published var interests: [String] = []
    @Published var isLoadingMajors = false
    @Published var isLoadingInterests = false
    @Published var errorMessage: String?
    
    private var majorsRef: DatabaseReference
    private var interestsRef: DatabaseReference
    
    private init() {
        self.majorsRef = database.reference().child("majors")
        self.interestsRef = database.reference().child("interests")
        
        loadMajors()
        loadInterests()
    }
    
    // MARK: - Majors Management
    
    /// Loads majors from Firebase Realtime Database
    func loadMajors() {
        isLoadingMajors = true
        errorMessage = nil
        
        print("🎓 RealtimeDatabaseService: Loading majors from Firebase...")
        
        majorsRef.observeSingleEvent(of: .value) { [weak self] snapshot in
            DispatchQueue.main.async {
                self?.isLoadingMajors = false
                
                if snapshot.exists() {
                    if let majorsDict = snapshot.value as? [String: Any] {
                        let loadedMajors = Array(majorsDict.keys).sorted()
                        print("✅ RealtimeDatabaseService: Loaded \(loadedMajors.count) majors from Firebase")
                        self?.majors = loadedMajors
                    }
                } else {
                    print("⚠️ RealtimeDatabaseService: No majors found in Firebase")
                    self?.errorMessage = "No majors data available"
                }
            }
        } withCancel: { [weak self] error in
            DispatchQueue.main.async {
                self?.isLoadingMajors = false
                self?.errorMessage = error.localizedDescription
                print("❌ RealtimeDatabaseService: Error loading majors: \(error.localizedDescription)")
            }
        }
    }
    

    
    // MARK: - Interests Management
    
    /// Loads interests from Firebase Realtime Database
    func loadInterests() {
        isLoadingInterests = true
        errorMessage = nil
        
        print("🎯 RealtimeDatabaseService: Loading interests from Firebase...")
        
        interestsRef.observeSingleEvent(of: .value) { [weak self] snapshot in
            DispatchQueue.main.async {
                self?.isLoadingInterests = false
                
                if snapshot.exists() {
                    if let interestsDict = snapshot.value as? [String: Any] {
                        let loadedInterests = Array(interestsDict.keys).sorted()
                        print("✅ RealtimeDatabaseService: Loaded \(loadedInterests.count) interests from Firebase")
                        self?.interests = loadedInterests
                    }
                } else {
                    print("⚠️ RealtimeDatabaseService: No interests found in Firebase")
                    self?.errorMessage = "No interests data available"
                }
            }
        } withCancel: { [weak self] error in
            DispatchQueue.main.async {
                self?.isLoadingInterests = false
                self?.errorMessage = error.localizedDescription
                print("❌ RealtimeDatabaseService: Error loading interests: \(error.localizedDescription)")
            }
        }
    }

    
    // MARK: - Search Functions
    
    /// Searches majors by query
    func searchMajors(query: String) -> [String] {
        guard !query.isEmpty else { return majors }
        return majors.filter { $0.localizedCaseInsensitiveContains(query) }
    }
    
    /// Searches interests by query
    func searchInterests(query: String) -> [String] {
        guard !query.isEmpty else { return interests }
        return interests.filter { $0.localizedCaseInsensitiveContains(query) }
    }
    
    // MARK: - Refresh Functions
    
    /// Refreshes both majors and interests
    func refreshAll() {
        loadMajors()
        loadInterests()
    }
    
    /// Clears error message
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Verification Functions
    
    /// Verifies that data was uploaded correctly
    func verifyDataIntegrity(completion: @escaping (Bool, String) -> Void) {
        let group = DispatchGroup()
        var majorsCount = 0
        var interestsCount = 0
        var errors: [String] = []
        
        // Check majors
        group.enter()
        majorsRef.observeSingleEvent(of: .value) { snapshot in
            if let majorsDict = snapshot.value as? [String: Any] {
                majorsCount = majorsDict.count
            } else {
                errors.append("Majors data not found")
            }
            group.leave()
        }
        
        // Check interests
        group.enter()
        interestsRef.observeSingleEvent(of: .value) { snapshot in
            if let interestsDict = snapshot.value as? [String: Any] {
                interestsCount = interestsDict.count
            } else {
                errors.append("Interests data not found")
            }
            group.leave()
        }
        
        group.notify(queue: .main) {
            let isValid = majorsCount > 0 &&
                         interestsCount > 0 &&
                         errors.isEmpty

            let message = """
            Data Verification Results:
            Majors: \(majorsCount) ✓
            Interests: \(interestsCount) ✓
            Errors: \(errors.isEmpty ? "None" : errors.joined(separator: ", "))
            """

            completion(isValid, message)
        }
    }
}
