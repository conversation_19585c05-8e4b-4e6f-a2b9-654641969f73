//
//  PerformanceMonitoringService.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebasePerformance
import SwiftUI

/// Service for monitoring app performance and user experience metrics
class PerformanceMonitoringService: ObservableObject {
    static let shared = PerformanceMonitoringService()
    
    private var activeTraces: [String: Trace] = [:]
    private var performanceMetrics: [String: PerformanceMetric] = [:]
    
    private init() {}
    
    // MARK: - Performance Metrics
    
    struct PerformanceMetric {
        let name: String
        let startTime: Date
        var endTime: Date?
        var duration: TimeInterval {
            guard let endTime = endTime else { return 0 }
            return endTime.timeIntervalSince(startTime)
        }
        var metadata: [String: Any] = [:]
    }
    
    // MARK: - Trace Management
    
    /// Start a performance trace
    func startTrace(_ name: String, metadata: [String: Any] = [:]) {
        guard let trace = Performance.startTrace(name: name) else {
            print("⚠️ Failed to start trace: \(name). Performance.startTrace returned nil.")
            // Optionally, still add to performanceMetrics for local tracking even if Firebase trace failed
            performanceMetrics[name] = PerformanceMetric(
                name: name,
                startTime: Date(),
                metadata: metadata
            )
            return
        }
        
        // Add custom attributes
        for (key, value) in metadata {
            if let stringValue = value as? String {
                trace.setValue(stringValue, forAttribute: key)
            }
            // Consider handling other types if necessary, e.g., Int, Bool
        }
        
        activeTraces[name] = trace
        
        // Also track locally for detailed analysis
        performanceMetrics[name] = PerformanceMetric(
            name: name,
            startTime: Date(),
            metadata: metadata
        )
        
        print("🚀 Started trace: \(name)")
    }
    
    /// Stop a performance trace
    func stopTrace(_ name: String, additionalMetadata: [String: Any] = [:]) {
        guard let trace = activeTraces[name] else {
            print("⚠️ No active trace found for: \(name)")
            return
        }
        
        // Add additional metadata
        for (key, value) in additionalMetadata {
            if let stringValue = value as? String {
                trace.setValue(stringValue, forAttribute: key)
            }
        }
        
        trace.stop()
        activeTraces.removeValue(forKey: name)
        
        // Update local metric
        if var metric = performanceMetrics[name] {
            metric.endTime = Date()
            metric.metadata.merge(additionalMetadata) { _, new in new }
            performanceMetrics[name] = metric
            
            logPerformanceMetric(metric)
        }
        
        print("🏁 Stopped trace: \(name)")
    }
    
    /// Add a custom metric to an active trace
    func addMetric(_ name: String, value: Int64, traceName: String) {
        guard let trace = activeTraces[traceName] else {
            print("⚠️ No active trace found for: \(traceName)")
            return
        }
        
        trace.setValue(value, forMetric: name)
        print("📊 Added metric \(name): \(value) to trace: \(traceName)")
    }
    
    // MARK: - Common Performance Traces
    
    /// Track app launch performance
    func trackAppLaunch() {
        startTrace("app_launch", metadata: [
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown",
            "device_model": UIDevice.current.model,
            "ios_version": UIDevice.current.systemVersion
        ])
    }
    
    /// Track screen load performance
    func trackScreenLoad(_ screenName: String) {
        startTrace("screen_load_\(screenName)", metadata: [
            "screen_name": screenName
        ])
    }
    
    /// Track search performance
    func trackSearchPerformance(query: String, resultCount: Int) {
        let traceName = "search_performance"
        startTrace(traceName, metadata: [
            "query_length": String(query.count),
            "has_filters": "false" // This could be dynamic based on actual filters
        ])
        
        // This would typically be called when search completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.addMetric("result_count", value: Int64(resultCount), traceName: traceName)
            self.stopTrace(traceName, additionalMetadata: [
                "result_count": String(resultCount)
            ])
        }
    }
    
    /// Track image load performance
    func trackImageLoad(url: String, size: CGSize) {
        let traceName = "image_load_\(url.hashValue)"
        startTrace(traceName, metadata: [
            "image_size": "\(Int(size.width))x\(Int(size.height))",
            "url_hash": String(url.hashValue)
        ])
    }
    
    /// Track Firestore operation performance
    func trackFirestoreOperation(_ operation: String, collection: String) {
        let traceName = "firestore_\(operation)_\(collection)"
        startTrace(traceName, metadata: [
            "operation": operation,
            "collection": collection
        ])
    }
    
    // MARK: - User Experience Metrics
    
    /// Track user interaction latency
    func trackUserInteraction(_ action: String, startTime: Date) {
        let latency = Date().timeIntervalSince(startTime)
        
        // Log slow interactions
        if latency > 0.5 {
            print("🐌 Slow interaction detected: \(action) took \(String(format: "%.3f", latency))s")
        }
        
        // Send to Firebase Performance
        if let trace = Performance.startTrace(name: "user_interaction") {
            trace.setValue(action, forAttribute: "action")
            trace.setValue(String(format: "%.3f", latency), forAttribute: "latency")
            trace.stop()
        } else {
            print("⚠️ Failed to start trace: user_interaction. Performance.startTrace returned nil.")
        }
    }
    
    /// Track memory usage
    func trackMemoryUsage() {
        let memoryUsage = getMemoryUsage()
        
        if let trace = Performance.startTrace(name: "memory_usage") {
            trace.setValue(Int64(memoryUsage), forMetric: "memory_mb")
            trace.stop()
        } else {
            print("⚠️ Failed to start trace: memory_usage. Performance.startTrace returned nil.")
        }
        
        // Log high memory usage
        if memoryUsage > 200 { // 200MB threshold
            print("🧠 High memory usage detected: \(memoryUsage)MB")
        }
    }
    
    // MARK: - Network Performance
    
    /// Track network request performance
    func trackNetworkRequest(url: String, method: String) -> String {
        let traceName = "network_\(method.lowercased())_\(url.hashValue)"
        startTrace(traceName, metadata: [
            "url": url,
            "method": method
        ])
        return traceName
    }
    
    /// Complete network request tracking
    func completeNetworkRequest(traceName: String, statusCode: Int, responseSize: Int) {
        stopTrace(traceName, additionalMetadata: [
            "status_code": String(statusCode),
            "response_size": String(responseSize)
        ])
    }
    
    // MARK: - Analytics and Reporting
    
    /// Get performance summary
    func getPerformanceSummary() -> [String: Any] {
        var summary: [String: Any] = [:]
        
        for (name, metric) in performanceMetrics {
            if metric.endTime != nil {
                summary[name] = [
                    "duration": metric.duration,
                    "metadata": metric.metadata
                ]
            }
        }
        
        return summary
    }
    
    /// Log performance metric for debugging
    private func logPerformanceMetric(_ metric: PerformanceMetric) {
        let duration = metric.duration
        let emoji = duration > 2.0 ? "🐌" : duration > 1.0 ? "⚠️" : "✅"
        
        print("\(emoji) Performance: \(metric.name) completed in \(String(format: "%.3f", duration))s")
        
        if !metric.metadata.isEmpty {
            print("   Metadata: \(metric.metadata)")
        }
    }
    
    // MARK: - System Metrics
    
    private func getMemoryUsage() -> Int {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Int(info.resident_size) / 1024 / 1024 // Convert to MB
        } else {
            return 0
        }
    }
}

// MARK: - SwiftUI Integration

struct PerformanceTrackingView<Content: View>: View {
    let traceName: String
    let content: Content
    
    @State private var startTime = Date()
    
    init(traceName: String, @ViewBuilder content: () -> Content) {
        self.traceName = traceName
        self.content = content()
    }
    
    var body: some View {
        content
            .onAppear {
                startTime = Date()
                PerformanceMonitoringService.shared.trackScreenLoad(traceName)
            }
            .onDisappear {
                PerformanceMonitoringService.shared.stopTrace("screen_load_\(traceName)")
            }
    }
}

extension View {
    func trackPerformance(_ traceName: String) -> some View {
        PerformanceTrackingView(traceName: traceName) {
            self
        }
    }
}
