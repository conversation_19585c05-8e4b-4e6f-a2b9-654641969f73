//
//  ChatManager.swift
//  CampusPads
//
//  Enhanced chat management service for seamless match-to-chat flow
//

import Foundation
import FirebaseFirestore
import FirebaseAuth
import Combine

/// Centralized chat management service
class ChatManager: ObservableObject {
    static let shared = ChatManager()

    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()

    @Published var activeChatID: String?
    @Published var errorMessage: String?

    private init() {}

    /// Create or get existing chat between two users
    func createOrGetChat(between userA: String, and userB: String, completion: @escaping (Result<String, Error>) -> Void) {
        print("💬 ChatManager: Creating or getting chat between \(userA) and \(userB)")

        // First, check if chat already exists
        findExistingChat(between: userA, and: userB) { [weak self] result in
            switch result {
            case .success(let existingChatID):
                print("✅ ChatManager: Found existing chat: \(existingChatID)")
                completion(.success(existingChatID))

            case .failure:
                // No existing chat found, create new one
                self?.createNewChat(between: userA, and: userB, completion: completion)
            }
        }
    }

    /// Find existing chat between two users
    private func findExistingChat(between userA: String, and userB: String, completion: @escaping (Result<String, Error>) -> Void) {
        db.collection("chats")
            .whereField("participants", arrayContains: userA)
            .getDocuments { snapshot, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let documents = snapshot?.documents else {
                    completion(.failure(NSError(domain: "ChatManager", code: 404, userInfo: [NSLocalizedDescriptionKey: "No chats found"])))
                    return
                }

                // Look for chat containing both users
                for document in documents {
                    if let participants = document.data()["participants"] as? [String],
                       participants.contains(userB) {
                        completion(.success(document.documentID))
                        return
                    }
                }

                // No existing chat found
                completion(.failure(NSError(domain: "ChatManager", code: 404, userInfo: [NSLocalizedDescriptionKey: "No existing chat found"])))
            }
    }

    /// Create new chat document
    private func createNewChat(between userA: String, and userB: String, completion: @escaping (Result<String, Error>) -> Void) {
        print("📝 ChatManager: Creating new chat between \(userA) and \(userB)")

        let chatData: [String: Any] = [
            "participants": [userA, userB].sorted(), // Sort for consistency
            "createdAt": FieldValue.serverTimestamp(),
            "lastMessageAt": FieldValue.serverTimestamp(),
            "lastMessage": "",
            "lastMessageSenderID": "",
            "unreadCount": [userA: 0, userB: 0],
            "isActive": true
        ]

        // FIXED: Properly get document reference and ID
        let chatRef = db.collection("chats").document()
        let chatID = chatRef.documentID

        chatRef.setData(chatData) { error in
            if let error = error {
                print("❌ ChatManager: Failed to create chat: \(error.localizedDescription)")
                completion(.failure(error))
            } else {
                print("✅ ChatManager: Successfully created new chat with ID: \(chatID)")
                completion(.success(chatID))
            }
        }
    }

    /// Update chat metadata when message is sent
    func updateChatMetadata(chatID: String, lastMessage: String, senderID: String, completion: @escaping (Error?) -> Void) {
        print("📊 ChatManager: Updating chat metadata for \(chatID)")

        let updateData: [String: Any] = [
            "lastMessage": lastMessage,
            "lastMessageAt": FieldValue.serverTimestamp(),
            "lastMessageSenderID": senderID
        ]

        db.collection("chats").document(chatID).updateData(updateData) { error in
            if let error = error {
                print("❌ ChatManager: Failed to update chat metadata: \(error.localizedDescription)")
            } else {
                print("✅ ChatManager: Successfully updated chat metadata")
            }
            completion(error)
        }
    }

    /// Update match status when conversation starts
    func markConversationStarted(for participants: [String], chatID: String) {
        print("💕 ChatManager: Marking conversation started for participants: \(participants)")

        let sortedParticipants = participants.sorted()

        db.collection("matches")
            .whereField("participants", isEqualTo: sortedParticipants)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ ChatManager: Error finding match to update: \(error.localizedDescription)")
                    return
                }

                guard let documents = snapshot?.documents, !documents.isEmpty else {
                    print("❌ ChatManager: No match found to update")
                    return
                }

                let matchDocument = documents.first!
                let updateData: [String: Any] = [
                    "hasConversationStarted": true,
                    "chatID": chatID,
                    "isNewMatch": false
                ]

                matchDocument.reference.updateData(updateData) { error in
                    if let error = error {
                        print("❌ ChatManager: Failed to update match status: \(error.localizedDescription)")
                    } else {
                        print("✅ ChatManager: Successfully updated match status")
                    }
                }
            }
    }

    /// Get chat ID for match participants
    func getChatID(for participants: [String], completion: @escaping (String?) -> Void) {
        let sortedParticipants = participants.sorted()

        db.collection("matches")
            .whereField("participants", isEqualTo: sortedParticipants)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ ChatManager: Error getting chat ID: \(error.localizedDescription)")
                    completion(nil)
                    return
                }

                guard let documents = snapshot?.documents, !documents.isEmpty else {
                    print("❌ ChatManager: No match found for chat ID")
                    completion(nil)
                    return
                }

                let matchData = documents.first!.data()
                let chatID = matchData["chatID"] as? String
                completion(chatID)
            }
    }
}
