//
//  MatchModel.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift

/// Model representing a mutual match between two users
struct MatchModel: Identifiable, Codable {
    @DocumentID var id: String?

    /// The two users who matched (order doesn't matter)
    let participants: [String]

    /// When the match was created (when the second user swiped right)
    let createdAt: Date

    /// Whether either user has sent a message yet
    var hasConversationStarted: Bool

    /// ID of the chat document (if conversation has started)
    var chatID: String?

    /// Whether this match is considered "new" (hasn't been viewed in dashboard yet)
    var isNewMatch: Bool

    /// Optional: Who initiated the match (the second person to swipe right)
    var initiatedBy: String?

    /// Optional: Match type (regular like or super like)
    var matchType: MatchType

    /// Optional: When the match was last viewed by each user
    var lastViewedBy: [String: Date]?

    init(
        participants: [String],
        createdAt: Date = Date(),
        hasConversationStarted: Bool = false,
        chatID: String? = nil,
        isNewMatch: Bool = true,
        initiatedBy: String? = nil,
        matchType: MatchType = .regular,
        lastViewedBy: [String: Date]? = nil
    ) {
        self.participants = participants
        self.createdAt = createdAt
        self.hasConversationStarted = hasConversationStarted
        self.chatID = chatID
        self.isNewMatch = isNewMatch
        self.initiatedBy = initiatedBy
        self.matchType = matchType
        self.lastViewedBy = lastViewedBy
    }

    /// Get the other user's ID given the current user's ID
    func getOtherUserID(currentUserID: String) -> String? {
        return participants.first { $0 != currentUserID }
    }

    /// Check if this match involves a specific user
    func involves(userID: String) -> Bool {
        return participants.contains(userID)
    }

    /// Check if the match has been viewed by a specific user
    func hasBeenViewedBy(userID: String) -> Bool {
        return lastViewedBy?[userID] != nil
    }

    /// Mark the match as viewed by a specific user
    mutating func markAsViewedBy(userID: String) {
        if lastViewedBy == nil {
            lastViewedBy = [:]
        }
        lastViewedBy?[userID] = Date()

        // If both users have viewed it, it's no longer "new"
        if lastViewedBy?.count == 2 {
            isNewMatch = false
        }
    }
}

/// Type of match based on how it was created
enum MatchType: String, Codable, CaseIterable {
    case regular = "regular"        // Both users liked each other
    case superLike = "super_like"   // At least one user super liked
    case premium = "premium"        // Premium top match like

    var displayName: String {
        switch self {
        case .regular:
            return "Match"
        case .superLike:
            return "Super Match"
        case .premium:
            return "Premium Match"
        }
    }

    var emoji: String {
        switch self {
        case .regular:
            return "💕"
        case .superLike:
            return "⭐"
        case .premium:
            return "👑"
        }
    }
}

/// Extension for Firestore operations
extension MatchModel {
    /// Convert to Firestore data dictionary
    func toFirestoreData() -> [String: Any] {
        var data: [String: Any] = [
            "participants": participants,
            "createdAt": Timestamp(date: createdAt),
            "hasConversationStarted": hasConversationStarted,
            "isNewMatch": isNewMatch,
            "matchType": matchType.rawValue
        ]

        if let chatID = chatID {
            data["chatID"] = chatID
        }

        if let initiatedBy = initiatedBy {
            data["initiatedBy"] = initiatedBy
        }

        if let lastViewedBy = lastViewedBy {
            let timestampDict = lastViewedBy.mapValues { Timestamp(date: $0) }
            data["lastViewedBy"] = timestampDict
        }

        return data
    }

    /// Create from Firestore data
    static func fromFirestoreData(_ data: [String: Any], documentID: String) -> MatchModel? {
        guard let participants = data["participants"] as? [String],
              let createdAtTimestamp = data["createdAt"] as? Timestamp else {
            return nil
        }

        let hasConversationStarted = data["hasConversationStarted"] as? Bool ?? false
        let chatID = data["chatID"] as? String
        let isNewMatch = data["isNewMatch"] as? Bool ?? true
        let initiatedBy = data["initiatedBy"] as? String
        let matchTypeRaw = data["matchType"] as? String ?? "regular"
        let matchType = MatchType(rawValue: matchTypeRaw) ?? .regular

        var lastViewedBy: [String: Date]?
        if let lastViewedData = data["lastViewedBy"] as? [String: Timestamp] {
            lastViewedBy = lastViewedData.mapValues { $0.dateValue() }
        }

        var match = MatchModel(
            participants: participants,
            createdAt: createdAtTimestamp.dateValue(),
            hasConversationStarted: hasConversationStarted,
            chatID: chatID,
            isNewMatch: isNewMatch,
            initiatedBy: initiatedBy,
            matchType: matchType,
            lastViewedBy: lastViewedBy
        )

        match.id = documentID
        return match
    }
}

/// Sample data for previews and testing
extension MatchModel {
    static let sampleMatch = MatchModel(
        participants: ["user1", "user2"],
        createdAt: Date().addingTimeInterval(-3600), // 1 hour ago
        hasConversationStarted: false,
        isNewMatch: true,
        matchType: .regular
    )

    static let sampleSuperMatch = MatchModel(
        participants: ["user1", "user3"],
        createdAt: Date().addingTimeInterval(-1800), // 30 minutes ago
        hasConversationStarted: true,
        chatID: "chat123",
        isNewMatch: false,
        matchType: .superLike
    )
}
