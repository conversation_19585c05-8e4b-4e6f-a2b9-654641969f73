import SwiftUI
import FirebaseAuth

/// NewProfileSetupOnboardingView – simplified onboarding trigger for new signups
/// This view is presented only after a user signs up and needs to complete their profile.
struct NewProfileSetupOnboardingView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var showAgeAlert: Bool = false

    var body: some View {
        Group {
            // Check if onboarding has been completed
            let completed = UserDefaults.standard.bool(forKey: "onboardingCompleted")

            if completed {
                // Onboarding already completed, dismiss immediately
                Color.clear
                    .onAppear {
                        print("🚫 NewProfileSetupOnboardingView: Onboarding already completed, dismissing")
                        presentationMode.wrappedValue.dismiss()
                    }
            } else {
                // Show enhanced onboarding flow for all new signups
                EnhancedOnboardingFlow()
                    .onAppear {
                        print("✅ NewProfileSetupOnboardingView: Onboarding not completed, showing EnhancedOnboardingFlow")
                    }
                    .alert("Age Restriction",
                           isPresented: $showAgeAlert,
                           actions: { But<PERSON>("OK", role: .cancel) { } },
                           message: { Text("You must be at least 16 years old to create an account.") }
                    )
            }
        }
        .onAppear {
            let completed = UserDefaults.standard.bool(forKey: "onboardingCompleted")
            print("🔍 NewProfileSetupOnboardingView appeared - onboardingCompleted: \(completed)")
        }
    }
}

struct NewProfileSetupOnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        NewProfileSetupOnboardingView()
            .preferredColorScheme(.dark)
    }
}
