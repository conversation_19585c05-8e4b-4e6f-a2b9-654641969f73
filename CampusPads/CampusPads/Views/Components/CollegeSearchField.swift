import SwiftUI

struct CollegeSearchField: View {
    @Binding var selectedCollege: String
    let label: String
    let placeholder: String
    
    @State private var searchQuery: String = ""
    @State private var filteredColleges: [String] = []
    @State private var showDropdown: Bool = false
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            // Label
            Text(label)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.9))
            
            // Search field
            VStack(spacing: 0) {
                TextField(placeholder, text: $searchQuery)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .disableAutocorrection(true)
                    .textInputAutocapitalization(.never)
                    .padding(AppTheme.spacing12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial.opacity(0.6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .focused($isTextFieldFocused)
                    .onChange(of: searchQuery) { _, newValue in
                        handleSearchChange(newValue)
                    }
                    .onChange(of: isTextFieldFocused) { _, focused in
                        if focused {
                            // Only show dropdown if there's actual search text and results
                            showDropdown = !searchQuery.isEmpty && !filteredColleges.isEmpty
                        } else {
                            showDropdown = false
                        }
                    }
                
                // Dropdown list (limit to 8 results for better performance)
                if showDropdown && !filteredColleges.isEmpty {
                    ScrollView {
                        LazyVStack(alignment: .leading, spacing: 4) {
                            ForEach(Array(filteredColleges.prefix(8)), id: \.self) { college in
                                Button(college) {
                                    selectCollege(college)
                                }
                                .foregroundColor(.white)
                                .padding(AppTheme.spacing8)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.ultraThinMaterial.opacity(0.4))
                                )
                            }
                        }
                    }
                    .frame(maxHeight: 200)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial.opacity(0.8))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                }
            }
            
            // Validation message
            if !searchQuery.isEmpty && !isValidSelection() {
                Text("Please select a college from the dropdown")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.red.opacity(0.8))
                    .padding(.top, 4)
            }
        }
        .onAppear {
            // Initialize search query with selected college
            if !selectedCollege.isEmpty {
                searchQuery = selectedCollege
            }
        }
        .onChange(of: selectedCollege) { _, newValue in
            // Update search query when selected college changes externally
            if newValue != searchQuery {
                searchQuery = newValue
            }
        }
    }
    
    // MARK: - Helper Functions
    
    private func handleSearchChange(_ newValue: String) {
        let trimmed = newValue.trimmingCharacters(in: .whitespaces)

        if trimmed.isEmpty {
            filteredColleges = []
            showDropdown = false
            // Clear selection if search is empty
            if !selectedCollege.isEmpty {
                selectedCollege = ""
            }
        } else if trimmed.count >= 2 { // Only search after 2+ characters for better performance
            filteredColleges = UniversityDataProvider.shared.searchUniversities(query: trimmed)
            showDropdown = !filteredColleges.isEmpty && isTextFieldFocused

            // Clear selection if current search doesn't match any valid college
            if !isValidSelection() && !selectedCollege.isEmpty {
                selectedCollege = ""
            }
        } else {
            // Less than 2 characters - hide dropdown but don't clear results yet
            showDropdown = false
        }
    }
    
    private func selectCollege(_ college: String) {
        HapticFeedbackManager.shared.generateImpact(style: .light)
        selectedCollege = college
        searchQuery = college
        filteredColleges = []
        showDropdown = false
        isTextFieldFocused = false
    }
    
    private func isValidSelection() -> Bool {
        // Check if current search query exactly matches a valid college
        let allColleges = UniversityDataProvider.shared.getAllUniversities()
        return allColleges.contains(searchQuery)
    }
}

// MARK: - Preview
struct CollegeSearchField_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()
            
            VStack {
                CollegeSearchField(
                    selectedCollege: .constant(""),
                    label: "College",
                    placeholder: "Search for your college"
                )
                .padding()
                
                Spacer()
            }
        }
    }
}
