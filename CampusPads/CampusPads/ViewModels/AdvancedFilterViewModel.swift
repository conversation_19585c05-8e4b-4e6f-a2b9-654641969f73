import SwiftUI
import Combine
import CoreLocation
import FirebaseAuth
import FirebaseFirestore
import FirebaseFirestoreCombineSwift

enum FilterMode: String, CaseIterable, Identifiable {
    case university
    var id: String { rawValue }
}

class AdvancedFilterViewModel: ObservableObject {
    // MARK: — Published Filter Inputs
    @Published var filterHousingPreference: PrimaryHousingPreference? = nil
    @Published var filterCollegeName: String = ""
    @Published var filterBudgetMin: Double? = nil
    @Published var filterBudgetMax: Double? = nil
    @Published var filterGradeGroup: String = ""
    @Published var filterInterests: String = ""
    @Published var filterRoomType: String = ""
    @Published var filterAmenities: [String] = []
    @Published var filterPetFriendly: Bool? = nil
    @Published var filterSmoker: Bool? = nil
    @Published var filterDrinker: Bool? = nil
    @Published var filterMarijuana: Bool? = nil
    @Published var filterWorkout: Bool? = nil
    @Published var filterCleanliness: Int? = nil

    // CRITICAL FIX: New "Show All Profiles" toggle to override all filter restrictions
    @Published var showAllProfiles: Bool = false
    @Published var filterSleepSchedule: String = ""
    @Published var filterMonthlyRentMin: Double? = nil
    @Published var filterMonthlyRentMax: Double? = nil
    // maxDistance REMOVED - college-only filtering now
    @Published var filterPreferredGender: String = ""
    @Published var maxAgeDifference: Double = 0.0
    @Published var filterMode: FilterMode = .university

    // MARK: — Published Results & Errors
    @Published var filteredUsers: [UserModel] = []
    @Published var errorMessage: String?


    // MARK: — Static Options (used by AdvancedFilterView)
    let propertyAmenitiesOptions = [
        "In-Unit Laundry", "On-Site Laundry", "Air Conditioning", "Heating",
        "Furnished", "Unfurnished", "High-Speed Internet", "Utilities Included",
        "Pet Friendly", "Parking Available", "Garage Parking", "Balcony / Patio",
        "Private Bathroom", "Shared Bathroom", "Gym / Fitness Center", "Common Area / Lounge",
        "Pool Access", "Rooftop Access", "Bike Storage", "Dishwasher", "Microwave",
        "Elevator Access", "Wheelchair Accessible", "24/7 Security", "Gated Community",
        "Study Rooms", "Game Room", "Smoke-Free", "Quiet Hours Enforced"
    ]
    let cleanlinessDescriptions = [
        1: "Very Messy", 2: "Messy", 3: "Average", 4: "Tidy", 5: "Very Tidy"
    ]

    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()
    private let locationManager = LocationManager()
    /// Prevents that first-merge save from stomping freshly‐loaded filters:
    private var hasLoadedInitialFilters = false

    /// Automatically load existing filters once, then re-apply & save on *any* local change.
    init() {
        // 1) load saved filters, then apply once
        loadFiltersFromUserDoc { [weak self] in
            guard let self = self else { return }
            self.applyFilters(currentLocation: self.locationManager.currentLocation)
            self.hasLoadedInitialFilters = true
        }

        // 2) react only to *filter inputs* (not filteredUsers) by merging their change-publishers:
        let inputs: [AnyPublisher<Void, Never>] = [
            $filterHousingPreference.map { _ in () }.eraseToAnyPublisher(),
            $filterCollegeName      .map { _ in () }.eraseToAnyPublisher(),
            $filterBudgetMin        .map { _ in () }.eraseToAnyPublisher(),
            $filterBudgetMax        .map { _ in () }.eraseToAnyPublisher(),
            $filterGradeGroup       .map { _ in () }.eraseToAnyPublisher(),
            $filterInterests        .map { _ in () }.eraseToAnyPublisher(),
            $filterRoomType         .map { _ in () }.eraseToAnyPublisher(),
            $filterAmenities        .map { _ in () }.eraseToAnyPublisher(),
            $filterPetFriendly      .map { _ in () }.eraseToAnyPublisher(),
            $filterSmoker           .map { _ in () }.eraseToAnyPublisher(),
            $filterDrinker          .map { _ in () }.eraseToAnyPublisher(),
            $filterMarijuana        .map { _ in () }.eraseToAnyPublisher(),
            $filterWorkout          .map { _ in () }.eraseToAnyPublisher(),
            $filterCleanliness      .map { _ in () }.eraseToAnyPublisher(),
            $filterSleepSchedule    .map { _ in () }.eraseToAnyPublisher(),
            $filterMonthlyRentMin   .map { _ in () }.eraseToAnyPublisher(),
            $filterMonthlyRentMax   .map { _ in () }.eraseToAnyPublisher(),
            // $maxDistance removed - college-only filtering now
            $filterPreferredGender  .map { _ in () }.eraseToAnyPublisher(),
            $maxAgeDifference       .map { _ in () }.eraseToAnyPublisher(),
            $filterMode             .map { _ in () }.eraseToAnyPublisher(),
            $showAllProfiles        .map { _ in () }.eraseToAnyPublisher()
        ]

        Publishers.MergeMany(inputs)
            .debounce(for: .seconds(0.2), scheduler: RunLoop.main)
            .sink { [weak self] in
                guard let self = self, self.hasLoadedInitialFilters else { return }
                let loc = self.locationManager.currentLocation
                self.applyFilters(currentLocation: loc)
                self.saveFiltersToUserDoc()
            }
            .store(in: &cancellables)
    }

    // MARK: — Public API
    /// Applies filters efficiently using Firestore queries where possible,
    /// then applies remaining filters locally for optimal performance.
    func applyFilters(currentLocation: CLLocation?) {
        // 1) grab the current user
        guard let me = ProfileViewModel.shared.userProfile else {
            self.errorMessage = "No current profile"
            return
        }

        // 2) Build efficient Firestore query based on filters
        let query = buildOptimizedQuery(for: me)

        // 3) Execute optimized query
        executeOptimizedQuery(query, currentLocation: currentLocation, currentUser: me)
    }

    /// Builds an optimized Firestore query using indexed fields
    private func buildOptimizedQuery(for currentUser: UserModel) -> Query {
        var query: Query = db.collection("users")

        // Apply most selective filters first for better performance

        // --- START OF MANDATORY FILTERS FOR PERMISSION ---
        // Ensure we only query for profiles the user is allowed to see
        query = query.whereField("isEmailVerified", isEqualTo: true)
        query = query.whereField("hideFromDiscovery", isEqualTo: false)
        // --- END OF MANDATORY FILTERS FOR PERMISSION ---

        // CRITICAL FIX: In "Show All Profiles" mode, skip all optional filters to show maximum profiles
        // Only apply filters for scoring/ranking, not exclusion
        if showAllProfiles {
            print("🔍 AdvancedFilterViewModel: 'Show All Profiles' mode - skipping optional Firestore filters for maximum profile visibility")

            // Exclude current user (mandatory for functionality)
            if let currentUserID = currentUser.id {
                query = query.whereField(FieldPath.documentID(), isNotEqualTo: currentUserID)
            }

            // Increase limit for "Show All Profiles" mode to show more profiles
            query = query.limit(to: 200)

            return query
        }

        // Apply filters based on mode
        switch filterMode {
        case .university:
            // College name filter (highly selective) - use exact match for Firestore query
            if !TextNormalizationUtility.normalize(filterCollegeName).isEmpty {
                query = query.whereField("collegeName", isEqualTo: filterCollegeName)
            }
        // Distance mode removed - only university mode now
        }

        // Housing status filter (moderately selective) - skip in "Show All Profiles" mode
        if let housingStatus = filterHousingPreference?.rawValue, !housingStatus.isEmpty {
            query = query.whereField("housingStatus", isEqualTo: housingStatus)
        }

        // Grade level filter (moderately selective) - skip in "Show All Profiles" mode
        if !TextNormalizationUtility.normalize(filterGradeGroup).isEmpty {
            query = query.whereField("gradeLevel", isEqualTo: filterGradeGroup)
        }

        // Gender filter (if specified) - skip in "Show All Profiles" mode
        if !TextNormalizationUtility.normalize(filterPreferredGender).isEmpty,
           !TextNormalizationUtility.matches(filterPreferredGender, "Any") {
            query = query.whereField("gender", isEqualTo: filterPreferredGender)
        }

        // Exclude current user and blocked users
        if let currentUserID = currentUser.id {
            query = query.whereField(FieldPath.documentID(), isNotEqualTo: currentUserID)
        }

        // Limit results for performance
        query = query.limit(to: 100)

        return query
    }

    /// Executes the optimized query and applies remaining filters locally
    private func executeOptimizedQuery(_ query: Query, currentLocation: CLLocation?, currentUser: UserModel) {
        // 3) assemble FilterSettings from all your @Published props
        let fs = FilterSettings(
            // MANDATORY FILTERS (must come first)
            housingStatus:     filterHousingPreference?.rawValue,
            collegeName:       filterCollegeName.isEmpty ? nil : filterCollegeName,
            preferredGender:   filterPreferredGender.isEmpty ? nil : filterPreferredGender,
            maxAgeDifference:  maxAgeDifference,

            // OPTIONAL FILTERS
            dormType:          nil,
            budgetMin:         (filterHousingPreference == .lookingToFindTogether ||
                                filterHousingPreference == .lookingForLease)
            ? filterBudgetMin
            : nil,
            budgetMax:         (filterHousingPreference == .lookingToFindTogether ||
                                filterHousingPreference == .lookingForLease)
            ? filterBudgetMax
            : nil,
            rentMin:           filterHousingPreference == .lookingForRoommate
            ? filterMonthlyRentMin
            : nil,
            rentMax:           filterHousingPreference == .lookingForRoommate
            ? filterMonthlyRentMax
            : nil,
            gradeGroup:        filterGradeGroup.isEmpty ? nil : filterGradeGroup,
            interests:         filterInterests.isEmpty   ? nil : filterInterests,
            maxDistance:       nil, // REMOVED: Distance filtering no longer used
            roomType:          filterRoomType.isEmpty ? nil : filterRoomType,
            amenities:         filterAmenities.isEmpty ? nil : filterAmenities,
            cleanliness:       filterCleanliness,
            sleepSchedule:     filterSleepSchedule.isEmpty
            ? nil : filterSleepSchedule,
            petFriendly:       filterPetFriendly,
            smoker:            filterSmoker,
            drinker:           filterDrinker,
            marijuana:         filterMarijuana,
            workout:           filterWorkout,
            mode:              "university", // FIXED: Always use university mode
            showAllProfiles:   showAllProfiles
        )

        // Execute optimized query — no Combine listener leak
        query.getDocuments { [weak self] snapshot, error in
            guard let self = self else { return }
            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Fetch failed: \(error.localizedDescription)"
                }
                return
            }

            // Get pre-filtered users from optimized query
            let preFilteredUsers = snapshot?.documents.compactMap {
                try? $0.data(as: UserModel.self)
            } ?? []

            // Apply remaining filters locally (distance, budget ranges, etc.)
            let finalFiltered = self.applyLocalFilters(
                to: preFilteredUsers,
                using: fs,
                currentLocation: currentLocation,
                currentUser: currentUser
            )

            DispatchQueue.main.async {
                self.filteredUsers = finalFiltered
            }
        }
    }

    /// Applies filters that can't be efficiently done in Firestore queries
    private func applyLocalFilters(
        to users: [UserModel],
        using filterSettings: FilterSettings,
        currentLocation: CLLocation?,
        currentUser: UserModel
    ) -> [UserModel] {
        var filtered = users

        // Always filter out blocked users (mandatory for safety)
        if let blockedIDs = currentUser.blockedUserIDs {
            filtered = filtered.filter { user in
                guard let userID = user.id else { return true }
                return !blockedIDs.contains(userID)
            }
        }

        // CRITICAL FIX: In "Show All Profiles" mode, skip local filtering for maximum profile visibility
        // Only apply SmartMatchingEngine for scoring/ranking, not exclusion
        if showAllProfiles {
            print("🔍 AdvancedFilterViewModel: 'Show All Profiles' mode - skipping local filters, applying SmartMatchingEngine for ranking only")

            // Use SmartMatchingEngine for ranking but don't filter out any profiles
            return SmartMatchingEngine.generateSortedMatches(
                from: filtered,
                currentUser: currentUser,
                using: filterSettings
            )
        }

        // Distance filter REMOVED - college-only filtering now

        // Budget range filters (complex range logic)
        if let minBudget = filterSettings.budgetMin {
            filtered = filtered.filter { user in
                guard let userBudgetMax = user.budgetMax else { return false }
                return userBudgetMax >= minBudget
            }
        }

        if let maxBudget = filterSettings.budgetMax {
            filtered = filtered.filter { user in
                guard let userBudgetMin = user.budgetMin else { return false }
                return userBudgetMin <= maxBudget
            }
        }

        // Use SmartMatchingEngine for final sorting and compatibility scoring
        return SmartMatchingEngine.generateSortedMatches(
            from: filtered,
            currentUser: currentUser,
            using: filterSettings
        )
    }

            /// Loads saved filters from Firestore into this view model.
           /// Calls the optional completion on the main thread when done (or on error).
    func loadFiltersFromUserDoc(completion: @escaping () -> Void = {}) {
        guard let uid = Auth.auth().currentUser?.uid else {
            self.errorMessage = "Not signed in"
            completion()
            return
        }
        db.collection("users").document(uid)
            .getDocument { [weak self] snap, err in
                DispatchQueue.main.async {
                    defer { completion() }
                    if let err = err {
                        self?.errorMessage = "Load failed: \(err.localizedDescription)"
                        return
                    }
                    guard let data = snap?.data() else { return }
                    self?.restoreFilters(from: data)
                }
            }
    }

    /// Persists the current filters back to Firestore under the user's document.
    func saveFiltersToUserDoc() {
        guard let uid = Auth.auth().currentUser?.uid else { return }

        // Build a single FilterSettings struct, applying your mode‑specific logic
        let fs = FilterSettings(
            // MANDATORY FILTERS (must come first)
            housingStatus:  filterHousingPreference?.rawValue,
            collegeName:    filterCollegeName.isEmpty ? nil : filterCollegeName,
            preferredGender: filterPreferredGender.isEmpty ? nil : filterPreferredGender,
            maxAgeDifference: maxAgeDifference,

            // OPTIONAL FILTERS
            dormType:       nil,
            budgetMin:      (filterHousingPreference == .lookingToFindTogether ||
                             filterHousingPreference == .lookingForLease)
            ? filterBudgetMin : nil,
            budgetMax:      (filterHousingPreference == .lookingToFindTogether ||
                             filterHousingPreference == .lookingForLease)
            ? filterBudgetMax : nil,
            rentMin:        filterHousingPreference == .lookingForRoommate
            ? filterMonthlyRentMin : nil,
            rentMax:        filterHousingPreference == .lookingForRoommate
            ? filterMonthlyRentMax : nil,
            gradeGroup:     filterGradeGroup.isEmpty ? nil : filterGradeGroup,
            interests:      filterInterests.isEmpty   ? nil : filterInterests,
            maxDistance:    nil, // REMOVED: Distance filtering no longer used
            roomType:       filterRoomType.isEmpty ? nil : filterRoomType,
            amenities:      filterAmenities.isEmpty ? nil : filterAmenities,
            cleanliness:    filterCleanliness,
            sleepSchedule:  filterSleepSchedule.isEmpty ? nil : filterSleepSchedule,
            petFriendly:    filterPetFriendly,
            smoker:         filterSmoker,
            drinker:        filterDrinker,
            marijuana:      filterMarijuana,
            workout:        filterWorkout,
            mode:           "university", // FIXED: Always use university mode
            showAllProfiles: showAllProfiles
        )

        // Enhanced logging for "Show All Profiles" mode
        if showAllProfiles {
            print("🎯 AdvancedFilterViewModel: Saving 'Show All Profiles' filter settings")
            print("   - Mode: \(fs.mode ?? "none")")
            print("   - Show All Profiles: \(fs.showAllProfiles ?? false)")
            print("   - This should ensure maximum profile visibility in discovery")
        }

        // Encode & write just that one field
        do {
            let fsData = try Firestore.Encoder().encode(fs)
            // ✅ merge ensures your filterSettings is replaced, not partially updated
            db.collection("users").document(uid)
                .setData(["filterSettings": fsData], merge: true) { err in
                    if let err = err {
                        self.errorMessage = "Save filters failed: \(err)"
                    }
                }
        } catch {
            self.errorMessage = "Save filters failed: \(error)"
        }
    }

    /// Restore filter values from Firestore data dictionary.
    private func restoreFilters(from data: [String:Any]) {
        if let fsDict = data["filterSettings"] as? [String:Any],
           let fs = try? Firestore.Decoder().decode(FilterSettings.self, from: fsDict) {
            // MANDATORY FILTERS
            filterHousingPreference = fs.housingStatus.flatMap(PrimaryHousingPreference.init)
            filterCollegeName       = fs.collegeName ?? ""
            filterPreferredGender   = fs.preferredGender ?? ""
            maxAgeDifference        = fs.maxAgeDifference ?? 0.0

            // OPTIONAL FILTERS
            filterBudgetMin         = fs.budgetMin
            filterBudgetMax         = fs.budgetMax
            filterMonthlyRentMin    = fs.rentMin
            filterMonthlyRentMax    = fs.rentMax
            filterGradeGroup        = fs.gradeGroup ?? ""
            filterInterests         = fs.interests ?? ""
            filterMode              = fs.mode.flatMap(FilterMode.init) ?? .university
            filterRoomType          = fs.roomType ?? ""
            filterAmenities         = fs.amenities ?? []
            filterCleanliness       = fs.cleanliness
            filterSleepSchedule     = fs.sleepSchedule ?? ""
            filterPetFriendly       = fs.petFriendly
            filterSmoker            = fs.smoker
            filterDrinker           = fs.drinker
            filterMarijuana         = fs.marijuana
            filterWorkout           = fs.workout
            showAllProfiles         = fs.showAllProfiles ?? false
            return
        }

        // NEW: Auto-match college from user's profile if no filters exist
        autoMatchUserCollege()
    }

    /// Auto-match the college filter to the user's profile college
    private func autoMatchUserCollege() {
        guard let userProfile = ProfileViewModel.shared.userProfile,
              let userCollege = userProfile.collegeName,
              !userCollege.isEmpty else {
            print("🎯 AdvancedFilterViewModel: No user college to auto-match")
            return
        }

        // Auto-set the college filter to match user's profile
        filterCollegeName = userCollege
        print("🎯 AdvancedFilterViewModel: Auto-matched college filter to user's profile: \(userCollege)")

        // Save the auto-matched filter
        saveFiltersToUserDoc()
    }
}
