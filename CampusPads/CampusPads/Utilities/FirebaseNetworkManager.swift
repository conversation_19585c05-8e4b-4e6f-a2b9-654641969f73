import Foundation
import FirebaseStorage
import FirebaseFirestore

/// Enhanced Firebase network manager with robust error handling and retry logic
class FirebaseNetworkManager: ObservableObject {
    static let shared = FirebaseNetworkManager()

    private let storage = Storage.storage()
    private let db = Firestore.firestore()

    // Network monitoring
    @Published var isNetworkAvailable = true
    @Published var networkQuality: NetworkQuality = .good

    // CRITICAL FIX: Proper timer management to prevent memory leaks
    private var networkMonitorTimer: Timer?
    private var isConfigured = false

    enum NetworkQuality {
        case excellent, good, poor, offline
    }

    private init() {
        configureFirebaseSettings()
        startNetworkMonitoring()
    }

    deinit {
        // CRITICAL FIX: Clean up timer to prevent memory leaks
        stopNetworkMonitoring()
    }

    private func configureFirebaseSettings() {
        // OPTIMIZATION: Only configure once to prevent redundant operations
        guard !isConfigured else { return }

        // CRITICAL FIX: Enhanced Firestore configuration to address NSURLErrorDomain issues
        let settings = FirestoreSettings()
        settings.cacheSettings = PersistentCacheSettings(sizeBytes: NSNumber(value: 100 * 1024 * 1024))

        // NETWORK RESILIENCE: Configure SSL and connection settings
        settings.isSSLEnabled = true

        // CRITICAL FIX: Configure host settings if available
        if let host = Bundle.main.object(forInfoDictionaryKey: "FIRESTORE_HOST") as? String {
            settings.host = host
        }

        db.settings = settings

        // CRITICAL FIX: Enhanced Storage configuration for network resilience
        storage.maxDownloadRetryTime = 60.0 // Increased for better reliability
        storage.maxUploadRetryTime = 60.0
        storage.maxOperationRetryTime = 60.0

        // NETWORK OPTIMIZATION: Configure URLSession for better connectivity
        configureURLSessionDefaults()

        isConfigured = true
        print("🔧 FirebaseNetworkManager: Enhanced Firebase settings configured with network resilience")
    }

    /// Configure URLSession defaults to handle NSURLErrorDomain issues and QUIC protocol violations
    private func configureURLSessionDefaults() {
        // CRITICAL FIX: Configure URLSession to prevent QUIC/HTTP3 protocol violations
        let config = URLSessionConfiguration.default

        // CRITICAL FIX: Force HTTP/2 and disable HTTP/3 to prevent QUIC protocol violations
        config.httpMaximumConnectionsPerHost = 4
        config.httpShouldUsePipelining = false

        // NETWORK RESILIENCE: Configure conservative timeouts
        config.timeoutIntervalForRequest = 15.0  // Reduced from 30s
        config.timeoutIntervalForResource = 60.0 // Reduced from 120s

        // CRITICAL FIX: Configure connection settings to prevent protocol violations
        config.waitsForConnectivity = true
        config.allowsCellularAccess = true
        config.allowsExpensiveNetworkAccess = true
        config.allowsConstrainedNetworkAccess = true

        // CRITICAL FIX: Disable HTTP/3 and QUIC by using conservative connection settings
        // Force HTTP/2 or HTTP/1.1 through connection management and headers

        // CRITICAL FIX: Configure HTTP headers to prevent parsing issues and force HTTP/2
        config.httpAdditionalHeaders = [
            "Accept": "application/json, image/*, */*",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "User-Agent": "CampusPads/1.0 (iOS)",
            "Cache-Control": "max-age=300", // 5 minute cache
            "Upgrade-Insecure-Requests": "1"
        ]

        // OPTIMIZATION: Enhanced caching with conservative settings
        let cache = URLCache(
            memoryCapacity: 30 * 1024 * 1024,  // Reduced to 30MB memory
            diskCapacity: 100 * 1024 * 1024,   // Reduced to 100MB disk
            diskPath: "firebase_network_cache"
        )
        config.urlCache = cache
        config.requestCachePolicy = .returnCacheDataElseLoad

        // CRITICAL FIX: Configure network service type for better reliability
        config.networkServiceType = .default
        config.allowsCellularAccess = true

        print("✅ FirebaseNetworkManager: URLSession configured with HTTP/2 enforcement and QUIC disabled")
    }

    private func startNetworkMonitoring() {
        // CRITICAL FIX: Proper timer management with weak self to prevent memory leaks
        stopNetworkMonitoring() // Clean up any existing timer

        networkMonitorTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            self?.assessNetworkQuality()
        }

        // Initial assessment
        assessNetworkQuality()
    }

    private func stopNetworkMonitoring() {
        networkMonitorTimer?.invalidate()
        networkMonitorTimer = nil
    }

    private func assessNetworkQuality() {
        // OPTIMIZATION: Use lightweight network check without Firestore queries
        guard NetworkMonitor.shared.isConnected else {
            DispatchQueue.main.async { [weak self] in
                self?.networkQuality = .offline
                self?.isNetworkAvailable = false
            }
            return
        }

        // Use simple URL request instead of Firestore to avoid permission issues
        guard let url = URL(string: "https://www.google.com") else {
            DispatchQueue.main.async { [weak self] in
                self?.networkQuality = .poor
                self?.isNetworkAvailable = false
            }
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "HEAD"
        request.timeoutInterval = 10.0

        URLSession.shared.dataTask(with: request) { [weak self] _, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("⚠️ FirebaseNetworkManager: Network quality check failed: \(error.localizedDescription)")
                    self?.networkQuality = .poor
                    self?.isNetworkAvailable = false
                } else if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 {
                    self?.networkQuality = .good
                    self?.isNetworkAvailable = true
                } else {
                    self?.networkQuality = .poor
                    self?.isNetworkAvailable = false
                }
            }
        }.resume()
    }

    /// Simple image download
    func downloadImage(from url: String, completion: @escaping (Result<Data, Error>) -> Void) {
        guard let storageRef = getStorageReference(from: url) else {
            completion(.failure(NetworkError.invalidURL))
            return
        }

        storageRef.getData(maxSize: 10 * 1024 * 1024) { data, error in
            if let error = error {
                print("❌ FirebaseNetworkManager: Download failed: \(error.localizedDescription)")
                completion(.failure(error))
            } else if let data = data {
                print("✅ FirebaseNetworkManager: Downloaded image (\(data.count) bytes)")
                completion(.success(data))
            } else {
                completion(.failure(NetworkError.noData))
            }
        }
    }

    private func getStorageReference(from urlString: String) -> StorageReference? {
        // Handle both full URLs and storage paths
        if urlString.hasPrefix("gs://") {
            return storage.reference(forURL: urlString)
        } else if urlString.hasPrefix("https://firebasestorage.googleapis.com") {
            return storage.reference(forURL: urlString)
        } else {
            // Assume it's a path
            return storage.reference().child(urlString)
        }
    }

    /// Simple Firestore query
    func performQuery<T: Codable>(
        collection: String,
        type: T.Type,
        filters: [QueryFilter] = [],
        completion: @escaping (Result<[T], Error>) -> Void
    ) {
        var query: Query = db.collection(collection)

        // Apply filters
        for filter in filters {
            query = query.whereField(filter.field, isEqualTo: filter.value)
        }

        query.getDocuments { snapshot, error in
            if let error = error {
                print("❌ FirebaseNetworkManager: Query failed: \(error.localizedDescription)")
                completion(.failure(error))
            } else if let snapshot = snapshot {
                let items = snapshot.documents.compactMap { doc in
                    try? doc.data(as: type)
                }
                completion(.success(items))
            } else {
                completion(.failure(NetworkError.noData))
            }
        }
    }
}

// MARK: - Supporting Types

struct QueryFilter {
    let field: String
    let value: Any
}

enum NetworkError: LocalizedError {
    case invalidURL
    case noData
    case timeout
    case parseError

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL provided"
        case .noData:
            return "No data received"
        case .timeout:
            return "Request timed out"
        case .parseError:
            return "Failed to parse response"
        }
    }
}

// MARK: - Network Quality Extensions

extension FirebaseNetworkManager.NetworkQuality {
    var description: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .poor: return "Poor"
        case .offline: return "Offline"
        }
    }

    var color: String {
        switch self {
        case .excellent: return "green"
        case .good: return "blue"
        case .poor: return "orange"
        case .offline: return "red"
        }
    }
}
