//
//  MainAppView.swift
//  CampusPads
//
//  Main app wrapper that handles splash screen and app initialization
//

import SwiftUI
import FirebaseAuth

struct MainAppView: View {
    @State private var showSplash = true
    @State private var isUserAuthenticated = false
    @State private var isCheckingAuth = true

    var body: some View {
        ZStack {
            if showSplash {
                // Beautiful splash screen
                SplashView {
                    withAnimation(.easeOut(duration: 0.5)) {
                        showSplash = false
                    }
                }
                .transition(.opacity)
            } else {
                // Main app content
                mainAppContent
                    .transition(.opacity)
            }
        }
        .onAppear {
            checkAuthenticationStatus()
        }
    }

    // MARK: - Main App Content
    @ViewBuilder
    private var mainAppContent: some View {
        if isCheckingAuth {
            // Loading state while checking authentication
            loadingView
        } else if isUserAuthenticated {
            // Authenticated user - show main app
            MainTabView()
        } else {
            // Not authenticated - show onboarding/login
            OnboardingView()
        }
    }

    // MARK: - Loading View
    private var loadingView: some View {
        ZStack {
            AnimatedBackground()

            VStack(spacing: 20) {
                AppIcon.medium()

                Text("Initializing...")
                    .font(AppTheme.headline)
                    .foregroundColor(AppTheme.textSecondary)

                ProgressView()
                    .tint(AppTheme.primaryColor)
            }
        }
    }

    // MARK: - Authentication Check
    private func checkAuthenticationStatus() {
        // Simulate some initialization time for better UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if let _ = Auth.auth().currentUser {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isUserAuthenticated = true
                    isCheckingAuth = false
                }
            } else {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isUserAuthenticated = false
                    isCheckingAuth = false
                }
            }
        }
    }
}

// MARK: - Modern Onboarding View
struct OnboardingView: View {
    @State private var showAuthView = false

    var body: some View {
        NavigationView {
            ZStack {
                // Stunning animated background
                OnboardingAnimatedBackground()

                VStack(spacing: 0) {
                    Spacer()

                    // Modern hero section
                    VStack(spacing: 32) {
                        // App branding - no floating icon
                        VStack(spacing: 16) {
                            Text(AppConfiguration.App.name)
                                .font(.custom("AvenirNext-Bold", size: 42))
                                .foregroundStyle(AppTheme.sexyGradient)
                                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 10, x: 0, y: 5)

                            Text("Find Your Perfect Roommate")
                                .font(.custom("AvenirNext-Medium", size: 18))
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        // Modern feature highlights
                        VStack(spacing: 20) {
                            Text("Welcome to the Future of\nRoommate Matching")
                                .font(.custom("AvenirNext-Bold", size: 26))
                                .foregroundColor(AppTheme.textPrimary)
                                .multilineTextAlignment(.center)
                                .lineSpacing(4)

                            VStack(spacing: 12) {
                                WelcomeFeature(
                                    icon: "heart.fill",
                                    text: "Smart matching algorithm",
                                    color: AppTheme.accentColor
                                )

                                WelcomeFeature(
                                    icon: "location.fill",
                                    text: "Find roommates near your college",
                                    color: AppTheme.primaryColor
                                )

                                WelcomeFeature(
                                    icon: "shield.fill",
                                    text: "Verified email addresses for safety",
                                    color: AppTheme.successColor
                                )
                            }
                        }
                    }

                    Spacer()

                    // Modern CTA section
                    VStack(spacing: 20) {
                        // Primary CTA
                        Button(action: {
                            showAuthView = true
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: "sparkles")
                                    .font(.system(size: 18, weight: .semibold))
                                Text("Get Started")
                                    .font(.custom("AvenirNext-Semibold", size: 18))
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 18)
                            .background(AppTheme.sexyGradient)
                            .cornerRadius(16)
                            .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 15, x: 0, y: 8)
                        }
                        .scaleEffect(1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: showAuthView)

                        // Secondary CTA
                        Button(action: {
                            showAuthView = true
                        }) {
                            Text("Already have an account? Sign In")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundStyle(AppTheme.sexyGradient)
                        }
                    }
                    .padding(.horizontal, 32)
                    .padding(.bottom, 50)
                }
                .padding(.horizontal, 24)
            }
            .navigationBarHidden(true)
            .fullScreenCover(isPresented: $showAuthView) {
                AuthenticationView()
            }
        }
    }
}

// MARK: - Welcome Feature Component
struct WelcomeFeature: View {
    let icon: String
    let text: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 24, height: 24)

            Text(text)
                .font(.custom("AvenirNext-Medium", size: 15))
                .foregroundColor(AppTheme.textSecondary)

            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Main Tab View (Your Real App)
struct MainTabView: View {
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Discover/Swipe Tab
            SwipeView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 0 ? "heart.fill" : "heart")
                            .font(.system(size: 20))
                        Text("Discover")
                            .font(.caption)
                    }
                }
                .tag(0)
                .accessibilityLabel("Discover")
                .accessibilityHint("Find and swipe on potential roommates")

            // Messages Tab
            CombinedMatchesChatView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 1 ? "message.fill" : "message")
                            .font(.system(size: 20))
                        Text("Messages")
                            .font(.caption)
                    }
                }
                .tag(1)
                .accessibilityLabel("Messages")
                .accessibilityHint("View your matches and chat conversations")

            // Profile Tab
            ProfileView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 2 ? "person.fill" : "person")
                            .font(.system(size: 20))
                        Text("Profile")
                            .font(.caption)
                    }
                }
                .tag(2)
                .accessibilityLabel("Profile")
                .accessibilityHint("View and edit your profile information")
        }
        .accentColor(AppTheme.primaryColor)
        .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
    }
}

// MARK: - Placeholder Views
struct SwipeView: View {
    var body: some View {
        ZStack {
            AnimatedBackground()

            VStack {
                Text("Swipe View")
                    .font(AppTheme.title1)
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Coming Soon")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
    }
}

struct ProfileView: View {
    var body: some View {
        ZStack {
            AnimatedBackground()

            VStack {
                AppIcon.medium()

                Text("Profile View")
                    .font(AppTheme.title1)
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Customize your profile")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
    }
}

// MARK: - Preview
struct MainAppView_Previews: PreviewProvider {
    static var previews: some View {
        MainAppView()
    }
}
