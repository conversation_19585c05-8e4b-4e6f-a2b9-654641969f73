//
//  AnimatedBackground.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

/// Stunning animated background with floating particles and dynamic gradients
struct AnimatedBackground: View {
    @State private var animateGradient = false
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        ZStack {
            // Dynamic gradient background
            dynamicGradientBackground

            // Removed floating particles to eliminate bouncing balls

            // Subtle overlay for depth
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.black.opacity(colorScheme == .dark ? 0.1 : 0.02)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
        .onAppear {
            startGradientAnimation()
        }
        .ignoresSafeArea()
    }

    private var dynamicGradientBackground: some View {
        ZStack {
            // Base gradient that adapts to light/dark mode
            AppTheme.dynamicBackgroundGradient

            // Animated overlay gradient with mode-aware opacity
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.2, green: 0.6, blue: 1.0).opacity(colorScheme == .dark ? 0.15 : 0.08),
                    Color(red: 1.0, green: 0.3, blue: 0.6).opacity(colorScheme == .dark ? 0.08 : 0.04),
                    Color(red: 0.6, green: 0.3, blue: 1.0).opacity(colorScheme == .dark ? 0.12 : 0.06)
                ]),
                startPoint: animateGradient ? .topLeading : .bottomTrailing,
                endPoint: animateGradient ? .bottomTrailing : .topLeading
            )
            .animation(
                .easeInOut(duration: 8.0)
                .repeatForever(autoreverses: true),
                value: animateGradient
            )

            // Additional depth layer for light mode
            if colorScheme == .light {
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.white.opacity(0.3),
                        Color.clear,
                        Color.white.opacity(0.1)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            }
        }
    }



    private func startGradientAnimation() {
        animateGradient = true
    }
}



// MARK: - Sexy Background Modifier
struct SexyBackgroundModifier: ViewModifier {
    func body(content: Content) -> some View {
        ZStack {
            AnimatedBackground()
            content
        }
    }
}

extension View {
    func sexyBackground() -> some View {
        modifier(SexyBackgroundModifier())
    }
}

// MARK: - Gradient Mesh Background (iOS 17+ style)
struct GradientMeshBackground: View {
    @State private var animateColors = false
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        ZStack {
            // Base mesh gradient
            MeshGradient(
                width: 3,
                height: 3,
                points: meshPoints,
                colors: meshColors
            )
            .animation(
                .easeInOut(duration: 6.0)
                .repeatForever(autoreverses: true),
                value: animateColors
            )

            // Overlay for depth
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.black.opacity(colorScheme == .dark ? 0.2 : 0.05)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
        .onAppear {
            animateColors = true
        }
        .ignoresSafeArea()
    }

    private var meshPoints: [SIMD2<Float>] {
        [
            [0.0, 0.0], [0.5, 0.0], [1.0, 0.0],
            [0.0, 0.5], [0.5, 0.5], [1.0, 0.5],
            [0.0, 1.0], [0.5, 1.0], [1.0, 1.0]
        ]
    }

    private var meshColors: [Color] {
        if colorScheme == .dark {
            return [
                Color(red: 0.02, green: 0.05, blue: 0.12),
                Color(red: 0.05, green: 0.02, blue: 0.08),
                Color(red: 0.08, green: 0.02, blue: 0.05),
                Color(red: 0.03, green: 0.06, blue: 0.10),
                Color(red: 0.06, green: 0.03, blue: 0.09),
                Color(red: 0.09, green: 0.03, blue: 0.06),
                Color(red: 0.04, green: 0.07, blue: 0.11),
                Color(red: 0.07, green: 0.04, blue: 0.10),
                Color(red: 0.10, green: 0.04, blue: 0.07)
            ]
        } else {
            return [
                Color(red: 0.98, green: 0.98, blue: 1.0),
                Color(red: 0.95, green: 0.97, blue: 1.0),
                Color(red: 0.97, green: 0.95, blue: 1.0),
                Color(red: 0.96, green: 0.98, blue: 0.99),
                Color(red: 0.94, green: 0.96, blue: 0.99),
                Color(red: 0.96, green: 0.94, blue: 0.99),
                Color(red: 0.97, green: 0.99, blue: 0.98),
                Color(red: 0.95, green: 0.97, blue: 0.98),
                Color(red: 0.97, green: 0.95, blue: 0.98)
            ]
        }
    }
}

// Fallback for iOS versions that don't support MeshGradient
struct MeshGradient: View {
    let width: Int
    let height: Int
    let points: [SIMD2<Float>]
    let colors: [Color]

    var body: some View {
        // Fallback to regular gradient for older iOS versions
        LinearGradient(
            gradient: Gradient(colors: Array(colors.prefix(3))),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}
