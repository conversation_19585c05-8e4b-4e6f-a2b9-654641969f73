// CampusPads Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initScrollEffects();
    initAnimations();
    initInteractiveElements();
    initAppShowcase();
});

// Navigation functionality
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.9)';
            navbar.style.backdropFilter = 'blur(20px)';
        }
    });

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for navbar height
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }

            // Close mobile menu if open
            if (navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            }
        });
    });
}

// Scroll-triggered animations
function initScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll(
        '.feature-card, .safety-feature, .testimonial-card, .step, .hero-content, .hero-visual'
    );
    
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// Initialize animations
function initAnimations() {
    // Add CSS for scroll animations
    const style = document.createElement('style');
    style.textContent = `
        .feature-card,
        .safety-feature,
        .testimonial-card,
        .step,
        .hero-content,
        .hero-visual {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .animate-in {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        .step:nth-child(even) {
            transform: translateY(30px) translateX(-30px);
        }

        .step:nth-child(even).animate-in {
            transform: translateY(0) translateX(0) !important;
        }

        .hero-content {
            transition-delay: 0.2s;
        }

        .hero-visual {
            transition-delay: 0.4s;
        }

        .feature-card:nth-child(1) { transition-delay: 0.1s; }
        .feature-card:nth-child(2) { transition-delay: 0.2s; }
        .feature-card:nth-child(3) { transition-delay: 0.3s; }
        .feature-card:nth-child(4) { transition-delay: 0.4s; }
        .feature-card:nth-child(5) { transition-delay: 0.5s; }
        .feature-card:nth-child(6) { transition-delay: 0.6s; }

        .testimonial-card:nth-child(1) { transition-delay: 0.1s; }
        .testimonial-card:nth-child(2) { transition-delay: 0.3s; }
        .testimonial-card:nth-child(3) { transition-delay: 0.5s; }
    `;
    document.head.appendChild(style);

    // Animate hero stats counter
    animateCounters();
}

// Counter animation for hero stats
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    
    counters.forEach(counter => {
        const target = counter.textContent;
        const isPercentage = target.includes('%');
        const isPlus = target.includes('+');
        const numericValue = parseInt(target.replace(/[^\d]/g, ''));
        
        let current = 0;
        const increment = numericValue / 50; // Animation duration control
        
        const updateCounter = () => {
            if (current < numericValue) {
                current += increment;
                let displayValue = Math.floor(current);
                
                if (isPercentage) {
                    counter.textContent = displayValue + '%';
                } else if (isPlus) {
                    if (displayValue >= 1000) {
                        displayValue = (displayValue / 1000).toFixed(0) + 'K';
                    }
                    counter.textContent = displayValue + '+';
                } else {
                    counter.textContent = displayValue;
                }
                
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target; // Ensure final value is exact
            }
        };
        
        // Start animation when hero section is visible
        const heroObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(updateCounter, 1000); // Delay for effect
                    heroObserver.unobserve(entry.target);
                }
            });
        });
        
        heroObserver.observe(counter.closest('.hero'));
    });
}

// Interactive elements
function initInteractiveElements() {
    // Swipe card interaction in hero
    const swipeCard = document.querySelector('.swipe-card');
    const swipeButtons = document.querySelectorAll('.swipe-btn');
    
    if (swipeCard && swipeButtons.length > 0) {
        swipeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // Add swipe animation
                const isLike = btn.classList.contains('like') || btn.classList.contains('super-like');
                const direction = isLike ? 'right' : 'left';
                
                swipeCard.style.transform = `translateX(${direction === 'right' ? '100%' : '-100%'}) rotate(${direction === 'right' ? '15deg' : '-15deg'})`;
                swipeCard.style.opacity = '0';
                
                // Reset card after animation
                setTimeout(() => {
                    swipeCard.style.transform = 'translateX(0) rotate(0)';
                    swipeCard.style.opacity = '1';
                    swipeCard.style.transition = 'none';
                    
                    // Re-enable transition
                    setTimeout(() => {
                        swipeCard.style.transition = 'all 0.3s ease';
                    }, 50);
                }, 600);
            });
        });
    }

    // Phone mockup hover effects
    const phones = document.querySelectorAll('.phone');
    phones.forEach((phone, index) => {
        phone.addEventListener('mouseenter', () => {
            phone.style.transform = `translateY(-${10 + index * 5}px) scale(1.05)`;
        });
        
        phone.addEventListener('mouseleave', () => {
            const baseTransform = index === 1 ? 'translateY(20px)' : index === 2 ? 'translateY(40px)' : '';
            phone.style.transform = baseTransform;
        });
    });

    // Floating cards animation
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach((card, index) => {
        // Add random movement
        setInterval(() => {
            const randomX = (Math.random() - 0.5) * 20;
            const randomY = (Math.random() - 0.5) * 20;
            card.style.transform = `translate(${randomX}px, ${randomY}px)`;
        }, 3000 + index * 1000);
    });

    // Feature card hover effects
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const icon = card.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            const icon = card.querySelector('.feature-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Testimonial card interactions
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    testimonialCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const avatar = card.querySelector('.author-avatar');
            if (avatar) {
                avatar.style.transform = 'scale(1.1)';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            const avatar = card.querySelector('.author-avatar');
            if (avatar) {
                avatar.style.transform = 'scale(1)';
            }
        });
    });
}

// Utility function for smooth scrolling
function smoothScrollTo(target, duration = 1000) {
    const targetElement = document.querySelector(target);
    if (!targetElement) return;

    const targetPosition = targetElement.offsetTop - 70;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }

    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    requestAnimationFrame(animation);
}

// Add parallax effect to gradient orbs
function initParallax() {
    const orbs = document.querySelectorAll('.gradient-orb');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        orbs.forEach((orb, index) => {
            const speed = (index + 1) * 0.3;
            orb.style.transform = `translateY(${rate * speed}px)`;
        });
    });
}

// Initialize parallax effect
initParallax();

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    
    // Add loaded class styles
    const loadedStyle = document.createElement('style');
    loadedStyle.textContent = `
        body {
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        body.loaded {
            opacity: 1;
        }
    `;
    document.head.appendChild(loadedStyle);
});

// App Showcase Interactive Flow
function initAppShowcase() {
    const flowSteps = document.querySelectorAll('.flow-step');
    const appScreenshots = document.querySelectorAll('.app-screenshot');

    if (flowSteps.length === 0 || appScreenshots.length === 0) return;

    // Auto-cycle through screens
    let currentScreen = 0;
    const screens = ['deck', 'home', 'chat', 'match'];

    function showScreen(screenIndex) {
        // Update flow indicators
        flowSteps.forEach((step, index) => {
            step.classList.toggle('active', index === screenIndex);
        });

        // Update screenshots
        appScreenshots.forEach(screenshot => {
            screenshot.classList.remove('active');
        });

        const targetScreen = document.querySelector(`.app-screenshot.${screens[screenIndex]}-screen`);
        if (targetScreen) {
            targetScreen.classList.add('active');
        }
    }

    // Auto-cycle every 4 seconds
    setInterval(() => {
        currentScreen = (currentScreen + 1) % screens.length;
        showScreen(currentScreen);
    }, 4000);

    // Manual control
    flowSteps.forEach((step, index) => {
        step.addEventListener('click', () => {
            currentScreen = index;
            showScreen(currentScreen);
        });
    });

    // Initialize first screen
    showScreen(0);
}

// Performance optimization: Throttle scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(() => {
    // Any scroll-based animations can be added here
}, 16); // ~60fps

window.addEventListener('scroll', throttledScrollHandler);
