//
//  TermsOfServiceView.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

struct TermsOfServiceView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                    // Header
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("Terms of Service")
                            .font(.custom("AvenirNext-Bold", size: 28))
                            .foregroundStyle(AppTheme.sexyGradient)
                        
                        Text("Effective Date: January 2025")
                            .font(.custom("AvenirNext-Medium", size: 14))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                    .padding(.bottom, AppTheme.spacing16)
                    
                    // Acceptance of Terms
                    PolicySection(
                        title: "1. Acceptance of Terms",
                        content: "By downloading, accessing, or using the CampusPads mobile application, you agree to be bound by these Terms of Service. If you do not agree to these Terms, do not use the App."
                    )
                    
                    // Description of Service
                    PolicySection(
                        title: "2. Description of Service",
                        content: """
                        CampusPads is a platform that helps college students find compatible roommates and housing options. Our services include:
                        
                        • Profile creation and matching algorithms
                        • Messaging between matched users
                        • Housing and roommate preference filtering
                        • Safety and verification features
                        """
                    )
                    
                    // Eligibility
                    PolicySection(
                        title: "3. Eligibility",
                        content: """
                        **Age Requirement:** You must be at least 18 years old to use CampusPads.
                        
                        **Student Status:** Our service is designed for college and university students.
                        
                        **Account Accuracy:** You must provide accurate, current, and complete information during registration and keep your account information updated.
                        """
                    )
                    
                    // User Conduct
                    PolicySection(
                        title: "4. User Conduct",
                        content: """
                        **You agree NOT to:**
                        • Create fake profiles or impersonate others
                        • Harass, abuse, or harm other users
                        • Share inappropriate, offensive, or illegal content
                        • Use the service for commercial purposes without permission
                        • Attempt to access other users' accounts
                        • Spam or send unsolicited messages
                        
                        **Content Standards:**
                        All content you share must be truthful, appropriate, compliant with laws, and respectful of others.
                        
                        **Safety Guidelines:**
                        • Meet potential roommates in public places
                        • Verify housing information independently
                        • Report suspicious behavior
                        • Trust your instincts about safety
                        """
                    )
                    
                    // Privacy and Data
                    PolicySection(
                        title: "5. Privacy and Data",
                        content: """
                        • Your privacy is governed by our Privacy Policy
                        • Profile information is visible to other users based on preferences
                        • Messages between matched users are private
                        • We may review communications for safety purposes
                        """
                    )
                    
                    // Premium Features
                    PolicySection(
                        title: "6. Premium Features",
                        content: """
                        **Paid Services:** Some features may require payment with clear pricing.
                        
                        **Billing:** Payments processed through app store platforms.
                        
                        **Cancellation:** Cancel subscriptions through app store settings.
                        
                        Premium features are non-transferable and subject to app store refund policies.
                        """
                    )
                    
                    // Disclaimers
                    PolicySection(
                        title: "7. Disclaimers and Limitations",
                        content: """
                        **Service Availability:** We strive for reliable service but cannot guarantee uninterrupted access.
                        
                        **User Interactions:** We are not responsible for:
                        • The accuracy of user profiles
                        • Interactions between users
                        • Housing arrangements made through the platform
                        • Disputes between roommates
                        
                        **Limitation of Liability:** Our liability is limited to the maximum extent permitted by law.
                        """
                    )
                    
                    // Termination
                    PolicySection(
                        title: "8. Termination",
                        content: """
                        **By You:** Delete your account anytime through app settings.
                        
                        **By Us:** We may suspend accounts for Terms violations, illegal activity, or technical reasons.
                        
                        **Effect:** Upon termination, access ends and your profile becomes invisible to other users.
                        """
                    )
                    
                    // Contact Information
                    PolicySection(
                        title: "9. Contact Us",
                        content: """
                        For questions about these Terms:
                        
                        • Email: <EMAIL>
                        • Support: Use "Report a Problem" in the app
                        • We respond to legal inquiries within 48 hours
                        """
                    )
                    
                    // Footer
                    VStack(spacing: AppTheme.spacing8) {
                        Text("Last Updated: January 2025")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(AppTheme.textSecondary)
                        
                        Text("By using CampusPads, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, AppTheme.spacing24)
                }
                .padding(AppTheme.spacing20)
            }
            .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
            .navigationBarHidden(true)
            .overlay(
                // Custom close button
                VStack {
                    HStack {
                        Spacer()
                        Button(action: { dismiss() }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(AppTheme.textSecondary)
                                .background(Color.black.opacity(0.1))
                                .clipShape(Circle())
                        }
                        .padding(.trailing, AppTheme.spacing20)
                        .padding(.top, AppTheme.spacing20)
                    }
                    Spacer()
                }
            )
        }
    }
}

#Preview {
    TermsOfServiceView()
}
