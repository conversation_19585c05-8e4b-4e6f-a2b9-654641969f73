/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors - More Sexy & Modern */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gold-gradient: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    --dark-gradient: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    --sexy-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    
    --primary-color: #667eea;
    --secondary-color: #f5576c;
    --accent-color: #4facfe;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #888888;
    --background-dark: #0a0a0a;
    --background-card: rgba(255, 255, 255, 0.05);
    --border-color: rgba(255, 255, 255, 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

body {
    font-family: var(--font-family);
    background: var(--background-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(245, 87, 108, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.05) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-logo i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 70px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.7;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 400px;
    height: 400px;
    background: var(--primary-gradient);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 300px;
    height: 300px;
    background: var(--secondary-gradient);
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: var(--accent-gradient);
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--background-card);
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(10px);
}

.hero-badge i {
    color: #ffd700;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.02em;
    text-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

.gradient-text {
    background: var(--sexy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-3xl);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-xl);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
    animation: pulse 2s infinite;
}

.btn-primary:hover::before {
    left: 100%;
}

@keyframes pulse {
    0%, 100% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.6); }
    50% { box-shadow: 0 0 60px rgba(102, 126, 234, 0.8); }
}

.btn-secondary {
    background: var(--background-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.phone-mockup {
    width: 300px;
    height: 600px;
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    border-radius: 40px;
    padding: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
    animation: phoneFloat 6s ease-in-out infinite;
}

@keyframes phoneFloat {
    0%, 100% { transform: translateY(0px) rotateY(-5deg); }
    50% { transform: translateY(-10px) rotateY(5deg); }
}

.phone-mockup:hover {
    transform: translateY(-15px) rotateY(0deg) scale(1.05);
    box-shadow:
        0 30px 60px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: var(--dark-gradient);
    border-radius: 30px;
    overflow: hidden;
    position: relative;
}

.app-interface {
    padding: var(--spacing-lg);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.swipe-card {
    width: 100%;
    height: 400px;
    background: var(--background-card);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.swipe-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 2s ease;
}

.swipe-card:hover::before {
    transform: translateX(100%);
}

.card-image {
    width: 100%;
    height: 60%;
    background:
        linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8)),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="35" r="15" fill="rgba(255,255,255,0.3)"/><path d="M25 75 Q25 60 50 60 Q75 60 75 75 L75 85 L25 85 Z" fill="rgba(255,255,255,0.3)"/></svg>');
    background-size: cover, 60px 60px;
    background-position: center, center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: var(--spacing-lg);
}

.card-image::after {
    content: '📸';
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    font-size: var(--font-size-lg);
    opacity: 0.7;
}

.card-info {
    padding: var(--spacing-lg);
}

.card-info h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.card-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.housing-status {
    background: var(--secondary-gradient);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    display: inline-block;
    margin-bottom: var(--spacing-xs);
}

.compatibility {
    background: var(--accent-gradient);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: inline-block;
}

.swipe-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
}

.swipe-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.swipe-btn.reject {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.swipe-btn.super-like {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
}

.swipe-btn.like {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
}

.swipe-btn:hover {
    transform: scale(1.1);
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    animation: floatCard 4s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: -20%;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: -25%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 30%;
    left: -15%;
    animation-delay: 3s;
}

@keyframes floatCard {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.8; }
    50% { transform: translateY(-10px) translateX(5px); opacity: 1; }
}

.floating-card i {
    color: var(--primary-color);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: var(--spacing-3xl) 0;
    position: relative;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.feature-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    right: 0;
    height: 3px;
    background: var(--sexy-gradient);
    transition: left 0.5s ease;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.5);
}

.feature-card:hover::before {
    left: 0;
}

.feature-card:hover::after {
    opacity: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-glow);
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.feature-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.feature-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.feature-details span {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    position: relative;
    padding-left: var(--spacing-lg);
}

.feature-details span::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* How It Works Section */
.how-it-works {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
}

.step {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--spacing-2xl);
    align-items: center;
}

.step:nth-child(even) {
    grid-template-columns: auto auto 1fr;
}

.step:nth-child(even) .step-content {
    order: 3;
}

.step:nth-child(even) .step-visual {
    order: 2;
}

.step-number {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: white;
    box-shadow: var(--shadow-glow);
}

.step-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.step-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.step-visual {
    width: 300px;
    height: 200px;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Step Visual Components */
.profile-preview {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.profile-image {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
}

.profile-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.profile-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.profile-tags {
    display: flex;
    gap: var(--spacing-xs);
}

.profile-tags span {
    background: var(--accent-gradient);
    color: white;
    padding: 2px var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.matching-interface {
    text-align: center;
    padding: var(--spacing-lg);
}

.match-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    backdrop-filter: blur(10px);
}

.compatibility-score {
    background: var(--accent-gradient);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: inline-block;
    margin-bottom: var(--spacing-sm);
}

.match-card h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.match-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.chat-preview {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    width: 100%;
}

.chat-message {
    max-width: 80%;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.chat-message.received {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    align-self: flex-start;
}

.chat-message.sent {
    background: var(--primary-gradient);
    color: white;
    align-self: flex-end;
}

.housing-preview {
    padding: var(--spacing-lg);
    width: 100%;
}

.housing-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.housing-image {
    width: 100%;
    height: 80px;
    background: var(--secondary-gradient);
}

.housing-card h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin: var(--spacing-sm) var(--spacing-md) var(--spacing-xs);
}

.housing-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0 var(--spacing-md) var(--spacing-sm);
}

.housing-features {
    display: flex;
    gap: var(--spacing-sm);
    padding: 0 var(--spacing-md) var(--spacing-md);
}

.housing-features span {
    background: var(--accent-gradient);
    color: white;
    padding: 2px var(--spacing-xs);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* App Showcase Section */
.app-showcase-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
    position: relative;
    overflow: hidden;
}

.app-showcase-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(245, 87, 108, 0.1) 0%, transparent 50%);
    z-index: 0;
}

.app-showcase-section .container {
    position: relative;
    z-index: 1;
}

/* Loading Screen Showcase */
.loading-showcase-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-3xl);
    margin: var(--spacing-3xl) 0;
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(10px);
}

.loading-screen-frame {
    position: relative;
    width: 350px;
    height: 700px;
    background:
        /* Premium background for loading screen showcase */
        radial-gradient(ellipse at center,
            rgba(102, 126, 234, 0.12) 0%,
            rgba(118, 75, 162, 0.1) 30%,
            rgba(26, 26, 26, 0.95) 70%,
            rgba(10, 10, 10, 1) 100%);
    border-radius: 45px;
    padding: 15px;
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

/* Remove gray background - let image blend naturally */

.loading-screen-frame:hover {
    transform: translateY(-10px) scale(1.02);
}

.loading-screen-image {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Show full image without cropping */
    object-position: center;
    border-radius: 30px;
    position: relative;
    z-index: 1;
}

/* Beautiful gradient overlay for loading screen - matches your brand colors */
.loading-screen-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30px;
    background:
        /* Gradient that matches your loading screen colors */
        linear-gradient(to bottom,
            rgba(102, 126, 234, 0.2) 0%,
            rgba(102, 126, 234, 0.05) 4%,
            transparent 10%),
        linear-gradient(to top,
            rgba(118, 75, 162, 0.2) 0%,
            rgba(118, 75, 162, 0.05) 4%,
            transparent 10%),
        linear-gradient(to right,
            rgba(102, 126, 234, 0.15) 0%,
            transparent 5%),
        linear-gradient(to left,
            rgba(118, 75, 162, 0.15) 0%,
            transparent 5%);
    pointer-events: none;
    z-index: 2;
}

.loading-screen-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.loading-screen-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: var(--sexy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-screen-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: 1.6;
}

.app-screens-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-3xl);
    align-items: center;
}

/* Specific positioning for 4 screens - DESKTOP ONLY */
@media (min-width: 769px) {
    .screen-showcase.deck-showcase {
        grid-column: 1;
        grid-row: 1;
    }

    .screen-showcase.home-showcase {
        grid-column: 2;
        grid-row: 1;
    }

    .screen-showcase.chat-showcase {
        grid-column: 1;
        grid-row: 2;
    }

    .screen-showcase.match-showcase {
        grid-column: 2;
        grid-row: 2;
    }
}

.screen-showcase {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--spacing-xl);
}

.screen-showcase.chat-showcase {
    flex-direction: column-reverse;
}

.screen-frame {
    position: relative;
    width: 320px;
    height: 640px;
    background:
        /* Dynamic background for app showcase */
        radial-gradient(ellipse at center,
            rgba(102, 126, 234, 0.08) 0%,
            rgba(118, 75, 162, 0.06) 30%,
            rgba(26, 26, 26, 0.9) 70%,
            rgba(10, 10, 10, 1) 100%);
    border-radius: 40px;
    padding: 15px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Beautiful backdrop for app showcase */
.screen-frame::before {
    content: '';
    position: absolute;
    top: 18px;
    left: 18px;
    right: 18px;
    bottom: 18px;
    border-radius: 25px;
    background:
        radial-gradient(ellipse at center,
            rgba(102, 126, 234, 0.02) 0%,
            rgba(118, 75, 162, 0.01) 50%,
            transparent 100%);
    z-index: 0;
}

.screen-frame:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.screen-image {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Show full image without cropping */
    object-position: center;
    border-radius: 25px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

/* Beautiful gradient overlay for app showcase images */
.screen-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 25px;
    background:
        /* Subtle edge fades that match the gradient theme */
        linear-gradient(to bottom,
            rgba(102, 126, 234, 0.15) 0%,
            transparent 8%),
        linear-gradient(to top,
            rgba(118, 75, 162, 0.15) 0%,
            transparent 8%),
        linear-gradient(to right,
            rgba(102, 126, 234, 0.1) 0%,
            transparent 6%),
        linear-gradient(to left,
            rgba(118, 75, 162, 0.1) 0%,
            transparent 6%);
    pointer-events: none;
    z-index: 2;
}

.screen-info {
    max-width: 400px;
}

.screen-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--sexy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.screen-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.screen-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.feature-tag {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.feature-tag:hover {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

/* Hero App Showcase */
.app-showcase {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 600px;
}

.main-phone {
    position: relative;
    z-index: 2;
}

.phone-frame {
    position: relative;
    width: 350px;
    height: 700px;
    background:
        /* Dynamic background that matches your app's dark theme */
        radial-gradient(ellipse at center,
            rgba(102, 126, 234, 0.1) 0%,
            rgba(118, 75, 162, 0.08) 30%,
            rgba(26, 26, 26, 0.95) 70%,
            rgba(10, 10, 10, 1) 100%);
    border-radius: 45px;
    padding: 15px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

/* Remove backdrop - let the phone frame background handle it */

.app-screenshot {
    position: absolute;
    top: 15px;
    left: 15px;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    object-fit: contain; /* Show full image without cropping */
    object-position: center;
    border-radius: 30px;
    transition: opacity 0.8s ease;
    z-index: 1;
}

/* Subtle edge enhancement that doesn't interfere with the image */
.app-screenshot::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30px;
    background:
        /* Very subtle edge enhancement only */
        linear-gradient(to bottom,
            rgba(102, 126, 234, 0.05) 0%,
            transparent 3%),
        linear-gradient(to top,
            rgba(118, 75, 162, 0.05) 0%,
            transparent 3%),
        linear-gradient(to right,
            rgba(102, 126, 234, 0.03) 0%,
            transparent 2%),
        linear-gradient(to left,
            rgba(118, 75, 162, 0.03) 0%,
            transparent 2%);
    pointer-events: none;
    z-index: 2;
}

/* App Screenshot Transitions - Updated for 4 screens */
.app-screenshot {
    opacity: 0;
    transition: opacity 0.8s ease;
}

/* Fixed carousel animation - all screens get the same treatment */
.app-screenshot.deck-screen {
    animation: screenTransition 16s infinite;
    animation-delay: 0s;
}

.app-screenshot.home-screen {
    animation: screenTransition 16s infinite;
    animation-delay: 4s;
}

.app-screenshot.chat-screen {
    animation: screenTransition 16s infinite;
    animation-delay: 8s;
}

.app-screenshot.match-screen {
    animation: screenTransition 16s infinite;
    animation-delay: 12s;
}

@keyframes screenTransition {
    0%, 18.75% { opacity: 0; }
    25%, 43.75% { opacity: 1; }
    50%, 100% { opacity: 0; }
}

.floating-notification {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: float 3s ease-in-out infinite;
}

.notification-1 {
    top: 100px;
    right: -50px;
    animation-delay: 0s;
}

.notification-2 {
    bottom: 120px;
    left: -80px;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 200px;
}

.notification-icon {
    font-size: var(--font-size-xl);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
}

.notification-text {
    display: flex;
    flex-direction: column;
}

.notification-text strong {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.notification-text span {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

/* Flow Indicators - HIDDEN */
.flow-indicators {
    display: none !important; /* Hide completely - causes issues */
}

.flow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.flow-step.active {
    opacity: 1;
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.flow-step:hover {
    opacity: 1;
    transform: translateY(-2px);
}

.step-icon {
    font-size: var(--font-size-lg);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    transition: all 0.3s ease;
}

.flow-step.active .step-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.flow-step span {
    font-size: var(--font-size-xs);
    font-weight: 500;
    color: var(--text-primary);
    text-align: center;
}

/* Loading Screen Showcase */
.loading-showcase-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-3xl);
    margin: var(--spacing-3xl) 0;
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.loading-screen-frame {
    flex-shrink: 0;
    width: 200px;
    height: 400px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-radius: 25px;
    padding: 15px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.loading-screen-frame:hover {
    transform: translateY(-5px) scale(1.02);
}

.loading-screen-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.loading-screen-info {
    flex: 1;
}

.loading-screen-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--sexy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-screen-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

/* Download Section Real Phones - Updated for 4 phones */
.real-phones-showcase {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    align-items: center;
    justify-content: center;
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.phone-showcase {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform 0.3s ease;
}

.phone-showcase:hover {
    transform: translateY(-10px);
}

.phone-showcase .phone-frame {
    width: 280px;
    height: 560px;
    background:
        /* Professional background for download section */
        radial-gradient(ellipse at center,
            rgba(102, 126, 234, 0.06) 0%,
            rgba(118, 75, 162, 0.04) 30%,
            rgba(26, 26, 26, 0.9) 70%,
            rgba(10, 10, 10, 1) 100%);
    border-radius: 35px;
    padding: 15px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

/* Remove gray background - let image blend naturally */

.phone-screenshot {
    width: 100%;
    height: 100%;
    object-fit: contain; /* Show full image without cropping */
    object-position: center;
    border-radius: 20px;
    position: relative;
    z-index: 1;
}

/* Beautiful gradient overlay for download section phones */
.phone-screenshot::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    background:
        /* Professional edge fades */
        linear-gradient(to bottom,
            rgba(102, 126, 234, 0.12) 0%,
            transparent 6%),
        linear-gradient(to top,
            rgba(118, 75, 162, 0.12) 0%,
            transparent 6%),
        linear-gradient(to right,
            rgba(102, 126, 234, 0.08) 0%,
            transparent 4%),
        linear-gradient(to left,
            rgba(118, 75, 162, 0.08) 0%,
            transparent 4%);
    pointer-events: none;
    z-index: 2;
}

.phone-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
}

.secondary-phone, .tertiary-phone, .quaternary-phone {
    transform: scale(0.9);
    opacity: 0.8;
}

.secondary-phone:hover, .tertiary-phone:hover, .quaternary-phone:hover {
    transform: scale(0.95) translateY(-10px);
    opacity: 1;
}

/* Stagger animation for phone showcase */
.phone-showcase.primary-phone {
    animation-delay: 0s;
}

.phone-showcase.secondary-phone {
    animation-delay: 0.2s;
}

.phone-showcase.tertiary-phone {
    animation-delay: 0.4s;
}

.phone-showcase.quaternary-phone {
    animation-delay: 0.6s;
}

/* Modern How It Works Section */
.how-it-works {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(245, 87, 108, 0.03) 100%);
    position: relative;
    overflow: hidden;
}

.modern-steps-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3xl);
    margin-top: var(--spacing-3xl);
}

.modern-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.modern-step:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
    width: 100%;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    opacity: 0.3;
    z-index: -1;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.1; }
}

.step-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    background: var(--sexy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.step-content p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.step-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.step-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.step-feature:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    transform: translateX(5px);
}

/* ========================================
   MOBILE RESPONSIVE DESIGN - UPDATED 2024
   ========================================
   Last Updated: June 5, 2024
   Changes: Fixed mobile navigation, responsive grids,
           demo screen sizing, image display issues
   ======================================== */

/* Mobile Carousel - REMOVED (Desktop Only Feature) */

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
    /* ULTIMATE NUCLEAR OPTION: Force 1x1 grid with maximum specificity */
    .app-showcase-section .container .app-screens-grid,
    .app-showcase-section .app-screens-grid,
    .container .app-screens-grid,
    .app-screens-grid,
    div.app-screens-grid,
    section .app-screens-grid {
        grid-template-columns: 1fr !important;
        grid-template-rows: repeat(4, auto) !important;
        display: grid !important;
        justify-items: center !important;
        align-items: center !important;
        gap: var(--spacing-2xl) !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 var(--spacing-lg) !important;
    }

    /* FORCE REMOVE SPECIFIC GRID POSITIONING - MOBILE 1x1 LAYOUT */
    .screen-showcase.deck-showcase,
    .screen-showcase.home-showcase,
    .screen-showcase.chat-showcase,
    .screen-showcase.match-showcase {
        grid-column: 1 !important;
        grid-row: auto !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Ensure each screen showcase takes full width on mobile */
    .screen-showcase {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
    }



    /* NUCLEAR HIDE: Desktop carousel completely hidden on mobile */
    .hero-visual {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }



    /* Mobile Navigation Fix */
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: var(--spacing-xl);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
        display: flex;
    }

    .nav-toggle {
        display: flex;
        z-index: 1001;
    }

    /* Hero Section Mobile */
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
        padding: var(--spacing-lg);
        /* Use flexbox for better mobile control */
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-lg);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .hero-stats {
        justify-content: center;
        gap: var(--spacing-lg);
    }



    .flow-indicators {
        display: none !important; /* Hide ugly tab bar on mobile */
    }

    .flow-step {
        padding: var(--spacing-xs);
    }

    .flow-step span {
        font-size: 10px;
    }

    .step-icon {
        width: 24px;
        height: 24px;
        font-size: var(--font-size-sm);
    }

    /* Floating notifications mobile */
    .floating-notification {
        display: none; /* Hide on mobile for cleaner look */
    }

    .floating-cards {
        display: none; /* Hide on mobile for cleaner look */
    }
    
    .step-visual {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    /* MAXIMUM SPECIFICITY - FORCE 1x1 GRID */
    .app-showcase-section .container .app-screens-grid,
    .app-showcase-section .app-screens-grid,
    .app-screens-grid {
        grid-template-columns: 1fr !important;
        grid-template-rows: auto auto auto auto !important;
        gap: var(--spacing-2xl) !important;
        display: grid !important;
        justify-items: center !important;
        align-items: center !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .screen-showcase.chat-showcase {
        flex-direction: column;
    }

    /* App Showcase Section Mobile - Fix Grid Layout */
    .app-showcase-section {
        padding: var(--spacing-2xl) 0;
    }

    .loading-showcase-section {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xl);
        padding: var(--spacing-lg);
        /* Remove gray background */
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .loading-screen-frame {
        width: 250px;
        height: 500px;
        margin: 0 auto;
        /* Remove gray background - use transparent background */
        background:
            radial-gradient(ellipse at center,
                rgba(102, 126, 234, 0.06) 0%,
                rgba(118, 75, 162, 0.04) 30%,
                transparent 70%);
    }

    /* Additional mobile grid enforcement */
    .container .app-screens-grid {
        grid-template-columns: 1fr !important;
        padding: 0 var(--spacing-lg) !important;
    }

    .screen-showcase {
        flex-direction: column;
        text-align: center;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .screen-frame {
        width: 280px;
        height: 560px;
        margin: 0 auto;
        flex-shrink: 0;
        /* Remove gray background - use transparent background that blends */
        background:
            radial-gradient(ellipse at center,
                rgba(102, 126, 234, 0.06) 0%,
                rgba(118, 75, 162, 0.04) 30%,
                transparent 70%);
    }

    .screen-info {
        max-width: 100%;
        padding: 0 var(--spacing-md);
    }

    .screen-features {
        justify-content: center;
        flex-wrap: wrap;
    }

    .floating-notification {
        display: none;
    }

    /* Features Section Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        padding: 0 var(--spacing-lg);
    }

    .feature-card {
        padding: var(--spacing-lg);
    }

    /* Download Section Mobile - Fix Phone Grid */
    .download-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .download-text {
        order: 2;
        padding: 0 var(--spacing-lg);
    }

    .download-visual {
        order: 1;
    }

    .real-phones-showcase {
        grid-template-columns: 1fr !important; /* Force single column on mobile */
        gap: var(--spacing-lg);
        max-width: 100%;
        padding: 0 var(--spacing-lg);
        justify-items: center;
    }

    /* COPY THE EXACT SAME APPROACH FOR FIRST SET */
    .app-screens-grid {
        grid-template-columns: 1fr !important; /* Force single column on mobile - SAME AS WORKING SET */
        gap: var(--spacing-lg);
        max-width: 100%;
        padding: 0 var(--spacing-lg);
        justify-items: center;
    }

    .phone-showcase .phone-frame {
        width: 200px;
        height: 400px;
        padding: 12px;
        margin: 0 auto;
        /* Remove gray background - use transparent background */
        background:
            radial-gradient(ellipse at center,
                rgba(102, 126, 234, 0.04) 0%,
                rgba(118, 75, 162, 0.03) 30%,
                transparent 70%);
    }

    .phone-label {
        font-size: var(--font-size-xs);
    }

    .download-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .secondary-phone, .tertiary-phone, .quaternary-phone {
        transform: scale(1);
        opacity: 1;
    }

    /* Modern Steps Responsive */
    .modern-step {
        padding: var(--spacing-lg);
        max-width: 100%;
        margin: 0 var(--spacing-md);
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .step-features {
        align-items: center;
    }

    .step-feature {
        justify-content: center;
        text-align: center;
    }

    /* Safety & Testimonials Mobile */
    .safety-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        padding: 0 var(--spacing-lg);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        padding: 0 var(--spacing-lg);
    }

    /* Footer Mobile */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    /* ENSURE desktop carousel is hidden on small mobile */
    .hero-visual {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
    }



    .hero-visual-mobile .phone-frame {
        width: 250px;
        height: 350px;
        /* Ensure no gray background on very small screens */
        background:
            radial-gradient(ellipse at center,
                rgba(102, 126, 234, 0.06) 0%,
                rgba(118, 75, 162, 0.04) 30%,
                transparent 70%);
    }

    .floating-cards {
        display: none;
    }

    /* Ensure grid stays 1x1 on very small screens */
    .app-screens-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .screen-frame {
        width: 250px;
        height: 500px;
    }
}

/* Safety Section */
.safety {
    padding: var(--spacing-3xl) 0;
    background: var(--background-dark);
}

.safety-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.safety-feature {
    text-align: center;
    padding: var(--spacing-2xl);
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.safety-feature:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.safety-icon {
    width: 80px;
    height: 80px;
    background: var(--secondary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    box-shadow: 0 0 20px rgba(245, 87, 108, 0.3);
}

.safety-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.safety-feature h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.safety-feature p {
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, rgba(245, 87, 108, 0.05), rgba(79, 172, 254, 0.05));
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.testimonial-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.testimonial-card:hover::before {
    opacity: 1;
}

.testimonial-content {
    margin-bottom: var(--spacing-lg);
}

.stars {
    display: flex;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.stars i {
    color: #ffd700;
    font-size: var(--font-size-sm);
}

.testimonial-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-style: italic;
    font-size: var(--font-size-lg);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    flex-shrink: 0;
}

.author-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.author-info span {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Download Section */
.download {
    padding: var(--spacing-3xl) 0;
    background: var(--background-dark);
    position: relative;
    overflow: hidden;
}

.download::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 50%, rgba(102, 126, 234, 0.1), transparent 50%),
                radial-gradient(circle at 70% 50%, rgba(245, 87, 108, 0.1), transparent 50%);
    z-index: 1;
}

.download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.download-text h2 {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.download-text p {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.download-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
}

.download-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.download-feature i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.download-feature span {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.download-buttons {
    display: flex;
    gap: var(--spacing-lg);
}

.download-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg) var(--spacing-xl);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.primary-download {
    background: var(--primary-gradient) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
}

.primary-download:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
}

.coming-soon {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg) var(--spacing-xl);
    color: var(--text-muted);
    backdrop-filter: blur(10px);
    opacity: 0.6;
    position: relative;
}

.coming-soon::after {
    content: 'Coming Soon';
    position: absolute;
    top: -10px;
    right: 10px;
    background: var(--secondary-gradient);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.coming-soon-hero {
    background: rgba(102, 126, 234, 0.2) !important;
    border: 2px solid rgba(102, 126, 234, 0.4) !important;
    color: var(--text-primary) !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
    position: relative;
    overflow: hidden;
}

.coming-soon-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.btn-icon {
    font-size: var(--font-size-3xl);
}

.btn-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.btn-text span {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.btn-text strong {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.download-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.phones-mockup {
    position: relative;
    display: flex;
    gap: var(--spacing-lg);
    transform: perspective(1000px) rotateY(-15deg);
}

.phone {
    width: 120px;
    height: 240px;
    background: linear-gradient(145deg, #1a1a1a, #2a2a2a);
    border-radius: 20px;
    padding: 10px;
    box-shadow: var(--shadow-xl);
    transition: all 0.3s ease;
}

.phone:hover {
    transform: translateY(-10px);
}

.phone-1 {
    z-index: 3;
}

.phone-2 {
    z-index: 2;
    transform: translateY(20px);
}

.phone-3 {
    z-index: 1;
    transform: translateY(40px);
}

.phone .phone-screen {
    width: 100%;
    height: 100%;
    background: var(--dark-gradient);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.screen-content {
    padding: var(--spacing-md);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.screen-content h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.mini-card {
    width: 80px;
    height: 100px;
    background: var(--primary-gradient);
    border-radius: var(--spacing-sm);
}

.match-notification {
    background: var(--secondary-gradient);
    color: white;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.chat-bubbles {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    width: 100%;
}

.chat-bubble {
    height: 20px;
    background: var(--background-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.chat-bubble:first-child {
    width: 70%;
    align-self: flex-start;
}

.chat-bubble:last-child {
    width: 60%;
    align-self: flex-end;
    background: var(--primary-gradient);
}

/* Realistic App Mockup Styles */
.tab-bar {
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 12px;
}

.tab {
    font-size: 16px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.tab.active {
    opacity: 1;
    transform: scale(1.2);
}

.swipe-stack {
    position: relative;
    width: 80px;
    height: 100px;
    margin: 12px auto;
}

.mini-swipe-card {
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.mini-swipe-card.behind {
    transform: scale(0.95) translateY(4px);
    opacity: 0.7;
    z-index: -1;
}

.swipe-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 12px;
}

.action-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    border: 1px solid var(--border-color);
}

.action-btn.reject {
    background: #ff4458;
    color: white;
}

.action-btn.super {
    background: #ffd700;
    color: #333;
}

.action-btn.like {
    background: #42c767;
    color: white;
}

.chat-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px;
    background: var(--background-card);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.chat-avatar {
    width: 24px;
    height: 24px;
    background: var(--primary-gradient);
    border-radius: 50%;
    flex-shrink: 0;
}

.chat-preview {
    flex: 1;
    min-width: 0;
}

.chat-name {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.chat-message {
    font-size: 8px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.profile-photo {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

.profile-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.profile-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
}

.detail-item {
    font-size: 8px;
    color: var(--text-secondary);
    text-align: center;
    padding: 2px 4px;
    background: var(--background-card);
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* Footer */
.footer {
    background: var(--background-dark);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.footer-logo i {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-2xl);
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.footer-section ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--text-primary);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-links a:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-xl);
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Additional Responsive Design */
@media (max-width: 768px) {
    .download-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .download-buttons {
        justify-content: center;
    }

    .phones-mockup {
        transform: none;
        gap: var(--spacing-md);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .safety-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}

/* Tablet Specific Adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .real-phones-showcase {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
        max-width: 600px;
        margin: 0 auto;
    }

    .phone-showcase .phone-frame {
        width: 180px;
        height: 360px;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    /* ENSURE desktop carousel is hidden on extra small mobile */
    .hero-visual {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
    }

    /* Hero Section - Very Small Screens */
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: var(--font-size-md);
    }

    .app-showcase {
        height: 350px;
        max-width: 240px;
    }

    .phone-frame {
        width: 240px;
        height: 350px;
    }

    /* App Showcase - Very Small Screens */
    .screen-frame {
        width: 240px;
        height: 480px;
    }

    .loading-screen-frame {
        width: 220px;
        height: 440px;
    }

    /* Download Section - Very Small Screens */
    .real-phones-showcase {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* FIRST SET - COPY EXACT SAME APPROACH */
    .app-screens-grid {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-lg);
    }

    .phone-showcase .phone-frame {
        width: 200px;
        height: 400px;
        margin: 0 auto;
    }

    .download-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .phones-mockup {
        flex-direction: column;
        align-items: center;
    }

    .phone {
        transform: none !important;
    }

    /* Typography Adjustments */
    .section-title {
        font-size: var(--font-size-2xl);
    }

    .feature-card h3 {
        font-size: var(--font-size-lg);
    }

    .step-content h3 {
        font-size: var(--font-size-lg);
    }
}
