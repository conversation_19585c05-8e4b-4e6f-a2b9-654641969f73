//
//  FirebaseStorageService.swift
//  CampusPads
//
//  Created by [Your Name] on [Date].
//

import Foundation
import FirebaseStorage
import FirebaseAuth
import UIKit

class FirebaseStorageService {
    static let shared = FirebaseStorageService()
    private let storage = Storage.storage().reference()
    private let cache = NSCache<NSString, UIImage>()
    private let networkMonitor = NetworkMonitor.shared

    init() {
        // Configure cache
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }

    /// Uploads a profile image to Firebase Storage with enhanced error handling and offline support.
    /// - Parameters:
    ///   - image: The UIImage to upload.
    ///   - completion: Returns a URL string on success or an AppError.
    func uploadProfileImage(image: UIImage, completion: @escaping (Result<String, AppError>) -> Void) {
        // Check network connectivity
        guard networkMonitor.isConnected else {
            let error = ErrorFactory.networkError(
                message: "Cannot upload image while offline",
                technicalDetails: "Network connection unavailable"
            )
            completion(.failure(error))
            return
        }

        // Validate and compress image
        guard let imageData = compressImage(image) else {
            let error = ErrorFactory.validationError(
                message: "Unable to process image",
                technicalDetails: "Image compression failed"
            )
            completion(.failure(error))
            return
        }

        // OPTIMIZED: Use user-based folder structure for better security
        guard let currentUser = Auth.auth().currentUser else {
            let error = ErrorFactory.authenticationError(
                message: "User not authenticated",
                technicalDetails: "No current user found"
            )
            completion(.failure(error))
            return
        }

        let imageID = UUID().uuidString
        let imageRef = storage.child("profile_images/\(currentUser.uid)/\(imageID).jpg")
        let metadata = StorageMetadata()
        metadata.contentType = "image/jpeg"
        metadata.customMetadata = [
            "uploadedBy": currentUser.uid,
            "uploadedAt": ISO8601DateFormatter().string(from: Date())
        ]

        // Upload with progress tracking
        let uploadTask = imageRef.putData(imageData, metadata: metadata) { [weak self] (metadata: StorageMetadata?, error: Error?) in
            if let error = error {
                let appError = ErrorFactory.storageError(
                    message: "Failed to upload image",
                    technicalDetails: error.localizedDescription
                )
                completion(.failure(appError))
                return
            }

            // Get download URL
            imageRef.downloadURL { url, error in
                if let error = error {
                    let appError = ErrorFactory.storageError(
                        message: "Failed to get image URL",
                        technicalDetails: error.localizedDescription
                    )
                    completion(.failure(appError))
                    return
                }

                guard let downloadURL = url else {
                    let appError = ErrorFactory.storageError(
                        message: "Download URL not available",
                        technicalDetails: "URL generation failed"
                    )
                    completion(.failure(appError))
                    return
                }

                // Cache the image for future use
                self?.cache.setObject(image, forKey: downloadURL.absoluteString as NSString)
                completion(.success(downloadURL.absoluteString))
            }
        }

        // Monitor upload progress (optional enhancement)
        uploadTask.observe(.progress) { snapshot in
            if let progress = snapshot.progress {
                let percentComplete = 100.0 * Double(progress.completedUnitCount) / Double(progress.totalUnitCount)
                print("Upload progress: \(percentComplete)%")
            }
        }
    }

    /// Simple image download with caching
    func downloadImage(from urlString: String, completion: @escaping (Result<UIImage, AppError>) -> Void) {
        // Check cache first
        if let cachedImage = cache.object(forKey: urlString as NSString) {
            completion(.success(cachedImage))
            return
        }

        guard let url = URL(string: urlString) else {
            let error = ErrorFactory.validationError(
                message: "Invalid image URL",
                technicalDetails: "URL parsing failed: \(urlString)"
            )
            completion(.failure(error))
            return
        }

        // Use SimpleImageLoader's session for consistent HTTP/1.1 configuration
        SimpleImageLoader.shared.session.dataTask(with: url) { [weak self] data, response, error in
            if let error = error {
                let appError = ErrorFactory.networkError(
                    message: "Failed to download image",
                    technicalDetails: error.localizedDescription
                )
                DispatchQueue.main.async {
                    completion(.failure(appError))
                }
                return
            }

            guard let data = data, let image = UIImage(data: data) else {
                let appError = ErrorFactory.validationError(
                    message: "Invalid image data",
                    technicalDetails: "Could not create image from downloaded data"
                )
                DispatchQueue.main.async {
                    completion(.failure(appError))
                }
                return
            }

            // Cache the downloaded image
            self?.cache.setObject(image, forKey: urlString as NSString)

            DispatchQueue.main.async {
                completion(.success(image))
            }
        }.resume()
    }

    /// Compresses image for optimal upload size
    private func compressImage(_ image: UIImage) -> Data? {
        // Start with high quality
        var compression: CGFloat = 0.8
        var imageData = image.jpegData(compressionQuality: compression)

        // Reduce quality if image is too large (max 2MB)
        let maxSize = 2 * 1024 * 1024 // 2MB
        while let data = imageData, data.count > maxSize && compression > 0.1 {
            compression -= 0.1
            imageData = image.jpegData(compressionQuality: compression)
        }

        return imageData
    }
}
