import SwiftUI

struct AuthenticationView: View {
    @State private var showSignIn: Bool = true

    var body: some View {
        ZStack {
            // Stunning animated background
            OnboardingAnimatedBackground()

            VStack(spacing: 0) {
                // App branding header
                VStack(spacing: AppTheme.spacing20) {
                    Spacer()

                    VStack(spacing: AppTheme.spacing8) {
                        Text("CampusPads")
                            .font(.custom("AvenirNext-Bold", size: 28))
                            .foregroundStyle(AppTheme.sexyGradient)

                        Text("Find Your Perfect Roommate")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    Spacer()
                }
                .frame(maxHeight: 200)

                // Authentication content
                VStack(spacing: AppTheme.spacing24) {
                    // Modern segmented control
                    HStack(spacing: 0) {
                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                showSignIn = true
                            }
                        }) {
                            Text("Sign In")
                                .font(AppTheme.subtitleFont)
                                .fontWeight(.medium)
                                .foregroundColor(showSignIn ? .white : AppTheme.textPrimary)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, AppTheme.spacing12)
                                .background(
                                    showSignIn ?
                                    AnyView(AppTheme.primaryGradient) :
                                    AnyView(Color.clear)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())

                        Button(action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                showSignIn = false
                            }
                        }) {
                            Text("Sign Up")
                                .font(AppTheme.subtitleFont)
                                .fontWeight(.medium)
                                .foregroundColor(!showSignIn ? .white : AppTheme.textPrimary)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, AppTheme.spacing12)
                                .background(
                                    !showSignIn ?
                                    AnyView(AppTheme.primaryGradient) :
                                    AnyView(Color.clear)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .background(AppTheme.cardBackground)
                    .cornerRadius(AppTheme.radiusMedium)
                    .padding(.horizontal, AppTheme.spacing20)

                    // Content area
                    if showSignIn {
                        SignInView(showSignIn: $showSignIn)
                            .transition(.asymmetric(
                                insertion: .move(edge: .leading).combined(with: .opacity),
                                removal: .move(edge: .trailing).combined(with: .opacity)
                            ))
                    } else {
                        SignUpView(showSignIn: $showSignIn)
                            .transition(.asymmetric(
                                insertion: .move(edge: .trailing).combined(with: .opacity),
                                removal: .move(edge: .leading).combined(with: .opacity)
                            ))
                    }
                }
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: showSignIn)
            }
        }
        .navigationBarHidden(true)
    }
}

struct AuthenticationView_Previews: PreviewProvider {
    static var previews: some View {
        AuthenticationView()
    }
}
