import SwiftUI
import FirebaseAuth

struct PasswordResetView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.presentationMode) var presentationMode

    @State private var email: String = ""
    @State private var error: String?
    @State private var successMessage: String?
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var animateGlow = false

    // Enhanced email validation
    private var isEmailValid: Bool {
        let trimmed = email.trimmingCharacters(in: .whitespacesAndNewlines)
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: trimmed)
    }

    let onDismiss: () -> Void

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Custom navigation bar
                customNavigationBar

                ScrollView(showsIndicators: false) {
                    VStack(spacing: AppTheme.spacing32) {
                        headerSection
                        contentSection
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing40)
                }
            }
        }
        .onAppear {
            // Pre-fill email from AuthViewModel if available
            if !authViewModel.email.isEmpty {
                email = authViewModel.email
            }

            // Start glow animation
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                animateGlow = true
            }
        }
    }

    // MARK: - Custom Navigation Bar
    private var customNavigationBar: some View {
        HStack {
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .light)
                onDismiss()
            }) {
                ZStack {
                    Circle()
                        .fill(.ultraThinMaterial)
                        .frame(width: 44, height: 44)
                        .overlay(
                            Circle()
                                .stroke(.white.opacity(0.3), lineWidth: 1)
                        )

                    Image(systemName: "xmark")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing16)
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Animated icon with glow
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 100, height: 100)
                    .blur(radius: animateGlow ? 25 : 15)
                    .opacity(animateGlow ? 0.8 : 0.5)

                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 80, height: 80)
                    .overlay(
                        Circle()
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )

                Image(systemName: showSuccess ? "checkmark.circle.fill" : "key.fill")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .scaleEffect(showSuccess ? 1.2 : 1.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.6), value: showSuccess)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text(showSuccess ? "Check Your Email!" : "Reset Password")
                    .font(.custom("AvenirNext-Bold", size: 32))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text(showSuccess ?
                     "We've sent password reset instructions to your email" :
                     "Enter your email address and we'll send you a link to reset your password")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.spacing16)
            }
        }
        .padding(.top, AppTheme.spacing32)
    }

    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: AppTheme.spacing24) {
            if !showSuccess {
                emailInputSection
                actionButtonSection
            } else {
                successActionsSection
            }

            // Error display
            if let error = error {
                errorMessageView(error)
            }
        }
    }

    // MARK: - Email Input Section
    private var emailInputSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "envelope.fill")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)

                Text("Email Address")
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(.white)

                Spacer()

                if isEmailValid {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.green)
                }
            }

            VStack(spacing: AppTheme.spacing8) {
                TextField("Enter your email address", text: $email)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .keyboardType(.emailAddress)
                    .padding(AppTheme.spacing16)
                    .background(.ultraThinMaterial)
                    .cornerRadius(16)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isEmailValid ? Color.green : Color.white.opacity(0.3), lineWidth: isEmailValid ? 2 : 1)
                    )
                    .onChange(of: email) { _, _ in
                        // Clear errors when user starts typing
                        error = nil
                    }

                if !email.isEmpty && !isEmailValid {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.orange)

                        Text("Please enter a valid email address")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(.orange)

                        Spacer()
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Action Button Section
    private var actionButtonSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .medium)
                sendPasswordReset()
            }) {
                HStack(spacing: AppTheme.spacing12) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.9)

                        Text("Sending Reset Link...")
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)

                        Text("Send Reset Link")
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isEmailValid && !isLoading ?
                              AnyShapeStyle(AppTheme.sexyGradient) :
                              AnyShapeStyle(Color.gray.opacity(0.5)))
                )
                .scaleEffect(isEmailValid && !isLoading ? 1.0 : 0.95)
                .opacity(isEmailValid && !isLoading ? 1.0 : 0.6)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(!isEmailValid || isLoading)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isEmailValid)

            // Help text
            VStack(spacing: AppTheme.spacing8) {
                HStack {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white.opacity(0.7))

                    Text("We'll send you a secure link to reset your password")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.7))

                    Spacer()
                }

                HStack {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white.opacity(0.7))

                    Text("The link will expire in 1 hour for security")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.7))

                    Spacer()
                }
            }
            .padding(.horizontal, AppTheme.spacing16)
        }
    }

    // MARK: - Success Actions Section
    private var successActionsSection: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Success message card
            VStack(spacing: AppTheme.spacing16) {
                HStack {
                    Image(systemName: "envelope.badge.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)

                    Text("Email Sent Successfully")
                        .font(.custom("AvenirNext-Bold", size: 18))
                        .foregroundColor(.white)

                    Spacer()
                }

                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("We've sent password reset instructions to:")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.8))

                    Text(email)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)
                        .padding(.horizontal, AppTheme.spacing12)
                        .padding(.vertical, AppTheme.spacing8)
                        .background(.ultraThinMaterial.opacity(0.5))
                        .cornerRadius(8)
                }
            }
            .padding(AppTheme.spacing20)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )

            // Action buttons
            VStack(spacing: AppTheme.spacing12) {
                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    onDismiss()
                }) {
                    HStack {
                        Image(systemName: "arrow.left.circle.fill")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)

                        Text("Back to Sign In")
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, AppTheme.spacing16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppTheme.sexyGradient)
                    )
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    sendPasswordReset() // Resend
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise.circle.fill")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white.opacity(0.8))

                        Text("Resend Email")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, AppTheme.spacing16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial.opacity(0.6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }

            // Help section
            VStack(spacing: AppTheme.spacing8) {
                HStack {
                    Image(systemName: "questionmark.circle.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white.opacity(0.7))

                    Text("Didn't receive the email?")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.7))

                    Spacer()
                }

                VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                    Text("• Check your spam/junk folder")
                    Text("• Make sure the email address is correct")
                    Text("• Wait a few minutes for delivery")
                    Text("• Try resending the email")
                }
                .font(.custom("AvenirNext-Regular", size: 12))
                .foregroundColor(.white.opacity(0.6))
            }
            .padding(.horizontal, AppTheme.spacing16)
        }
    }

    // MARK: - Error Message View
    private func errorMessageView(_ errorText: String) -> some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.red)

            Text(errorText)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.red)
                .multilineTextAlignment(.leading)

            Spacer()
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
        )
        .transition(.slide.combined(with: .opacity))
    }

    // MARK: - Enhanced Password Reset Function
    private func sendPasswordReset() {
        // Clear previous states
        error = nil
        successMessage = nil
        isLoading = true

        let trimmedEmail = email.trimmingCharacters(in: .whitespacesAndNewlines)

        // Enhanced validation
        guard isEmailValid else {
            isLoading = false
            error = "Please enter a valid email address"
            HapticFeedbackManager.shared.generateNotification(.error)
            return
        }

        print("🔄 PasswordResetView: Sending password reset to: \(trimmedEmail)")

        Auth.auth().sendPasswordReset(withEmail: trimmedEmail) { [self] authError in
            DispatchQueue.main.async {
                self.isLoading = false

                if let authError = authError {
                    print("❌ PasswordResetView: Password reset failed: \(authError.localizedDescription)")

                    // Provide user-friendly error messages
                    let friendlyError = self.getFriendlyErrorMessage(for: authError)
                    self.error = friendlyError

                    HapticFeedbackManager.shared.generateNotification(.error)
                } else {
                    print("✅ PasswordResetView: Password reset email sent successfully")

                    // Show success state
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        self.showSuccess = true
                    }

                    HapticFeedbackManager.shared.generateNotification(.success)

                    // Auto-dismiss after longer delay for better UX
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        self.onDismiss()
                    }
                }
            }
        }
    }

    // MARK: - User-Friendly Error Messages
    private func getFriendlyErrorMessage(for error: Error) -> String {
        let nsError = error as NSError

        switch nsError.code {
        case 17011: // FIRAuthErrorCodeUserNotFound
            return "No account found with this email address. Please check your email or create a new account."
        case 17008: // FIRAuthErrorCodeInvalidEmail
            return "Please enter a valid email address."
        case 17010: // FIRAuthErrorCodeTooManyRequests
            return "Too many password reset attempts. Please wait a few minutes before trying again."
        case 17020: // FIRAuthErrorCodeNetworkError
            return "Network error. Please check your internet connection and try again."
        default:
            return "Unable to send password reset email. Please try again later."
        }
    }
}
