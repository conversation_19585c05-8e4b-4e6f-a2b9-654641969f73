import SwiftUI
import MapKit

struct ListingsView: View {
    @StateObject private var viewModel = ListingsViewModel()

    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 37.7749, longitude: -122.4194),
        span: MKCoordinateSpan(latitudeDelta: 0.1, longitudeDelta: 0.1)
    )

    // Listings with valid location.
    private var validListings: [ListingModel] {
        viewModel.listings.filter { $0.location != nil }
    }

    @State private var selectedListing: ListingModel? = nil

    var body: some View {
        NavigationView {
            VStack {
                // Interactive Map with custom annotations.
                Map(initialPosition: .region(region)) {
                    ForEach(validListings) { listing in
                        Annotation(listing.title, coordinate: listing.coordinate) {
                        Button(action: {
                            selectedListing = listing
                        }) {
                            VStack {
                                Image(systemName: "mappin.circle.fill")
                                    .resizable()
                                    .frame(width: 30, height: 30)
                                    .foregroundColor(AppTheme.primaryColor)
                                Text(listing.title)
                                    .font(AppTheme.bodyFont)
                                    .fixedSize()
                            }
                        }
                        .accessibilityLabel("Listing: \(listing.title)")
                        }
                    }
                }
                .frame(height: 300)
                .cornerRadius(AppTheme.defaultCornerRadius)
                .padding()

                // Listings List
                List(viewModel.listings) { listing in
                    HStack {
                        if let urlStr = listing.imageUrl, let url = URL(string: urlStr) {
                            RobustAsyncImage(url: url)
                                .frame(width: 80, height: 80)
                                .cornerRadius(AppTheme.defaultCornerRadius)
                        } else {
                            AppTheme.cardBackground
                                .frame(width: 80, height: 80)
                                .cornerRadius(AppTheme.defaultCornerRadius)
                        }

                        VStack(alignment: .leading) {
                            Text(listing.title)
                                .font(AppTheme.bodyFont)
                                .foregroundColor(.primary)
                            Text(listing.address)
                                .font(AppTheme.bodyFont)
                                .foregroundColor(AppTheme.secondaryColor)
                            Text("Rent: \(listing.rent)")
                                .font(AppTheme.bodyFont)
                                .foregroundColor(AppTheme.accentColor)
                        }
                    }
                    .padding(.vertical, 4)
                }
                .listStyle(PlainListStyle())
                .scrollContentBackground(.hidden)
            }
            .background(AppTheme.backgroundGradient.ignoresSafeArea())
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("Listings")
                        .font(AppTheme.titleFont)
                        .foregroundColor(.primary)
                }
            }
            .onAppear {
                viewModel.fetchListings()
            }
            .alert(item: Binding(
                get: {
                    if let errorMessage = viewModel.errorMessage {
                        return GenericAlertError(message: errorMessage)
                    }
                    return nil
                },
                set: { _ in viewModel.errorMessage = nil }
            )) { alertError in
                Alert(title: Text("Error"),
                      message: Text(alertError.message),
                      dismissButton: .default(Text("OK")))
            }
            .sheet(item: $selectedListing) { listing in
                ListingDetailView(listing: listing)
            }
        }
    }
}

struct ListingsView_Previews: PreviewProvider {
    static var previews: some View {
        ListingsView()
    }
}
