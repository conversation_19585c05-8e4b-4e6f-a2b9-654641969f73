//
//  ProfileViewHistoryView.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI
import FirebaseAuth

struct ProfileViewHistoryView: View {
    @StateObject private var viewModel = ProfileViewTrackingViewModel()
    @StateObject private var profileVM = ProfileViewModel.shared
    @State private var selectedTimeframe: TimeFrame = .week
    @State private var showingSourceFilter = false
    @State private var selectedSource: ProfileViewSource? = nil
    @State private var navigateToProfileEdit = false
    @State private var selectedCategory: ViewCategory = .all
    @State private var showPremiumUpgrade = false
    @Environment(\.dismiss) private var dismiss

    enum TimeFrame: String, CaseIterable {
        case day = "Today"
        case week = "This Week"
        case month = "This Month"
        case all = "All Time"
    }

    enum ViewCategory: String, CaseIterable {
        case all = "All Views"
        case discovery = "Discovery Views"
        case search = "Search Views"

        var icon: String {
            switch self {
            case .all: return "eye.fill"
            case .discovery: return "shuffle"
            case .search: return "magnifyingglass"
            }
        }

        var description: String {
            switch self {
            case .all: return "All profile views"
            case .discovery: return "Anonymous views from discovery"
            case .search: return "Identified views from search"
            }
        }
    }

    var body: some View {
        ZStack {
            // Background
            AppTheme.backgroundGradient.ignoresSafeArea()

            // Content area - only show for premium users, completely hide for non-premium
            if profileVM.userProfile?.isPremium == true {
                VStack(spacing: 0) {
                    // Stats Header
                    statsHeaderView

                    // Category Filter
                    categoryFilterView

                    // Time Filter
                    timeFilterView

                    // Views List - full access for premium users
                    viewsListView
                }
            } else {
                // Show placeholder content that gets completely covered by paywall
                VStack(spacing: 0) {
                    // Empty placeholder to maintain layout structure
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 100)

                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 50)

                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: 50)

                    Rectangle()
                        .fill(Color.clear)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }

            // Paywall overlay for non-premium users - covers entire screen
            if profileVM.userProfile?.isPremium != true {
                PremiumPaywallOverlay(
                    title: "Profile Views",
                    subtitle: "See who viewed your profile and connect with interested users",
                    onUpgrade: {
                        showPremiumUpgrade = true
                    },
                    onDismiss: {
                        dismiss()
                    }
                )
            }
        }
        .navigationTitle("Profile Views")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Filter") {
                    showingSourceFilter = true
                }
                .foregroundColor(AppTheme.primaryColor)
            }
        }
        .onAppear {
            viewModel.refresh()
            print("📱 ProfileViewHistoryView: View appeared, total views: \(viewModel.totalViewsCount)")
        }
        .refreshable {
            viewModel.refresh()
        }
        .sheet(isPresented: $showingSourceFilter) {
            sourceFilterSheet
        }
        .sheet(isPresented: $showPremiumUpgrade) {
            PremiumUpgradeView()
        }
        .background(
            NavigationLink(
                destination: MyProfileView(),
                isActive: $navigateToProfileEdit
            ) {
                EmptyView()
            }
            .hidden()
            .onChange(of: navigateToProfileEdit) { _, isActive in
                if isActive {
                    print("🚀 ProfileViewHistoryView: NavigationLink activated - navigating to MyProfileView")
                } else {
                    print("🔙 ProfileViewHistoryView: NavigationLink deactivated - returned from MyProfileView")
                }
            }
        )
    }

    // MARK: - Stats Header

    private var statsHeaderView: some View {
        EnhancedCard(shadowStyle: .medium) {
            VStack(spacing: AppTheme.spacing16) {
                HStack {
                    Text("Profile Analytics")
                        .font(AppTheme.headline)
                        .foregroundColor(AppTheme.textPrimary)
                    Spacer()
                }

                HStack(spacing: AppTheme.spacing20) {
                    StatCard(
                        title: "Total Views",
                        value: "\(viewModel.totalViewsCount)",
                        icon: "eye.fill",
                        color: AppTheme.primaryColor
                    )

                    StatCard(
                        title: "This Week",
                        value: "\(viewModel.viewsThisWeek)",
                        icon: "calendar",
                        color: AppTheme.successColor
                    )

                    StatCard(
                        title: "Avg/Day",
                        value: String(format: "%.1f", viewModel.averageViewsPerDay),
                        icon: "chart.line.uptrend.xyaxis",
                        color: AppTheme.accentColor
                    )
                }
            }
        }
        .padding(.horizontal, AppTheme.spacing16)
        .padding(.top, AppTheme.spacing8)
    }

    // MARK: - Category Filter

    private var categoryFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppTheme.spacing12) {
                ForEach(ViewCategory.allCases, id: \.self) { category in
                    categoryButton(for: category)
                }
            }
            .padding(.horizontal, AppTheme.spacing20)
        }
        .padding(.vertical, AppTheme.spacing12)
    }

    private func categoryButton(for category: ViewCategory) -> some View {
        let isSelected = selectedCategory == category

        return Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedCategory = category
            }
        }) {
            categoryButtonContent(for: category, isSelected: isSelected)
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: selectedCategory)
    }

    private func categoryButtonContent(for category: ViewCategory, isSelected: Bool) -> some View {
        HStack(spacing: AppTheme.spacing6) {
            Image(systemName: category.icon)
                .font(.system(size: 14, weight: .medium))
            Text(category.rawValue)
                .font(.system(size: 14, weight: .medium))
        }
        .foregroundColor(isSelected ? .white : AppTheme.textSecondary)
        .padding(.horizontal, AppTheme.spacing16)
        .padding(.vertical, AppTheme.spacing8)
        .background(categoryButtonBackground(isSelected: isSelected))
    }

    private func categoryButtonBackground(isSelected: Bool) -> some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(isSelected ? AppTheme.primaryColor : AppTheme.surfaceSecondary)
            .shadow(
                color: isSelected ? AppTheme.primaryColor.opacity(0.3) : .clear,
                radius: 4,
                x: 0,
                y: 2
            )
    }

    // MARK: - Time Filter

    private var timeFilterView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppTheme.spacing12) {
                ForEach(TimeFrame.allCases, id: \.self) { timeframe in
                    Button(action: {
                        selectedTimeframe = timeframe
                    }) {
                        Text(timeframe.rawValue)
                            .font(AppTheme.body)
                            .padding(.horizontal, AppTheme.spacing16)
                            .padding(.vertical, AppTheme.spacing8)
                            .background(
                                selectedTimeframe == timeframe ?
                                AppTheme.primaryColor : AppTheme.surfaceSecondary
                            )
                            .foregroundColor(
                                selectedTimeframe == timeframe ?
                                .white : AppTheme.textSecondary
                            )
                            .cornerRadius(AppTheme.defaultCornerRadius)
                    }
                }
            }
            .padding(.horizontal, AppTheme.spacing16)
        }
        .padding(.vertical, AppTheme.spacing12)
    }

    // MARK: - Views List

    private var viewsListView: some View {
        Group {
            if viewModel.isLoading {
                LoadingView(message: "Loading profile views...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if filteredViews.isEmpty {
                EmptyStateView(
                    icon: "eye.slash",
                    title: "No Profile Views",
                    subtitle: "Your profile hasn't been viewed yet. Keep your profile active and engaging!",
                    actionTitle: "Improve Profile",
                    action: {
                        print("🔄 ProfileViewHistoryView: Improve Profile button tapped")

                        // Add haptic feedback for better UX
                        HapticFeedbackManager.shared.generateImpact(style: .medium)

                        withAnimation(.easeInOut(duration: 0.3)) {
                            navigateToProfileEdit = true
                        }
                        print("✅ ProfileViewHistoryView: Navigation state set to true")

                        // Fallback: Reset navigation state after delay if navigation doesn't work
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            if navigateToProfileEdit {
                                print("⚠️ ProfileViewHistoryView: Navigation may have failed, resetting state")
                                navigateToProfileEdit = false
                            }
                        }
                    }
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(groupedViews.keys.sorted(by: >), id: \.self) { date in
                        Section(header: sectionHeader(for: date)) {
                            ForEach(groupedViews[date] ?? []) { view in
                                EnhancedProfileViewRow(
                                    view: view,
                                    showLikeButton: selectedCategory == .search || (selectedCategory == .all && view.source == .search),
                                    onLike: { userID in
                                        viewModel.likeUserFromSearch(userID: userID)
                                    }
                                )
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
        .animation(.easeInOut, value: viewModel.isLoading)
    }

    // MARK: - Source Filter Sheet

    private var sourceFilterSheet: some View {
        NavigationView {
            List {
                Section("Filter by Source") {
                    Button("All Sources") {
                        selectedSource = nil
                        showingSourceFilter = false
                    }
                    .foregroundColor(selectedSource == nil ? AppTheme.primaryColor : AppTheme.textPrimary)

                    ForEach(ProfileViewSource.allCases, id: \.self) { source in
                        Button(action: {
                            selectedSource = source
                            showingSourceFilter = false
                        }) {
                            HStack {
                                Image(systemName: source.icon)
                                    .foregroundColor(AppTheme.primaryColor)
                                Text(source.displayName)
                                    .foregroundColor(AppTheme.textPrimary)
                                Spacer()
                                if selectedSource == source {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppTheme.primaryColor)
                                }
                            }
                        }
                    }
                }
            }
            .navigationTitle("Filter Views")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        showingSourceFilter = false
                    }
                }
            }
        }
        .presentationDetents([.medium])
    }

    // MARK: - Helper Views

    private func sectionHeader(for date: Date) -> some View {
        Text(viewModel.formatDate(date))
            .font(AppTheme.subheadline)
            .foregroundColor(AppTheme.textSecondary)
            .textCase(nil)
    }

    // MARK: - Computed Properties

    private var filteredViews: [ProfileViewEvent] {
        var views: [ProfileViewEvent]

        // Filter by category first
        switch selectedCategory {
        case .all:
            views = viewModel.profileViews
        case .discovery:
            views = viewModel.discoveryViews
        case .search:
            views = viewModel.searchViews
        }

        // Filter by source
        if let selectedSource = selectedSource {
            views = views.filter { $0.source == selectedSource }
        }

        // Filter by timeframe
        let now = Date()
        switch selectedTimeframe {
        case .day:
            views = views.filter { Calendar.current.isDate($0.timestamp, inSameDayAs: now) }
        case .week:
            let weekAgo = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
            views = views.filter { $0.timestamp >= weekAgo }
        case .month:
            let monthAgo = Calendar.current.date(byAdding: .month, value: -1, to: now) ?? now
            views = views.filter { $0.timestamp >= monthAgo }
        case .all:
            break // No filtering
        }

        return views.sorted { $0.timestamp > $1.timestamp }
    }

    private var groupedViews: [Date: [ProfileViewEvent]] {
        Dictionary(grouping: filteredViews) { view in
            Calendar.current.startOfDay(for: view.timestamp)
        }
    }
}

// MARK: - Enhanced Profile View Row

struct EnhancedProfileViewRow: View {
    let view: ProfileViewEvent
    let showLikeButton: Bool
    let onLike: (String) -> Void

    @State private var viewerProfile: UserModel?
    @State private var isLoading = false
    @State private var loadError: String?
    @State private var hasLiked = false
    @State private var isLiking = false

    private let profileLoader = ProfileLoaderService.shared

    var body: some View {
        HStack(spacing: AppTheme.spacing12) {
            // Profile Image with loading state
            ProfileImageView(
                imageUrl: viewerProfile?.profileImageUrl,
                size: 50
            )
            .overlay(
                Group {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.7)
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                }
            )

            // View Details
            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                HStack {
                    Group {
                        if isLoading {
                            Text("Loading...")
                                .foregroundColor(AppTheme.textSecondary)
                        } else if let error = loadError {
                            Text("Failed to load")
                                .foregroundColor(AppTheme.errorColor)
                        } else {
                            Text(displayName)
                                .foregroundColor(AppTheme.textPrimary)
                        }
                    }
                    .font(AppTheme.subheadline)

                    Spacer()

                    Text(RelativeDateTimeFormatter().localizedString(for: view.timestamp, relativeTo: Date()))
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                }

                HStack {
                    Image(systemName: view.source.icon)
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: 12))

                    Text(view.source.displayName)
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)

                    if let searchQuery = view.metadata?.searchQuery {
                        Text("• \"\(searchQuery)\"")
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)
                            .lineLimit(1)
                    }

                    Spacer()

                    // Like button for search views
                    if showLikeButton && !hasLiked {
                        likeButton
                    } else if hasLiked {
                        likedIndicator
                    }
                }

                // Show additional profile info if available
                if let profile = viewerProfile {
                    HStack {
                        if let college = profile.collegeName {
                            Text("• \(college)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textTertiary)
                        }
                        if let major = profile.major {
                            Text("• \(major)")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textTertiary)
                        }
                    }
                }
            }
        }
        .padding(.vertical, AppTheme.spacing4)
        .onAppear {
            loadViewerProfile()
        }
    }

    // MARK: - Like Button Components

    private var likeButton: some View {
        Button(action: {
            handleLike()
        }) {
            HStack(spacing: 4) {
                if isLiking {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "heart")
                        .font(.system(size: 14, weight: .medium))
                }
                Text("Like")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppTheme.primaryColor)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)
            )
        }
        .disabled(isLiking)
        .scaleEffect(isLiking ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isLiking)
    }

    private var likedIndicator: some View {
        HStack(spacing: 4) {
            Image(systemName: "heart.fill")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.likeColor)
            Text("Liked")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppTheme.textSecondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppTheme.likeColor.opacity(0.1))
        )
    }

    // MARK: - Actions

    private func handleLike() {
        guard !isLiking else { return }

        isLiking = true

        withAnimation(.easeInOut(duration: 0.3)) {
            onLike(view.viewerUserID)
        }

        // Simulate like completion
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(.spring()) {
                hasLiked = true
                isLiking = false
            }
        }
    }

    private var displayName: String {
        if let profile = viewerProfile {
            let firstName = profile.firstName ?? ""
            let lastName = profile.lastName ?? ""

            if !firstName.isEmpty && !lastName.isEmpty {
                return "\(firstName) \(lastName)"
            } else if !firstName.isEmpty {
                return firstName
            } else if !lastName.isEmpty {
                return lastName
            } else {
                return "Anonymous User"
            }
        }
        return "Unknown User"
    }

    private func loadViewerProfile() {
        guard !view.viewerUserID.isEmpty else {
            loadError = "Invalid viewer ID"
            return
        }

        // Prevent duplicate loading
        guard viewerProfile == nil && !isLoading else { return }

        isLoading = true
        loadError = nil

        print("🔄 ProfileViewRow: Loading viewer profile for ID: \(view.viewerUserID)")

        profileLoader.loadUserProfile(userID: view.viewerUserID) { result in
            DispatchQueue.main.async {
                isLoading = false

                switch result {
                case .success(let profile):
                    print("✅ ProfileViewRow: Successfully loaded viewer profile: \(profile.firstName ?? "Unknown")")
                    viewerProfile = profile
                    loadError = nil
                case .failure(let error):
                    print("❌ ProfileViewRow: Failed to load viewer profile: \(error.localizedDescription)")
                    loadError = error.localizedDescription
                }
            }
        }
    }
}

// MARK: - Preview

struct ProfileViewHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileViewHistoryView()
    }
}
