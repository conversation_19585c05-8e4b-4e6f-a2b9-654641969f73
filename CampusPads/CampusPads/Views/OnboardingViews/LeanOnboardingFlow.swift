import SwiftUI
import FirebaseAuth

// MARK: - Lean Onboarding Flow
// Optimized, simplified onboarding with reusable components

struct LeanOnboardingFlow: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = ProfileViewModel.shared

    // Current step
    @State private var currentStep: OnboardingStep = .emailVerification

    // Form data
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var birthDate = Date()
    @State private var gender = ""
    @State private var height = ""
    @State private var aboutMe = ""
    @State private var gradeLevel = ""
    @State private var major = ""
    @State private var collegeName = ""
    @State private var housingStatus = ""
    @State private var dormType = ""
    @State private var monthlyRentMin = 500
    @State private var monthlyRentMax = 1500
    @State private var budgetMin = 400
    @State private var budgetMax = 1200
    @State private var cleanliness = 3
    @State private var sleepSchedule = ""
    @State private var smoker = false
    @State private var petFriendly = false
    @State private var selectedImages: [UIImage] = []

    var body: some View {
        ZStack {
            OnboardingAnimatedBackground()

            VStack(spacing: 0) {
                // Progress indicator
                VStack(spacing: AppTheme.spacing16) {
                    OnboardingProgressIndicator(
                        currentStep: currentStep.rawValue,
                        totalSteps: OnboardingStep.allCases.count - 1
                    )

                    Text("\(currentStep.rawValue + 1) of \(OnboardingStep.allCases.count)")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                }
                .padding(.top, AppTheme.spacing20)

                // Content with scroll to top functionality
                ScrollViewReader { proxy in
                    ScrollView {
                        OnboardingCard {
                            currentStepView
                        }
                        .id("top")
                    }
                    .onChange(of: currentStep) { _, _ in
                        // Scroll to top when step changes
                        withAnimation(.easeInOut(duration: 0.5)) {
                            proxy.scrollTo("top", anchor: .top)
                        }
                    }
                }

                // Navigation
                navigationButtons
            }
        }
        .navigationBarHidden(true)
    }

    @ViewBuilder
    private var currentStepView: some View {
        switch currentStep {
        case .emailVerification:
            OptimizedEmailVerificationView()
        case .welcome:
            OptimizedWelcomeView()
        case .personalInfo:
            OptimizedPersonalInfoView(firstName: $firstName, lastName: $lastName, birthDate: $birthDate, gender: $gender)
        case .physicalInfo:
            OptimizedPhysicalInfoView(height: $height, aboutMe: $aboutMe)
        case .academicInfo:
            OptimizedAcademicInfoView(gradeLevel: $gradeLevel, major: $major, collegeName: $collegeName)
        case .housingPreferences:
            OptimizedHousingPreferencesView(housingStatus: $housingStatus, dormType: $dormType)
        case .lifestylePreferences:
            OptimizedLifestylePreferencesView(cleanliness: $cleanliness, sleepSchedule: $sleepSchedule, smoker: $smoker, petFriendly: $petFriendly)
        case .interests:
            // Placeholder for interests - lean onboarding skips this
            Text("Interests step - skipped in lean flow")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
        case .goingOutQuiz:
            // Placeholder for going out quiz - lean onboarding skips this
            Text("Going out quiz - skipped in lean flow")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
        case .weekendsQuiz:
            // Placeholder for weekends quiz - lean onboarding skips this
            Text("Weekends quiz - skipped in lean flow")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
        case .phoneQuiz:
            // Placeholder for phone quiz - lean onboarding skips this
            Text("Phone quiz - skipped in lean flow")
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
        case .profilePhotos:
            OptimizedProfilePhotosView(selectedImages: $selectedImages)
        case .completion:
            OptimizedCompletionView()
        }
    }

    private var navigationButtons: some View {
        HStack(spacing: AppTheme.spacing16) {
            if currentStep != .emailVerification {
                Button("Back") {
                    goToPreviousStep()
                }
                .buttonStyle(SecondaryButtonStyle())
                .frame(maxWidth: .infinity)
            }

            Button(currentStep == .completion ? "Get Started" : "Continue") {
                if currentStep == .completion {
                    completeOnboarding()
                } else {
                    goToNextStep()
                }
            }
            .buttonStyle(PrimaryButtonStyle())
            .frame(maxWidth: .infinity)
            .disabled(!canProceed)
        }
        .padding(AppTheme.spacing20)
    }

    private var canProceed: Bool {
        switch currentStep {
        case .emailVerification:
            return Auth.auth().currentUser?.isEmailVerified ?? false
        case .welcome:
            return true
        case .personalInfo:
            return !firstName.isEmpty && !lastName.isEmpty && !gender.isEmpty
        case .physicalInfo:
            return !height.isEmpty && !aboutMe.isEmpty
        case .academicInfo:
            return !gradeLevel.isEmpty && !major.isEmpty && !collegeName.isEmpty
        case .housingPreferences:
            return !housingStatus.isEmpty && !dormType.isEmpty
        case .lifestylePreferences:
            return !sleepSchedule.isEmpty
        case .interests:
            return true // Skip in lean flow
        case .goingOutQuiz:
            return true // Skip in lean flow
        case .weekendsQuiz:
            return true // Skip in lean flow
        case .phoneQuiz:
            return true // Skip in lean flow
        case .profilePhotos:
            return selectedImages.count >= 1
        case .completion:
            return true
        }
    }

    private func goToNextStep() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            var nextStepRawValue = currentStep.rawValue + 1

            // Skip quiz steps in lean flow
            while let step = OnboardingStep(rawValue: nextStepRawValue),
                  isQuizStep(step) {
                nextStepRawValue += 1
            }

            if let nextStep = OnboardingStep(rawValue: nextStepRawValue) {
                currentStep = nextStep
            }
        }
        HapticFeedbackManager.shared.generateImpact(style: .light)
    }

    private func goToPreviousStep() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            var previousStepRawValue = currentStep.rawValue - 1

            // Skip quiz steps in lean flow
            while let step = OnboardingStep(rawValue: previousStepRawValue),
                  isQuizStep(step) {
                previousStepRawValue -= 1
            }

            if let previousStep = OnboardingStep(rawValue: previousStepRawValue) {
                currentStep = previousStep
            }
        }
        HapticFeedbackManager.shared.generateImpact(style: .light)
    }

    private func isQuizStep(_ step: OnboardingStep) -> Bool {
        switch step {
        case .interests, .goingOutQuiz, .weekendsQuiz, .phoneQuiz:
            return true
        default:
            return false
        }
    }

    private func completeOnboarding() {
        saveProfileData()
        UserDefaults.standard.set(true, forKey: "onboardingCompleted")
        presentationMode.wrappedValue.dismiss()
        HapticFeedbackManager.shared.generateNotification(.success)
    }

    private func saveProfileData() {
        print("💾 Saving lean profile data...")

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        let newProfile = UserModel(
            email: Auth.auth().currentUser?.email ?? "<EMAIL>",
            isEmailVerified: Auth.auth().currentUser?.isEmailVerified ?? false,
            aboutMe: aboutMe,
            firstName: firstName,
            lastName: lastName,
            dateOfBirth: birthDate,
            gender: gender,
            height: height,
            gradeLevel: gradeLevel,
            major: major,
            collegeName: collegeName,
            housingStatus: housingStatus,
            dormType: dormType,
            monthlyRentMin: Double(monthlyRentMin),
            monthlyRentMax: Double(monthlyRentMax),
            budgetMin: Double(budgetMin),
            budgetMax: Double(budgetMax),
            cleanliness: cleanliness,
            sleepSchedule: sleepSchedule,
            smoker: smoker,
            petFriendly: petFriendly
        )

        viewModel.updateUserProfile(updatedProfile: newProfile) { result in
            switch result {
            case .success:
                print("✅ Lean profile created successfully!")
            case .failure(let error):
                print("❌ Error creating profile: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Lean Profile Setup View
struct LeanProfileSetupOnboardingView: View {
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        Group {
            if UserDefaults.standard.bool(forKey: "onboardingCompleted") {
                Color.clear
                    .onAppear {
                        presentationMode.wrappedValue.dismiss()
                    }
            } else {
                LeanOnboardingFlow()
            }
        }
    }
}

struct LeanOnboardingFlow_Previews: PreviewProvider {
    static var previews: some View {
        LeanOnboardingFlow()
            .preferredColorScheme(.dark)
    }
}
