//
//  ChatNavigationWrapper.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

/// FIXED: Wrapper view to handle proper chat navigation from match celebrations
struct ChatNavigationWrapper: View {
    let currentUserID: String
    let chatPartnerID: String
    let chatPartner: UserModel
    let onClose: () -> Void
    
    @State private var chatID: String?
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    // Loading state while creating/finding chat
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)
                        
                        Text("Starting conversation...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
                    
                } else if let chatID = chatID {
                    // Chat is ready - show conversation view
                    ChatConversationView(
                        viewModel: ChatConversationViewModel(chatID: chatID),
                        chatPartnerID: chatPartnerID
                    )
                    
                } else {
                    // Error state
                    VStack(spacing: 20) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)
                        
                        Text("Unable to start conversation")
                            .font(.headline)
                        
                        if let errorMessage = errorMessage {
                            Text(errorMessage)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        
                        Button("Try Again") {
                            createOrGetChat()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        onClose()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if let chatPartner = chatPartner.firstName {
                        Text(chatPartner)
                            .font(.headline)
                            .foregroundColor(.primary)
                    }
                }
            }
        }
        .onAppear {
            createOrGetChat()
        }
    }
    
    // MARK: - Chat Creation
    
    /// Create or get existing chat between users
    private func createOrGetChat() {
        isLoading = true
        errorMessage = nil
        
        print("💬 ChatNavigationWrapper: Creating chat between \(currentUserID) and \(chatPartnerID)")
        
        ChatManager.shared.createOrGetChat(between: currentUserID, and: chatPartnerID) { result in
            DispatchQueue.main.async {
                isLoading = false
                
                switch result {
                case .success(let foundChatID):
                    print("✅ ChatNavigationWrapper: Successfully created/found chat: \(foundChatID)")
                    
                    // Mark conversation as started
                    ChatManager.shared.markConversationStarted(for: [currentUserID, chatPartnerID], chatID: foundChatID)
                    
                    // Set chat ID to show conversation
                    chatID = foundChatID
                    
                case .failure(let error):
                    print("❌ ChatNavigationWrapper: Failed to create chat: \(error.localizedDescription)")
                    errorMessage = "Failed to create conversation: \(error.localizedDescription)"
                }
            }
        }
    }
}

// MARK: - Preview

#Preview {
    ChatNavigationWrapper(
        currentUserID: "user1",
        chatPartnerID: "user2",
        chatPartner: UserModel(email: "<EMAIL>", isEmailVerified: true, firstName: "Alex"),
        onClose: {}
    )
}
