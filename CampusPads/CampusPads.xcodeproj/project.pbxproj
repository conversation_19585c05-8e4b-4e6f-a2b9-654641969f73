// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		47000D582DF9D74400C0040E /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D572DF9D74400C0040E /* FirebaseAuth */; };
		47000D5A2DF9D74400C0040E /* FirebaseDatabase in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D592DF9D74400C0040E /* FirebaseDatabase */; };
		47000D5C2DF9D74400C0040E /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D5B2DF9D74400C0040E /* FirebaseFirestore */; };
		47000D5E2DF9D74400C0040E /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D5D2DF9D74400C0040E /* FirebaseMessaging */; };
		47000D602DF9D74400C0040E /* FirebaseStorage in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D5F2DF9D74400C0040E /* FirebaseStorage */; };
		47000D622DF9D74400C0040E /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D612DF9D74400C0040E /* FirebaseAnalytics */; };
		47000D642DF9D74400C0040E /* FirebaseInAppMessaging-Beta in Frameworks */ = {isa = PBXBuildFile; productRef = 47000D632DF9D74400C0040E /* FirebaseInAppMessaging-Beta */; };
		4702A58B2DF9E15100BC4610 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = 4702A58A2DF9E15100BC4610 /* FirebaseCrashlytics */; };
		4702A58D2DF9E15900BC4610 /* FirebasePerformance in Frameworks */ = {isa = PBXBuildFile; productRef = 4702A58C2DF9E15900BC4610 /* FirebasePerformance */; };
		4702A58F2DF9E17400BC4610 /* FirebaseFirestoreSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 4702A58E2DF9E17400BC4610 /* FirebaseFirestoreSwift */; };
		4702A5912DF9E18A00BC4610 /* FirebaseFirestoreCombine-Community in Frameworks */ = {isa = PBXBuildFile; productRef = 4702A5902DF9E18A00BC4610 /* FirebaseFirestoreCombine-Community */; };
		47DDA7542DFFC3FD00D5D37C /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 47DDA7532DFFC3FD00D5D37C /* StoreKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		47000D392DF9D64100C0040E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 47000D1F2DF9D63E00C0040E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 47000D262DF9D63E00C0040E;
			remoteInfo = CampusPads;
		};
		47000D432DF9D64100C0040E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 47000D1F2DF9D63E00C0040E /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 47000D262DF9D63E00C0040E;
			remoteInfo = CampusPads;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		47000D272DF9D63E00C0040E /* CampusPads.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CampusPads.app; sourceTree = BUILT_PRODUCTS_DIR; };
		47000D382DF9D64100C0040E /* CampusPadsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CampusPadsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		47000D422DF9D64100C0040E /* CampusPadsUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CampusPadsUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		47DDA7532DFFC3FD00D5D37C /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/StoreKit.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		47000D4A2DF9D64100C0040E /* Exceptions for "CampusPads" folder in "CampusPads" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 47000D262DF9D63E00C0040E /* CampusPads */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		47000D292DF9D63E00C0040E /* CampusPads */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				47000D4A2DF9D64100C0040E /* Exceptions for "CampusPads" folder in "CampusPads" target */,
			);
			path = CampusPads;
			sourceTree = "<group>";
		};
		47000D3B2DF9D64100C0040E /* CampusPadsTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CampusPadsTests;
			sourceTree = "<group>";
		};
		47000D452DF9D64100C0040E /* CampusPadsUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = CampusPadsUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		47000D242DF9D63E00C0040E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				47000D5E2DF9D74400C0040E /* FirebaseMessaging in Frameworks */,
				47000D5A2DF9D74400C0040E /* FirebaseDatabase in Frameworks */,
				4702A58D2DF9E15900BC4610 /* FirebasePerformance in Frameworks */,
				47DDA7542DFFC3FD00D5D37C /* StoreKit.framework in Frameworks */,
				47000D582DF9D74400C0040E /* FirebaseAuth in Frameworks */,
				47000D602DF9D74400C0040E /* FirebaseStorage in Frameworks */,
				4702A5912DF9E18A00BC4610 /* FirebaseFirestoreCombine-Community in Frameworks */,
				4702A58F2DF9E17400BC4610 /* FirebaseFirestoreSwift in Frameworks */,
				47000D5C2DF9D74400C0040E /* FirebaseFirestore in Frameworks */,
				4702A58B2DF9E15100BC4610 /* FirebaseCrashlytics in Frameworks */,
				47000D622DF9D74400C0040E /* FirebaseAnalytics in Frameworks */,
				47000D642DF9D74400C0040E /* FirebaseInAppMessaging-Beta in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D352DF9D64100C0040E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D3F2DF9D64100C0040E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		47000D1E2DF9D63E00C0040E = {
			isa = PBXGroup;
			children = (
				47000D292DF9D63E00C0040E /* CampusPads */,
				47000D3B2DF9D64100C0040E /* CampusPadsTests */,
				47000D452DF9D64100C0040E /* CampusPadsUITests */,
				4702A5892DF9E15100BC4610 /* Frameworks */,
				47000D282DF9D63E00C0040E /* Products */,
			);
			sourceTree = "<group>";
		};
		47000D282DF9D63E00C0040E /* Products */ = {
			isa = PBXGroup;
			children = (
				47000D272DF9D63E00C0040E /* CampusPads.app */,
				47000D382DF9D64100C0040E /* CampusPadsTests.xctest */,
				47000D422DF9D64100C0040E /* CampusPadsUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4702A5892DF9E15100BC4610 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				47DDA7532DFFC3FD00D5D37C /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		47000D262DF9D63E00C0040E /* CampusPads */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47000D4B2DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPads" */;
			buildPhases = (
				47000D232DF9D63E00C0040E /* Sources */,
				47000D242DF9D63E00C0040E /* Frameworks */,
				47000D252DF9D63E00C0040E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				47000D292DF9D63E00C0040E /* CampusPads */,
			);
			name = CampusPads;
			packageProductDependencies = (
				47000D572DF9D74400C0040E /* FirebaseAuth */,
				47000D592DF9D74400C0040E /* FirebaseDatabase */,
				47000D5B2DF9D74400C0040E /* FirebaseFirestore */,
				47000D5D2DF9D74400C0040E /* FirebaseMessaging */,
				47000D5F2DF9D74400C0040E /* FirebaseStorage */,
				47000D612DF9D74400C0040E /* FirebaseAnalytics */,
				47000D632DF9D74400C0040E /* FirebaseInAppMessaging-Beta */,
				4702A58A2DF9E15100BC4610 /* FirebaseCrashlytics */,
				4702A58C2DF9E15900BC4610 /* FirebasePerformance */,
				4702A58E2DF9E17400BC4610 /* FirebaseFirestoreSwift */,
				4702A5902DF9E18A00BC4610 /* FirebaseFirestoreCombine-Community */,
			);
			productName = CampusPads;
			productReference = 47000D272DF9D63E00C0040E /* CampusPads.app */;
			productType = "com.apple.product-type.application";
		};
		47000D372DF9D64100C0040E /* CampusPadsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47000D502DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPadsTests" */;
			buildPhases = (
				47000D342DF9D64100C0040E /* Sources */,
				47000D352DF9D64100C0040E /* Frameworks */,
				47000D362DF9D64100C0040E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				47000D3A2DF9D64100C0040E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				47000D3B2DF9D64100C0040E /* CampusPadsTests */,
			);
			name = CampusPadsTests;
			packageProductDependencies = (
			);
			productName = CampusPadsTests;
			productReference = 47000D382DF9D64100C0040E /* CampusPadsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		47000D412DF9D64100C0040E /* CampusPadsUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 47000D532DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPadsUITests" */;
			buildPhases = (
				47000D3E2DF9D64100C0040E /* Sources */,
				47000D3F2DF9D64100C0040E /* Frameworks */,
				47000D402DF9D64100C0040E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				47000D442DF9D64100C0040E /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				47000D452DF9D64100C0040E /* CampusPadsUITests */,
			);
			name = CampusPadsUITests;
			packageProductDependencies = (
			);
			productName = CampusPadsUITests;
			productReference = 47000D422DF9D64100C0040E /* CampusPadsUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		47000D1F2DF9D63E00C0040E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					47000D262DF9D63E00C0040E = {
						CreatedOnToolsVersion = 16.4;
					};
					47000D372DF9D64100C0040E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 47000D262DF9D63E00C0040E;
					};
					47000D412DF9D64100C0040E = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 47000D262DF9D63E00C0040E;
					};
				};
			};
			buildConfigurationList = 47000D222DF9D63E00C0040E /* Build configuration list for PBXProject "CampusPads" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 47000D1E2DF9D63E00C0040E;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 47000D282DF9D63E00C0040E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				47000D262DF9D63E00C0040E /* CampusPads */,
				47000D372DF9D64100C0040E /* CampusPadsTests */,
				47000D412DF9D64100C0040E /* CampusPadsUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		47000D252DF9D63E00C0040E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D362DF9D64100C0040E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D402DF9D64100C0040E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		47000D232DF9D63E00C0040E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D342DF9D64100C0040E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		47000D3E2DF9D64100C0040E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		47000D3A2DF9D64100C0040E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 47000D262DF9D63E00C0040E /* CampusPads */;
			targetProxy = 47000D392DF9D64100C0040E /* PBXContainerItemProxy */;
		};
		47000D442DF9D64100C0040E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 47000D262DF9D63E00C0040E /* CampusPads */;
			targetProxy = 47000D432DF9D64100C0040E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		47000D4C2DF9D64100C0040E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CampusPads/CampusPads.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CampusPads/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CampusPads;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.social-networking";
				INFOPLIST_KEY_NSCameraUsageDescription = "CampusPads needs camera access to let you take profile photos and verify your identity for safety.";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "CampusPads uses your location to show you potential roommates and housing options near your college campus.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "CampusPads uses location services only to help you find nearby colleges and validate addresses you enter. Your precise location is never tracked or shared.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "CampusPads needs photo library access to let you select profile photos and share images in conversations.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = NO;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		47000D4D2DF9D64100C0040E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CampusPads/CampusPads.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CampusPads/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = CampusPads;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.social-networking";
				INFOPLIST_KEY_NSCameraUsageDescription = "CampusPads needs camera access to let you take profile photos and verify your identity for safety.";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "CampusPads uses your location to show you potential roommates and housing options near your college campus.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "CampusPads uses location services only to help you find nearby colleges and validate addresses you enter. Your precise location is never tracked or shared.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "CampusPads needs photo library access to let you select profile photos and share images in conversations.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIRequiresFullScreen = NO;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsApp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		47000D4E2DF9D64100C0040E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = UX9F52X65X;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		47000D4F2DF9D64100C0040E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = UX9F52X65X;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		47000D512DF9D64100C0040E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CampusPads.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CampusPads";
			};
			name = Debug;
		};
		47000D522DF9D64100C0040E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CampusPads.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CampusPads";
			};
			name = Release;
		};
		47000D542DF9D64100C0040E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CampusPads;
			};
			name = Debug;
		};
		47000D552DF9D64100C0040E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UX9F52X65X;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.CampusPads.CampusPadsAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CampusPads;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		47000D222DF9D63E00C0040E /* Build configuration list for PBXProject "CampusPads" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47000D4E2DF9D64100C0040E /* Debug */,
				47000D4F2DF9D64100C0040E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47000D4B2DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPads" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47000D4C2DF9D64100C0040E /* Debug */,
				47000D4D2DF9D64100C0040E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47000D502DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPadsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47000D512DF9D64100C0040E /* Debug */,
				47000D522DF9D64100C0040E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		47000D532DF9D64100C0040E /* Build configuration list for PBXNativeTarget "CampusPadsUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47000D542DF9D64100C0040E /* Debug */,
				47000D552DF9D64100C0040E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 10.29.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		47000D572DF9D74400C0040E /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		47000D592DF9D74400C0040E /* FirebaseDatabase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseDatabase;
		};
		47000D5B2DF9D74400C0040E /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		47000D5D2DF9D74400C0040E /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		47000D5F2DF9D74400C0040E /* FirebaseStorage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseStorage;
		};
		47000D612DF9D74400C0040E /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		47000D632DF9D74400C0040E /* FirebaseInAppMessaging-Beta */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = "FirebaseInAppMessaging-Beta";
		};
		4702A58A2DF9E15100BC4610 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		4702A58C2DF9E15900BC4610 /* FirebasePerformance */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebasePerformance;
		};
		4702A58E2DF9E17400BC4610 /* FirebaseFirestoreSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestoreSwift;
		};
		4702A5902DF9E18A00BC4610 /* FirebaseFirestoreCombine-Community */ = {
			isa = XCSwiftPackageProductDependency;
			package = 47000D562DF9D74400C0040E /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = "FirebaseFirestoreCombine-Community";
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 47000D1F2DF9D63E00C0040E /* Project object */;
}
