//
//  SplashView.swift
//  CampusPads
//
//  Beautiful animated splash screen with app icon and loading states
//

import SwiftUI

struct SplashView: View {
    @State private var isAnimating = false
    @State private var showAppName = false
    @State private var showTagline = false
    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var backgroundOpacity: Double = 0.0
    @State private var iconScale: CGFloat = 0.5
    @State private var loadingProgress: CGFloat = 0.0
    
    let onComplete: () -> Void
    
    var body: some View {
        ZStack {
            // Stunning animated background
            AnimatedBackground()
                .opacity(backgroundOpacity)
            
            // Main content
            VStack(spacing: 40) {
                Spacer()
                
                // App Icon with animations
                appIconView
                
                // App name and tagline
                appInfoView
                
                Spacer()
                
                // Loading indicator
                loadingIndicatorView
                
                Spacer(minLength: 80)
            }
            .padding(.horizontal, 40)
        }
        .onAppear {
            startAnimationSequence()
        }
    }
    
    // MARK: - App Icon View
    private var appIconView: some View {
        ZStack {
            // Outer glow ring
            Circle()
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            AppTheme.primaryColor.opacity(0.6),
                            AppTheme.accentColor.opacity(0.4),
                            Color.clear
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 3
                )
                .frame(width: 160, height: 160)
                .rotationEffect(.degrees(rotationAngle))
                .scaleEffect(pulseScale)
            
            // App icon background
            Circle()
                .fill(AppTheme.sexyGradient)
                .frame(width: 120, height: 120)
                .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 20, x: 0, y: 10)
                .scaleEffect(iconScale)
            
            // Icon content - Modern house/roommate symbol
            VStack(spacing: 8) {
                // House icon with modern twist
                ZStack {
                    // House base
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.white)
                        .frame(width: 40, height: 32)
                    
                    // House roof
                    Triangle()
                        .fill(Color.white)
                        .frame(width: 48, height: 20)
                        .offset(y: -16)
                    
                    // Heart in center (representing connection)
                    Image(systemName: "heart.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(AppTheme.accentColor)
                        .offset(y: 2)
                }
                
                // Two dots representing roommates
                HStack(spacing: 12) {
                    Circle()
                        .fill(Color.white)
                        .frame(width: 8, height: 8)
                    
                    Circle()
                        .fill(Color.white)
                        .frame(width: 8, height: 8)
                }
            }
            .scaleEffect(iconScale)
        }
        .animation(.spring(response: 0.8, dampingFraction: 0.6), value: iconScale)
        .animation(.linear(duration: 3.0).repeatForever(autoreverses: false), value: rotationAngle)
        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseScale)
    }
    
    // MARK: - App Info View
    private var appInfoView: some View {
        VStack(spacing: 16) {
            // App name
            if showAppName {
                Text(AppConfiguration.App.name)
                    .font(.custom("AvenirNext-Bold", size: 36))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .opacity
                    ))
            }
            
            // Tagline
            if showTagline {
                Text("Find Your Perfect Roommate")
                    .font(.custom("AvenirNext-Medium", size: 18))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .opacity
                    ))
            }
        }
    }
    
    // MARK: - Loading Indicator
    private var loadingIndicatorView: some View {
        VStack(spacing: 20) {
            // Progress bar
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.white.opacity(0.2))
                    .frame(width: 200, height: 8)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 200 * loadingProgress, height: 8)
                    .animation(.easeInOut(duration: 0.3), value: loadingProgress)
            }
            
            // Loading text
            Text("Loading your perfect match...")
                .font(.custom("AvenirNext-Regular", size: 14))
                .foregroundColor(AppTheme.textSecondary)
                .opacity(0.8)
        }
    }
    
    // MARK: - Animation Sequence
    private func startAnimationSequence() {
        // Background fade in
        withAnimation(.easeIn(duration: 0.5)) {
            backgroundOpacity = 1.0
        }
        
        // Icon scale in
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
                iconScale = 1.0
            }
        }
        
        // Start continuous animations
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            rotationAngle = 360
            pulseScale = 1.1
        }
        
        // Show app name
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showAppName = true
            }
        }
        
        // Show tagline
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                showTagline = true
            }
        }
        
        // Start loading progress
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            startLoadingProgress()
        }
    }
    
    private func startLoadingProgress() {
        let timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            loadingProgress += 0.05
            
            if loadingProgress >= 1.0 {
                timer.invalidate()
                
                // Complete splash screen
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    withAnimation(.easeOut(duration: 0.5)) {
                        onComplete()
                    }
                }
            }
        }
        timer.fire()
    }
}

// MARK: - Triangle Shape for House Roof
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        path.move(to: CGPoint(x: rect.midX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.closeSubpath()
        
        return path
    }
}

// MARK: - Preview
struct SplashView_Previews: PreviewProvider {
    static var previews: some View {
        SplashView {
            print("Splash completed")
        }
    }
}
