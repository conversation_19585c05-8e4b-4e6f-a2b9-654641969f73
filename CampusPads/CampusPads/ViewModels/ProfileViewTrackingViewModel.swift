//
//  ProfileViewTrackingViewModel.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseAuth
import FirebaseFirestore
import Combine

/// ViewModel for managing profile view tracking and display
@MainActor
class ProfileViewTrackingViewModel: ObservableObject {
    @Published var profileViews: [ProfileViewEvent] = []
    @Published var viewStats: ProfileViewStats = ProfileViewStats()
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    private let trackingService = ProfileViewTrackingService.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Public Methods

    /// Records a profile view
    /// - Parameters:
    ///   - viewedUserID: The ID of the user whose profile was viewed
    ///   - source: The source of the view
    ///   - searchQuery: Optional search query if view came from search
    ///   - viewDuration: Optional duration of the view
    func recordProfileView(
        viewedUserID: String,
        source: ProfileViewSource,
        searchQuery: String? = nil,
        viewDuration: TimeInterval? = nil
    ) {
        let metadata = ProfileViewMetadata(
            searchQuery: searchQuery,
            viewDuration: viewDuration,
            hadInteraction: viewDuration != nil && viewDuration! > 3.0, // Consider 3+ seconds as interaction
            deviceType: "iOS",
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        )

        trackingService.recordProfileView(
            viewedUserID: viewedUserID,
            source: source,
            metadata: metadata
        )
    }

    /// Loads profile views for the current user
    func loadMyProfileViews() {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            errorMessage = "User not authenticated"
            return
        }

        isLoading = true
        errorMessage = nil

        trackingService.fetchProfileViews(for: currentUserID) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false

                switch result {
                case .success(let views):
                    self?.profileViews = views
                case .failure(let error):
                    self?.errorMessage = "Failed to load profile views: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Loads view statistics for the current user
    func loadViewStats() {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            errorMessage = "User not authenticated"
            return
        }

        trackingService.fetchViewStats(for: currentUserID) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let stats):
                    self?.viewStats = stats
                case .failure(let error):
                    self?.errorMessage = "Failed to load view stats: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Refreshes both profile views and stats
    func refresh() {
        loadMyProfileViews()
        loadViewStats()
    }

    /// Likes a user from search views and adds them to discovery stack
    /// - Parameter userID: The ID of the user to like
    func likeUserFromSearch(userID: String) {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            errorMessage = "User not authenticated"
            return
        }

        print("🔄 ProfileViewTrackingViewModel: Liking user \(userID) from search")

        // Load the user first, then record the swipe and add to discovery
        let db = Firestore.firestore()
        db.collection("users").document(userID).getDocument { [weak self] document, error in
            guard let self = self else { return }

            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to load user: \(error.localizedDescription)"
                }
                print("❌ ProfileViewTrackingViewModel: Failed to load user for like: \(error.localizedDescription)")
                return
            }

            guard let document = document, document.exists else {
                DispatchQueue.main.async {
                    self.errorMessage = "User not found"
                }
                print("❌ ProfileViewTrackingViewModel: User document not found: \(userID)")
                return
            }

            do {
                let user = try document.data(as: UserModel.self)
                print("✅ ProfileViewTrackingViewModel: Successfully loaded user \(user.firstName ?? "Unknown") for like")

                // Use MatchingViewModel to record the swipe (this will handle mutual match detection)
                Task { @MainActor in
                    MatchingViewModel.shared.swipeRight(on: user)

                    // Add to discovery stack so they appear in swipe interface
                    MatchingViewModel.shared.addUserToDiscoveryStack(userID: userID)

                    print("✅ ProfileViewTrackingViewModel: Successfully liked user from search and added to discovery")
                }

            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to decode user data: \(error.localizedDescription)"
                }
                print("❌ ProfileViewTrackingViewModel: Failed to decode user: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - Computed Properties

    /// Recent profile views (last 7 days)
    var recentViews: [ProfileViewEvent] {
        let sevenDaysAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return profileViews.filter { $0.timestamp >= sevenDaysAgo }
    }

    /// Views grouped by date
    var viewsByDate: [Date: [ProfileViewEvent]] {
        Dictionary(grouping: profileViews) { view in
            Calendar.current.startOfDay(for: view.timestamp)
        }
    }

    /// Views grouped by source
    var viewsBySource: [ProfileViewSource: [ProfileViewEvent]] {
        Dictionary(grouping: profileViews) { $0.source }
    }

    /// Total views count
    var totalViewsCount: Int {
        viewStats.totalViews
    }

    /// Views this week
    var viewsThisWeek: Int {
        let weekAgo = Calendar.current.date(byAdding: .weekOfYear, value: -1, to: Date()) ?? Date()
        return profileViews.filter { $0.timestamp >= weekAgo }.count
    }

    /// Views in last 7 days
    var viewsLast7Days: Int {
        let sevenDaysAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        return profileViews.filter { $0.timestamp >= sevenDaysAgo }.count
    }

    /// Most recent viewer profile info
    var mostRecentViewer: ProfileViewEvent? {
        return profileViews.first
    }

    /// Most common view source
    var topViewSource: ProfileViewSource? {
        viewStats.viewsBySource.max(by: { $0.value < $1.value })?.key
    }

    /// Average views per day (last 30 days)
    var averageViewsPerDay: Double {
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let recentViews = profileViews.filter { $0.timestamp >= thirtyDaysAgo }
        return Double(recentViews.count) / 30.0
    }

    /// Discovery views (anonymous from swipe interface)
    var discoveryViews: [ProfileViewEvent] {
        return profileViews.filter { $0.source == .discovery }
    }

    /// Search views (identified from search functionality)
    var searchViews: [ProfileViewEvent] {
        return profileViews.filter { $0.source == .search }
    }

    /// Summary data for home page display
    var homeSummary: ProfileViewHomeSummary {
        return ProfileViewHomeSummary(
            totalViews: totalViewsCount,
            recentViews: viewsLast7Days,
            mostRecentViewer: mostRecentViewer,
            isLoading: isLoading
        )
    }

    // MARK: - Helper Methods

    /// Formats a date for display
    func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current

        if calendar.isDate(date, inSameDayAs: Date()) {
            return "Today"
        } else if let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()), calendar.isDate(date, inSameDayAs: yesterday) {
            return "Yesterday"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            formatter.dateFormat = "EEEE" // Day of week
            return formatter.string(from: date)
        } else {
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        }
    }

    /// Formats a time for display
    func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    /// Gets relative time string (e.g., "2 hours ago")
    func relativeTimeString(for date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}
