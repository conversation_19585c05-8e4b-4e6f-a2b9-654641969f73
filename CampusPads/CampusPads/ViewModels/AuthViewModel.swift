import SwiftUI
import FirebaseAuth
import FirebaseFirestore
import Combine

@MainActor
class AuthViewModel: ObservableObject {
    @Published var email: String = ""
    @Published var password: String = ""
    @Published var errorMessage: String?
    @Published var passwordResetMessage: String?
    @Published var isLoading: Bool = false
    @Published var userSession: FirebaseAuth.User? = nil
    @Published var isEmailValid: Bool = false
    @Published var isPasswordValid: Bool = false

    // Age verification properties
    @Published var showAgeVerification: Bool = false
    @Published var dateOfBirth: Date?
    @Published var isAgeVerified: Bool = false

    private var cancellables = Set<AnyCancellable>()
    private let authService = FirebaseAuthService()

    init() {
        self.userSession = Auth.auth().currentUser
        setupValidation()
    }

    private func setupValidation() {
        // Email validation
        $email
            .map { email in
                self.isValidEmail(email)
            }
            .assign(to: \.isEmailValid, on: self)
            .store(in: &cancellables)

        // Password validation - match FirebaseAuthService requirements
        $password
            .map { password in
                self.isValidPassword(password)
            }
            .assign(to: \.isPasswordValid, on: self)
            .store(in: &cancellables)
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    private func isValidPassword(_ password: String) -> Bool {
        // Match the same validation as FirebaseAuthService
        guard password.count >= 8 else { return false }

        let hasUppercase = password.range(of: "[A-Z]", options: .regularExpression) != nil
        let hasLowercase = password.range(of: "[a-z]", options: .regularExpression) != nil
        let hasNumber = password.range(of: "[0-9]", options: .regularExpression) != nil
        let hasSpecialChar = password.range(of: "[!@#$%^&*(),.?\":{}|<>]", options: .regularExpression) != nil

        return hasUppercase && hasLowercase && hasNumber && hasSpecialChar
    }

    /// Starts listening for auth state changes.
    func listenToAuthState() {
        _ = Auth.auth().addStateDidChangeListener { [weak self] _, user in
            self?.userSession = user
        }
    }

    func signUp() {
        // Clear previous errors
        errorMessage = nil
        passwordResetMessage = nil

        Logger.shared.info("Starting signup process - Email: \(email)")
        Logger.shared.debug("Email valid: \(isEmailValid), Password valid: \(isPasswordValid)")

        // Validate inputs before proceeding
        guard isEmailValid else {
            Logger.shared.warning("Email validation failed")
            errorMessage = "Please enter a valid email address"
            return
        }

        guard isPasswordValid else {
            Logger.shared.warning("Password validation failed")
            errorMessage = "Password must be at least 8 characters long"
            return
        }

        // Show age verification before creating account
        Logger.shared.info("Validation passed, showing age verification")
        showAgeVerification = true
    }

    func completeSignUpWithAge(dateOfBirth: Date) {
        self.dateOfBirth = dateOfBirth
        self.isAgeVerified = true

        Logger.shared.info("Age verified, creating Firebase account")
        isLoading = true

        // Use the enhanced FirebaseAuthService with age verification
        guard let dateOfBirth = self.dateOfBirth else {
            Logger.shared.error("Date of birth not set during signup")
            self.errorMessage = "Age verification required"
            return
        }

        authService.signUpWithEmailAndAge(email: email, password: password, dateOfBirth: dateOfBirth) { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoading = false

                switch result {
                case .success:
                    Logger.shared.info("Sign up successful, setting user session")
                    self?.userSession = Auth.auth().currentUser
                    self?.errorMessage = nil

                    // CRITICAL: Clear onboarding completion flag for new signups
                    UserDefaults.standard.set(false, forKey: "onboardingCompleted")
                    UserDefaults.standard.synchronize() // Force immediate save
                    let flagAfterSet = UserDefaults.standard.bool(forKey: "onboardingCompleted")
                    Logger.shared.debug("Cleared onboarding completion flag for new signup: \(flagAfterSet)")

                    // Clear form fields on success
                    self?.email = ""
                    self?.password = ""
                    Logger.shared.debug("User session set to: \(Auth.auth().currentUser?.uid ?? "nil")")

                case .failure(let error):
                    Logger.shared.error("Sign up failed: \(error.localizedDescription)")
                    self?.errorMessage = self?.friendlyErrorMessage(for: error) ?? error.localizedDescription
                }
            }
        }
    }

    func cancelAgeVerification() {
        showAgeVerification = false
        dateOfBirth = nil
        isAgeVerified = false
        Logger.shared.info("Age verification cancelled")
    }

    func signIn() {
        // Clear previous errors
        errorMessage = nil
        passwordResetMessage = nil

        // Basic validation
        guard !email.isEmpty else {
            errorMessage = "Please enter your email address"
            return
        }

        guard !password.isEmpty else {
            errorMessage = "Please enter your password"
            return
        }

        isLoading = true

        Auth.auth().signIn(withEmail: email, password: password) { [weak self] authResult, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    // Provide more user-friendly error messages
                    self?.errorMessage = self?.friendlyErrorMessage(for: error) ?? error.localizedDescription
                    return
                }

                self?.userSession = authResult?.user
                self?.errorMessage = nil

                // Clear form fields on success
                self?.email = ""
                self?.password = ""
            }
        }
    }

    private func friendlyErrorMessage(for error: Error) -> String? {
        let nsError = error as NSError

        switch nsError.code {
        case AuthErrorCode.userNotFound.rawValue:
            return "No account found with this email address"
        case AuthErrorCode.wrongPassword.rawValue:
            return "Incorrect password. Please try again"
        case AuthErrorCode.invalidEmail.rawValue:
            return "Please enter a valid email address"
        case AuthErrorCode.userDisabled.rawValue:
            return "This account has been disabled"
        case AuthErrorCode.tooManyRequests.rawValue:
            return "Too many failed attempts. Please try again later"
        case AuthErrorCode.networkError.rawValue:
            return "Network error. Please check your connection"
        case AuthErrorCode.emailAlreadyInUse.rawValue:
            return "An account with this email already exists. Try signing in instead."
        case AuthErrorCode.weakPassword.rawValue:
            return "Password is too weak. Please choose a stronger password."
        case AuthErrorCode.operationNotAllowed.rawValue:
            return "Account creation is currently disabled. Please try again later."
        default:
            return nil
        }
    }
    func sendPasswordReset() {
        errorMessage = nil
        passwordResetMessage = nil

        // Validate email before sending reset
        guard !email.isEmpty else {
            errorMessage = "Please enter your email address"
            return
        }

        guard email.contains("@") else {
            errorMessage = "Please enter a valid email address"
            return
        }

        isLoading = true

        Auth.auth().sendPasswordReset(withEmail: email) { [weak self] error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    self?.errorMessage = self?.friendlyErrorMessage(for: error) ?? error.localizedDescription
                } else {
                    self?.passwordResetMessage = "Password reset email sent to \(self?.email ?? "your inbox"). Please check your email and follow the instructions."
                    self?.errorMessage = nil
                }
            }
        }
    }

    func signOut() {
        do {
            try Auth.auth().signOut()
            userSession = nil
            errorMessage = nil
            passwordResetMessage = nil
            // Clear form fields
            email = ""
            password = ""
        } catch {
            errorMessage = "Failed to sign out: \(error.localizedDescription)"
        }
    }

    deinit {
        cancellables.forEach { $0.cancel() }
    }
}


// MARK: - Account Management Extension

extension AuthViewModel {
    func deleteAccount(completion: @escaping (Result<Void, Error>) -> Void) {
        guard let user = Auth.auth().currentUser else {
            completion(.failure(NSError(domain: "Auth", code: 401, userInfo: [NSLocalizedDescriptionKey: "No authenticated user found."])))
            return
        }
        user.delete { error in
            if let error = error {
                completion(.failure(error))
            } else {
                completion(.success(()))
            }
        }
    }
}
