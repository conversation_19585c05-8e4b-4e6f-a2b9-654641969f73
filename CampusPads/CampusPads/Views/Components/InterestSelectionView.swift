// InterestSelectionView.swift
// Modern UI component for selecting interests with multi-selection support

import SwiftUI

struct InterestSelectionView: View {
    @StateObject private var interestsService = InterestsService.shared
    @Binding var selectedInterests: [String]
    @Environment(\.dismiss) private var dismiss

    @State private var searchText = ""
    @State private var selectedCategory: String? = nil
    @State private var showingCategories = true

    let maxSelections: Int
    let title: String

    init(selectedInterests: Binding<[String]>, maxSelections: Int = 10, title: String = "Select Interests") {
        self._selectedInterests = selectedInterests
        self.maxSelections = maxSelections
        self.title = title
    }

    private var filteredInterests: [Interest] {
        let interests = selectedCategory != nil ?
            interestsService.getInterests(for: selectedCategory!) :
            interestsService.interests

        if searchText.isEmpty {
            return interests
        } else {
            return interests.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }

    private var categories: [String] {
        interestsService.getCategories()
    }
    
    private var canSelectMore: Bool {
        selectedInterests.count < maxSelections
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Selection counter
                    selectionCounter
                    
                    // Search bar
                    searchSection
                    
                    // Category/Interest selection
                    if showingCategories && selectedCategory == nil {
                        categorySelectionView
                    } else {
                        interestSelectionView
                    }
                }
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                    .font(.custom("AvenirNext-Bold", size: 16))
                }
                
                if selectedCategory != nil {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Categories") {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                selectedCategory = nil
                                searchText = ""
                            }
                        }
                        .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
    }
    
    // MARK: - Selection Counter
    
    private var selectionCounter: some View {
        HStack {
            Text("\(selectedInterests.count) of \(maxSelections) selected")
                .font(.custom("AvenirNext-Bold", size: 14))
                .foregroundColor(.white)
            
            Spacer()
            
            if !selectedInterests.isEmpty {
                Button("Clear All") {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selectedInterests.removeAll()
                    }
                }
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.vertical, AppTheme.spacing8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial.opacity(0.3))
        )
        .padding(.horizontal, AppTheme.spacing20)
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: AppTheme.spacing12) {
            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                
                TextField("Search interests...", text: $searchText)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
            
            // Quick filters
            if selectedCategory == nil && searchText.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: AppTheme.spacing8) {
                        quickFilterButton("Popular", icon: "star.fill") {
                            showPopularInterests()
                        }
                        
                        quickFilterButton("Sports", icon: "figure.run") {
                            selectedCategory = "Sports & Fitness"
                        }
                        
                        quickFilterButton("Music", icon: "music.note") {
                            selectedCategory = "Music"
                        }
                        
                        quickFilterButton("Tech", icon: "laptopcomputer") {
                            selectedCategory = "Technology"
                        }
                        
                        quickFilterButton("Arts", icon: "paintbrush.fill") {
                            selectedCategory = "Arts & Creativity"
                        }
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                }
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing8)
    }
    
    private func quickFilterButton(_ title: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .bold))
                
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 12))
            }
            .foregroundColor(.white)
            .padding(.horizontal, AppTheme.spacing12)
            .padding(.vertical, AppTheme.spacing8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Category Selection View
    
    private var categorySelectionView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 150), spacing: AppTheme.spacing12)
            ], spacing: AppTheme.spacing12) {
                ForEach(categories, id: \.self) { category in
                    categoryCard(category)
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func categoryCard(_ category: String) -> some View {
        let interestCount = interestsService.getInterests(for: category).count
        
        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                selectedCategory = category
                showingCategories = false
            }
        }) {
            VStack(spacing: AppTheme.spacing12) {
                // Category icon
                Image(systemName: iconForCategory(category))
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(
                        Circle()
                            .fill(AppTheme.sexyGradient)
                    )
                
                VStack(spacing: 4) {
                    Text(category)
                        .font(.custom("AvenirNext-Bold", size: 14))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text("\(interestCount) interests")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .frame(maxWidth: .infinity, minHeight: 140)
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Interest Selection View
    
    private var interestSelectionView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 120), spacing: AppTheme.spacing8)
            ], spacing: AppTheme.spacing8) {
                ForEach(filteredInterests, id: \.id) { interest in
                    interestChip(interest)
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func interestChip(_ interest: Interest) -> some View {
        let isSelected = selectedInterests.contains(interest.name)
        let isDisabled = !isSelected && !canSelectMore
        
        return Button(action: {
            toggleInterest(interest.name)
        }) {
            HStack(spacing: AppTheme.spacing8) {
                if let emoji = interest.emoji {
                    Text(emoji)
                        .font(.system(size: 16))
                }
                
                Text(interest.name)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.9))
                    .lineLimit(1)
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, AppTheme.spacing12)
            .padding(.vertical, AppTheme.spacing8)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .opacity(isDisabled ? 0.5 : 1.0)
        .disabled(isDisabled)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    // MARK: - Helper Methods
    
    private func toggleInterest(_ interestName: String) {
        HapticFeedbackManager.shared.generateImpact(style: .light)
        
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            if selectedInterests.contains(interestName) {
                selectedInterests.removeAll { $0 == interestName }
            } else if canSelectMore {
                selectedInterests.append(interestName)
            }
        }
    }
    
    private func showPopularInterests() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            showingCategories = false
            selectedCategory = nil
            searchText = ""
        }
    }
    
    private func iconForCategory(_ category: String) -> String {
        switch category {
        case "Sports & Fitness": return "figure.run"
        case "Music": return "music.note"
        case "Arts & Creativity": return "paintbrush.fill"
        case "Technology": return "laptopcomputer"
        case "Outdoors & Nature": return "leaf.fill"
        case "Food & Cooking": return "fork.knife"
        case "Travel": return "airplane"
        case "Entertainment": return "tv.fill"
        case "Reading & Learning": return "book.fill"
        case "Gaming": return "gamecontroller.fill"
        case "Social Activities": return "person.3.fill"
        case "Health & Wellness": return "heart.fill"
        case "Hobbies & Crafts": return "scissors"
        case "Volunteering & Causes": return "hand.raised.fill"
        case "Business & Entrepreneurship": return "briefcase.fill"
        case "Lifestyle": return "house.fill"
        default: return "star.fill"
        }
    }
}
