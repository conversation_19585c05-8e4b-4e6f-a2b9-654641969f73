import Foundation
import CoreLocation

// MARK: - Text Normalization Utility
struct TextNormalizationUtility {
    /// Comprehensive text normalization for robust filtering
    static func normalize(_ text: String?) -> String {
        guard let text = text else { return "" }

        return text
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .lowercased()
            .folding(options: [.diacriticInsensitive, .caseInsensitive], locale: .current)
            .replacingOccurrences(of: #"\s+"#, with: " ", options: .regularExpression)
            .replacingOccurrences(of: #"[^\w\s]"#, with: "", options: .regularExpression)
            .trimmingCharacters(in: .whitespaces)
    }

    /// Normalize and split comma-separated values (for interests, amenities, etc.)
    static func normalizeAndSplit(_ text: String?) -> [String] {
        guard let text = text else { return [] }

        return text
            .components(separatedBy: CharacterSet(charactersIn: ",;|"))
            .map { normalize($0) }
            .filter { !$0.isEmpty }
    }

    /// Check if two normalized strings match
    static func matches(_ text1: String?, _ text2: String?) -> Bool {
        let normalized1 = normalize(text1)
        let normalized2 = normalize(text2)
        return !normalized1.isEmpty && !normalized2.isEmpty && normalized1 == normalized2
    }

    /// Check if normalized text contains search term
    static func contains(_ text: String?, searchTerm: String?) -> Bool {
        let normalizedText = normalize(text)
        let normalizedTerm = normalize(searchTerm)
        return !normalizedText.isEmpty && !normalizedTerm.isEmpty && normalizedText.contains(normalizedTerm)
    }
}

struct SmartMatchingEngine {

    /// 1) Compatibility + bonus
    static func calculateSmartMatchScore(
        between me: UserModel, and them: UserModel,
        averageRating: Double? = nil
    ) -> Double {
        let base = CompatibilityCalculator.calculateUserCompatibility(between: me, and: them)
        var bonus = 0.0

        // Verification bonus - simplified to use only isEmailVerified
        if me.isEmailVerified && them.isEmailVerified { bonus += 10 }

        if TextNormalizationUtility.matches(me.housingStatus, them.housingStatus) { bonus += 5 }
        if TextNormalizationUtility.matches(me.leaseDuration, them.leaseDuration) { bonus += 5 }
        if let r = averageRating { bonus += r * 2 }
        return min(base + bonus, 100.0)
    }

    /// 2) Filter-based match count
    static func calculateFilterMatchScore(
        filterSettings s: FilterSettings,
        otherUser u: UserModel,
        currentUser me: UserModel
    ) -> Int {
        var score = 0
        let userName = u.firstName ?? "Unknown"

        // ENHANCED DEBUGGING: Start detailed score calculation logging
        print("🔍 SmartMatchingEngine: Starting score calculation for \(userName)")
        print("   - Initial score: \(score)")
        print("   - Show All Profiles mode: \(s.showAllProfiles ?? false)")
        print("   - Filter mode: \(s.mode ?? "none")")

        // CRITICAL FIX: Special handling for "Show All Profiles" mode - ensure NO profiles are filtered out
        if s.showAllProfiles == true {
            // In "Show All Profiles" mode, we want to show ALL users based on compatibility rather than strict filter matching
            // Give a substantial base score to ensure ALL users are included, then add compatibility bonuses
            score = 50 // MUCH HIGHER base score to ensure user is ALWAYS included in "Show All Profiles" mode
            print("🔍 SmartMatchingEngine: 'Show All Profiles' mode - giving base score of \(score) to \(userName) to GUARANTEE inclusion")

            // Add compatibility bonus for ranking but don't subtract anything
            let compatibilityScore = CompatibilityCalculator.calculateUserCompatibility(between: me, and: u)
            score += Int(compatibilityScore / 10) // Add compatibility as bonus for ranking

            print("🔍 SmartMatchingEngine: 'Show All Profiles' mode - final score for \(userName): \(score) (guaranteed > 0)")
            return score // Return immediately to skip all filtering logic
        }

        // NEW: MANDATORY FILTERS - User must meet ALL mandatory criteria to be included
        print("🎯 SmartMatchingEngine: Checking MANDATORY filters for \(userName)")

        // MANDATORY 1: College Match (if specified)
        if let myCollege = s.collegeName, !TextNormalizationUtility.normalize(myCollege).isEmpty {
            guard TextNormalizationUtility.matches(myCollege, u.collegeName) else {
                let normalizedMine = TextNormalizationUtility.normalize(myCollege)
                let normalizedTheirs = TextNormalizationUtility.normalize(u.collegeName)
                print("   - ❌ MANDATORY EXCLUSION: College mismatch (mine: '\(normalizedMine)', theirs: '\(normalizedTheirs)')")
                return 0 // EXCLUDE - mandatory filter not met
            }
            score += 5 // Higher score for mandatory match
            print("   - ✅ MANDATORY: College match (+5)")
        }

        // MANDATORY 2: Housing Status Compatibility (if specified)
        if let myHousingStatus = s.housingStatus, !myHousingStatus.isEmpty {
            guard let theirHousingStatus = u.housingStatus else {
                print("   - ❌ MANDATORY EXCLUSION: \(userName) has no housing status")
                return 0 // EXCLUDE - mandatory filter not met
            }

            // Check housing compatibility
            let isCompatible = checkHousingCompatibility(myStatus: myHousingStatus, theirStatus: theirHousingStatus)
            if !isCompatible {
                print("   - ❌ MANDATORY EXCLUSION: Housing incompatible (mine: \(myHousingStatus), theirs: \(theirHousingStatus))")
                return 0 // EXCLUDE - mandatory filter not met
            }
            score += 5 // Higher score for mandatory match
            print("   - ✅ MANDATORY: Housing compatible (+5)")
        }

        // MANDATORY 3: Gender Preference (if specified)
        if let preferredGender = s.preferredGender, !preferredGender.isEmpty, preferredGender != "Any" {
            guard let theirGender = u.gender, theirGender == preferredGender else {
                print("   - ❌ MANDATORY EXCLUSION: Gender mismatch (preferred: \(preferredGender), theirs: \(u.gender ?? "none"))")
                return 0 // EXCLUDE - mandatory filter not met
            }
            score += 3 // Higher score for mandatory match
            print("   - ✅ MANDATORY: Gender match (+3)")
        }

        // MANDATORY 4: Age Difference (if specified)
        if let maxAgeDiff = s.maxAgeDifference, maxAgeDiff > 0 {
            if let myDOB = me.dateOfBirth, let theirDOB = u.dateOfBirth {
                let ageComponents = Calendar.current.dateComponents([.year], from: theirDOB, to: myDOB)
                let ageDifference = abs(ageComponents.year ?? 0)

                if Double(ageDifference) > maxAgeDiff {
                    print("   - ❌ MANDATORY EXCLUSION: Age difference too large (\(ageDifference) years, max: \(maxAgeDiff))")
                    return 0 // EXCLUDE - mandatory filter not met
                }
                score += 3 // Higher score for mandatory match
                print("   - ✅ MANDATORY: Age difference acceptable (\(ageDifference) years, +3)")
            }
        }

        // Give base score for passing all mandatory filters
        if score == 0 {
            score = 10 // Base score for users who pass mandatory filters
            print("   - ✅ MANDATORY: All mandatory filters passed - base score: \(score)")
        }

        // NEW: OPTIONAL FILTERS - Improve ranking but don't exclude
        print("🎯 SmartMatchingEngine: Checking OPTIONAL filters for \(userName)")

        // 2-c) distance filtering REMOVED - college-only filtering now
        print("   - Distance filtering: ⏭️ REMOVED (college-only mode)")
        // Distance filtering has been completely removed to focus on college-based matching

        // OPTIONAL 1: Grade Level - apply as bonus
        if let filterGrade = s.gradeGroup, !TextNormalizationUtility.normalize(filterGrade).isEmpty,
           let userGrade = u.gradeLevel, !TextNormalizationUtility.normalize(userGrade).isEmpty {
            let normalizedFilter = TextNormalizationUtility.normalize(filterGrade)
            let normalizedUser = TextNormalizationUtility.normalize(userGrade)

            let gradeMatches = switch normalizedFilter {
            case "freshman" where normalizedUser == "freshman": true
            case "underclassmen" where ["freshman","sophomore"].contains(normalizedUser): true
            case "upperclassmen" where ["junior","senior"].contains(normalizedUser): true
            case "graduate" where normalizedUser == "graduate": true
            default: false
            }
            if gradeMatches {
                score += 1
                print("   - ✅ OPTIONAL: Grade level match (+1)")
            }
        }

        // OPTIONAL 2: Room Type - apply as bonus
        if let filterRoomType = s.roomType, !TextNormalizationUtility.normalize(filterRoomType).isEmpty {
            if TextNormalizationUtility.matches(filterRoomType, u.roomType) {
                score += 1
                print("   - ✅ OPTIONAL: Room type match (+1)")
            }
        }

        // OPTIONAL 3: Amenities - apply as bonus
        if let want = s.amenities, !want.isEmpty, let have = u.amenities,
           Set(want).isSubset(of: Set(have)) {
            score += 1
            print("   - ✅ OPTIONAL: Amenities match (+1)")
        }

        // OPTIONAL 4: Cleanliness & Sleep Schedule - apply as bonus
        if let cleanliness = s.cleanliness, cleanliness == u.cleanliness {
            score += 1
            print("   - ✅ OPTIONAL: Cleanliness match (+1)")
        }
        if let filterSleep = s.sleepSchedule, !TextNormalizationUtility.normalize(filterSleep).isEmpty {
            if TextNormalizationUtility.matches(filterSleep, u.sleepSchedule) {
                score += 1
                print("   - ✅ OPTIONAL: Sleep schedule match (+1)")
            }
        }

        // OPTIONAL 5: Lifestyle Preferences - apply as bonus
        if let wantPet = s.petFriendly,
           let hasPet  = u.petFriendly,
           wantPet == hasPet {
            score += 1
            print("   - ✅ OPTIONAL: Pet preference match (+1)")
        }

        if let wantSmoke = s.smoker,
           let isSmoker  = u.smoker,
           wantSmoke == isSmoker {
            score += 1
            print("   - ✅ OPTIONAL: Smoking preference match (+1)")
        }

        // Drinker → user.drinking is String?
        if let wantDrink = s.drinker,
           let userDrinking = u.drinking {
            let normalizedDrinking = TextNormalizationUtility.normalize(userDrinking)
            // wantDrink==true means user must *not* be "not for me"
            if wantDrink ? (normalizedDrinking != "not for me")
                : (normalizedDrinking == "not for me") {
                score += 1
                print("   - ✅ OPTIONAL: Drinking preference match (+1)")
            }
        }

        // Marijuana → user.cannabis is String?
        if let wantMj = s.marijuana,
           let userCannabis = u.cannabis {
            let normalizedCannabis = TextNormalizationUtility.normalize(userCannabis)
            if wantMj ? (normalizedCannabis != "never")
                : (normalizedCannabis == "never") {
                score += 1
                print("   - ✅ OPTIONAL: Cannabis preference match (+1)")
            }
        }

        // Workout → user.workout is String?
        if let wantWorkout = s.workout,
           let userWorkout = u.workout {
            let normalizedWorkout = TextNormalizationUtility.normalize(userWorkout)
            if wantWorkout ? (normalizedWorkout != "never")
                : (normalizedWorkout == "never") {
                score += 1
                print("   - ✅ OPTIONAL: Workout preference match (+1)")
            }
        }

        // OPTIONAL 6: Interests - apply as bonus with robust text processing
        if let filterInterests = s.interests, !TextNormalizationUtility.normalize(filterInterests).isEmpty,
           let userInterests = u.interests, !userInterests.isEmpty {

            let normalizedFilterInterests = Set(TextNormalizationUtility.normalizeAndSplit(filterInterests))
            let normalizedUserInterests = Set(userInterests.map { TextNormalizationUtility.normalize($0) })

            if !normalizedFilterInterests.isDisjoint(with: normalizedUserInterests) {
                score += 1
                print("   - ✅ OPTIONAL: Interests overlap (+1)")
                let overlap = normalizedFilterInterests.intersection(normalizedUserInterests)
                print("     Matching interests: \(overlap.joined(separator: ", "))")
            }
        }

        // ENHANCED DEBUGGING: Final score summary
        print("🔍 SmartMatchingEngine: Final score for \(userName): \(score)")
        if score <= 0 {
            print("   - ⚠️ WARNING: Score is \(score), user will be EXCLUDED from results")
        } else {
            print("   - ✅ Score is \(score), user will be INCLUDED in results")
        }

        return score
    }

    /// 3) churn out only those > 0, sorted by descending score
    static func generateSortedMatches(
      from all: [UserModel],
      currentUser me: UserModel
    ) -> [UserModel] {
      let myBlockedUsers = Set(me.blockedUserIDs ?? [])
      let myUserID = me.id ?? ""

      // 1) remove self, users I blocked, and users who blocked me (mutual blocking)
      let candidates = all
        .filter { user in
            guard let userID = user.id, userID != me.id else { return false }

            // Check if I blocked them
            if myBlockedUsers.contains(userID) { return false }

            // Check if they blocked me (mutual blocking)
            if let theirBlockedUsers = user.blockedUserIDs,
               theirBlockedUsers.contains(myUserID) { return false }

            return true
        }

      // 2) if user has explicit FilterSettings, use them…
      if let fs = me.filterSettings {
        return candidates
          .map { user in
            (user, calculateFilterMatchScore(
                       filterSettings: fs,
                       otherUser:    user,
                       currentUser:  me))
          }
          // after
          .filter { $0.1 > 0 }
          .sorted { lhs, rhs in
            if lhs.1 != rhs.1 {
              return lhs.1 > rhs.1                    // primary: filter score
            } else {
              // secondary: overall compatibility
              let scoreL = calculateSmartMatchScore(between: me, and: lhs.0)
              let scoreR = calculateSmartMatchScore(between: me, and: rhs.0)
              return scoreL > scoreR
            }
          }
          .map { $0.0 }
      }

      // 3) …otherwise, fall back to pure compatibility sorting
      return candidates
        .sorted {
          calculateSmartMatchScore(between: me, and: $0)
          >
          calculateSmartMatchScore(between: me, and: $1)
        }
    }
}

// New overload that takes a pre-built FilterSettings
extension SmartMatchingEngine {
  static func generateSortedMatches(
    from all: [UserModel],
    currentUser me: UserModel,
    using fs: FilterSettings
  ) -> [UserModel] {
    let myBlockedUsers = Set(me.blockedUserIDs ?? [])
    let myUserID = me.id ?? ""

    // ENHANCED DEBUGGING: Track the filtering pipeline
    print("🔍 SmartMatchingEngine.generateSortedMatches: Starting with \(all.count) users")
    print("   - Show All Profiles mode: \(fs.showAllProfiles ?? false)")
    print("   - Filter mode: \(fs.mode ?? "none")")

    let candidates = all.filter { user in
        guard let userID = user.id else { return false }

        let isNotSelf = userID != me.id
        let isNotBlockedByMe = !myBlockedUsers.contains(userID)
        let hasNotBlockedMe = !(user.blockedUserIDs?.contains(myUserID) ?? false)
        let hasProfileImage = hasValidProfileImage(user)

        let isEligible = isNotSelf && isNotBlockedByMe && hasNotBlockedMe && hasProfileImage

        if !isEligible && userID != me.id {
            let reasons = [
                !isNotBlockedByMe ? "blocked by me" : nil,
                !hasNotBlockedMe ? "has blocked me" : nil,
                !hasProfileImage ? "no profile image" : nil
            ].compactMap { $0 }.joined(separator: ", ")
            if !reasons.isEmpty {
                print("   ⚠️ Excluding \(user.firstName ?? "Unknown"): \(reasons)")
            }
        }

        return isEligible
    }
    print("🔍 SmartMatchingEngine: After removing self, blocked users, and users without profile images: \(candidates.count)")

    let scoredUsers = candidates.map { user in
        let score = calculateFilterMatchScore(filterSettings: fs, otherUser: user, currentUser: me)
        return (user, score)
    }
    print("🔍 SmartMatchingEngine: Scored \(scoredUsers.count) users")

    // Log score distribution
    let scoreDistribution = Dictionary(grouping: scoredUsers, by: { $0.1 })
    for (score, users) in scoreDistribution.sorted(by: { $0.key > $1.key }) {
        print("   - Score \(score): \(users.count) users")
    }

    let filteredUsers = scoredUsers.filter { $0.1 > 0 }
    print("🔍 SmartMatchingEngine: After filtering score > 0: \(filteredUsers.count)")

    if filteredUsers.count < scoredUsers.count {
        let excludedCount = scoredUsers.count - filteredUsers.count
        print("   - ⚠️ EXCLUDED \(excludedCount) users with score ≤ 0")

        // Log specific excluded users for debugging
        let excludedUsers = scoredUsers.filter { $0.1 <= 0 }
        for (user, score) in excludedUsers.prefix(5) { // Show first 5 excluded users
            print("     - \(user.firstName ?? "Unknown") (score: \(score))")
        }
        if excludedUsers.count > 5 {
            print("     - ... and \(excludedUsers.count - 5) more")
        }

        // Special warning for Show All Profiles mode
        if fs.showAllProfiles == true {
            print("   - 🚨 CRITICAL: Show All Profiles mode should NOT exclude any users!")
        }
    }

    let sortedUsers = filteredUsers.sorted { $0.1 > $1.1 }
    let finalResult = sortedUsers.map { $0.0 }

    print("🔍 SmartMatchingEngine: Final result: \(finalResult.count) users")

    return finalResult
  }

  /// Simplified profile image validation - check if user has at least 1 image total
  private static func hasValidProfileImage(_ user: UserModel) -> Bool {
      var imageCount = 0

      // Count legacy single image
      if let imageUrl = user.profileImageUrl, !imageUrl.isEmpty {
          imageCount += 1
      }

      // Count multiple images array
      if let imageUrls = user.profileImageUrls {
          imageCount += imageUrls.filter { !$0.isEmpty }.count
      }

      return imageCount >= 1
  }

  /// Helper function to check housing compatibility
  private static func checkHousingCompatibility(myStatus: String, theirStatus: String) -> Bool {
      // Housing compatibility logic:
      // - "Looking for Roommate" is compatible with "Looking for Lease"
      // - "Looking for Lease" is compatible with "Looking for Roommate"
      // - "Looking to Find Together" is compatible with "Looking to Find Together" and "Looking for Lease"

      switch myStatus {
      case PrimaryHousingPreference.lookingForRoommate.rawValue:
          return theirStatus == PrimaryHousingPreference.lookingForLease.rawValue
      case PrimaryHousingPreference.lookingForLease.rawValue:
          return theirStatus == PrimaryHousingPreference.lookingForRoommate.rawValue
      case PrimaryHousingPreference.lookingToFindTogether.rawValue:
          return [
              PrimaryHousingPreference.lookingToFindTogether.rawValue,
              PrimaryHousingPreference.lookingForLease.rawValue
          ].contains(theirStatus)
      default:
          return false
      }
  }
}
