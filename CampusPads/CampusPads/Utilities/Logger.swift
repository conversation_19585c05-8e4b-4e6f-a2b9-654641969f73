//
//  Logger.swift
//  CampusPads
//
//  Centralized logging system with proper levels and formatting
//

import Foundation
import os.log

/// Centralized logging system for the app
final class Logger {
    static let shared = Logger()
    
    private let osLog = OSLog(subsystem: AppConfiguration.App.bundleIdentifier, category: "CampusPads")
    private let dateFormatter: DateFormatter
    
    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
    }
    
    // MARK: - Public Logging Methods
    
    func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .debug, message: message, file: file, function: function, line: line)
    }
    
    func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, message: message, file: file, function: function, line: line)
    }
    
    func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .warning, message: message, file: file, function: function, line: line)
    }
    
    func error(_ message: String, error: Error? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        var fullMessage = message
        if let error = error {
            fullMessage += " | Error: \(error.localizedDescription)"
        }
        log(level: .error, message: fullMessage, file: file, function: function, line: line)
    }
    
    func critical(_ message: String, error: Error? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        var fullMessage = message
        if let error = error {
            fullMessage += " | Error: \(error.localizedDescription)"
        }
        log(level: .critical, message: fullMessage, file: file, function: function, line: line)
    }
    
    // MARK: - Private Implementation
    
    private func log(level: LogLevel, message: String, file: String, function: String, line: Int) {
        // Check if we should log this level
        guard shouldLog(level: level) else { return }
        
        let fileName = URL(fileURLWithPath: file).lastPathComponent
        let timestamp = dateFormatter.string(from: Date())
        
        let formattedMessage: String
        if AppConfiguration.Debug.verboseLogging {
            formattedMessage = "\(level.emoji) [\(timestamp)] \(fileName):\(line) \(function) - \(message)"
        } else {
            formattedMessage = "\(level.emoji) \(message)"
        }
        
        // Log to console in debug builds
        if AppConfiguration.Debug.isEnabled {
            print(formattedMessage)
        }
        
        // Log to system log for production
        logToSystem(level: level, message: formattedMessage)
    }
    
    private func shouldLog(level: LogLevel) -> Bool {
        let configuredLevel = AppConfiguration.Debug.logLevel
        return level.priority >= configuredLevel.priority
    }
    
    private func logToSystem(level: LogLevel, message: String) {
        let osLogType: OSLogType
        switch level {
        case .debug:
            osLogType = .debug
        case .info:
            osLogType = .info
        case .warning:
            osLogType = .default
        case .error:
            osLogType = .error
        case .critical:
            osLogType = .fault
        }
        
        os_log("%{public}@", log: osLog, type: osLogType, message)
    }
}

// MARK: - LogLevel Priority
extension LogLevel {
    var priority: Int {
        switch self {
        case .debug: return 0
        case .info: return 1
        case .warning: return 2
        case .error: return 3
        case .critical: return 4
        }
    }
}

// MARK: - Convenience Extensions
extension Logger {
    
    /// Log Firebase operations
    func firebase(_ message: String, operation: String, file: String = #file, function: String = #function, line: Int = #line) {
        debug("🔥 Firebase \(operation): \(message)", file: file, function: function, line: line)
    }
    
    /// Log network operations
    func network(_ message: String, url: String? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        let fullMessage = url != nil ? "\(message) | URL: \(url!)" : message
        debug("🌐 Network: \(fullMessage)", file: file, function: function, line: line)
    }
    
    /// Log UI operations
    func ui(_ message: String, view: String? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        let fullMessage = view != nil ? "\(message) | View: \(view!)" : message
        debug("📱 UI: \(fullMessage)", file: file, function: function, line: line)
    }
    
    /// Log matching operations
    func matching(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        debug("💕 Matching: \(message)", file: file, function: function, line: line)
    }
    
    /// Log authentication operations
    func auth(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        debug("🔐 Auth: \(message)", file: file, function: function, line: line)
    }
    
    /// Log chat operations
    func chat(_ message: String, chatID: String? = nil, file: String = #file, function: String = #function, line: Int = #line) {
        let fullMessage = chatID != nil ? "\(message) | Chat: \(chatID!)" : message
        debug("💬 Chat: \(fullMessage)", file: file, function: function, line: line)
    }
}

// MARK: - Global Logging Functions
func logDebug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.debug(message, file: file, function: function, line: line)
}

func logInfo(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.info(message, file: file, function: function, line: line)
}

func logWarning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.warning(message, file: file, function: function, line: line)
}

func logError(_ message: String, error: Error? = nil, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.error(message, error: error, file: file, function: function, line: line)
}

func logCritical(_ message: String, error: Error? = nil, file: String = #file, function: String = #function, line: Int = #line) {
    Logger.shared.critical(message, error: error, file: file, function: function, line: line)
}
