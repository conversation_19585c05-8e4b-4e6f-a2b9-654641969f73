import SwiftUI
import FirebaseFirestore

struct SwipeCardView: View {
    // Internal storage: either a fully provided candidate or loaded from candidateID.
    private let initialUser: UserModel?
    private let candidateID: String?
    var onSwipe: (_ user: UserModel, _ direction: SwipeDirection) -> Void = { _, _ in }

    // State for loaded candidate if candidateID initializer is used.
    @State private var loadedUser: UserModel?

    @State private var offset: CGSize = .zero
    @State private var rotation: Double = 0
    @State private var showLikeOverlay: Bool = false
    @State private var showNopeOverlay: Bool = false
    @State private var showSuperLikeOverlay: Bool = false

    // Optimized performance tracking
    @State private var isDragging: Bool = false
    @State private var overlayOpacity: Double = 0.0

    // 🔹 singletons for Firestore & favorites
    private let db = Firestore.firestore()
    private let profileViewTracker = ProfileViewTrackingService.shared

    // Pull the current user from a shared view model if needed.
    var currentUser: UserModel? {
        ProfileViewModel.shared.userProfile
    }

    // Use the provided candidate if available; otherwise, use the loaded candidate.
    var user: UserModel? {
        initialUser ?? loadedUser
    }

    // Example compatibility score.
    var compatibilityScore: Double? {
      guard let me = currentUser, let them = user else { return nil }
      return SmartMatchingEngine.calculateSmartMatchScore(between: me, and: them)
    }

    // MARK: - Initializers

    /// Initialize with a full UserModel.
    init(user: UserModel, onSwipe: @escaping (_ user: UserModel, _ direction: SwipeDirection) -> Void) {
        self.initialUser = user
        self.candidateID = nil
        self.onSwipe = onSwipe
    }

    /// Initialize with a candidateID; candidate data will be loaded from Firestore.
    init(candidateID: String) {
        self.candidateID = candidateID
        self.initialUser = nil
    }

    var body: some View {
        ZStack {
            // Beautiful enhanced card background with premium styling
            RoundedRectangle(cornerRadius: 28)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color.white.opacity(0.98),
                            Color.white.opacity(0.95)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 28)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    AppTheme.primaryColor.opacity(0.3),
                                    AppTheme.accentColor.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
                .shadow(color: AppTheme.primaryColor.opacity(0.15), radius: 25, x: 0, y: 15)
                .shadow(color: .black.opacity(0.1), radius: 15, x: 0, y: 8)
                .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 3)

            if let candidate = user {
                // Determine if the candidate’s preview should be forced into lease mode.
                let currentUserStatus = ProfileViewModel.shared.userProfile?.housingStatus
                let forcedPreview: PreviewMode? = (currentUserStatus == PrimaryHousingPreference.lookingForLease.rawValue &&
                                                   candidate.housingStatus == PrimaryHousingPreference.lookingForRoommate.rawValue)
                                                  ? .lease : nil
                // Use the ProfilePreviewView as the card content with enhanced styling
                ProfilePreviewView(user: candidate, forcePreviewMode: forcedPreview)
                    .cornerRadius(24)
                    .clipped()
                    .padding(0) // Zero padding for true Tinder-style full-screen experience
            } else {
                LoadingView(message: "Loading profile...", size: 50)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }

            // Optimized overlays - particles only show on swipe completion, not during drag
            if showLikeOverlay {
                optimizedOverlay(text: "LIKE", color: .green, icon: "heart.fill", rotation: -15)
                    .opacity(overlayOpacity)
            }
            if showNopeOverlay {
                optimizedOverlay(text: "NOPE", color: .red, icon: "xmark", rotation: 15)
                    .opacity(overlayOpacity)
            }
            if showSuperLikeOverlay {
                optimizedOverlay(text: "SUPER LIKE", color: .yellow, icon: "star.fill", rotation: 0)
                    .opacity(overlayOpacity)
            }
        }
        // Removed aspect ratio constraint to allow vertical expansion
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .clipped()
        .overlay(
            // Modern sexy hue ring for extra aura
            RoundedRectangle(cornerRadius: 24)
                .stroke(
                    AngularGradient(
                        gradient: Gradient(colors: [
                            AppTheme.primaryColor,
                            AppTheme.accentColor,
                            .purple,
                            .pink,
                            AppTheme.primaryColor,
                            .cyan,
                            AppTheme.accentColor
                        ]),
                        center: .center,
                        startAngle: .degrees(0),
                        endAngle: .degrees(360)
                    ),
                    lineWidth: 3
                )
                .blur(radius: 1)
                .opacity(0.8)
        )
        .overlay(
            // Inner subtle glow ring
            RoundedRectangle(cornerRadius: 24)
                .stroke(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.6),
                            Color.white.opacity(0.2),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
        .shadow(color: isDragging ? .clear : AppTheme.primaryColor.opacity(0.3), radius: 20, x: 0, y: 10)
        .shadow(color: isDragging ? .clear : AppTheme.accentColor.opacity(0.2), radius: 15, x: 0, y: 5)
        .offset(x: offset.width, y: offset.height)
        .rotationEffect(Angle(degrees: rotation))
        .scaleEffect(1 + abs(offset.width) / 2000) // Subtle scale effect during drag
        .gesture(optimizedDragGesture)
        .onAppear(perform: loadData)
        .onAppear {
            // Track profile view when card appears (but not for anonymous discovery)
            // Only track if user actually views the profile for more than 2 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                if let user = user, let userID = user.id {
                    profileViewTracker.recordProfileView(
                        viewedUserID: userID,
                        source: .discovery,
                        metadata: ProfileViewMetadata(
                            viewDuration: 2.0,
                            hadInteraction: true,
                            deviceType: "iOS"
                        )
                    )
                }
            }
        }
        .animation(isDragging ? .none : .interactiveSpring(response: 0.4, dampingFraction: 0.8), value: offset)
    }
}

// MARK: - Subviews & Helpers
extension SwipeCardView {
    /// 1) Entry point for either “check favorite” or “load from Firestore”
    private func loadData() {
        if let id = candidateID {
            loadCandidate(id)
        }
    }

    /// 2) Loads candidate details from Firestore using the candidateID.
    private func loadCandidate(_ id: String) {
        db.collection("users").document(id).getDocument { snap, err in
            if let err = err {
                print("SwipeCardView load error:", err.localizedDescription)
                return
            }
            guard let doc = snap, doc.exists,
                  let candidate = try? doc.data(as: UserModel.self)
            else { return }

            DispatchQueue.main.async {
                self.loadedUser = candidate
            }
        }
    }

    /// Returns the profile image view for a candidate.
    private func profileImage(for candidate: UserModel) -> some View {
        Group {
            if let urlStr = candidate.profileImageUrl, let url = URL(string: urlStr) {
                AsyncImage(url: url) { phase in
                    if let image = phase.image {
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 150, height: 150)
                            .clipShape(Circle())
                            .overlay(Circle().stroke(AppTheme.primaryColor, lineWidth: 4))
                    } else {
                        placeholderImage(for: candidate)
                    }
                }
            } else {
                placeholderImage(for: candidate)
            }
        }
    }


    /// Returns a circular placeholder view showing candidate initials.
    private func placeholderImage(for candidate: UserModel) -> some View {
        let initials = candidate.email.components(separatedBy: "@").first?.prefix(2).uppercased() ?? "??"
        return Text(initials)
            .font(AppTheme.bodyFont.weight(.bold))
            .frame(width: 150, height: 150)
            .background(AppTheme.primaryColor.opacity(0.5))
            .foregroundColor(.white)
            .clipShape(Circle())
    }

    /// Returns a verified badge view if the candidate's email is verified
    private func verifiedBadge(for candidate: UserModel) -> some View {
        Group {
            // Simplified verification check - only use isEmailVerified
            if candidate.isEmailVerified {
                Text("✓ Verified")
                    .font(AppTheme.bodyFont)
                    .foregroundColor(.white)
                    .padding(4)
                    .background(AppTheme.primaryColor.opacity(0.8))
                    .clipShape(Capsule())
                    .offset(x: -10, y: 10)
            }
        }
    }

    /// Returns a view showing candidate info.
    private func userInfoSection(for candidate: UserModel) -> some View {
        VStack(spacing: 4) {
            // Name
            if let first = candidate.firstName, let last = candidate.lastName,
               !first.isEmpty, !last.isEmpty {
                Text("\(first) \(last)")
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(.primary)
            }

            // Housing type
            if let housing = candidate.desiredLeaseHousingType ?? candidate.dormType,
               !housing.isEmpty {
                Text("Housing: \(housing)")
                    .font(AppTheme.bodyFont)
            }

            // Rent (roommate mode) vs. Budget (lease / find‑together)
            if candidate.housingStatus == PrimaryHousingPreference.lookingForRoommate.rawValue {
                let minRent = Int(candidate.monthlyRentMin ?? 0)
                let maxRent = Int(candidate.monthlyRentMax ?? 0)
                Text("Rent: \(minRent)–\(maxRent) USD")
                    .font(AppTheme.bodyFont)
            } else {
                let minB = Int(candidate.budgetMin ?? 0)
                let maxB = Int(candidate.budgetMax ?? 0)
                Text("Budget: \(minB)–\(maxB) USD")
                    .font(AppTheme.bodyFont)
            }

            // Sleep schedule
            if let schedule = candidate.sleepSchedule, !schedule.isEmpty {
                Text("Sleep: \(schedule)")
                    .font(AppTheme.bodyFont)
            }

            // Compatibility score
            if let score = compatibilityScore {
              Text("Compatibility: \(Int(score))%")
                .font(AppTheme.subtitleFont)
                .foregroundColor(score > 70
                   ? AppTheme.likeColor
                   : AppTheme.accentColor)
            }
        }
        .padding(.horizontal)
        .padding(.bottom, AppTheme.defaultPadding)
    }


    /// Optimized overlay with reduced visual effects for better performance
    private func optimizedOverlay(text: String, color: Color, icon: String, rotation: Double) -> some View {
        VStack(spacing: 12) {
            // Simplified icon
            Image(systemName: icon)
                .font(.system(size: 50, weight: .bold))
                .foregroundColor(.white)
                .shadow(color: color.opacity(0.8), radius: 8, x: 0, y: 0)

            // Simplified text
            Text(text)
                .font(.system(size: 36, weight: .black, design: .rounded))
                .foregroundColor(.white)
                .tracking(2)
                .shadow(color: color.opacity(0.8), radius: 6, x: 0, y: 0)
        }
        .padding(.horizontal, 28)
        .padding(.vertical, 20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            color.opacity(0.9),
                            color.opacity(0.7)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .shadow(color: color.opacity(0.4), radius: 12, x: 0, y: 6)
        .rotationEffect(Angle(degrees: rotation))
        .scaleEffect(1.1)
    }

    /// Displays overlay text (e.g., "LIKE" or "NOPE").
    private func overlayText(_ text: String, color: Color, rotation: Double, xPos: CGFloat) -> some View {
        Text(text)
            .font(.system(size: 48, weight: .heavy))
            .foregroundColor(color)
            .rotationEffect(.degrees(rotation))
            .padding()
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                    .stroke(color, lineWidth: 4)
            )
            .position(x: xPos, y: 50)
            .transition(.scale)
    }

    /// Ultra-optimized drag gesture for smooth 120fps performance
    private var optimizedDragGesture: some Gesture {
        DragGesture()
            .onChanged { gesture in
                // Direct state updates without animation during drag for maximum performance
                offset = gesture.translation
                rotation = Double(gesture.translation.width / 20)

                // Set dragging state
                if !isDragging {
                    isDragging = true
                }

                // Update overlays efficiently
                updateOverlaysOptimized()
            }
            .onEnded { _ in
                isDragging = false
                finalizeSwipe()
            }
    }

    /// Optimized overlay updates without animations during drag
    private func updateOverlaysOptimized() {
        let previousLike = showLikeOverlay
        let previousNope = showNopeOverlay
        let previousSuper = showSuperLikeOverlay

        // Direct state updates without animation for performance
        if offset.height < -60 && abs(offset.width) < 60 {
            showSuperLikeOverlay = true
            showLikeOverlay = false
            showNopeOverlay = false
            overlayOpacity = min(1.0, abs(offset.height) / 100.0)
        } else if offset.width > 60 {
            showLikeOverlay = true
            showNopeOverlay = false
            showSuperLikeOverlay = false
            overlayOpacity = min(1.0, abs(offset.width) / 100.0)
        } else if offset.width < -60 {
            showNopeOverlay = true
            showLikeOverlay = false
            showSuperLikeOverlay = false
            overlayOpacity = min(1.0, abs(offset.width) / 100.0)
        } else {
            showLikeOverlay = false
            showNopeOverlay = false
            showSuperLikeOverlay = false
            overlayOpacity = 0.0
        }

        // Provide haptic feedback when overlay state changes (throttled)
        if (showLikeOverlay && !previousLike) ||
           (showNopeOverlay && !previousNope) ||
           (showSuperLikeOverlay && !previousSuper) {
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
    }

    /// Optimized swipe finalization with smooth animations
    private func finalizeSwipe() {
        let swipeThreshold: CGFloat = 120
        let superLikeThreshold: CGFloat = 100

        if offset.height < -superLikeThreshold {
            // Super like - enhanced haptic feedback
            HapticFeedbackManager.shared.generateNotification(.success)
            if let candidate = user {
                onSwipe(candidate, .up)
            }
        } else if offset.width > swipeThreshold {
            // Like - success haptic
            HapticFeedbackManager.shared.generateNotification(.success)
            if let candidate = user {
                onSwipe(candidate, .right)
            }
        } else if offset.width < -swipeThreshold {
            // Nope - warning haptic
            HapticFeedbackManager.shared.generateNotification(.warning)
            if let candidate = user {
                onSwipe(candidate, .left)
            }
        } else {
            // Return to center with optimized spring animation
            withAnimation(.interactiveSpring(response: 0.5, dampingFraction: 0.7)) {
                offset = .zero
                rotation = 0
                showLikeOverlay = false
                showNopeOverlay = false
                showSuperLikeOverlay = false
                overlayOpacity = 0.0
            }
        }
    }
}

struct SwipeCardView_Previews: PreviewProvider {
    static var previews: some View {
        SwipeCardView(user: UserModel(
            email: "test@edu",
            isEmailVerified: true,
            firstName: "Taylor",
            lastName: "Johnson",
            // old `dormType` stays
            dormType: "On-Campus",
            // supply numeric rent & budget sliders
            monthlyRentMin: 600,
            monthlyRentMax: 900,
            budgetMin: 400,
            budgetMax: 1100,
            cleanliness: 4,
            sleepSchedule: "Flexible",
            smoker: false,
            petFriendly: true,
        )) { _, _ in }
        .padding()
        SwipeCardView(candidateID: "dummyCandidateID")
            .padding()
    }
}
