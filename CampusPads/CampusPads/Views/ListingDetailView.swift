import SwiftUI

struct ListingDetailView: View {
    let listing: ListingModel
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: AppTheme.spacing20) {
                // Header
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text(listing.title)
                        .font(AppTheme.title2)
                        .foregroundColor(AppTheme.textPrimary)
                    
                        HStack {
                            Image(systemName: "location")
                                .foregroundColor(AppTheme.primaryColor)
                        Text(listing.address)
                                .font(AppTheme.body)
                                .foregroundColor(AppTheme.textSecondary)
                    }
                }
                
                // Price and details
                if !listing.rent.isEmpty, let rentValue = Double(listing.rent) {
                    EnhancedCard {
                        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                            Text("Monthly Rent")
                                .font(AppTheme.headline)
                                .foregroundColor(AppTheme.textPrimary)
                            
                            Text("$\(Int(rentValue))")
                                .font(AppTheme.title1)
                                .foregroundColor(AppTheme.primaryColor)
                        }
                    }
                }
                
                // Description
                if let description = listing.description {
                    EnhancedCard {
                        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                            Text("Description")
                                .font(AppTheme.headline)
                                .foregroundColor(AppTheme.textPrimary)
                            
                            Text(description)
                                .font(AppTheme.body)
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }
                }
                
                // Contact button
                Button("Contact About This Listing") {
                    // TODO: Implement contact functionality
                }
                .buttonStyle(PrimaryButtonStyle())
                
                Spacer()
            }
            .padding(AppTheme.spacing20)
        }
        .background(AppTheme.backgroundGradient.ignoresSafeArea())
        .navigationTitle("Listing Details")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct ListingDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ListingDetailView(listing: ListingModel(
                title: "Sample Listing",
                address: "123 College St",
                rent: "1200",
                description: "A great place to live near campus",
                imageUrl: nil,
                location: nil,
                createdAt: Date(),
                isActive: true
            ))
        }
    }
}
