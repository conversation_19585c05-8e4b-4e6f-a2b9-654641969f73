import SwiftUI
import FirebaseFirestore

struct TopMatchesGridView: View {
    @StateObject private var topMatchesManager = TopMatchesManager.shared
    @StateObject private var profileVM = ProfileViewModel.shared
    @State private var showPremiumPrompt = false

    var body: some View {
        NavigationLink(destination: DedicatedTopMatchesView()) {
            VStack(spacing: AppTheme.spacing12) {
                // Premium Header
                headerView

                // Summary Content
                summaryContentView
            }
            .padding(AppTheme.spacing16)
            .background(premiumCardBackground)
            .cornerRadius(AppTheme.radiusLarge)
            .overlay(premiumBorder)
            .shadow(color: Color.yellow.opacity(0.3), radius: 12, x: 0, y: 6)
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            loadTopMatchesSummary()
        }
        .alert("Premium Required", isPresented: $showPremiumPrompt) {
            But<PERSON>("Upgrade") { showPremiumUpgrade() }
            But<PERSON>("Cancel", role: .cancel) { }
        } message: {
            Text("Upgrade to premium to access top matches!")
        }
    }

    // MARK: - Header View
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                HStack(spacing: AppTheme.spacing8) {
                    Text("Top Matches")
                        .font(.custom("AvenirNext-Bold", size: 18))
                        .foregroundStyle(AppTheme.sexyGradient)

                    // Premium crown icon
                    Image(systemName: "crown.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.yellow, .orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: .yellow.opacity(0.5), radius: 4, x: 0, y: 2)
                }

                Text("Tap to view premium matches")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()

            // Navigation arrow
            Image(systemName: "chevron.right")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(AppTheme.primaryColor)
        }
    }

    // MARK: - Summary Content View
    private var summaryContentView: some View {
        HStack(spacing: AppTheme.spacing16) {
            // Match count
            VStack(spacing: AppTheme.spacing4) {
                Text("\(topMatchesManager.topMatches.count)")
                    .font(.system(size: 24, weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Available")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()

            // Usage stats
            VStack(spacing: AppTheme.spacing4) {
                Text("\(topMatchesManager.maxDailyLikes - topMatchesManager.likedCount)")
                    .font(.system(size: 24, weight: .black, design: .rounded))
                    .foregroundColor(.red)

                Text("Likes Left")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()

            // Premium indicator
            VStack(spacing: AppTheme.spacing4) {
                Image(systemName: "star.fill")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.yellow)

                Text("Premium")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
    }



    // MARK: - Background Styling
    private var premiumCardBackground: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    AppTheme.primaryColor.opacity(0.1)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Glass effect
            AppTheme.glassEffect

            // Subtle shimmer
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.white.opacity(0.1),
                    Color.clear
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
            .stroke(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.yellow.opacity(0.6),
                        Color.orange.opacity(0.4),
                        Color.yellow.opacity(0.3),
                        Color.clear
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 2
            )
    }

    // MARK: - Action Handlers
    private func loadTopMatchesSummary() {
        guard let userID = profileVM.userProfile?.id else { return }

        Task {
            await topMatchesManager.loadTopMatches(for: userID)
        }
    }

    private func showPremiumUpgrade() {
        // Navigate to premium upgrade screen
        // This would typically be handled by a navigation coordinator
        print("🎯 Navigate to premium upgrade")
    }
}

// MARK: - Top Match Card Component
struct TopMatchCard: View {
    let match: UserModel
    let index: Int
    let canView: Bool
    let canLike: Bool
    let onTap: () -> Void
    let onLike: () -> Void

    @State private var isPressed = false
    @State private var showLikeAnimation = false

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Card Background
                cardBackground

                // Content
                if canView {
                    viewableContent
                } else {
                    lockedContent
                }

                // Like button overlay
                if canView {
                    likeButtonOverlay
                }

                // Compatibility score badge
                if canView {
                    compatibilityBadge
                }
            }
        }
        .frame(height: 100)
        .clipShape(RoundedRectangle(cornerRadius: AppTheme.radiusMedium))
        .overlay(cardBorder)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
    }

    private var cardBackground: some View {
        ZStack {
            if canView {
                // Profile image background
                AsyncImage(url: URL(string: match.profileImageUrls?.first ?? "")) { image in
                    image
                        .resizable()
                        .scaledToFill()
                } placeholder: {
                    Rectangle()
                        .fill(AppTheme.sexyGradient)
                        .overlay(
                            ProgressView()
                                .tint(.white)
                                .scaleEffect(0.8)
                        )
                }
            } else {
                // Locked gradient background
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppTheme.primaryColor.opacity(0.8),
                        AppTheme.accentColor.opacity(0.6)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }

            // Overlay gradient for text readability
            if canView {
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        Color.black.opacity(0.3)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            }
        }
    }

    private var viewableContent: some View {
        VStack {
            Spacer()

            HStack {
                VStack(alignment: .leading, spacing: AppTheme.spacing2) {
                    Text(match.firstName ?? "Unknown")
                        .font(.custom("AvenirNext-Bold", size: 12))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)

                    if let age = match.age {
                        Text("\(age)")
                            .font(.custom("AvenirNext-Medium", size: 10))
                            .foregroundColor(.white.opacity(0.9))
                            .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                    }
                }

                Spacer()
            }
            .padding(AppTheme.spacing8)
        }
    }

    private var lockedContent: some View {
        VStack(spacing: AppTheme.spacing8) {
            Image(systemName: "lock.fill")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white)
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)

            Text("Premium")
                .font(.custom("AvenirNext-Bold", size: 10))
                .foregroundColor(.white)
                .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
        }
    }

    private var likeButtonOverlay: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                        showLikeAnimation = true
                    }
                    onLike()

                    // Reset animation
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        showLikeAnimation = false
                    }
                }) {
                    ZStack {
                        Circle()
                            .fill(canLike ? Color.red : Color.gray)
                            .frame(width: 24, height: 24)
                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)

                        Image(systemName: "heart.fill")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .scaleEffect(showLikeAnimation ? 1.3 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.6), value: showLikeAnimation)
                    }
                }
                .disabled(!canLike)
            }
        }
        .padding(AppTheme.spacing6)
    }

    private var compatibilityBadge: some View {
        VStack {
            HStack {
                ZStack {
                    Capsule()
                        .fill(Color.green)
                        .frame(height: 16)

                    Text("\(Int((match.compatibilityScore ?? 0.0) * 100))%")
                        .font(.custom("AvenirNext-Bold", size: 9))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                }
                .padding(.horizontal, AppTheme.spacing6)

                Spacer()
            }
            Spacer()
        }
        .padding(AppTheme.spacing6)
    }

    private var cardBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
            .stroke(
                LinearGradient(
                    gradient: Gradient(colors: canView ? [
                        Color.white.opacity(0.4),
                        Color.white.opacity(0.2),
                        Color.clear
                    ] : [
                        Color.yellow.opacity(0.6),
                        Color.orange.opacity(0.4),
                        Color.clear
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: canView ? 1 : 2
            )
    }
}

// MARK: - Preview
struct TopMatchesGridView_Previews: PreviewProvider {
    static var previews: some View {
        TopMatchesGridView()
            .padding()
            .background(AppTheme.dynamicBackgroundGradient)
    }
}
