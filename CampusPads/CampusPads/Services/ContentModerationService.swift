import Foundation
import FirebaseFirestore
import FirebaseAuth

/// Comprehensive content moderation service for App Store compliance
class ContentModerationService: ObservableObject {
    static let shared = ContentModerationService()
    
    private let db = Firestore.firestore()
    
    // MARK: - Content Filtering
    
    /// Filters text content for inappropriate language and content
    func filterTextContent(_ text: String) -> ContentModerationResult {
        let cleanedText = TextNormalizationUtility.normalize(text)

        // FIXED: Allow common greetings and simple messages
        if isSimpleGreeting(cleanedText) {
            return ContentModerationResult(
                isApproved: true,
                filteredText: text,
                violationType: nil,
                severity: .none
            )
        }

        // Check for profanity
        if containsProfanity(cleanedText) {
            return ContentModerationResult(
                isApproved: false,
                filteredText: censorProfanity(text),
                violationType: .profanity,
                severity: .medium
            )
        }

        // Check for inappropriate content
        if containsInappropriateContent(cleanedText) {
            return ContentModerationResult(
                isApproved: false,
                filteredText: text,
                violationType: .inappropriateContent,
                severity: .high
            )
        }

        // Check for spam patterns
        if containsSpamPatterns(cleanedText) {
            return ContentModerationResult(
                isApproved: false,
                filteredText: text,
                violationType: .spam,
                severity: .low
            )
        }

        return ContentModerationResult(
            isApproved: true,
            filteredText: text,
            violationType: nil,
            severity: .none
        )
    }
    
    /// Validates profile content before saving
    func validateProfileContent(
        firstName: String?,
        lastName: String?,
        aboutMe: String?,
        interests: [String]?
    ) -> ProfileModerationResult {
        var violations: [ContentViolation] = []
        var filteredContent: [String: String] = [:]
        
        // Check first name
        if let firstName = firstName {
            let result = filterTextContent(firstName)
            if !result.isApproved {
                violations.append(ContentViolation(field: "firstName", type: result.violationType ?? .inappropriateContent))
            }
            filteredContent["firstName"] = result.filteredText
        }
        
        // Check last name
        if let lastName = lastName {
            let result = filterTextContent(lastName)
            if !result.isApproved {
                violations.append(ContentViolation(field: "lastName", type: result.violationType ?? .inappropriateContent))
            }
            filteredContent["lastName"] = result.filteredText
        }
        
        // Check about me
        if let aboutMe = aboutMe {
            let result = filterTextContent(aboutMe)
            if !result.isApproved {
                violations.append(ContentViolation(field: "aboutMe", type: result.violationType ?? .inappropriateContent))
            }
            filteredContent["aboutMe"] = result.filteredText
        }
        
        // Check interests
        if let interests = interests {
            for (index, interest) in interests.enumerated() {
                let result = filterTextContent(interest)
                if !result.isApproved {
                    violations.append(ContentViolation(field: "interests[\(index)]", type: result.violationType ?? .inappropriateContent))
                }
            }
        }
        
        return ProfileModerationResult(
            isApproved: violations.isEmpty,
            violations: violations,
            filteredContent: filteredContent
        )
    }
    
    /// Validates message content before sending
    func validateMessageContent(_ message: String) -> ContentModerationResult {
        return filterTextContent(message)
    }
    
    // MARK: - Image Content Moderation
    
    /// Enhanced image content validation with quarantine support
    func validateImageContent(_ imageData: Data, imageType: ImageType = .profile) -> ImageModerationResult {
        // Basic checks
        guard imageData.count > 0 else {
            return ImageModerationResult(isApproved: false, reason: "Empty image data", shouldQuarantine: false)
        }

        // Check file size based on image type
        let maxSize = getMaxSizeForImageType(imageType)
        guard imageData.count <= maxSize else {
            return ImageModerationResult(isApproved: false, reason: "Image too large for \(imageType.rawValue)", shouldQuarantine: false)
        }

        // Basic image format validation
        guard isValidImageFormat(imageData) else {
            return ImageModerationResult(isApproved: false, reason: "Invalid image format", shouldQuarantine: false)
        }

        // FUTURE: Integrate with Apple's Vision framework for content analysis
        // For now, perform basic heuristic checks
        let heuristicResult = performBasicImageHeuristics(imageData)

        if !heuristicResult.isApproved {
            return ImageModerationResult(
                isApproved: false,
                reason: heuristicResult.reason,
                shouldQuarantine: true // Flag for admin review
            )
        }

        // FUTURE: ML integration points for enhanced moderation:
        // - Apple Vision framework for object detection
        // - Third-party services like Google Cloud Vision API
        // - Custom ML models for inappropriate content detection

        return ImageModerationResult(isApproved: true, reason: nil, shouldQuarantine: false)
    }

    /// Quarantines inappropriate image for admin review
    func quarantineImage(imageData: Data, originalPath: String, userID: String, reason: String) async {
        let quarantineData: [String: Any] = [
            "originalPath": originalPath,
            "userID": userID,
            "reason": reason,
            "timestamp": FieldValue.serverTimestamp(),
            "status": "quarantined",
            "reviewStatus": "pending_admin_review",
            "imageSize": imageData.count
        ]

        do {
            // Record quarantine in Firestore
            try await db.collection("quarantinedImages").addDocument(data: quarantineData)

            // Create admin review entry
            await createAdminReviewEntry(
                type: "quarantined_image",
                userID: userID,
                details: "Image quarantined: \(reason)",
                severity: "high",
                data: quarantineData
            )

            print("🚨 Image quarantined for review: \(reason)")
        } catch {
            print("❌ Failed to quarantine image: \(error)")
        }
    }

    // MARK: - Private Image Validation Helpers

    private func getMaxSizeForImageType(_ type: ImageType) -> Int {
        switch type {
        case .profile: return 10 * 1024 * 1024 // 10MB
        case .property: return 15 * 1024 * 1024 // 15MB
        case .chat: return 50 * 1024 * 1024 // 50MB
        case .floorplan: return 10 * 1024 * 1024 // 10MB
        case .document: return 25 * 1024 * 1024 // 25MB
        }
    }

    private func isValidImageFormat(_ imageData: Data) -> Bool {
        // Check for valid image headers
        guard imageData.count >= 4 else { return false }

        let header = imageData.prefix(4)
        let bytes = [UInt8](header)

        // JPEG: FF D8 FF
        if bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF {
            return true
        }

        // PNG: 89 50 4E 47
        if bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47 {
            return true
        }

        // WebP: 52 49 46 46 (RIFF)
        if bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 {
            return true
        }

        return false
    }

    private func performBasicImageHeuristics(_ imageData: Data) -> ImageModerationResult {
        // Basic heuristic checks (placeholder for more sophisticated analysis)

        // Check for suspiciously small images (might be inappropriate)
        if imageData.count < 1024 { // Less than 1KB
            return ImageModerationResult(
                isApproved: false,
                reason: "Image too small, possibly inappropriate",
                shouldQuarantine: true
            )
        }

        // Check for extremely large images (might be inappropriate high-res content)
        if imageData.count > 50 * 1024 * 1024 { // More than 50MB
            return ImageModerationResult(
                isApproved: false,
                reason: "Image extremely large, requires review",
                shouldQuarantine: true
            )
        }

        // TODO: Add more sophisticated heuristics:
        // - Image dimension analysis
        // - Color histogram analysis
        // - Metadata examination

        return ImageModerationResult(isApproved: true, reason: nil, shouldQuarantine: false)
    }
    
    // MARK: - Automated Enforcement
    
    /// Records content violation for user and creates admin review entry
    func recordViolation(userID: String, violation: ContentViolation) async {
        let violationData: [String: Any] = [
            "userID": userID,
            "field": violation.field,
            "type": violation.type.rawValue,
            "timestamp": FieldValue.serverTimestamp(),
            "severity": violation.severity.rawValue,
            "status": "recorded",
            "reviewStatus": "pending"
        ]

        do {
            // Record violation in main collection
            try await db.collection("contentViolations").addDocument(data: violationData)

            // Create admin review entry for serious violations
            if violation.severity == .high || violation.severity == .critical {
                await createAdminReviewEntry(
                    type: "violation",
                    userID: userID,
                    details: "Content violation: \(violation.type.rawValue) in \(violation.field)",
                    severity: violation.severity.rawValue,
                    data: violationData
                )
            }

            // Check if user should be flagged for review
            await checkUserViolationHistory(userID: userID)

        } catch {
            print("❌ Failed to record content violation: \(error)")
        }
    }
    
    /// Checks user's violation history and takes appropriate action
    private func checkUserViolationHistory(userID: String) async {
        do {
            let snapshot = try await db.collection("contentViolations")
                .whereField("userID", isEqualTo: userID)
                .whereField("timestamp", isGreaterThan: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date())
                .getDocuments()
            
            let violationCount = snapshot.documents.count
            
            // Escalate based on violation count
            if violationCount >= 5 {
                await flagUserForReview(userID: userID, reason: "Multiple content violations")
            } else if violationCount >= 3 {
                await sendWarningToUser(userID: userID)
            }
            
        } catch {
            print("❌ Failed to check violation history: \(error)")
        }
    }
    
    /// Flags user account for manual review and creates admin review entry
    private func flagUserForReview(userID: String, reason: String) async {
        let flagData: [String: Any] = [
            "userID": userID,
            "reason": reason,
            "timestamp": FieldValue.serverTimestamp(),
            "status": "pending_review",
            "flaggedBy": "automated_system",
            "reviewStatus": "pending",
            "priority": "high"
        ]

        do {
            // Add to flagged users collection
            try await db.collection("flaggedUsers").addDocument(data: flagData)

            // Create admin review entry
            await createAdminReviewEntry(
                type: "flagged_user",
                userID: userID,
                details: reason,
                severity: "high",
                data: flagData
            )

            print("🚨 User \(userID) flagged for review: \(reason)")
        } catch {
            print("❌ Failed to flag user for review: \(error)")
        }
    }
    
    /// Sends warning notification to user
    private func sendWarningToUser(userID: String) async {
        // TODO: Implement in-app notification system
        print("⚠️ Warning sent to user \(userID) for content violations")
    }
    
    // MARK: - Private Helper Methods

    /// FIXED: Allow simple greetings and common messages
    private func isSimpleGreeting(_ text: String) -> Bool {
        let simpleGreetings = [
            "hello", "hi", "hey", "hello!", "hi!", "hey!",
            "good morning", "good afternoon", "good evening",
            "how are you", "how's it going", "what's up", "whats up",
            "nice to meet you", "thanks", "thank you", "you're welcome",
            "have a good day", "have a great day", "good night",
            "see you later", "talk soon", "ttyl", "bye", "goodbye",
            "lol", "haha", "😊", "😄", "👋", "🙂", "😀"
        ]

        // Allow if text is exactly a simple greeting or very short friendly message
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines.union(.punctuationCharacters))

        // Allow very short messages (under 20 characters) that don't contain bad words
        if trimmedText.count <= 20 && !containsActualProfanity(text) {
            return true
        }

        // Allow exact matches to simple greetings using robust text comparison
        return simpleGreetings.contains { greeting in
            TextNormalizationUtility.matches(trimmedText, greeting) ||
            TextNormalizationUtility.contains(text, searchTerm: greeting)
        }
    }

    /// Check for actual profanity (not just any flagged word)
    private func containsActualProfanity(_ text: String) -> Bool {
        let hardProfanity = ["fuck", "shit", "bitch", "asshole", "nigger", "faggot"]
        return hardProfanity.contains { profanity in
            TextNormalizationUtility.contains(text, searchTerm: profanity)
        }
    }

    private func containsProfanity(_ text: String) -> Bool {
        let profanityList = getProfanityList()

        // FIXED: Only flag if profanity is a standalone word, not part of other words
        let words = TextNormalizationUtility.normalize(text).components(separatedBy: .whitespaces)
        return profanityList.contains { profanity in
            words.contains(TextNormalizationUtility.normalize(profanity))
        }
    }
    
    private func containsInappropriateContent(_ text: String) -> Bool {
        // FIXED: More targeted inappropriate content detection
        let inappropriatePatterns = [
            "explicit sexual", "nude photos", "naked pics", "porn videos", "xxx content",
            "drug dealer", "selling drugs", "buy drugs", "weed dealer",
            "hate speech", "racist comments", "nazi ideology", "terrorist activity",
            "send nudes", "hook up tonight", "dtf", "netflix and chill"
        ]

        // Only flag if text contains full inappropriate phrases using robust text matching
        return inappropriatePatterns.contains { pattern in
            TextNormalizationUtility.contains(text, searchTerm: pattern)
        }
    }
    
    private func containsSpamPatterns(_ text: String) -> Bool {
        // FIXED: Much less aggressive spam detection - only block obvious spam
        let spamPatterns = [
            "click here now", "free money fast", "make money quick",
            "visit my website", "check out my link", "follow me on",
            "add me on snap", "dm me on insta", "text me at",
            "call me at", "email me at", "venmo me", "cashapp me"
        ]

        // Only flag if text contains full spam phrases, not individual words
        return spamPatterns.contains { pattern in
            text.lowercased().contains(pattern.lowercased())
        }
    }
    
    private func censorProfanity(_ text: String) -> String {
        var censored = text
        let profanityList = getProfanityList()
        
        for word in profanityList {
            let replacement = String(repeating: "*", count: word.count)
            censored = censored.replacingOccurrences(of: word, with: replacement, options: .caseInsensitive)
        }
        
        return censored
    }
    
    private func getProfanityList() -> [String] {
        // FIXED: More reasonable profanity list - only block truly offensive content
        return [
            // Strong profanity (censored but allowed with *)
            "fuck", "fucking", "shit", "bitch", "asshole", "bastard",
            "motherfucker", "cocksucker", "dickhead", "prick",

            // Slurs and hate speech (blocked completely)
            "nigger", "faggot", "retard", "spic", "chink", "kike",
            "wetback", "towelhead", "raghead", "gook", "jap",

            // Explicit sexual content (blocked)
            "cock", "pussy", "porn", "xxx", "masturbate",
            "send nudes", "nude pics", "naked photos",

            // Hard drugs (blocked)
            "cocaine", "heroin", "meth", "ecstasy", "molly",
            "selling drugs", "buy drugs", "drug dealer",

            // Violence/threats (blocked)
            "kill yourself", "kys", "murder", "rape",
            "terrorist", "bomb threat", "school shooting"
        ]

        // REMOVED: Common words that were being flagged incorrectly:
        // - "damn", "hell", "crap", "stupid", "idiot", "moron" (too mild)
        // - "penis", "vagina", "sex", "nude", "naked" (medical/normal terms)
        // - "tits", "boobs" (mild, often used casually)
        // - "acid", "shrooms" (too broad, could be chemistry/food)
        // - "suicide", "violence" (important topics that should be discussable)
    }

    /// Enhanced content filtering with machine learning integration placeholder
    func enhancedContentFilter(_ text: String) -> ContentModerationResult {
        // FUTURE: Integrate with Apple's Natural Language framework
        // FUTURE: Integrate with third-party services like Perspective API

        // For now, use rule-based filtering
        return filterTextContent(text)
    }

    /// Reports content for manual review
    func reportContentForReview(
        contentType: String,
        contentID: String,
        reportedBy: String,
        reason: String,
        content: String
    ) async {
        let reportData: [String: Any] = [
            "contentType": contentType,
            "contentID": contentID,
            "reportedBy": reportedBy,
            "reason": reason,
            "content": content,
            "timestamp": FieldValue.serverTimestamp(),
            "status": "pending_review",
            "reviewStatus": "pending"
        ]

        do {
            // Add to content reports collection
            try await db.collection("contentReports").addDocument(data: reportData)

            // Create admin review entry
            await createAdminReviewEntry(
                type: "content_report",
                userID: reportedBy,
                details: "Content reported: \(reason)",
                severity: "medium",
                data: reportData
            )

            print("📝 Content reported for manual review")
        } catch {
            print("❌ Failed to report content: \(error)")
        }
    }

    /// Creates an admin review entry for Firebase Console review
    private func createAdminReviewEntry(
        type: String,
        userID: String,
        details: String,
        severity: String,
        data: [String: Any]
    ) async {
        let adminReviewData: [String: Any] = [
            "type": type,
            "userID": userID,
            "details": details,
            "severity": severity,
            "timestamp": FieldValue.serverTimestamp(),
            "status": "pending_admin_review",
            "priority": severity == "critical" ? "urgent" : (severity == "high" ? "high" : "normal"),
            "reviewedBy": "",
            "reviewNotes": "",
            "actionTaken": "",
            "originalData": data
        ]

        do {
            try await db.collection("adminReview").addDocument(data: adminReviewData)
            print("📋 Admin review entry created for \(type)")
        } catch {
            print("❌ Failed to create admin review entry: \(error)")
        }
    }
}

// MARK: - Supporting Types

struct ContentModerationResult {
    let isApproved: Bool
    let filteredText: String
    let violationType: ViolationType?
    let severity: ViolationSeverity
}

struct ProfileModerationResult {
    let isApproved: Bool
    let violations: [ContentViolation]
    let filteredContent: [String: String]
}

struct ImageModerationResult {
    let isApproved: Bool
    let reason: String?
    let shouldQuarantine: Bool
}

enum ImageType: String, CaseIterable {
    case profile = "profile"
    case property = "property"
    case chat = "chat"
    case floorplan = "floorplan"
    case document = "document"
}

struct ContentViolation {
    let field: String
    let type: ViolationType
    let severity: ViolationSeverity = .medium
}

enum ViolationType: String, CaseIterable {
    case profanity = "profanity"
    case inappropriateContent = "inappropriate_content"
    case spam = "spam"
    case harassment = "harassment"
    case hate = "hate_speech"
}

enum ViolationSeverity: String, CaseIterable {
    case none = "none"
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
}
