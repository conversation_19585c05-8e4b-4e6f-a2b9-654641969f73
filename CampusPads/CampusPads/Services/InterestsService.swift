// InterestsService.swift
// Service for managing interests from Firebase

import Foundation
import FirebaseDatabase
import Combine

// MARK: - Data Models

struct Interest: Codable, Identifiable, Hashable {
    let id = UUID()
    let name: String
    let category: String
    let emoji: String?
    
    init(name: String, category: String, emoji: String? = nil) {
        self.name = name
        self.category = category
        self.emoji = emoji
    }
}

class InterestsService: ObservableObject {
    static let shared = InterestsService()
    
    private let database = Database.database()
    private var interestsRef: DatabaseReference
    
    @Published var interests: [Interest] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        interestsRef = database.reference().child("interests")
        loadInterests()
    }
    
    // MARK: - Public Methods
    
    /// Loads interests from Firebase Realtime Database
    func loadInterests() {
        isLoading = true
        errorMessage = nil
        
        print("🎯 InterestsService: Loading interests from Firebase...")
        
        interestsRef.observeSingleEvent(of: .value) { [weak self] snapshot in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if snapshot.exists() {
                    if let interestsDict = snapshot.value as? [String: Any] {
                        let loadedInterests = interestsDict.compactMap { (key, value) -> Interest? in
                            guard let interestData = value as? [String: Any],
                                  let name = interestData["name"] as? String,
                                  let category = interestData["category"] as? String else {
                                return nil
                            }
                            let emoji = interestData["emoji"] as? String
                            return Interest(name: name, category: category, emoji: emoji)
                        }.sorted { $0.name < $1.name }
                        
                        print("✅ InterestsService: Loaded \(loadedInterests.count) interests from Firebase")
                        self?.interests = loadedInterests
                    }
                } else {
                    print("⚠️ InterestsService: No interests found in Firebase")
                    self?.errorMessage = "No interests data available"
                }
            }
        } withCancel: { [weak self] error in
            DispatchQueue.main.async {
                self?.isLoading = false
                self?.errorMessage = error.localizedDescription
                print("❌ InterestsService: Error loading interests: \(error.localizedDescription)")
            }
        }
    }
    
    /// Searches interests by name
    func searchInterests(query: String) -> [Interest] {
        guard !query.isEmpty else { return interests }
        return interests.filter { $0.name.localizedCaseInsensitiveContains(query) }
    }
    
    /// Gets interests for a specific category
    func getInterests(for category: String) -> [Interest] {
        return interests.filter { $0.category == category }
    }
    
    /// Gets all unique categories
    func getCategories() -> [String] {
        let categories = Set(interests.map { $0.category })
        return Array(categories).sorted()
    }
    
    /// Gets popular interests (first 20 alphabetically for now)
    func getPopularInterests() -> [Interest] {
        return Array(interests.prefix(20))
    }
    
    /// Refreshes interests data
    func refresh() {
        loadInterests()
    }
    
    /// Clears error message
    func clearError() {
        errorMessage = nil
    }
}
