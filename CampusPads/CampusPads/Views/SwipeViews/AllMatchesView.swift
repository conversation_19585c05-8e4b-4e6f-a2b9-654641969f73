import SwiftUI
import FirebaseFirestore
import FirebaseFirestoreSwift
import FirebaseFirestoreCombineSwift
import FirebaseAuth
import Combine

struct AllMatchesView: View {
    @StateObject private var viewModel = MatchesDashboardViewModel()
    @StateObject private var chatsViewModel = ChatsListViewModel()
    @State private var selectedMatch: MatchItem?
    @State private var showMatchDetail = false
    @State private var navigateToChatID: String?

    // Use shared ChatManager instance
    private let chatManager = ChatManager.shared

    // Callback to navigate to discover tab
    var onNavigateToDiscover: (() -> Void)?

    // Filtered matches that exclude users with existing conversations
    private var availableMatches: [MatchItem] {
        let existingChatParticipants = Set(chatsViewModel.chats.flatMap { $0.participants })

        return viewModel.matches.filter { match in
            // Check if any participant (other than current user) has an existing chat
            let otherParticipants = match.participants.filter { $0 != viewModel.currentUserID }
            return !otherParticipants.contains { existingChatParticipants.contains($0) }
        }
    }

    var body: some View {
        ZStack {
            // Enhanced background with dynamic gradient
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()

            // Subtle animated overlay for depth
            AnimatedBackground()
                .opacity(0.3)
                .ignoresSafeArea()

            if availableMatches.isEmpty {
                emptyStateView
            } else {
                matchesGridView
            }
        }
        .toolbar {
            ToolbarItem(placement: .principal) {
                VStack(spacing: 2) {
                    Text("Your Matches")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .fontWeight(.bold)
                        .foregroundStyle(AppTheme.sexyGradient)
                        .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)

                    Text("\(availableMatches.count) new connections")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(AppTheme.textSecondary)
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    // Handle filter/sort
                }) {
                    Image(systemName: "slider.horizontal.3")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundStyle(AppTheme.sexyGradient)
                        .frame(width: 32, height: 32)
                        .background(AppTheme.glassEffect)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                }
            }
        }
        .onAppear {
            viewModel.startRealtimeMatchLoading()
            chatsViewModel.fetchChats()
        }
        .onDisappear {
            // Clean up real-time listener when view disappears
            viewModel.stopRealtimeMatchLoading()
        }
        .refreshable {
            // Pull-to-refresh support
            viewModel.startRealtimeMatchLoading()
            chatsViewModel.fetchChats()
        }
        .fullScreenCover(isPresented: $showMatchDetail) {
            if let match = selectedMatch {
                TinderStyleMatchProfileSheet(
                    match: match,
                    onStartChat: { chatID in
                        navigateToChatID = chatID
                        showMatchDetail = false
                    },
                    onDismiss: {
                        showMatchDetail = false
                        selectedMatch = nil
                    }
                )
            }
        }
        .background(
            NavigationLink(
                destination: navigateToChatID.map { chatID in
                    ChatConversationView(
                        viewModel: ChatConversationViewModel(chatID: chatID),
                        chatPartnerID: selectedMatch.map { candidateID(for: $0) } ?? ""
                    )
                },
                tag: "navigate",
                selection: Binding(
                    get: { navigateToChatID != nil ? "navigate" : nil },
                    set: { _ in navigateToChatID = nil }
                )
            ) {
                EmptyView()
            }
            .hidden()
        )
    }

    // MARK: - Enhanced Match Grid View with Proper Sizing
    private var matchesGridView: some View {
        GeometryReader { geometry in
            let screenWidth = geometry.size.width
            let padding: CGFloat = AppTheme.spacing20 * 2 // Left and right padding
            let spacing: CGFloat = AppTheme.spacing16 // Space between cards
            let cardWidth = (screenWidth - padding - spacing) / 2 // Perfect 2-column layout

            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.fixed(cardWidth), spacing: AppTheme.spacing16),
                    GridItem(.fixed(cardWidth), spacing: AppTheme.spacing16)
                ], spacing: AppTheme.spacing20) {
                    ForEach(availableMatches) { match in
                        EnhancedMatchCard(
                            match: match,
                            candidateID: candidateID(for: match),
                            cardWidth: cardWidth,
                            onTap: {
                                selectedMatch = match
                                showMatchDetail = true
                            },
                            onChatTap: {
                                selectedMatch = match
                                showMatchDetail = true
                            },
                            viewModel: viewModel
                        )
                    }
                }
                .padding(.horizontal, AppTheme.spacing20)
                .padding(.top, AppTheme.spacing16)
                .padding(.bottom, AppTheme.spacing32) // Extra bottom padding
            }
        }
    }

    // MARK: - Enhanced Empty State
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing32) {
            // Animated heart icon
            ZStack {
                Circle()
                    .fill(AppTheme.primaryGradient)
                    .frame(width: 120, height: 120)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 20, x: 0, y: 10)

                Image(systemName: "heart.fill")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.white)
            }

            VStack(spacing: AppTheme.spacing16) {
                Text(viewModel.matches.isEmpty ? "No Matches Yet" : "All Caught Up!")
                    .font(AppTheme.title1)
                    .fontWeight(.bold)
                    .foregroundColor(AppTheme.textPrimary)

                Text(viewModel.matches.isEmpty ?
                     "Start swiping to find your perfect roommate match!" :
                     "You've already started conversations with all your matches. Keep swiping to find more!")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.spacing32)
            }

            Button(action: {
                // Navigate to discover tab
                onNavigateToDiscover?()
            }) {
                HStack(spacing: AppTheme.spacing12) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 20, weight: .semibold))
                    Text("Start Matching")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing32)
                .padding(.vertical, AppTheme.spacing16)
                .background(AppTheme.primaryGradient)
                .cornerRadius(AppTheme.radiusXLarge)
                .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 15, x: 0, y: 8)
            }
            .scaleEffect(1.0)
            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: UUID())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(AppTheme.spacing24)
    }

    private func candidateID(for match: MatchItem) -> String {
        guard let currentUID = viewModel.currentUserID else { return "" }
        return match.participants.first(where: { $0 != currentUID }) ?? ""
    }

    /// Handle match tap - creates or gets chat and navigates
    private func handleMatchTap(match: MatchItem, candidateID: String) {
        guard let currentUserID = viewModel.currentUserID else { return }

        // Create or get existing chat
        chatManager.createOrGetChat(between: currentUserID, and: candidateID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let chatID):
                    // Update match status
                    ChatManager.shared.markConversationStarted(for: [currentUserID, candidateID], chatID: chatID)

                    // Navigate to chat
                    navigateToChatID = chatID

                case .failure(let error):
                    print("❌ Failed to create chat: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - Enhanced Match Card Component with High-Quality Design
struct EnhancedMatchCard: View {
    let match: MatchItem
    let candidateID: String
    let cardWidth: CGFloat
    let onTap: () -> Void
    let onChatTap: () -> Void
    let viewModel: MatchesDashboardViewModel

    @State private var isPressed = false
    @State private var profile: UserModel?

    // Calculate optimal image height based on card width for perfect aspect ratio
    private var imageHeight: CGFloat {
        cardWidth * 1.2 // 1.2:1 aspect ratio for modern card design
    }

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // Ultra High-Quality Profile Image Section
                ZStack {
                    // Background with enhanced gradient and subtle glow
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.sexyGradient)
                        .frame(width: cardWidth, height: imageHeight)
                        .overlay(
                            // Subtle outer glow with perfect blur
                            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                                .stroke(AppTheme.primaryColor.opacity(0.4), lineWidth: 0.5)
                                .blur(radius: 1.5)
                        )
                        .shadow(color: AppTheme.primaryColor.opacity(0.15), radius: 8, x: 0, y: 4)
                        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)

                    // Ultra High-Quality Profile Image with Perfect Sizing
                    if let imageUrl = profile?.profileImageUrls?.first,
                       let url = URL(string: imageUrl) {
                        AsyncImage(url: url) { image in
                            image
                                .resizable()
                                .scaledToFill()
                                .frame(width: cardWidth, height: imageHeight)
                                .clipped()
                        } placeholder: {
                            ZStack {
                                AppTheme.sexyGradient

                                // Enhanced loading animation with better spacing
                                VStack(spacing: 12) {
                                    ProgressView()
                                        .tint(.white)
                                        .scaleEffect(1.3)

                                    Text("Loading...")
                                        .font(.custom("AvenirNext-Medium", size: 13))
                                        .foregroundColor(.white.opacity(0.9))
                                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                                }
                            }
                            .frame(width: cardWidth, height: imageHeight)
                            .shimmerEffect()
                        }
                        .cornerRadius(AppTheme.radiusLarge, corners: [.topLeft, .topRight])
                        .overlay(
                            // Ultra-subtle glass overlay for premium depth
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.clear,
                                    Color.clear,
                                    Color.clear,
                                    Color.black.opacity(0.05),
                                    Color.black.opacity(0.2)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .cornerRadius(AppTheme.radiusLarge, corners: [.topLeft, .topRight])
                        )
                    } else {
                        VStack(spacing: AppTheme.spacing16) {
                            Image(systemName: "person.circle.fill")
                                .font(.system(size: min(cardWidth * 0.25, 70), weight: .light))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)

                            Text("Loading...")
                                .font(.custom("AvenirNext-Medium", size: 15))
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.5), radius: 2, x: 0, y: 1)
                        }
                        .frame(width: cardWidth, height: imageHeight)
                        .shimmerEffect()
                    }

                    // Match indicator overlay
                    VStack {
                        HStack {
                            Spacer()
                            ZStack {
                                Circle()
                                    .fill(AppTheme.successColor)
                                    .frame(width: 24, height: 24)

                                Image(systemName: "heart.fill")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            .padding(AppTheme.spacing12)
                        }
                        Spacer()
                    }
                }

                // Enhanced Info Section with Modern Typography
                VStack(spacing: AppTheme.spacing12) {
                    HStack {
                        VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                            Text(profile?.firstName ?? "Loading...")
                                .font(.custom("AvenirNext-Bold", size: 18))
                                .fontWeight(.semibold)
                                .foregroundStyle(AppTheme.sexyGradient)
                                .lineLimit(1)

                            Text(timeAgoString)
                                .font(.custom("AvenirNext-Medium", size: 12))
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()

                        // Enhanced Chat button with modern styling
                        Button(action: {
                            onChatTap()
                        }) {
                            ZStack {
                                Circle()
                                    .fill(AppTheme.sexyGradient)
                                    .frame(width: 44, height: 44)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 6, x: 0, y: 3)

                                Image(systemName: "message.fill")
                                    .font(.system(size: 18, weight: .medium))
                                    .foregroundColor(.white)
                                    .shadow(color: .black.opacity(0.2), radius: 1, x: 0, y: 1)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                        .scaleEffect(1.0)
                        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: UUID())
                    }
                }
                .padding(AppTheme.spacing20)
                .background(
                    ZStack {
                        // Glass morphism background
                        AppTheme.modernCardGradient

                        // Subtle overlay
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.1),
                                Color.clear
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    }
                )
                .cornerRadius(AppTheme.radiusLarge, corners: [.bottomLeft, .bottomRight])
            }
        }
        .frame(width: cardWidth) // Ensure consistent card width
        .buttonStyle(PlainButtonStyle())
        .background(
            ZStack {
                // Ultra High-Quality card background with perfect glass morphism
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.08),
                                Color.white.opacity(0.03)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(AppTheme.glassEffect)
                    .opacity(0.8)
            }
        )
        .cornerRadius(AppTheme.radiusLarge)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.6),
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.05),
                            Color.clear
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .shadow(color: AppTheme.primaryColor.opacity(0.08), radius: 16, x: 0, y: 8)
        .shadow(color: Color.black.opacity(0.03), radius: 6, x: 0, y: 3)
        .shadow(color: Color.black.opacity(0.02), radius: 2, x: 0, y: 1)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.9), value: isPressed)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
        .onAppear {
            // Use cached profile if available, otherwise load
            if let cachedProfile = viewModel.profilesCache[candidateID] {
                profile = cachedProfile
            } else {
                // Fallback to individual loading if not cached
                Task {
                    do {
                        let snapshot = try await Firestore.firestore()
                            .collection("users")
                            .document(candidateID)
                            .getDocument()

                        if snapshot.exists {
                            let loadedProfile = try snapshot.data(as: UserModel.self)
                            await MainActor.run {
                                profile = loadedProfile
                                viewModel.profilesCache[candidateID] = loadedProfile
                            }
                        }
                    } catch {
                        print("❌ EnhancedMatchCard: Error loading profile: \(error.localizedDescription)")
                    }
                }
            }
        }
        .onChange(of: viewModel.profilesCache[candidateID]) { newProfile in
            if let newProfile = newProfile {
                profile = newProfile
            }
        }
    }

    private var timeAgoString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return "Matched " + formatter.localizedString(for: match.createdAt, relativeTo: Date())
    }
}



// MARK: - Helper Extensions
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
