//
//  ProfileViewTrackingService.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseFirestore
import FirebaseAuth
import Combine

/// Service for tracking and managing profile view events
class ProfileViewTrackingService: ObservableObject {
    static let shared = ProfileViewTrackingService()

    private let db = Firestore.firestore()
    private let networkMonitor = NetworkMonitor.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Private Properties

    private let profileViewsCollection = "profileViews"
    private let viewStatsCollection = "profileViewStats"

    // CRITICAL FIX: Add flag to disable profile view tracking if needed
    private let isTrackingEnabled = true // Set to false to completely disable tracking

    // Cache for recent views to avoid duplicate tracking
    private var recentViewCache: Set<String> = []
    private let cacheExpirationTime: TimeInterval = 300 // 5 minutes

    private init() {
        setupCacheCleanup()
    }

    // MARK: - Public Methods

    /// Records a profile view event
    /// - Parameters:
    ///   - viewedUserID: The ID of the user whose profile was viewed
    ///   - source: The source of the view
    ///   - metadata: Additional metadata about the view
    func recordProfileView(
        viewedUserID: String,
        source: ProfileViewSource,
        metadata: ProfileViewMetadata? = nil
    ) {
        // CRITICAL FIX: Check if tracking is enabled
        guard isTrackingEnabled else {
            print("ProfileViewTrackingService: Profile view tracking is disabled")
            return
        }

        guard let currentUserID = Auth.auth().currentUser?.uid else {
            print("ProfileViewTrackingService: Cannot record view - user not authenticated")
            return
        }

        // Don't track self-views
        guard currentUserID != viewedUserID else {
            return
        }

        // Check cache to avoid duplicate tracking within short time window
        let cacheKey = "\(currentUserID)_\(viewedUserID)"
        if recentViewCache.contains(cacheKey) {
            return
        }

        // Add to cache
        recentViewCache.insert(cacheKey)

        let viewEvent = ProfileViewEvent(
            viewedUserID: viewedUserID,
            viewerUserID: currentUserID,
            source: source,
            metadata: metadata
        )

        // Record the view event
        recordViewEvent(viewEvent)

        // Update aggregated stats
        updateViewStats(for: viewedUserID, source: source)
    }

    /// CRITICAL FIX: Simplified profile views fetching without requiring Firestore index
    /// Fetches profile view events for a specific user using basic query only
    /// - Parameters:
    ///   - userID: The user ID to fetch views for
    ///   - limit: Maximum number of views to fetch
    ///   - completion: Completion handler with the result
    func fetchProfileViews(
        for userID: String,
        limit: Int = 50,
        completion: @escaping (Result<[ProfileViewEvent], Error>) -> Void
    ) {
        // CRITICAL FIX: If tracking is disabled, return empty results
        guard isTrackingEnabled else {
            print("ProfileViewTrackingService: Profile view tracking is disabled, returning empty results")
            completion(.success([]))
            return
        }

        print("🔍 ProfileViewTrackingService: Fetching profile views for user: \(userID) (simplified query)")

        // CRITICAL FIX: Use simple query without ordering to avoid index requirement
        // This removes the need for a composite index (viewedUserID + timestamp)
        db.collection(profileViewsCollection)
            .whereField("viewedUserID", isEqualTo: userID)
            .limit(to: limit)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("❌ ProfileViewTrackingService: Failed to fetch profile views: \(error.localizedDescription)")
                    completion(.failure(error))
                    return
                }

                guard let documents = snapshot?.documents else {
                    print("📊 ProfileViewTrackingService: No profile views found for user: \(userID)")
                    completion(.success([]))
                    return
                }

                print("📊 ProfileViewTrackingService: Retrieved \(documents.count) profile view documents")

                do {
                    let views = try documents.compactMap { document in
                        try document.data(as: ProfileViewEvent.self)
                    }

                    // CRITICAL FIX: Sort in memory instead of using Firestore ordering
                    // This avoids the need for a composite index
                    let sortedViews = views.sorted { $0.timestamp > $1.timestamp }

                    print("✅ ProfileViewTrackingService: Successfully parsed and sorted \(sortedViews.count) profile views")
                    completion(.success(Array(sortedViews.prefix(limit))))
                } catch {
                    print("❌ ProfileViewTrackingService: Failed to parse profile views: \(error.localizedDescription)")
                    completion(.failure(error))
                }
            }
    }

    /// Fetches aggregated view statistics for a user
    /// - Parameters:
    ///   - userID: The user ID to fetch stats for
    ///   - completion: Completion handler with the result
    func fetchViewStats(
        for userID: String,
        completion: @escaping (Result<ProfileViewStats, Error>) -> Void
    ) {
        db.collection(viewStatsCollection)
            .document(userID)
            .getDocument { snapshot, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let document = snapshot, document.exists else {
                    // Return empty stats if no document exists
                    completion(.success(ProfileViewStats()))
                    return
                }

                do {
                    let stats = try document.data(as: ProfileViewStats.self)
                    completion(.success(stats))
                } catch {
                    completion(.failure(error))
                }
            }
    }

    // MARK: - Private Methods

    private func recordViewEvent(_ viewEvent: ProfileViewEvent) {
        do {
            try db.collection(profileViewsCollection).addDocument(from: viewEvent) { error in
                if let error = error {
                    print("ProfileViewTrackingService: Failed to record view event - \(error.localizedDescription)")
                }
            }
        } catch {
            print("ProfileViewTrackingService: Failed to encode view event - \(error.localizedDescription)")
        }
    }

    private func updateViewStats(for userID: String, source: ProfileViewSource) {
        let statsRef = db.collection(viewStatsCollection).document(userID)

        db.runTransaction { transaction, errorPointer in
            let statsDocument: DocumentSnapshot
            do {
                try statsDocument = transaction.getDocument(statsRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }

            var stats: ProfileViewStats
            if statsDocument.exists {
                do {
                    stats = try statsDocument.data(as: ProfileViewStats.self)
                } catch {
                    stats = ProfileViewStats()
                }
            } else {
                stats = ProfileViewStats()
            }

            // Update stats
            let newTotalViews = stats.totalViews + 1
            var newViewsBySource = stats.viewsBySource
            newViewsBySource[source] = (newViewsBySource[source] ?? 0) + 1

            let updatedStats = ProfileViewStats(
                totalViews: newTotalViews,
                uniqueViewers: stats.uniqueViewers, // This would need separate calculation
                viewsBySource: newViewsBySource,
                recentViews: stats.recentViews,
                topViewers: stats.topViewers
            )

            do {
                try transaction.setData(from: updatedStats, forDocument: statsRef, merge: true)
            } catch let updateError as NSError {
                errorPointer?.pointee = updateError
                return nil
            }

            return nil
        } completion: { _, error in
            if let error = error {
                print("ProfileViewTrackingService: Failed to update view stats - \(error.localizedDescription)")
            }
        }
    }

    private func setupCacheCleanup() {
        // Clean cache every 5 minutes
        Timer.scheduledTimer(withTimeInterval: cacheExpirationTime, repeats: true) { _ in
            self.recentViewCache.removeAll()
        }
    }
}
