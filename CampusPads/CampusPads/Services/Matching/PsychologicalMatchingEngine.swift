import Foundation
import FirebaseFirestore

// MARK: - Match Quality Tiers
struct MatchTiers {
    let premium: [UserModel]   // 90%+ compatibility
    let good: [UserModel]      // 75-89% compatibility
    let medium: [UserModel]    // 60-74% compatibility
    let basic: [UserModel]     // <60% compatibility
}

// MARK: - Engagement Phases
enum EngagementPhase {
    case hook        // Swipes 1-5: Build confidence
    case exploration // Swipes 6-15: Maintain interest
    case reward      // Swipes 16-20: Strategic premium placement
    case sustained   // Swipes 21+: Variable ratio reinforcement
}

// MARK: - Psychological Matching Engine
@MainActor
class PsychologicalMatchingEngine: ObservableObject {
    static let shared = PsychologicalMatchingEngine()
    private let db = Firestore.firestore()

    @Published var isOptimizing = false
    @Published var lastOptimizationTime: Date?

    private init() {}

    // MARK: - Main Distribution Algorithm
    func distributeMatches(for userID: String, allMatches: [UserModel]) async -> [UserModel] {
        print("🧠 Applying psychological distribution for user: \(userID)")

        DispatchQueue.main.async {
            self.isOptimizing = true
        }

        // 1. Remove top matches that are reserved for premium
        let availableMatches = filterOutTopMatches(allMatches)

        // 2. Score and sort remaining matches
        guard let currentUser = await getCurrentUser(userID) else {
            print("❌ Could not load current user for psychological matching")
            return availableMatches.shuffled()
        }

        let scoredMatches = SmartMatchingEngine.generateSortedMatches(
            from: availableMatches,
            currentUser: currentUser
        )

        // 3. Get user's engagement pattern
        let engagementPattern = await getUserEngagementPattern(userID: userID)

        // 4. Apply psychological distribution
        let distributedMatches = applyPsychologicalDistribution(scoredMatches, pattern: engagementPattern)

        DispatchQueue.main.async {
            self.isOptimizing = false
            self.lastOptimizationTime = Date()
        }

        // 5. Track optimization event
        AnalyticsManager.shared.trackEvent("psychological_optimization_applied", parameters: [
            "user_id": userID,
            "total_matches": distributedMatches.count,
            "behavior_tier": engagementPattern.behaviorTier,
            "selectivity": engagementPattern.swipeSelectivity
        ])

        print("✅ Psychological distribution completed: \(distributedMatches.count) matches")
        return distributedMatches
    }

    private func applyPsychologicalDistribution(_ matches: [UserModel], pattern: EngagementPattern) -> [UserModel] {
        // Separate matches into quality tiers
        let tiers = separateIntoTiers(matches)

        print("🎯 Match tiers - Premium: \(tiers.premium.count), Good: \(tiers.good.count), Medium: \(tiers.medium.count), Basic: \(tiers.basic.count)")

        var distributedStack: [UserModel] = []

        // Phase 1: Hook (Swipes 1-5) - Good but not perfect matches
        let hookPhase = createHookPhase(from: tiers, pattern: pattern)
        distributedStack.append(contentsOf: hookPhase)

        // Phase 2: Exploration (Swipes 6-15) - Mixed quality with occasional premium
        let explorationPhase = createExplorationPhase(from: tiers, pattern: pattern)
        distributedStack.append(contentsOf: explorationPhase)

        // Phase 3: Reward (Swipes 16-20) - Strategic premium placement
        let rewardPhase = createRewardPhase(from: tiers, pattern: pattern)
        distributedStack.append(contentsOf: rewardPhase)

        // Phase 4: Sustained Engagement (Swipes 21+) - Variable ratio reinforcement
        let sustainedPhase = createSustainedPhase(from: tiers, pattern: pattern)
        distributedStack.append(contentsOf: sustainedPhase)

        return distributedStack
    }

    private func separateIntoTiers(_ matches: [UserModel]) -> MatchTiers {
        let sortedMatches = matches.sorted { ($0.compatibilityScore ?? 0) > ($1.compatibilityScore ?? 0) }

        let premiumThreshold = 0.90  // 90%+ compatibility
        let goodThreshold = 0.75     // 75%+ compatibility
        let mediumThreshold = 0.60   // 60%+ compatibility

        return MatchTiers(
            premium: sortedMatches.filter { ($0.compatibilityScore ?? 0) >= premiumThreshold },
            good: sortedMatches.filter {
                let score = $0.compatibilityScore ?? 0
                return score >= goodThreshold && score < premiumThreshold
            },
            medium: sortedMatches.filter {
                let score = $0.compatibilityScore ?? 0
                return score >= mediumThreshold && score < goodThreshold
            },
            basic: sortedMatches.filter { ($0.compatibilityScore ?? 0) < mediumThreshold }
        )
    }

    // MARK: - Engagement Phases
    private func createHookPhase(from tiers: MatchTiers, pattern: EngagementPattern) -> [UserModel] {
        // Start with good matches to build confidence, avoid premium to prevent immediate satisfaction
        var hookMatches: [UserModel] = []

        // Adjust based on user's selectivity
        if pattern.swipeSelectivity > 0.5 {
            // Picky users get better quality in hook phase
            hookMatches.append(contentsOf: Array(tiers.good.shuffled().prefix(4)))
            hookMatches.append(contentsOf: Array(tiers.medium.shuffled().prefix(1)))
        } else {
            // Less picky users get more variety
            hookMatches.append(contentsOf: Array(tiers.good.shuffled().prefix(2)))
            hookMatches.append(contentsOf: Array(tiers.medium.shuffled().prefix(3)))
        }

        return hookMatches.shuffled()
    }

    private func createExplorationPhase(from tiers: MatchTiers, pattern: EngagementPattern) -> [UserModel] {
        var explorationMatches: [UserModel] = []

        // Mix of good and medium with strategic premium injection
        explorationMatches.append(contentsOf: Array(tiers.good.dropFirst(pattern.swipeSelectivity > 0.5 ? 4 : 2).shuffled().prefix(4)))
        explorationMatches.append(contentsOf: Array(tiers.medium.dropFirst(pattern.swipeSelectivity > 0.5 ? 1 : 3).shuffled().prefix(3)))

        // Strategic premium injection based on behavior tier
        let premiumCount = pattern.behaviorTier == "power_user" ? 2 : 1
        if tiers.premium.count >= premiumCount {
            explorationMatches.append(contentsOf: Array(tiers.premium.shuffled().prefix(premiumCount)))
        }

        // Insert premium at strategic positions (not at the beginning)
        return strategicallyShuffleWithPremium(explorationMatches, premiumCount: premiumCount)
    }

    private func createRewardPhase(from tiers: MatchTiers, pattern: EngagementPattern) -> [UserModel] {
        var rewardMatches: [UserModel] = []

        // Higher concentration of premium matches as reward
        let premiumUsed = pattern.behaviorTier == "power_user" ? 2 : 1
        let remainingPremium = Array(tiers.premium.dropFirst(premiumUsed))

        if remainingPremium.count >= 2 {
            rewardMatches.append(contentsOf: Array(remainingPremium.shuffled().prefix(2)))
        } else {
            rewardMatches.append(contentsOf: remainingPremium)
        }

        rewardMatches.append(contentsOf: Array(tiers.good.dropFirst(8).shuffled().prefix(2)))
        rewardMatches.append(contentsOf: Array(tiers.medium.dropFirst(6).shuffled().prefix(1)))

        return rewardMatches.shuffled()
    }

    private func createSustainedPhase(from tiers: MatchTiers, pattern: EngagementPattern) -> [UserModel] {
        // Variable ratio reinforcement - unpredictable premium placement
        let premiumUsed = (pattern.behaviorTier == "power_user" ? 2 : 1) + 2 // From exploration + reward phases
        let remainingPremium = Array(tiers.premium.dropFirst(premiumUsed))
        let remainingGood = Array(tiers.good.dropFirst(10))
        let remainingMedium = Array(tiers.medium.dropFirst(7))
        let remainingBasic = tiers.basic

        var sustainedMatches: [UserModel] = []
        var premiumIndex = 0

        // Create base stack with good/medium/basic matches
        let baseStack = (remainingGood + remainingMedium + remainingBasic).shuffled()

        // Apply variable ratio schedule for premium injection based on user behavior
        let injectionPoints = calculateInjectionPoints(for: pattern, stackSize: baseStack.count)

        for (index, match) in baseStack.enumerated() {
            sustainedMatches.append(match)

            // Check if we should inject a premium match
            if injectionPoints.contains(index) && premiumIndex < remainingPremium.count {
                sustainedMatches.append(remainingPremium[premiumIndex])
                premiumIndex += 1
            }
        }

        return sustainedMatches
    }

    private func calculateInjectionPoints(for pattern: EngagementPattern, stackSize: Int) -> [Int] {
        // Adjust injection frequency based on user behavior
        var basePoints: [Int]

        switch pattern.behaviorTier {
        case "power_user":
            // More frequent rewards for power users
            basePoints = [5, 12, 18, 28, 40, 55, 75, 100]
        case "active":
            // Moderate frequency for active users
            basePoints = [7, 15, 25, 40, 60, 85]
        default: // casual
            // Less frequent but more impactful for casual users
            basePoints = [10, 25, 50, 80]
        }

        // Filter points that are within the stack size
        return basePoints.filter { $0 < stackSize }
    }

    private func strategicallyShuffleWithPremium(_ matches: [UserModel], premiumCount: Int) -> [UserModel] {
        var result = matches
        let premiumIndices = result.indices.filter { index in
            (result[index].compatibilityScore ?? 0) >= 0.90
        }

        // Move premium matches to strategic positions (not first 2 positions)
        for (i, premiumIndex) in premiumIndices.enumerated() {
            let targetPosition = min(3 + (i * 3), result.count - 1) // Positions 3, 6, 9, etc.
            if targetPosition < result.count && targetPosition != premiumIndex {
                let premiumMatch = result.remove(at: premiumIndex)
                result.insert(premiumMatch, at: targetPosition)
            }
        }

        return result
    }

    // MARK: - Helper Methods
    private func getCurrentUser(_ userID: String) async -> UserModel? {
        do {
            let document = try await db.collection("users").document(userID).getDocument()
            return try document.data(as: UserModel.self)
        } catch {
            print("❌ Failed to load current user: \(error)")
            return nil
        }
    }

    private func getUserEngagementPattern(userID: String) async -> EngagementPattern {
        do {
            let doc = try await db.collection("user_analytics")
                .document(userID)
                .collection("engagement_patterns")
                .document("current")
                .getDocument()

            if let data = doc.data() {
                var pattern = EngagementPattern(userID: userID)
                pattern.averageSessionLength = data["averageSessionLength"] as? TimeInterval ?? 300
                pattern.preferredSwipeTime = data["preferredSwipeTime"] as? [Int] ?? [19, 20, 21]
                pattern.swipeSelectivity = data["swipeSelectivity"] as? Double ?? 0.3
                pattern.matchConversationRate = data["matchConversationRate"] as? Double ?? 0.2
                pattern.retentionScore = data["retentionScore"] as? Double ?? 0.5
                pattern.behaviorTier = data["behaviorTier"] as? String ?? "casual"
                return pattern
            }
        } catch {
            print("❌ Failed to load engagement pattern: \(error)")
        }

        // Return default pattern for new users
        return EngagementPattern(userID: userID)
    }

    private func filterOutTopMatches(_ matches: [UserModel]) -> [UserModel] {
        return matches.filter { match in
            // Filter out users who are currently in someone's top matches
            !(match.isInTopMatches ?? false)
        }
    }
}
