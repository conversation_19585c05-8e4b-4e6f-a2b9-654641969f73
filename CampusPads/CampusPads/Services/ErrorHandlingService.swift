//
//  ErrorHandlingService.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import SwiftUI
import FirebaseFirestore
import FirebaseCrashlytics

/// Centralized error handling service for production-ready error management
class ErrorHandlingService: ObservableObject {
    static let shared = ErrorHandlingService()
    
    @Published var currentError: AppError?
    @Published var isShowingError: Bool = false
    
    private let crashlytics = Crashlytics.crashlytics()
    
    private init() {}
    
    // MARK: - Error Types
    
    enum AppError: Error, Identifiable, LocalizedError {
        case network(NetworkError)
        case authentication(AuthError)
        case firestore(FirestoreError)
        case validation(ValidationError)
        case unknown(String)
        
        var id: String {
            switch self {
            case .network(let error):
                return "network_\(error.rawValue)"
            case .authentication(let error):
                return "auth_\(error.rawValue)"
            case .firestore(let error):
                return "firestore_\(error.rawValue)"
            case .validation(let error):
                return "validation_\(error.rawValue)"
            case .unknown(let message):
                return "unknown_\(message.hashValue)"
            }
        }
        
        var errorDescription: String? {
            switch self {
            case .network(let error):
                return error.localizedDescription
            case .authentication(let error):
                return error.localizedDescription
            case .firestore(let error):
                return error.localizedDescription
            case .validation(let error):
                return error.localizedDescription
            case .unknown(let message):
                return message
            }
        }
        
        var recoverySuggestion: String? {
            switch self {
            case .network(.noConnection):
                return "Please check your internet connection and try again."
            case .network(.timeout):
                return "The request timed out. Please try again."
            case .authentication(.notAuthenticated):
                return "Please sign in to continue."
            case .firestore(.permissionDenied):
                return "You don't have permission to perform this action."
            case .validation(.invalidInput):
                return "Please check your input and try again."
            default:
                return "Please try again. If the problem persists, contact support."
            }
        }
        
        var isRetryable: Bool {
            switch self {
            case .network(.noConnection), .network(.timeout):
                return true
            case .firestore(.unavailable), .firestore(.timeout):
                return true
            case .authentication(.tokenExpired):
                return true
            default:
                return false
            }
        }
    }
    
    enum NetworkError: String, LocalizedError {
        case noConnection = "no_connection"
        case timeout = "timeout"
        case serverError = "server_error"
        case invalidResponse = "invalid_response"
        
        var errorDescription: String? {
            switch self {
            case .noConnection:
                return "No internet connection"
            case .timeout:
                return "Request timed out"
            case .serverError:
                return "Server error occurred"
            case .invalidResponse:
                return "Invalid server response"
            }
        }
    }
    
    enum AuthError: String, LocalizedError {
        case notAuthenticated = "not_authenticated"
        case tokenExpired = "token_expired"
        case invalidCredentials = "invalid_credentials"
        case accountDisabled = "account_disabled"
        
        var errorDescription: String? {
            switch self {
            case .notAuthenticated:
                return "You are not signed in"
            case .tokenExpired:
                return "Your session has expired"
            case .invalidCredentials:
                return "Invalid email or password"
            case .accountDisabled:
                return "Your account has been disabled"
            }
        }
    }
    
    enum FirestoreError: String, LocalizedError {
        case permissionDenied = "permission_denied"
        case unavailable = "unavailable"
        case timeout = "timeout"
        case quotaExceeded = "quota_exceeded"
        
        var errorDescription: String? {
            switch self {
            case .permissionDenied:
                return "Permission denied"
            case .unavailable:
                return "Service temporarily unavailable"
            case .timeout:
                return "Database request timed out"
            case .quotaExceeded:
                return "Usage quota exceeded"
            }
        }
    }
    
    enum ValidationError: String, LocalizedError {
        case invalidInput = "invalid_input"
        case missingRequiredField = "missing_required_field"
        case invalidFormat = "invalid_format"
        
        var errorDescription: String? {
            switch self {
            case .invalidInput:
                return "Invalid input provided"
            case .missingRequiredField:
                return "Required field is missing"
            case .invalidFormat:
                return "Invalid format"
            }
        }
    }
    
    // MARK: - Error Handling Methods
    
    /// Handle and display an error to the user
    func handleError(_ error: Error, context: String = "") {
        let appError = mapToAppError(error)
        
        // Log error for debugging
        logError(appError, context: context)
        
        // Report to Crashlytics
        reportToCrashlytics(appError, context: context)
        
        // Show to user
        DispatchQueue.main.async {
            self.currentError = appError
            self.isShowingError = true
        }
    }
    
    /// Handle error silently (log only, don't show to user)
    func handleErrorSilently(_ error: Error, context: String = "") {
        let appError = mapToAppError(error)
        logError(appError, context: context)
        reportToCrashlytics(appError, context: context)
    }
    
    /// Clear current error
    func clearError() {
        DispatchQueue.main.async {
            self.currentError = nil
            self.isShowingError = false
        }
    }
    
    /// Retry the last failed operation
    func retryLastOperation() {
        // This would be implemented based on the specific error context
        clearError()
    }
    
    // MARK: - Private Methods
    
    private func mapToAppError(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        
        // Map system errors to app errors
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .network(.noConnection)
            case .timedOut:
                return .network(.timeout)
            default:
                return .network(.serverError)
            }
        }
        
        if let firestoreError = error as? FirestoreErrorCode {
            switch firestoreError.code {
            case .permissionDenied:
                return .firestore(.permissionDenied)
            case .unavailable:
                return .firestore(.unavailable)
            case .deadlineExceeded:
                return .firestore(.timeout)
            case .resourceExhausted:
                return .firestore(.quotaExceeded)
            default:
                return .unknown("Firestore error: \(error.localizedDescription)")
            }
        }
        
        return .unknown(error.localizedDescription)
    }
    
    private func logError(_ error: AppError, context: String) {
        let errorMessage = """
        🚨 ERROR OCCURRED:
        Context: \(context)
        Type: \(error.id)
        Description: \(error.localizedDescription)
        Recovery: \(error.recoverySuggestion ?? "None")
        Retryable: \(error.isRetryable)
        """
        print(errorMessage)
    }
    
    private func reportToCrashlytics(_ error: AppError, context: String) {
        crashlytics.setCustomValue(error.id, forKey: "error_type")
        crashlytics.setCustomValue(context, forKey: "error_context")
        crashlytics.setCustomValue(error.isRetryable, forKey: "is_retryable")
        
        let nsError = NSError(
            domain: "CampusPadsError",
            code: error.id.hashValue,
            userInfo: [
                NSLocalizedDescriptionKey: error.localizedDescription,
                NSLocalizedRecoverySuggestionErrorKey: error.recoverySuggestion ?? "No suggestion"
            ]
        )
        
        crashlytics.record(error: nsError)
    }
}

// MARK: - Error Alert View

struct ErrorAlertView: ViewModifier {
    @ObservedObject var errorService = ErrorHandlingService.shared
    
    func body(content: Content) -> some View {
        content
            .alert(
                "Error",
                isPresented: $errorService.isShowingError,
                presenting: errorService.currentError
            ) { error in
                if error.isRetryable {
                    Button("Retry") {
                        errorService.retryLastOperation()
                    }
                }
                
                Button("OK") {
                    errorService.clearError()
                }
            } message: { error in
                VStack(alignment: .leading, spacing: 8) {
                    Text(error.localizedDescription)
                    
                    if let suggestion = error.recoverySuggestion {
                        Text(suggestion)
                            .font(.caption)
                    }
                }
            }
    }
}

extension View {
    func errorHandling() -> some View {
        modifier(ErrorAlertView())
    }
}
