import SwiftUI
import PhotosUI

/// Enhanced image picker with automatic cropping for Tinder-style cards
struct EnhancedImagePicker: View {
    @Binding var selectedImages: [UIImage]
    let maxImages: Int
    let targetAspectRatio: CGFloat = 1.25
    @Environment(\.presentationMode) var presentationMode

    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var showingCropper = false
    @State private var imageToCrop: UIImage?
    @State private var croppedImage: UIImage?
    @State private var isProcessing = false
    @State private var processingProgress: Double = 0.0

    var body: some View {
        NavigationView {
            ZStack {
                AppTheme.backgroundGradient
                    .ignoresSafeArea()

                mainContentView
            }
            .navigationBarHidden(true)
            .onChange(of: selectedItems) { _, items in
                processSelectedItems(items)
            }
            .sheet(isPresented: $showingCropper) {
                if let imageToCrop = imageToCrop {
                    ImageCropperView(
                        originalImage: imageToCrop,
                        croppedImage: $croppedImage
                    )
                }
            }
            .onChange(of: croppedImage) { _, image in
                if let image = image {
                    selectedImages.append(image)
                    croppedImage = nil
                    imageToCrop = nil
                }
            }
        }
    }

    private var mainContentView: some View {
        VStack(spacing: AppTheme.spacing20) {
            headerView
            photoPickerView
            selectedImagesView
            processingView
            Spacer()
            actionButtonsView
        }
    }

    private var headerView: some View {
        VStack(spacing: AppTheme.spacing8) {
            Text("Select Photos")
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundStyle(AppTheme.sexyGradient)

            Text("Choose up to \(maxImages) photos for your profile")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(AppTheme.textSecondary)
        }
        .padding(.top, AppTheme.spacing16)
    }

    private var photoPickerView: some View {
        PhotosPicker(
            selection: $selectedItems,
            maxSelectionCount: maxImages,
            matching: .images
        ) {
            photoPickerContent
        }
        .padding(.horizontal, AppTheme.spacing20)
    }

    private var photoPickerContent: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "photo.on.rectangle.angled")
                .font(.system(size: 48, weight: .light))
                .foregroundStyle(AppTheme.sexyGradient)

            VStack(spacing: AppTheme.spacing4) {
                Text("Tap to Select Photos")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppTheme.textPrimary)

                Text("Images will be optimized for cards")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, AppTheme.spacing40)
        .background(photoPickerBackground)
        .shadow(color: AppTheme.primaryColor.opacity(0.1), radius: 20, x: 0, y: 10)
    }

    private var photoPickerBackground: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .fill(.ultraThinMaterial)
            .overlay(photoPickerBorder)
    }

    private var photoPickerBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .stroke(photoPickerGradient, lineWidth: 2)
    }

    private var photoPickerGradient: LinearGradient {
        LinearGradient(
            colors: [
                AppTheme.primaryColor.opacity(0.3),
                AppTheme.accentColor.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    @ViewBuilder
    private var selectedImagesView: some View {
        if !selectedImages.isEmpty {
            VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                Text("Selected Photos (\(selectedImages.count)/\(maxImages))")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppTheme.textPrimary)
                    .padding(.horizontal, AppTheme.spacing20)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: AppTheme.spacing12) {
                        ForEach(Array(selectedImages.enumerated()), id: \.offset) { index, image in
                            selectedImageItem(image: image, index: index)
                        }
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                }
            }
        }
    }

    private func selectedImageItem(image: UIImage, index: Int) -> some View {
        ZStack(alignment: .topTrailing) {
            // Image preview with card aspect ratio
            Image(uiImage: image)
                .resizable()
                .scaledToFill()
                .frame(width: 80, height: 80 / targetAspectRatio)
                .clipShape(RoundedRectangle(cornerRadius: AppTheme.radiusMedium))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)

            // Remove button
            Button(action: {
                withAnimation(.spring()) {
                    let _ = selectedImages.remove(at: index)
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(.red)
                    .background(Color.white, in: Circle())
            }
            .offset(x: 8, y: -8)
        }
    }

    @ViewBuilder
    private var processingView: some View {
        if isProcessing {
            VStack(spacing: AppTheme.spacing12) {
                ProgressView(value: processingProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: AppTheme.primaryColor))
                    .scaleEffect(y: 2)

                Text("Optimizing images for cards...")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
            }
            .padding(.horizontal, AppTheme.spacing20)
        }
    }

    private var actionButtonsView: some View {
        HStack(spacing: AppTheme.spacing16) {
            Button("Cancel") {
                presentationMode.wrappedValue.dismiss()
            }
            .buttonStyle(PickerSecondaryButtonStyle())

            Button("Done") {
                presentationMode.wrappedValue.dismiss()
            }
            .buttonStyle(PickerPrimaryButtonStyle())
            .disabled(selectedImages.isEmpty)
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.bottom, AppTheme.spacing20)
    }

    private func processSelectedItems(_ items: [PhotosPickerItem]) {
        guard !items.isEmpty else { return }

        isProcessing = true
        processingProgress = 0.0

        let totalItems = Double(items.count)
        var processedItems = 0.0

        for item in items {
            item.loadTransferable(type: Data.self) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let data):
                        if let data = data, let uiImage = UIImage(data: data) {
                            // Auto-optimize image for card aspect ratio
                            let optimizedImage = optimizeImageForCard(uiImage)
                            selectedImages.append(optimizedImage)
                        }
                    case .failure(let error):
                        print("Error loading image: \(error)")
                    }

                    processedItems += 1
                    processingProgress = processedItems / totalItems

                    if processedItems == totalItems {
                        isProcessing = false
                        selectedItems.removeAll() // Clear to prevent reprocessing
                    }
                }
            }
        }
    }

    private func optimizeImageForCard(_ image: UIImage) -> UIImage {
        let imageAspectRatio = image.size.width / image.size.height

        // If image already fits well, return as-is
        if abs(imageAspectRatio - targetAspectRatio) < 0.1 {
            return image
        }

        // Auto-crop to target aspect ratio
        let imageSize = image.size
        var cropRect: CGRect

        if imageAspectRatio > targetAspectRatio {
            // Image is wider - crop width
            let targetWidth = imageSize.height * targetAspectRatio
            cropRect = CGRect(
                x: (imageSize.width - targetWidth) / 2,
                y: 0,
                width: targetWidth,
                height: imageSize.height
            )
        } else {
            // Image is taller - crop height
            let targetHeight = imageSize.width / targetAspectRatio
            cropRect = CGRect(
                x: 0,
                y: (imageSize.height - targetHeight) / 2,
                width: imageSize.width,
                height: targetHeight
            )
        }

        guard let cgImage = image.cgImage?.cropping(to: cropRect) else {
            return image
        }

        return UIImage(cgImage: cgImage, scale: image.scale, orientation: image.imageOrientation)
    }
}

// MARK: - Picker Button Styles
struct PickerPrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                LinearGradient(
                    colors: [AppTheme.primaryColor, AppTheme.accentColor],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(AppTheme.radiusLarge)
            .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 12, x: 0, y: 6)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

struct PickerSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(AppTheme.primaryColor)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 1)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

#Preview {
    EnhancedImagePicker(
        selectedImages: .constant([]),
        maxImages: 9
    )
}
