// MajorsService.swift
// Service for managing college majors in Firebase

import Foundation
import FirebaseFirestore
import Combine

// MARK: - Data Models

struct CollegeMajor: Codable, Identifiable, Hashable {
    let id = UUID()
    let name: String
    let category: String
    let description: String?

    init(name: String, category: String, description: String? = nil) {
        self.name = name
        self.category = category
        self.description = description
    }
}

class MajorsService: ObservableObject {
    static let shared = MajorsService()
    
    private let db = Firestore.firestore()
    private let majorsCollection = "majors"
    
    @Published var majors: [CollegeMajor] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadMajorsFromFirebase()
    }
    
    // MARK: - Public Methods
    
    /// Loads majors from Firebase
    func loadMajorsFromFirebase() {
        isLoading = true
        errorMessage = nil
        
        print("🎓 MajorsService: Loading majors from Firebase...")
        
        db.collection(majorsCollection)
            .order(by: "name")
            .getDocuments { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    self?.isLoading = false
                    
                    if let error = error {
                        print("❌ MajorsService: Error loading majors: \(error.localizedDescription)")
                        self?.errorMessage = error.localizedDescription
                        return
                    }
                    
                    guard let documents = snapshot?.documents else {
                        print("⚠️ MajorsService: No majors found in Firebase")
                        self?.errorMessage = "No majors data available"
                        return
                    }
                    
                    let loadedMajors = documents.compactMap { doc -> CollegeMajor? in
                        let data = doc.data()
                        guard let name = data["name"] as? String,
                              let category = data["category"] as? String else {
                            return nil
                        }
                        let description = data["description"] as? String
                        return CollegeMajor(name: name, category: category, description: description)
                    }
                    
                    print("✅ MajorsService: Loaded \(loadedMajors.count) majors from Firebase")
                    self?.majors = loadedMajors
                    
                    // Note: Firebase should already be populated with majors data
                }
            }
    }

    
    /// Searches majors by name
    func searchMajors(query: String) -> [CollegeMajor] {
        guard !query.isEmpty else { return majors }
        return majors.filter { $0.name.localizedCaseInsensitiveContains(query) }
    }
    
    /// Gets majors grouped by category
    func majorsByCategory() -> [String: [CollegeMajor]] {
        return Dictionary(grouping: majors) { $0.category }
    }
    
    /// Gets all unique categories
    func getCategories() -> [String] {
        let categories = Set(majors.map { $0.category })
        return Array(categories).sorted()
    }
    
    /// Gets majors for a specific category
    func getMajors(for category: String) -> [CollegeMajor] {
        return majors.filter { $0.category == category }.sorted { $0.name < $1.name }
    }
    
    /// Adds a new major to Firebase (admin function)
    func addMajor(name: String, category: String, description: String? = nil, completion: @escaping (Result<Void, Error>) -> Void) {
        let data: [String: Any] = [
            "name": name,
            "category": category,
            "description": description ?? "",
            "createdAt": FieldValue.serverTimestamp(),
            "isActive": true
        ]
        
        db.collection(majorsCollection).addDocument(data: data) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ MajorsService: Error adding major: \(error.localizedDescription)")
                    completion(.failure(error))
                } else {
                    print("✅ MajorsService: Successfully added major: \(name)")
                    completion(.success(()))
                    // Reload majors
                    self.loadMajorsFromFirebase()
                }
            }
        }
    }
    
    /// Updates an existing major in Firebase (admin function)
    func updateMajor(id: String, name: String, category: String, description: String? = nil, completion: @escaping (Result<Void, Error>) -> Void) {
        let data: [String: Any] = [
            "name": name,
            "category": category,
            "description": description ?? "",
            "updatedAt": FieldValue.serverTimestamp()
        ]
        
        db.collection(majorsCollection).document(id).updateData(data) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ MajorsService: Error updating major: \(error.localizedDescription)")
                    completion(.failure(error))
                } else {
                    print("✅ MajorsService: Successfully updated major: \(name)")
                    completion(.success(()))
                    // Reload majors
                    self.loadMajorsFromFirebase()
                }
            }
        }
    }
    
    /// Deactivates a major in Firebase (admin function)
    func deactivateMajor(id: String, completion: @escaping (Result<Void, Error>) -> Void) {
        let data: [String: Any] = [
            "isActive": false,
            "deactivatedAt": FieldValue.serverTimestamp()
        ]
        
        db.collection(majorsCollection).document(id).updateData(data) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ MajorsService: Error deactivating major: \(error.localizedDescription)")
                    completion(.failure(error))
                } else {
                    print("✅ MajorsService: Successfully deactivated major")
                    completion(.success(()))
                    // Reload majors
                    self.loadMajorsFromFirebase()
                }
            }
        }
    }
    
    /// Gets popular majors (most commonly selected)
    func getPopularMajors() -> [CollegeMajor] {
        // Return a curated list of popular majors
        let popularMajorNames = [
            "Business Administration",
            "Computer Science",
            "Psychology",
            "Biology",
            "Engineering",
            "Nursing",
            "Economics",
            "Marketing",
            "Communications",
            "English"
        ]
        
        return majors.filter { major in
            popularMajorNames.contains { popular in
                major.name.localizedCaseInsensitiveContains(popular)
            }
        }
    }
    
    /// Refreshes majors data
    func refresh() {
        loadMajorsFromFirebase()
    }
    
    /// Clears error message
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Firebase Document Structure
/*
 majors collection structure:
 {
   "name": "Computer Science",
   "category": "Computer Science & Information Technology",
   "description": "Study of computational systems and design of computer systems",
   "createdAt": timestamp,
   "updatedAt": timestamp,
   "isActive": true,
   "popularity": 0 // for future use
 }
 */
