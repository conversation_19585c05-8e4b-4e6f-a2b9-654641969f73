import SwiftUI
import FirebaseAuth
import FirebaseFirestore

struct RootView: View {
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var profileViewModel = ProfileViewModel.shared
    @State private var showOnboarding: Bool = false
    @State private var showSplash: Bool = true
    @AppStorage("isDarkMode") private var isDarkMode: Bool = true

    var body: some View {
        ZStack {
            if showSplash {
                // Beautiful splash screen
                SplashView {
                    withAnimation(.easeOut(duration: 0.5)) {
                        showSplash = false
                    }
                }
                .transition(.opacity)
            } else {
                // Main app content
                Group {
                    if authViewModel.userSession == nil {
                        AuthenticationView()
                            .environmentObject(authViewModel)
                    } else {
                        // CRITICAL FIX: Wait for profile to load before showing main app
                        ProfileLoadingGateView()
                            .environmentObject(authViewModel)
                            .environmentObject(profileViewModel)
                    }
                }
                .background(AppTheme.dynamicBackgroundGradient.ignoresSafeArea())
                .transition(.opacity)
            }
        }
        .preferredColorScheme(isDarkMode ? .dark : .light)
        .onAppear {
            // Start auth listening immediately but don't show UI until splash completes
            authViewModel.listenToAuthState()
        }
        .onChange(of: authViewModel.userSession) { _, userSession in
            // Start analytics session when user is authenticated
            if let userSession = userSession {
                AnalyticsManager.shared.startSession(userID: userSession.uid)
            }
        }
        .onChange(of: showSplash) { _, splashVisible in
            if !splashVisible {
                // Splash completed, now check onboarding
                checkOnboardingStatus()
            }
        }
        .onChange(of: authViewModel.userSession) { _, newSession in
            if newSession != nil {
                // User just signed in/up - check if they need onboarding
                print("🔄 RootView: User session changed, checking onboarding status")
                print("🔍 RootView: New session UID: \(newSession?.uid ?? "nil")")

                // Add a small delay to ensure Firebase state is fully updated
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.checkOnboardingStatus()
                }
            } else {
                // User signed out - hide onboarding
                print("🔄 RootView: User signed out, hiding onboarding")
                showOnboarding = false
            }
        }
        .fullScreenCover(isPresented: $showOnboarding) {
            if authViewModel.userSession == nil {
                TutorialOnboardingView()
                    .onAppear {
                        print("🎭 RootView: Showing TutorialOnboardingView (no user session)")
                    }
            } else {
                NewProfileSetupOnboardingView()
                    .onAppear {
                        print("🎭 RootView: Showing NewProfileSetupOnboardingView (user session exists)")
                    }
            }
        }
    }

    /// Enhanced onboarding check with better debugging and timing
    private func checkOnboardingStatus() {
        let completed = UserDefaults.standard.bool(forKey: "onboardingCompleted")
        let hasSession = authViewModel.userSession != nil
        let userUID = authViewModel.userSession?.uid ?? "nil"

        print("🔍 RootView: Checking onboarding status:")
        print("   - Has user session: \(hasSession)")
        print("   - User UID: \(userUID)")
        print("   - Onboarding completed: \(completed)")
        print("   - Current showOnboarding: \(showOnboarding)")
        print("   - Profile loaded: \(profileViewModel.userProfile != nil)")
        print("   - Profile loading: \(profileViewModel.isLoading)")

        // Additional profile debugging for legacy users
        if let profile = profileViewModel.userProfile {
            print("   - Profile details:")
            print("     - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
            print("     - DOB: \(profile.dateOfBirth?.description ?? "nil")")
            print("     - About: \(profile.aboutMe?.isEmpty == false ? "present" : "empty/nil")")
            print("     - College: \(profile.collegeName ?? "nil")")
            print("     - Housing: \(profile.housingStatus ?? "nil")")
            print("     - Images: \(profile.profileImageUrls?.count ?? 0)")
        }

        // Enhanced logic for onboarding trigger with profile validation
        if hasSession && !completed {
            print("🔄 RootView: User authenticated but onboarding not completed - checking if profile is actually complete")

            // CRITICAL FIX: Check if user has a complete profile despite missing onboarding flag
            // This handles legacy users who completed profiles before onboarding tracking was implemented
            if let profile = profileViewModel.userProfile {
                let hasRequiredFields = !(profile.firstName?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                       !(profile.lastName?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                       profile.dateOfBirth != nil

                // Check if profile has substantial completion (more than just basic fields)
                let hasSubstantialProfile = hasRequiredFields &&
                                          !(profile.aboutMe?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                          !(profile.collegeName?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                          !(profile.housingStatus?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                          (profile.profileImageUrls?.count ?? 0) > 0

                if hasSubstantialProfile {
                    print("✅ RootView: Legacy user with complete profile detected - setting onboarding completed")
                    UserDefaults.standard.set(true, forKey: "onboardingCompleted")
                    DispatchQueue.main.async {
                        self.showOnboarding = false
                    }
                } else {
                    print("🔄 RootView: Profile incomplete - showing onboarding")
                    DispatchQueue.main.async {
                        self.showOnboarding = true
                    }
                }
            } else if !profileViewModel.isLoading {
                print("🔄 RootView: No profile loaded - showing onboarding")
                DispatchQueue.main.async {
                    self.showOnboarding = true
                }
            } else {
                print("🔄 RootView: Profile loading - waiting before showing onboarding")
                // Wait a bit for profile to load, then recheck
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.checkOnboardingStatus()
                }
            }
        } else if !hasSession && !completed {
            print("🔄 RootView: No user session and onboarding not completed - showing tutorial")

            // Show tutorial for non-authenticated users
            DispatchQueue.main.async {
                self.showOnboarding = true
            }
        } else if hasSession && completed {
            print("🔄 RootView: User authenticated and onboarding completed - checking profile validity")

            // CRITICAL FIX: Additional validation for completed onboarding
            // Check if profile actually exists and has required fields
            if let profile = profileViewModel.userProfile {
                let hasRequiredFields = !(profile.firstName?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                       !(profile.lastName?.trimmingCharacters(in: .whitespaces).isEmpty ?? true) &&
                                       profile.dateOfBirth != nil

                if hasRequiredFields {
                    print("✅ RootView: Profile is valid - hiding onboarding")
                    DispatchQueue.main.async {
                        self.showOnboarding = false
                    }
                } else {
                    print("⚠️ RootView: Profile incomplete despite onboarding flag - forcing onboarding")
                    // Reset onboarding flag and show onboarding
                    UserDefaults.standard.set(false, forKey: "onboardingCompleted")
                    DispatchQueue.main.async {
                        self.showOnboarding = true
                    }
                }
            } else if !profileViewModel.isLoading {
                print("⚠️ RootView: No profile loaded and not loading - may need onboarding")
                // Profile might not be loaded yet, let ProfileLoadingGateView handle this
                DispatchQueue.main.async {
                    self.showOnboarding = false
                }
            } else {
                print("🔄 RootView: Profile is loading - hiding onboarding for now")
                DispatchQueue.main.async {
                    self.showOnboarding = false
                }
            }
        } else {
            print("🔄 RootView: No session and completed (edge case) - hiding onboarding")

            // Edge case: no session but marked completed
            DispatchQueue.main.async {
                self.showOnboarding = false
            }
        }

        print("   - Final showOnboarding: \(showOnboarding)")

        // Additional debugging: Check if user was just created
        if let user = authViewModel.userSession {
            let creationTime = user.metadata.creationDate
            let now = Date()
            let timeSinceCreation = now.timeIntervalSince(creationTime ?? now)

            print("🔍 RootView: User creation time: \(creationTime?.description ?? "unknown")")
            print("🔍 RootView: Time since creation: \(timeSinceCreation) seconds")

            // If user was created very recently (within 30 seconds), definitely show onboarding
            if timeSinceCreation < 30 && !completed {
                print("🚀 RootView: Recently created user detected, forcing onboarding")
                DispatchQueue.main.async {
                    self.showOnboarding = true
                }
            }
        }
    }


    struct RootView_Previews: PreviewProvider {
        static var previews: some View {
            RootView()
        }
    }
}
