import SwiftUI

struct GlobalSearchView: View {
    @StateObject private var viewModel = GlobalSearchViewModel()
    @StateObject private var profileViewTracker = ProfileViewTrackingViewModel()
    @StateObject private var profileVM = ProfileViewModel.shared
    @State private var showPremiumUpgrade = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // Enhanced background with dynamic gradient and animated effects
                    AppTheme.dynamicBackgroundGradient
                        .ignoresSafeArea()

                    // Subtle animated overlay for depth
                    AnimatedBackground()
                        .opacity(0.3)

                    // Content area - only show for premium users, completely hide for non-premium
                    if profileVM.userProfile?.isPremium == true {
                        AdaptiveContainer {
                            VStack(spacing: ResponsiveSpacing.lg) {
                                searchBarSection
                                contentArea
                            }
                        }
                    } else {
                        // Show placeholder content that gets completely covered by paywall
                        VStack(spacing: ResponsiveSpacing.lg) {
                            // Empty placeholder to maintain layout structure
                            Rectangle()
                                .fill(Color.clear)
                                .frame(height: 50)

                            Rectangle()
                                .fill(Color.clear)
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                        }
                    }

                    // Premium paywall overlay for non-premium users - covers entire screen
                    if profileVM.userProfile?.isPremium != true {
                        PremiumPaywallOverlay(
                            title: "Premium Search",
                            subtitle: "Search for specific users by name, major, or interests. Find exactly who you're looking for.",
                            onUpgrade: {
                                showPremiumUpgrade = true
                            },
                            onDismiss: {
                                dismiss()
                            }
                        )
                    }
                }
                .navigationTitle("Search")
                .navigationBarTitleDisplayMode(.large)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle()) // Ensures consistent behavior on iPad
        .sheet(isPresented: $showPremiumUpgrade) {
            PremiumUpgradeView()
        }
    }

    // MARK: - Search Bar Section
    private var searchBarSection: some View {
        VStack(spacing: AppTheme.spacing12) {
            SearchBar(
                text: $viewModel.query,
                placeholder: "Search users and listings...",
                onSearchButtonClicked: {
                    viewModel.triggerSearch()
                }
            )

            // Recent searches and suggestions
            if viewModel.query.isEmpty && (!viewModel.recentSearches.isEmpty || !viewModel.searchSuggestions.isEmpty) {
                suggestionsScrollView
            }
        }
        .padding(.horizontal, AppTheme.spacing16)
    }

    // MARK: - Suggestions Scroll View
    private var suggestionsScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppTheme.spacing8) {
                recentSearchesSection
                suggestionsSection
            }
            .padding(.horizontal, AppTheme.spacing16)
        }
    }

    // MARK: - Recent Searches Section
    private var recentSearchesSection: some View {
        Group {
            if !viewModel.recentSearches.isEmpty {
                ForEach(viewModel.recentSearches.prefix(5), id: \.self) { search in
                    Button(action: {
                        viewModel.query = search
                    }) {
                        HStack(spacing: AppTheme.spacing4) {
                            Image(systemName: "clock")
                                .font(.caption)
                            Text(search)
                                .font(AppTheme.caption)
                        }
                    }
                    .buttonStyle(TertiaryButtonStyle())
                }
            }
        }
    }

    // MARK: - Suggestions Section
    private var suggestionsSection: some View {
        ForEach(viewModel.searchSuggestions.prefix(3), id: \.self) { suggestion in
            Button(action: {
                viewModel.query = suggestion
            }) {
                HStack(spacing: AppTheme.spacing4) {
                    Image(systemName: "lightbulb")
                        .font(.caption)
                    Text(suggestion)
                        .font(AppTheme.caption)
                }
            }
            .buttonStyle(TertiaryButtonStyle())
        }
    }

    // MARK: - Content Area
    private var contentArea: some View {
        Group {
            if viewModel.isLoading {
                LoadingView(message: "Searching...", size: 60)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = viewModel.errorMessage {
                errorStateView(error: error)
            } else if viewModel.query.isEmpty {
                emptySearchView
            } else if viewModel.userResults.isEmpty && viewModel.listingResults.isEmpty {
                noResultsView
            } else {
                searchResultsView
            }
        }
    }

    // MARK: - State Views
    private func errorStateView(error: String) -> some View {
        EmptyStateView(
            icon: "exclamationmark.triangle",
            title: "Search Error",
            subtitle: error,
            actionTitle: "Try Again",
            action: {
                Task {
                    await viewModel.performSearch()
                }
            }
        )
    }

    private var emptySearchView: some View {
        EmptyStateView(
            icon: "magnifyingglass",
            title: "Start Searching",
            subtitle: "Enter a search term to find users and listings"
        )
    }

    private var noResultsView: some View {
        EmptyStateView(
            icon: "magnifyingglass",
            title: "No Results Found",
            subtitle: "Try adjusting your search terms or search type",
            actionTitle: "Clear Search",
            action: { viewModel.query = "" }
        )
    }

    // MARK: - Search Results View
    private var searchResultsView: some View {
        ScrollView {
            LazyVStack(spacing: AppTheme.spacing12) {
                usersSection
                listingsSection
            }
            .padding(.vertical, AppTheme.spacing16)
        }
    }

    // MARK: - Users Section
    private var usersSection: some View {
        Group {
            if !viewModel.userResults.isEmpty {
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Text("People (\(viewModel.userResults.count))")
                            .font(AppTheme.headline)
                            .fontWeight(.bold)
                            .foregroundColor(AppTheme.textPrimary)
                        Spacer()
                    }
                    .padding(.horizontal, AppTheme.spacing16)

                    ForEach(viewModel.userResults) { user in
                        UserSearchResultRow(
                            user: user,
                            searchQuery: viewModel.query,
                            profileViewTracker: profileViewTracker
                        )
                    }
                }
            }
        }
    }

    // MARK: - Listings Section
    private var listingsSection: some View {
        Group {
            if !viewModel.listingResults.isEmpty {
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Text("Listings (\(viewModel.listingResults.count))")
                            .font(AppTheme.headline)
                            .fontWeight(.bold)
                            .foregroundColor(AppTheme.textPrimary)
                        Spacer()
                    }
                    .padding(.horizontal, AppTheme.spacing16)

                    ForEach(viewModel.listingResults) { listing in
                        NavigationLink(destination: ListingDetailView(listing: listing)) {
                            ListingSearchResultCard(listing: listing)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.horizontal, AppTheme.spacing16)
                    }
                }
            }
        }
    }
}

// MARK: - Search Results Summary
struct SearchResultsSummaryView: View {
    @ObservedObject var viewModel: GlobalSearchViewModel

    var body: some View {
        EnhancedCard {
            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: 16, weight: .medium))

                    Text("Search Results")
                        .font(AppTheme.headline)
                        .foregroundColor(AppTheme.textPrimary)

                    Spacer()
                }

                if viewModel.searchType == .users {
                    let hiddenCount = viewModel.userResults.filter { $0.hideFromDiscovery == true }.count
                    let visibleCount = viewModel.userResults.count - hiddenCount

                    HStack(spacing: AppTheme.spacing12) {
                        // Total results
                        VStack(alignment: .leading, spacing: 2) {
                            Text("\(viewModel.userResults.count)")
                                .font(AppTheme.title2)
                                .fontWeight(.bold)
                                .foregroundColor(AppTheme.primaryColor)
                            Text("Total Users")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()

                        // Visible users
                        VStack(alignment: .leading, spacing: 2) {
                            Text("\(visibleCount)")
                                .font(AppTheme.title2)
                                .fontWeight(.bold)
                                .foregroundColor(AppTheme.successColor)
                            Text("Discoverable")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()

                        // Hidden users
                        if hiddenCount > 0 {
                            VStack(alignment: .leading, spacing: 2) {
                                Text("\(hiddenCount)")
                                    .font(AppTheme.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(AppTheme.warningColor)
                                Text("Limited Discovery")
                                    .font(AppTheme.caption)
                                    .foregroundColor(AppTheme.textSecondary)
                            }
                        }
                    }
                } else {
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("\(viewModel.listingResults.count)")
                                .font(AppTheme.title2)
                                .fontWeight(.bold)
                                .foregroundColor(AppTheme.primaryColor)
                            Text("Listings Found")
                                .font(AppTheme.caption)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()
                    }
                }
            }
        }
    }
}

// MARK: - Search Result Row Component
struct UserSearchResultRow: View {
    let user: UserModel
    let searchQuery: String
    let profileViewTracker: ProfileViewTrackingViewModel

    var body: some View {
        NavigationLink(destination: profileDestination) {
            UserSearchResultCard(user: user)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, AppTheme.spacing16)
        .onTapGesture {
            trackProfileView()
        }
    }

    private var profileDestination: some View {
        ProfilePreviewView(user: user, showPremiumActions: true)
    }

    private func trackProfileView() {
        if let userID = user.id {
            profileViewTracker.recordProfileView(
                viewedUserID: userID,
                source: .search,
                searchQuery: searchQuery,
                viewDuration: nil
            )
        }
    }
}

// MARK: - Search Result Cards
struct UserSearchResultCard: View {
    let user: UserModel

    var body: some View {
        HStack(spacing: AppTheme.spacing12) {
            // Fixed size profile image with proper constraints
            ZStack {
                if let imageUrl = user.profileImageUrl, let url = URL(string: imageUrl) {
                    AsyncImage(url: url) { image in
                        image
                            .resizable()
                            .scaledToFill()
                            .frame(width: 44, height: 44)
                            .clipped()
                    } placeholder: {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .overlay(
                                ProgressView()
                                    .tint(.white)
                                    .scaleEffect(0.7)
                            )
                    }
                    .frame(width: 44, height: 44)
                    .clipShape(Circle())
                } else {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 44, height: 44)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                        )
                }
            }
            .frame(width: 44, height: 44) // Enforce fixed size

            // Text content with strict constraints to prevent overflow
            VStack(alignment: .leading, spacing: AppTheme.spacing2) {
                if let firstName = user.firstName, let lastName = user.lastName {
                    Text("\(firstName) \(lastName)")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppTheme.textPrimary)
                        .lineLimit(1)
                        .truncationMode(.tail)
                }

                Text(user.email)
                    .font(.system(size: 13, weight: .regular))
                    .foregroundColor(AppTheme.textSecondary)
                    .lineLimit(1)
                    .truncationMode(.tail)

                // Compact badges with proper sizing
                HStack(spacing: AppTheme.spacing4) {
                    if let housing = user.housingStatus {
                        Text(housing)
                            .font(.system(size: 9, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 1)
                            .background(AppTheme.sexyGradient)
                            .cornerRadius(3)
                            .lineLimit(1)
                    }

                    if user.isEmailVerified {
                        Image(systemName: "checkmark.seal.fill")
                            .foregroundStyle(AppTheme.sexyGradient)
                            .font(.system(size: 10))
                    }

                    Spacer(minLength: 0) // Prevent badge overflow
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .layoutPriority(1) // Give text priority over other elements

            // Chevron with proper sizing
            Image(systemName: "chevron.right")
                .foregroundColor(AppTheme.textTertiary)
                .font(.system(size: 11, weight: .medium))
                .frame(width: 12, height: 12)
        }
        .frame(height: 60) // Fixed height to prevent overflow
        .frame(maxWidth: .infinity) // Respect container width
        .padding(.horizontal, AppTheme.spacing12)
        .padding(.vertical, AppTheme.spacing8)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(AppTheme.modernCardGradient)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        .clipped() // Ensure nothing goes outside bounds
    }
}

struct ListingSearchResultCard: View {
    let listing: ListingModel

    var body: some View {
        EnhancedCard {
            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                Text(listing.title)
                    .font(AppTheme.headline)
                    .foregroundColor(AppTheme.textPrimary)

                Text(listing.address)
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)

                HStack {
                    if !listing.rent.isEmpty, let rentValue = Double(listing.rent) {
                        BadgeView(
                            text: "$\(Int(rentValue))/month",
                            backgroundColor: AppTheme.successColor,
                            size: .small
                        )
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(AppTheme.textTertiary)
                        .font(.system(size: 14, weight: .medium))
                }
            }
        }
    }
}

struct GlobalSearchView_Previews: PreviewProvider {
    static var previews: some View {
        GlobalSearchView()
    }
}
