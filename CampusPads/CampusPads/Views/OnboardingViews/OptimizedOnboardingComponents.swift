import SwiftUI
import FirebaseAuth

// MARK: - Optimized Onboarding Components
// Lean, reusable components for the onboarding flow

/// Reusable selection button with gradient background
struct SelectionButton: View {
    let text: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(text)
                .font(AppTheme.body)
                .foregroundColor(isSelected ? .white : AppTheme.textPrimary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing12)
                .background(
                    isSelected ?
                    AnyView(AppTheme.primaryGradient) :
                    AnyView(AppTheme.cardBackground)
                )
                .cornerRadius(AppTheme.radiusMedium)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Compact selection button for smaller items
struct CompactSelectionButton: View {
    let text: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(text)
                .font(AppTheme.caption)
                .foregroundColor(isSelected ? .white : AppTheme.textPrimary)
                .padding(.horizontal, AppTheme.spacing12)
                .padding(.vertical, AppTheme.spacing8)
                .background(
                    isSelected ?
                    AnyView(AppTheme.primaryGradient) :
                    AnyView(AppTheme.cardBackground)
                )
                .cornerRadius(AppTheme.radiusSmall)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Onboarding text field with consistent styling
struct OnboardingTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(title)
                .font(AppTheme.subtitleFont)
                .foregroundColor(AppTheme.textPrimary)

            TextField(placeholder, text: $text)
                .font(AppTheme.body)
                .padding()
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.radiusMedium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 1)
                )
        }
    }
}

/// Section header with consistent styling
struct SectionHeader: View {
    let title: String

    var body: some View {
        Text(title)
            .font(AppTheme.subtitleFont)
            .foregroundColor(AppTheme.textPrimary)
    }
}

/// Info card with icon and text
struct InfoCard: View {
    let icon: String
    let title: String
    let items: [String]

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(AppTheme.accentColor)
                Text(title)
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.textPrimary)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                ForEach(items, id: \.self) { item in
                    Text("• \(item)")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                }
            }
        }
        .padding()
        .background(AppTheme.cardBackground)
        .cornerRadius(AppTheme.radiusMedium)
    }
}

/// Range display with values
struct RangeDisplay: View {
    let title: String
    let minValue: Int
    let maxValue: Int
    let prefix: String

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("\(prefix)\(minValue) - \(prefix)\(maxValue)")
                .font(AppTheme.title3)
                .foregroundStyle(AppTheme.primaryGradient)
                .fontWeight(.bold)

            HStack {
                Text("Min: \(prefix)\(minValue)")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
                Spacer()
                Text("Max: \(prefix)\(maxValue)")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }
        }
    }
}

/// Star rating component
struct StarRating: View {
    @Binding var rating: Int
    let maxRating: Int = 5

    var body: some View {
        HStack(spacing: AppTheme.spacing8) {
            ForEach(1...maxRating, id: \.self) { level in
                Button(action: {
                    rating = level
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                }) {
                    Image(systemName: level <= rating ? "star.fill" : "star")
                        .font(.system(size: 24))
                        .foregroundColor(level <= rating ? AppTheme.accentColor : AppTheme.textTertiary)
                }
                .buttonStyle(PlainButtonStyle())
            }

            Spacer()

            Text(ratingDescription)
                .font(AppTheme.caption)
                .foregroundColor(AppTheme.textSecondary)
        }
    }

    private var ratingDescription: String {
        switch rating {
        case 1: return "Very Relaxed"
        case 2: return "Casual"
        case 3: return "Moderate"
        case 4: return "Neat"
        case 5: return "Very Clean"
        default: return ""
        }
    }
}

/// Toggle with icon and description
struct IconToggle: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool

    var body: some View {
        HStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(isOn ? AnyShapeStyle(AppTheme.primaryGradient) : AnyShapeStyle(AppTheme.cardBackground))
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 20))
                    .foregroundColor(isOn ? .white : AppTheme.textSecondary)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(title)
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.textPrimary)

                Text(subtitle)
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding()
        .background(AppTheme.cardBackground)
        .cornerRadius(AppTheme.radiusMedium)
    }
}

/// Grid selection component
struct GridSelection<T: Hashable>: View {
    let items: [T]
    @Binding var selectedItem: T?
    let columns: Int
    let itemText: (T) -> String

    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: columns), spacing: AppTheme.spacing12) {
            ForEach(items, id: \.self) { item in
                SelectionButton(
                    text: itemText(item),
                    isSelected: selectedItem == item,
                    action: {
                        selectedItem = item
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                    }
                )
            }
        }
    }
}

/// Horizontal scroll selection
struct HorizontalSelection<T: Hashable>: View {
    let items: [T]
    @Binding var selectedItem: T?
    let itemText: (T) -> String

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppTheme.spacing8) {
                ForEach(items, id: \.self) { item in
                    CompactSelectionButton(
                        text: itemText(item),
                        isSelected: selectedItem == item,
                        action: {
                            selectedItem = item
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                        }
                    )
                }
            }
            .padding(.horizontal, AppTheme.spacing4)
        }
    }
}

/// Simple range slider
struct SimpleRangeSlider: View {
    @Binding var minValue: Int
    @Binding var maxValue: Int
    let bounds: ClosedRange<Int>
    let step: Int

    var body: some View {
        VStack(spacing: AppTheme.spacing8) {
            HStack {
                VStack {
                    Text("Min")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                    Slider(
                        value: Binding(
                            get: { Double(minValue) },
                            set: { minValue = Int($0) }
                        ),
                        in: Double(bounds.lowerBound)...Double(maxValue - step),
                        step: Double(step)
                    )
                    .accentColor(AppTheme.primaryColor)
                }

                VStack {
                    Text("Max")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                    Slider(
                        value: Binding(
                            get: { Double(maxValue) },
                            set: { maxValue = Int($0) }
                        ),
                        in: Double(minValue + step)...Double(bounds.upperBound),
                        step: Double(step)
                    )
                    .accentColor(AppTheme.primaryColor)
                }
            }
        }
    }
}
