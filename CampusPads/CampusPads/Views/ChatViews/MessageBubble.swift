import SwiftUI

struct MessageBubble: View {
    let message: MessageModel
    let isCurrentUser: Bool
    var onReact: ((String) -> Void)? = nil

    @State private var showReactionPicker: Bool = false
    private let reactionEmojis = ["👍", "❤️", "😂", "😮", "😢", "👏"]

    var body: some View {
        VStack(alignment: isCurrentUser ? .trailing : .leading, spacing: ResponsiveSpacing.sm) {
            HStack {
                if isCurrentUser { Spacer() }

                VStack(alignment: isCurrentUser ? .trailing : .leading, spacing: ResponsiveSpacing.xs) {
                    // Message content based on type
                    messageContentView
                        .background(
                            ZStack {
                                // Base background with subtle elegance
                                RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                                    .fill(
                                        isCurrentUser ?
                                        // Elegant solid color for sent messages
                                        LinearGradient(
                                            colors: [
                                                AppTheme.primaryColor.opacity(0.9),
                                                AppTheme.primaryColor.opacity(0.8)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ) :
                                        // Clean, readable background for received messages
                                        LinearGradient(
                                            colors: [
                                                Color(.systemGray6),
                                                Color(.systemGray5)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )

                                // Subtle inner highlight for depth
                                RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.white.opacity(isCurrentUser ? 0.15 : 0.3),
                                                Color.clear
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .center
                                        )
                                    )
                            }
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                                .stroke(
                                    isCurrentUser ?
                                    Color.white.opacity(0.2) :
                                    Color.black.opacity(0.08),
                                    lineWidth: 0.5
                                )
                        )
                        .shadow(
                            color: isCurrentUser ?
                            AppTheme.primaryColor.opacity(0.25) :
                            Color.black.opacity(0.08),
                            radius: isCurrentUser ? 4 : 2,
                            x: 0,
                            y: isCurrentUser ? 2 : 1
                        )
                        .onLongPressGesture {
                            // Haptic feedback for better UX
                            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                            impactFeedback.impactOccurred()

                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                showReactionPicker = true
                            }
                        }

                    // Timestamp
                    Text(timeString)
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)
                        .padding(.horizontal, AppTheme.spacing8)
                }

                if !isCurrentUser { Spacer() }
            }

            if isCurrentUser, let isRead = message.isRead, isRead {
                Text("Read")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.accentColor)
                    .padding(.trailing, AppTheme.spacing8)
            }

            if let reactions = message.reactions, !reactions.isEmpty {
                HStack(spacing: AppTheme.spacing4) {
                    ForEach(reactions.keys.sorted(), id: \.self) { emoji in
                        let count = reactions[emoji] ?? 0
                        Text("\(emoji) \(count)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color(.label))
                            .padding(.horizontal, AppTheme.spacing8)
                            .padding(.vertical, AppTheme.spacing4)
                            .background(
                                RoundedRectangle(cornerRadius: AppTheme.radiusSmall)
                                    .fill(Color(.systemGray6))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: AppTheme.radiusSmall)
                                            .stroke(Color.black.opacity(0.05), lineWidth: 0.5)
                                    )
                            )
                    }
                }
                .padding(.top, AppTheme.spacing4)
            }
        }
        .overlay(
            Group {
                if showReactionPicker {
                    ReactionPicker(emojis: reactionEmojis) { selectedEmoji in
                        // Haptic feedback for reaction selection
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()

                        onReact?(selectedEmoji)
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showReactionPicker = false
                        }
                    }
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showReactionPicker = false
                        }
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
        )
    }

    // MARK: - Computed Properties

    @ViewBuilder
    private var messageContentView: some View {
        switch message.messageType {
        case .text:
            textMessageView
        case .image:
            imageMessageView
        case .video:
            videoMessageView
        case .document:
            documentMessageView
        }
    }

    private var textMessageView: some View {
        Text(message.text)
            .font(ResponsiveTypography.body.weight(.medium))
            .foregroundColor(isCurrentUser ? .white : Color(.label))
            .padding(.horizontal, ResponsiveSpacing.md)
            .padding(.vertical, ResponsiveSpacing.sm)
            .frame(maxWidth: LayoutHelpers.chatBubbleMaxWidth, alignment: .leading)
    }

    private var imageMessageView: some View {
        VStack(spacing: AppTheme.spacing8) {
            if let uploadProgress = message.uploadProgress, uploadProgress < 1.0 {
                // Show upload progress
                uploadProgressView
            } else if let imageUrl = message.mediaUrl, let url = URL(string: imageUrl) {
                // Show uploaded image
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .scaledToFill()
                        .frame(maxWidth: min(LayoutHelpers.chatBubbleMaxWidth * 0.8, 300), maxHeight: DeviceInfo.isIPad ? 300 : 200)
                        .clipShape(RoundedRectangle(cornerRadius: ResponsiveRadius.medium))
                } placeholder: {
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .fill(Color(.systemGray5))
                        .frame(width: 250, height: 200)
                        .overlay(
                            ProgressView()
                                .tint(isCurrentUser ? .white : AppTheme.primaryColor)
                        )
                }
            } else {
                // Fallback for failed uploads
                Text("📸 Image")
                    .font(AppTheme.body)
                    .foregroundColor(isCurrentUser ? .white : Color(.label))
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.vertical, AppTheme.spacing12)
            }

            // Caption if text exists
            if !message.text.isEmpty {
                Text(message.text)
                    .font(AppTheme.caption)
                    .foregroundColor(isCurrentUser ? .white.opacity(0.9) : Color(.secondaryLabel))
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.bottom, AppTheme.spacing8)
            }
        }
    }

    private var videoMessageView: some View {
        VStack(spacing: AppTheme.spacing8) {
            if let uploadProgress = message.uploadProgress, uploadProgress < 1.0 {
                // Show upload progress
                uploadProgressView
            } else if let videoUrl = message.mediaUrl {
                // Show video thumbnail with play button
                ZStack {
                    if let thumbnailUrl = message.thumbnailUrl, let url = URL(string: thumbnailUrl) {
                        AsyncImage(url: url) { image in
                            image
                                .resizable()
                                .scaledToFill()
                                .frame(maxWidth: 250, maxHeight: 200)
                                .clipShape(RoundedRectangle(cornerRadius: AppTheme.radiusMedium))
                        } placeholder: {
                            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                                .fill(Color(.systemGray5))
                                .frame(width: 250, height: 200)
                        }
                    } else {
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .fill(Color(.systemGray5))
                            .frame(width: 250, height: 200)
                    }

                    // Play button overlay
                    Button(action: {
                        // TODO: Open video player
                        if let url = URL(string: videoUrl) {
                            print("🎥 Playing video: \(url)")
                        }
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color.black.opacity(0.6))
                                .frame(width: 50, height: 50)

                            Image(systemName: "play.fill")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(.white)
                        }
                    }
                }
            } else {
                // Fallback for failed uploads
                Text("🎥 Video")
                    .font(AppTheme.body)
                    .foregroundColor(isCurrentUser ? .white : Color(.label))
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.vertical, AppTheme.spacing12)
            }

            // Caption if text exists
            if !message.text.isEmpty {
                Text(message.text)
                    .font(AppTheme.caption)
                    .foregroundColor(isCurrentUser ? .white.opacity(0.9) : Color(.secondaryLabel))
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.bottom, AppTheme.spacing8)
            }
        }
    }

    private var documentMessageView: some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: "doc.fill")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(isCurrentUser ? .white : AppTheme.primaryColor)

            VStack(alignment: .leading, spacing: 2) {
                Text(message.fileName ?? "Document")
                    .font(AppTheme.body)
                    .fontWeight(.medium)
                    .foregroundColor(isCurrentUser ? .white : Color(.label))

                if let fileSize = message.fileSize {
                    Text(ByteCountFormatter.string(fromByteCount: Int64(fileSize), countStyle: .file))
                        .font(AppTheme.caption)
                        .foregroundColor(isCurrentUser ? .white.opacity(0.8) : Color(.secondaryLabel))
                }
            }

            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing16)
        .padding(.vertical, AppTheme.spacing12)
    }

    private var uploadProgressView: some View {
        VStack(spacing: AppTheme.spacing8) {
            Text(message.text)
                .font(AppTheme.body)
                .foregroundColor(isCurrentUser ? .white : Color(.label))

            ProgressView(value: message.uploadProgress ?? 0.0)
                .progressViewStyle(LinearProgressViewStyle(tint: isCurrentUser ? .white : AppTheme.primaryColor))
                .frame(width: 200)

            Text("\(Int((message.uploadProgress ?? 0.0) * 100))%")
                .font(AppTheme.caption)
                .foregroundColor(isCurrentUser ? .white.opacity(0.8) : Color(.secondaryLabel))
        }
        .padding(.horizontal, AppTheme.spacing16)
        .padding(.vertical, AppTheme.spacing12)
    }

    private var timeString: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: message.timestamp)
    }
}

struct ReactionPicker: View {
    let emojis: [String]
    let onSelect: (String) -> Void

    var body: some View {
        HStack(spacing: AppTheme.spacing12) {
            ForEach(emojis, id: \.self) { emoji in
                Button(action: {
                    onSelect(emoji)
                }) {
                    Text(emoji)
                        .font(.title2)
                        .padding(AppTheme.spacing8)
                        .background(
                            Circle()
                                .fill(Color(.systemGray6))
                                .overlay(
                                    Circle()
                                        .stroke(Color.black.opacity(0.05), lineWidth: 0.5)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: AppTheme.animationFast), value: emoji)
            }
        }
        .padding(AppTheme.spacing12)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.black.opacity(0.1), lineWidth: 0.5)
                )
                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
        )
        .padding(.top, -AppTheme.spacing40)
    }
}

struct MessageBubble_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            MessageBubble(message: MessageModel(id: "dummy1",
                                                senderID: "123",
                                                text: "Hello!",
                                                timestamp: Date(),
                                                isRead: true,
                                                reactions: ["👍": 2, "❤️": 1]),
                          isCurrentUser: true,
                          onReact: { emoji in print("Reacted with \(emoji)") })
            MessageBubble(message: MessageModel(id: "dummy2",
                                                senderID: "456",
                                                text: "Hi there!",
                                                timestamp: Date(),
                                                isRead: false,
                                                reactions: nil),
                          isCurrentUser: false,
                          onReact: { emoji in print("Reacted with \(emoji)") })
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
