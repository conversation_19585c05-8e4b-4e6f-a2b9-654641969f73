import Foundation
import FirebaseFirestore
import Combine
import UIKit

// MARK: - Data Models
struct UserSession: Codable {
    let sessionID: String
    let userID: String
    let startTime: Date
    var endTime: Date?
    var sessionLength: TimeInterval
    var swipeCount: Int
    var likeCount: Int
    var passCount: Int
    var matchesCreated: Int
    var conversationsStarted: Int
    let appVersion: String
    let deviceType: String

    init(sessionID: String, userID: String) {
        self.sessionID = sessionID
        self.userID = userID
        self.startTime = Date()
        self.endTime = nil
        self.sessionLength = 0
        self.swipeCount = 0
        self.likeCount = 0
        self.passCount = 0
        self.matchesCreated = 0
        self.conversationsStarted = 0
        self.appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        self.deviceType = UIDevice.current.model
    }
}

struct DailyMetrics: Codable {
    let date: String
    let userID: String
    var totalSessions: Int
    var totalSwipes: Int
    var totalMatches: Int
    var averageSessionLength: TimeInterval
    var firstOpenTime: Date?
    var lastOpenTime: Date?
    var timeBetweenOpens: [TimeInterval]
    var conversationRate: Double
    var premiumMatchesViewed: Int
    var premiumMatchesLiked: Int

    init(userID: String, date: Date = Date()) {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        self.date = formatter.string(from: date)
        self.userID = userID
        self.totalSessions = 0
        self.totalSwipes = 0
        self.totalMatches = 0
        self.averageSessionLength = 0
        self.firstOpenTime = nil
        self.lastOpenTime = nil
        self.timeBetweenOpens = []
        self.conversationRate = 0
        self.premiumMatchesViewed = 0
        self.premiumMatchesLiked = 0
    }
}

struct EngagementPattern: Codable {
    let userID: String
    var averageSessionLength: TimeInterval
    var preferredSwipeTime: [Int]
    var swipeSelectivity: Double
    var matchConversationRate: Double
    var retentionScore: Double
    let lastUpdated: Date
    var behaviorTier: String

    init(userID: String) {
        self.userID = userID
        self.averageSessionLength = 300
        self.preferredSwipeTime = [19, 20, 21]
        self.swipeSelectivity = 0.3
        self.matchConversationRate = 0.2
        self.retentionScore = 0.5
        self.lastUpdated = Date()
        self.behaviorTier = "casual"
    }
}

struct PremiumAnalytics: Codable {
    let userID: String
    var topMatchesViewedToday: Int
    var topMatchesLikedToday: Int
    var lastTopMatchReset: Date
    var totalPremiumValue: Double
    var conversionEvents: [PremiumEvent]

    init(userID: String) {
        self.userID = userID
        self.topMatchesViewedToday = 0
        self.topMatchesLikedToday = 0
        self.lastTopMatchReset = Date()
        self.totalPremiumValue = 0
        self.conversionEvents = []
    }
}

struct PremiumEvent: Codable {
    let eventType: String
    let timestamp: Date
    let value: Double
    let metadata: [String: String]
}

enum SwipeAction {
    case like, pass
}

// MARK: - Analytics Manager
class AnalyticsManager: ObservableObject {
    static let shared = AnalyticsManager()
    private let db = Firestore.firestore()

    @Published var currentSession: UserSession?
    @Published var dailyMetrics: DailyMetrics?
    @Published var isTracking = false

    private var sessionTimer: Timer?
    private var lastAppOpenTime: Date?

    private init() {
        setupNotifications()
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }

    // MARK: - Session Management
    func startSession(userID: String) {
        print("📊 Starting analytics session for user: \(userID)")

        let sessionID = UUID().uuidString
        let session = UserSession(sessionID: sessionID, userID: userID)

        currentSession = session
        isTracking = true

        saveSessionToFirestore(session)
        updateDailyMetricsOnStart(userID: userID)

        // Start session timer for real-time updates
        startSessionTimer()
    }

    func endSession() {
        guard var session = currentSession else { return }

        print("📊 Ending analytics session: \(session.sessionID)")

        session.endTime = Date()
        session.sessionLength = session.endTime!.timeIntervalSince(session.startTime)

        updateSessionInFirestore(session)
        updateDailyMetricsOnEnd(session)
        updateEngagementPattern(from: session)

        currentSession = nil
        isTracking = false
        sessionTimer?.invalidate()
        sessionTimer = nil
    }

    private func startSessionTimer() {
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            self.updateCurrentSession()
        }
    }

    private func updateCurrentSession() {
        guard var session = currentSession else { return }
        session.sessionLength = Date().timeIntervalSince(session.startTime)
        currentSession = session
    }

    // MARK: - Event Tracking
    func trackSwipe(action: SwipeAction, matchCreated: Bool = false) {
        guard var session = currentSession else { return }

        session.swipeCount += 1

        switch action {
        case .like:
            session.likeCount += 1
        case .pass:
            session.passCount += 1
        }

        if matchCreated {
            session.matchesCreated += 1
        }

        currentSession = session

        print("📊 Tracked swipe: \(action), total swipes: \(session.swipeCount)")
    }

    func trackConversationStarted() {
        guard var session = currentSession else { return }
        session.conversationsStarted += 1
        currentSession = session

        print("📊 Tracked conversation started")
    }

    func trackTopMatchView(userID: String) {
        Task {
            do {
                let docRef = db.collection("premium_analytics").document(userID)

                try await docRef.setData([
                    "topMatchesViewedToday": FieldValue.increment(Int64(1)),
                    "lastUpdated": FieldValue.serverTimestamp()
                ], merge: true)

                print("📊 Tracked top match view for user: \(userID)")
            } catch {
                print("❌ Failed to track top match view: \(error)")
            }
        }
    }

    func trackTopMatchLike(userID: String) {
        Task {
            do {
                let docRef = db.collection("premium_analytics").document(userID)

                try await docRef.setData([
                    "topMatchesLikedToday": FieldValue.increment(Int64(1)),
                    "lastUpdated": FieldValue.serverTimestamp()
                ], merge: true)

                print("📊 Tracked top match like for user: \(userID)")
            } catch {
                print("❌ Failed to track top match like: \(error)")
            }
        }
    }

    func trackEvent(_ eventName: String, parameters: [String: Any] = [:]) {
        print("📊 Event tracked: \(eventName) with parameters: \(parameters)")

        // Store custom events in Firestore
        Task {
            guard let userID = currentSession?.userID else { return }

            do {
                let eventData: [String: Any] = [
                    "eventName": eventName,
                    "parameters": parameters,
                    "timestamp": FieldValue.serverTimestamp(),
                    "sessionID": currentSession?.sessionID ?? ""
                ]

                try await db.collection("user_analytics")
                    .document(userID)
                    .collection("events")
                    .addDocument(data: eventData)

            } catch {
                print("❌ Failed to track event: \(error)")
            }
        }
    }

    // MARK: - Notification Handlers
    @objc private func appDidEnterBackground() {
        endSession()
    }

    @objc private func appWillEnterForeground() {
        if let userID = ProfileViewModel.shared.userProfile?.id {
            // Track time between opens
            if let lastOpen = lastAppOpenTime {
                let timeBetween = Date().timeIntervalSince(lastOpen)
                trackTimeBetweenOpens(timeBetween, userID: userID)
            }

            lastAppOpenTime = Date()
            startSession(userID: userID)
        }
    }

    private func trackTimeBetweenOpens(_ timeInterval: TimeInterval, userID: String) {
        Task {
            do {
                let docRef = db.collection("user_analytics")
                    .document(userID)
                    .collection("daily_metrics")
                    .document(todayDateString())

                try await docRef.setData([
                    "timeBetweenOpens": FieldValue.arrayUnion([timeInterval])
                ], merge: true)

            } catch {
                print("❌ Failed to track time between opens: \(error)")
            }
        }
    }

    // MARK: - Helper Methods
    private func todayDateString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Date())
    }

    private func calculateBehaviorTier(_ session: UserSession) -> String {
        if session.sessionLength > 600 && session.swipeCount > 50 {
            return "power_user"
        } else if session.sessionLength > 180 && session.swipeCount > 15 {
            return "active"
        } else {
            return "casual"
        }
    }

    // MARK: - Firestore Operations
    private func saveSessionToFirestore(_ session: UserSession) {
        let docRef = db.collection("user_analytics")
            .document(session.userID)
            .collection("sessions")
            .document(session.sessionID)

        do {
            try docRef.setData(from: session) { error in
                if let error = error {
                    print("❌ Failed to save session: \(error)")
                }
            }
        } catch {
            print("❌ Failed to encode session: \(error)")
        }
    }

    private func updateSessionInFirestore(_ session: UserSession) {
        let docRef = db.collection("user_analytics")
            .document(session.userID)
            .collection("sessions")
            .document(session.sessionID)

        do {
            try docRef.setData(from: session, merge: true) { error in
                if let error = error {
                    print("❌ Failed to update session: \(error)")
                }
            }
        } catch {
            print("❌ Failed to encode session: \(error)")
        }
    }

    private func updateDailyMetricsOnStart(userID: String) {
        Task {
            do {
                let docRef = db.collection("user_analytics")
                    .document(userID)
                    .collection("daily_metrics")
                    .document(todayDateString())

                // Current time is not used, removing unused variable

                try await docRef.setData([
                    "userID": userID,
                    "date": todayDateString(),
                    "totalSessions": FieldValue.increment(Int64(1)),
                    "lastOpenTime": FieldValue.serverTimestamp(),
                    "firstOpenTime": FieldValue.serverTimestamp()
                ], merge: true)

            } catch {
                print("❌ Failed to update daily metrics on start: \(error)")
            }
        }
    }

    private func updateDailyMetricsOnEnd(_ session: UserSession) {
        Task {
            do {
                let docRef = db.collection("user_analytics")
                    .document(session.userID)
                    .collection("daily_metrics")
                    .document(todayDateString())

                let conversationRate = session.swipeCount > 0 ?
                    Double(session.conversationsStarted) / Double(session.swipeCount) : 0.0

                try await docRef.setData([
                    "totalSwipes": FieldValue.increment(Int64(session.swipeCount)),
                    "totalMatches": FieldValue.increment(Int64(session.matchesCreated)),
                    "averageSessionLength": session.sessionLength,
                    "conversationRate": conversationRate
                ], merge: true)

            } catch {
                print("❌ Failed to update daily metrics on end: \(error)")
            }
        }
    }

    private func updateEngagementPattern(from session: UserSession) {
        Task {
            do {
                let docRef = db.collection("user_analytics")
                    .document(session.userID)
                    .collection("engagement_patterns")
                    .document("current")

                let selectivity = session.swipeCount > 0 ?
                    Double(session.likeCount) / Double(session.swipeCount) : 0.0

                let matchConversationRate = session.matchesCreated > 0 ?
                    Double(session.conversationsStarted) / Double(session.matchesCreated) : 0.0

                let currentHour = Calendar.current.component(.hour, from: session.startTime)

                try await docRef.setData([
                    "userID": session.userID,
                    "averageSessionLength": session.sessionLength,
                    "swipeSelectivity": selectivity,
                    "matchConversationRate": matchConversationRate,
                    "lastUpdated": FieldValue.serverTimestamp(),
                    "behaviorTier": calculateBehaviorTier(session),
                    "preferredSwipeTime": FieldValue.arrayUnion([currentHour])
                ], merge: true)

            } catch {
                print("❌ Failed to update engagement pattern: \(error)")
            }
        }
    }

    // MARK: - Daily Reset Operations
    func resetDailyPremiumLimits() async {
        print("🌅 Resetting daily premium limits...")

        do {
            let batch = db.batch()

            let premiumCollection = db.collection("premium_analytics")
            let snapshot = try await premiumCollection.getDocuments()

            for document in snapshot.documents {
                batch.updateData([
                    "topMatchesViewedToday": 0,
                    "topMatchesLikedToday": 0,
                    "lastTopMatchReset": FieldValue.serverTimestamp()
                ], forDocument: document.reference)
            }

            try await batch.commit()
            print("✅ Reset premium limits for \(snapshot.documents.count) users")

        } catch {
            print("❌ Failed to reset daily premium limits: \(error)")
        }
    }
}
