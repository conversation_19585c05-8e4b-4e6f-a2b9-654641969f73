//
//  NetworkMonitor.swift
//  CampusPads
//
//  Network monitoring service for offline support and connectivity management.
//

import Foundation
import Network
import Combine

class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()

    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")

    @Published var isConnected = true
    @Published var connectionType: ConnectionType = .unknown
    @Published var isExpensive = false
    @Published var isConstrained = false
    @Published var supportsIPv4 = false
    @Published var supportsIPv6 = false
    @Published var supportsDNS = false

    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown
    }

    private init() {
        startMonitoring()
    }

    deinit {
        stopMonitoring()
    }

    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.isExpensive = path.isExpensive
                self?.isConstrained = path.isConstrained
                self?.supportsIPv4 = path.supportsIPv4
                self?.supportsIPv6 = path.supportsIPv6
                self?.supportsDNS = path.supportsDNS
                self?.updateConnectionType(path)
            }
        }
        monitor.start(queue: queue)
    }

    private func stopMonitoring() {
        monitor.cancel()
    }

    private func updateConnectionType(_ path: NWPath) {
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unknown
        }
    }
}

// MARK: - Offline Support Manager

class OfflineManager: ObservableObject {
    static let shared = OfflineManager()

    @Published var isOfflineMode = false
    @Published var pendingOperations: [PendingOperation] = []

    private let networkMonitor = NetworkMonitor.shared
    private var cancellables = Set<AnyCancellable>()

    struct PendingOperation: Identifiable, Codable {
        let id = UUID()
        let type: OperationType
        let data: Data
        let timestamp: Date

        enum OperationType: String, Codable {
            case profileUpdate
            case messageSend
            case swipeAction
            case imageUpload
        }
    }

    private init() {
        setupNetworkObserver()
        loadPendingOperations()
    }

    private func setupNetworkObserver() {
        networkMonitor.$isConnected
            .sink { [weak self] isConnected in
                if isConnected && self?.isOfflineMode == true {
                    self?.isOfflineMode = false
                    self?.processPendingOperations()
                } else if !isConnected {
                    self?.isOfflineMode = true
                }
            }
            .store(in: &cancellables)
    }

    func addPendingOperation(_ operation: PendingOperation) {
        pendingOperations.append(operation)
        savePendingOperations()
    }

    private func processPendingOperations() {
        guard !pendingOperations.isEmpty else { return }

        // Process operations in order
        for operation in pendingOperations {
            processOperation(operation)
        }

        // Clear processed operations
        pendingOperations.removeAll()
        savePendingOperations()
    }

    private func processOperation(_ operation: PendingOperation) {
        // Implementation would depend on operation type
        switch operation.type {
        case .profileUpdate:
            // Process profile update
            break
        case .messageSend:
            // Process message send
            break
        case .swipeAction:
            // Process swipe action
            break
        case .imageUpload:
            // Process image upload
            break
        }
    }

    private func savePendingOperations() {
        if let data = try? JSONEncoder().encode(pendingOperations) {
            UserDefaults.standard.set(data, forKey: "pendingOperations")
        }
    }

    private func loadPendingOperations() {
        if let data = UserDefaults.standard.data(forKey: "pendingOperations"),
           let operations = try? JSONDecoder().decode([PendingOperation].self, from: data) {
            pendingOperations = operations
        }
    }
}

// MARK: - Simple Utilities

extension NetworkMonitor {
    /// Simple network quality description
    var qualityDescription: String {
        if !isConnected { return "Offline" }
        if isConstrained { return "Poor" }
        if isExpensive { return "Limited" }
        return "Good"
    }
}
