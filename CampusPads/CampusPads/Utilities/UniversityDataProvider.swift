import Foundation
import FirebaseDatabase

/// Optimized college data provider using Firebase Realtime Database
final class UniversityDataProvider {
    static let shared = UniversityDataProvider()

    // Core data
    private(set) var universities: [String] = []
    private var isLoaded = false

    // Firebase Realtime Database
    private let database = Database.database()
    private var collegesRef: DatabaseReference

    // Search optimization with intelligent caching
    private var searchCache: [String: SearchCacheEntry] = [:]
    private let cacheTimeout: TimeInterval = 300 // 5 minutes
    private let maxCacheSize = 100 // Limit cache size

    // Search performance tracking
    private var searchMetrics: SearchMetrics = SearchMetrics()

    private init() {
        // Initialize Firebase Realtime Database reference
        collegesRef = database.reference().child("colleges")

        // Load universities from Firebase on initialization
        loadUniversitiesFromFirebase()
    }

    // MARK: - Core Loading Functions

    /// Load universities from Firebase with optimized caching
    func loadUniversities(completion: @escaping ([String]) -> Void) {
        if isLoaded {
            completion(universities)
            return
        }

        loadUniversitiesFromFirebase { [weak self] success in
            completion(self?.universities ?? [])
        }
    }

    /// Load universities from Firebase Realtime Database
    private func loadUniversitiesFromFirebase(completion: ((Bool) -> Void)? = nil) {
        print("🔄 UniversityDataProvider: Loading universities from Firebase...")

        collegesRef.observeSingleEvent(of: .value) { [weak self] snapshot in
            guard let self = self else {
                completion?(false)
                return
            }

            if snapshot.exists(), let collegeData = snapshot.value as? [String: Any] {
                // Extract college names from Firebase data
                var collegeSet = Set<String>()

                for (_, value) in collegeData {
                    if let collegeName = value as? String {
                        collegeSet.insert(collegeName)
                    } else if let collegeDict = value as? [String: Any],
                              let name = collegeDict["name"] as? String {
                        collegeSet.insert(name)
                    }
                }

                let sortedColleges = Array(collegeSet).sorted()

                DispatchQueue.main.async {
                    self.universities = sortedColleges
                    self.isLoaded = true
                    print("✅ UniversityDataProvider: Loaded \(sortedColleges.count) universities from Firebase")
                    completion?(true)
                }
            } else {
                print("⚠️ UniversityDataProvider: No data found in Firebase")
                DispatchQueue.main.async {
                    completion?(false)
                }
            }
        } withCancel: { error in
            print("❌ UniversityDataProvider: Firebase load failed: \(error.localizedDescription)")
            DispatchQueue.main.async {
                completion?(false)
            }
        }
    }

    // MARK: - Optimized Search Functions

    /// OPTIMIZED: Fast search with intelligent caching and ranking using robust text normalization
    func searchUniversities(query: String) -> [String] {
        let normalizedQuery = TextNormalizationUtility.normalize(query)

        // Return empty for very short queries
        guard normalizedQuery.count >= 2 else { return [] }

        // Check cache first
        if let cachedResult = getCachedResult(for: normalizedQuery) {
            searchMetrics.cacheHits += 1
            return cachedResult
        }

        // Perform search if data is loaded
        guard isLoaded else {
            // Trigger background load if not loaded
            loadUniversities { _ in }
            return []
        }

        let results = performOptimizedSearch(query: normalizedQuery)

        // Cache the results
        cacheSearchResult(query: normalizedQuery, results: results)

        searchMetrics.totalSearches += 1
        return results
    }

    /// Perform optimized search with intelligent ranking using robust text normalization
    private func performOptimizedSearch(query: String) -> [String] {
        let startTime = CFAbsoluteTimeGetCurrent()

        // Multi-tier search strategy for better performance and relevance with robust text processing
        var exactMatches: [String] = []
        var prefixMatches: [String] = []
        var containsMatches: [String] = []

        for college in universities {
            let normalizedCollege = TextNormalizationUtility.normalize(college)

            if normalizedCollege == query {
                exactMatches.append(college)
            } else if normalizedCollege.hasPrefix(query) {
                prefixMatches.append(college)
            } else if normalizedCollege.contains(query) {
                containsMatches.append(college)
            }

            // Early termination for performance
            if exactMatches.count + prefixMatches.count + containsMatches.count >= 20 {
                break
            }
        }

        // Combine results with priority ranking
        var finalResults: [String] = []
        finalResults.append(contentsOf: exactMatches)
        finalResults.append(contentsOf: prefixMatches.sorted())
        finalResults.append(contentsOf: containsMatches.sorted())

        let searchTime = CFAbsoluteTimeGetCurrent() - startTime
        searchMetrics.averageSearchTime = (searchMetrics.averageSearchTime + searchTime) / 2

        return Array(finalResults.prefix(15)) // Limit to 15 results
    }

    // MARK: - Cache Management

    private func getCachedResult(for query: String) -> [String]? {
        guard let cacheEntry = searchCache[query] else { return nil }

        // Check if cache is still valid
        if Date().timeIntervalSince(cacheEntry.timestamp) < cacheTimeout {
            return cacheEntry.results
        } else {
            // Remove expired cache entry
            searchCache.removeValue(forKey: query)
            return nil
        }
    }

    private func cacheSearchResult(query: String, results: [String]) {
        // Manage cache size
        if searchCache.count >= maxCacheSize {
            // Remove oldest entries
            let sortedEntries = searchCache.sorted { $0.value.timestamp < $1.value.timestamp }
            for i in 0..<(searchCache.count - maxCacheSize + 1) {
                searchCache.removeValue(forKey: sortedEntries[i].key)
            }
        }

        searchCache[query] = SearchCacheEntry(results: results, timestamp: Date())
    }

    /// Clear search cache (useful for memory management)
    func clearSearchCache() {
        searchCache.removeAll()
        print("🧹 UniversityDataProvider: Search cache cleared")
    }

    // MARK: - Utility Functions

    /// Get all universities (for validation purposes)
    func getAllUniversities() -> [String] {
        return universities
    }

    /// Get search performance metrics
    func getSearchMetrics() -> SearchMetrics {
        return searchMetrics
    }

    /// Check if Firebase data exists
    func checkFirebaseDataExists(completion: @escaping (Bool) -> Void) {
        collegesRef.observeSingleEvent(of: .value) { snapshot in
            let exists = snapshot.exists() && snapshot.childrenCount > 0
            print("🔍 UniversityDataProvider: Firebase data exists: \(exists), count: \(snapshot.childrenCount)")
            completion(exists)
        }
    }
}

// MARK: - Supporting Data Structures

struct SearchCacheEntry {
    let results: [String]
    let timestamp: Date
}

struct SearchMetrics {
    var totalSearches: Int = 0
    var cacheHits: Int = 0
    var averageSearchTime: Double = 0.0

    var cacheHitRate: Double {
        guard totalSearches > 0 else { return 0.0 }
        return Double(cacheHits) / Double(totalSearches)
    }
}
