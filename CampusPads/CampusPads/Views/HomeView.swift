import SwiftUI
import FirebaseAuth

struct HomeView: View {
    @StateObject private var profileVM = ProfileViewModel.shared
    @StateObject private var matchingVM = MatchingViewModel()
    @StateObject private var profileViewVM = ProfileViewTrackingViewModel()
    @StateObject private var swipeAnalyticsVM = SwipeAnalyticsViewModel()

    // Navigation state
    @State private var showSettings = false
    @State private var showMyProfile = false
    @State private var showTopMatches = false
    @State private var showProfileViews = false

    var body: some View {
        ZStack {
            // Background that fills entire screen
            AppTheme.backgroundGradient
                .ignoresSafeArea(.all)

            ScrollView {
                AdaptiveContainer {
                    VStack(spacing: ResponsiveSpacing.lg) {
                        // Settings button at the top
                        HStack {
                            Spacer()
                            settingsButton
                        }
                        .responsiveHorizontalPadding()

                        // Header Section
                        headerSection

                        // Profile Section
                        profileSection

                        // Stats Grid
                        statsGrid

                        Spacer(minLength: ResponsiveSpacing.xl)
                    }
                }
            }
        }
        .fullScreenCover(isPresented: $showMyProfile) {
            MyProfileView()
        }
        .onAppear {
            // OPTIMIZATION: Removed automatic MatchingViewModel refresh
            // MatchingViewModel will only refresh through explicit user actions:
            // 1. Manual refresh button in SwipeDeckView
            // 2. Filter changes in AdvancedFilterView
            // 3. Initial load when SwipeDeckView first appears
            profileViewVM.refresh()

            // Load accurate swipe analytics from Firebase
            swipeAnalyticsVM.loadSwipeAnalytics()

            // Sync MatchingViewModel stats with Firebase for accuracy
            matchingVM.syncStatsWithFirebase()
        }
    }

    // MARK: - Settings Button
    private var settingsButton: some View {
        Button(action: {
            showSettings = true
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                AppTheme.primaryColor.opacity(0.2),
                                AppTheme.accentColor.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: ResponsiveSizing.buttonHeight * 0.8, height: ResponsiveSizing.buttonHeight * 0.8)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 8, x: 0, y: 4)

                Image(systemName: "gearshape.fill")
                    .font(.system(size: ResponsiveSizing.iconSize * 0.8, weight: .semibold))
                    .foregroundColor(AppTheme.primaryColor)
            }
            .scaleEffect(1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: true)
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showSettings) {
            NavigationView {
                SettingsView()
                    .environmentObject(AuthViewModel())
            }
        }
    }

    // MARK: - Ultra-Modern Header Section
    private var headerSection: some View {
        VStack(spacing: ResponsiveSpacing.sm) {
            // Main welcome text with gradient
            Text("Welcome to CampusPads!")
                .font(.system(size: ResponsiveTypography.fontSize(32), weight: .black, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .multilineTextAlignment(.center)
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)

            if let profile = profileVM.userProfile {
                HStack(spacing: ResponsiveSpacing.xs) {
                    Text("Hi, \(profile.firstName ?? "there")!")
                        .font(.system(size: ResponsiveTypography.fontSize(20), weight: .semibold, design: .rounded))
                        .foregroundColor(AppTheme.textSecondary)

                    // Animated wave emoji
                    Text("👋")
                        .font(.system(size: ResponsiveTypography.fontSize(24)))
                        .scaleEffect(1.2)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: true)
                }
                .responsivePadding(.horizontal)
                .padding(.vertical, ResponsiveSpacing.xs)
                .background(
                    Capsule()
                        .fill(.ultraThinMaterial)
                        .overlay(
                            Capsule()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            }
        }
        .padding(.top, ResponsiveSpacing.xs)
    }

    // MARK: - Profile Section
    private var profileSection: some View {
        EnhancedCard(shadowStyle: .medium) {
            VStack(spacing: ResponsiveSpacing.md) {
                if let profile = profileVM.userProfile {
                    let completion = ProfileCompletionCalculator.calculateCompletion(for: profile)

                    Button(action: {
                        showMyProfile = true
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                    }) {
                        HStack(spacing: ResponsiveSpacing.md) {
                            // Enhanced Profile Ring
                            ProfileCompletionRingView(
                                completion: completion,
                                imageUrl: getProfileImageUrl(from: profile),
                                size: ResponsiveSizing.profileImageLarge
                            )
                            .onAppear {
                                print("🔍 HomeView: Progress ring image URL sources:")
                                print("   - profileImageUrls: \(profile.profileImageUrls ?? [])")
                                print("   - profileImageUrl: \(profile.profileImageUrl ?? "nil")")
                                print("   - Selected URL: \(getProfileImageUrl(from: profile) ?? "nil")")
                            }

                            VStack(alignment: .leading, spacing: ResponsiveSpacing.xs) {
                                Text("Profile Completion")
                                    .font(ResponsiveTypography.headline)
                                    .foregroundColor(AppTheme.textPrimary)

                                Text("\(Int(completion))% Complete")
                                    .font(ResponsiveTypography.title3)
                                    .foregroundColor(AppTheme.primaryColor)

                                // Modern progress bar with gradient
                                ZStack(alignment: .leading) {
                                    RoundedRectangle(cornerRadius: ResponsiveRadius.small)
                                        .fill(AppTheme.surfaceSecondary.opacity(0.3))
                                        .frame(height: 8)

                                    RoundedRectangle(cornerRadius: ResponsiveRadius.small)
                                        .fill(
                                            LinearGradient(
                                                colors: [AppTheme.primaryColor, AppTheme.accentColor],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: CGFloat(completion / 100) * (DeviceInfo.isIPad ? 300 : 200), height: 8)
                                        .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 4, x: 0, y: 2)
                                        .animation(.spring(response: 0.8, dampingFraction: 0.7), value: completion)
                                }
                                .frame(width: DeviceInfo.isIPad ? 300 : 200)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.system(size: ResponsiveSizing.iconSize * 0.8, weight: .medium))
                                .foregroundColor(AppTheme.textTertiary)
                        }
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    HStack {
                        ProfileImageView(imageUrl: nil, size: ResponsiveSizing.profileImageLarge)

                        VStack(alignment: .leading) {
                            Text("Complete your profile")
                                .font(ResponsiveTypography.headline)
                                .foregroundColor(AppTheme.textPrimary)

                            Text("Get started to find matches")
                                .font(ResponsiveTypography.body)
                                .foregroundColor(AppTheme.textSecondary)
                        }

                        Spacer()
                    }
                }
            }
        }
    }

    // MARK: - Stats Grid (Responsive Layout)
    private var statsGrid: some View {
        Group {
            if DeviceInfo.isIPad {
                // iPad: 2x2 grid layout
                VStack(spacing: ResponsiveSpacing.lg) {
                    HStack(spacing: ResponsiveSpacing.lg) {
                        StatCard(
                            title: "Potential Matches",
                            value: potentialMatchesText,
                            icon: "heart.fill",
                            color: .red
                        )

                        StatCard(
                            title: "Match Rate",
                            value: accurateMatchRateText,
                            icon: "chart.line.uptrend.xyaxis",
                            color: AppTheme.accentColor
                        )
                    }

                    HStack(spacing: ResponsiveSpacing.lg) {
                        // Top Matches Premium Feature
                        topMatchesNavigationCard

                        // Profile Views StatCard
                        profileViewsExpandedCard
                    }
                }
            } else {
                // iPhone: Vertical layout
                VStack(spacing: ResponsiveSpacing.md) {
                    // Row 1 - Keep existing 2-grid layout
                    HStack(spacing: ResponsiveSpacing.md) {
                        StatCard(
                            title: "Potential Matches",
                            value: potentialMatchesText,
                            icon: "heart.fill",
                            color: .red
                        )

                        StatCard(
                            title: "Match Rate",
                            value: accurateMatchRateText,
                            icon: "chart.line.uptrend.xyaxis",
                            color: AppTheme.accentColor
                        )
                    }

                    // Row 2 - Expanded cards (full width)
                    VStack(spacing: ResponsiveSpacing.md) {
                        // Top Matches Premium Feature (full width)
                        topMatchesNavigationCard

                        // Profile Views StatCard (full width with premium styling)
                        profileViewsExpandedCard
                    }
                }
            }
        }
    }



    // MARK: - Top Matches Navigation Card (Premium Feature)
    private var topMatchesNavigationCard: some View {
        Button(action: {
            showTopMatches = true
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            PremiumStatCard(
                title: "Premium Top Matches",
                value: topMatchesDisplayValue,
                subtitle: "Daily curated matches",
                icon: "crown.fill",
                isPremium: true
            )
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showTopMatches) {
            DedicatedTopMatchesView()
        }
    }

    // MARK: - Profile Views Expanded Card (Premium Feature)
    private var profileViewsExpandedCard: some View {
        Button(action: {
            showProfileViews = true
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            PremiumStatCard(
                title: "Profile Views",
                value: profileViewVM.homeSummary.totalViewsText,
                subtitle: "See who viewed you",
                icon: "eye.fill",
                isPremium: true
            )
        }
        .buttonStyle(PlainButtonStyle())
        .fullScreenCover(isPresented: $showProfileViews) {
            NavigationView {
                ProfileViewHistoryView()
            }
        }
    }

    // MARK: - Top Matches Display Value
    private var topMatchesDisplayValue: String {
        if profileVM.userProfile?.isPremium == true {
            return "8 Daily"
        } else {
            return "Upgrade"
        }
    }



    // MARK: - Computed Properties for Stats

    private var matchRateText: String {
        if matchingVM.rightSwipesCount > 0 {
            let rate = (Double(matchingVM.mutualMatchesCount) / Double(matchingVM.rightSwipesCount)) * 100
            return String(format: "%.0f%%", rate)
        } else {
            return "0%"
        }
    }

    // Improved match rate using Firebase analytics data
    private var accurateMatchRateText: String {
        let totalRightSwipes = swipeAnalyticsVM.totalRightSwipes
        let totalMatches = swipeAnalyticsVM.totalMutualMatches

        if totalRightSwipes > 0 {
            let rate = (Double(totalMatches) / Double(totalRightSwipes)) * 100
            return String(format: "%.0f%%", rate)
        } else {
            return "0%"
        }
    }

    // Better potential matches display
    private var potentialMatchesText: String {
        let currentLoaded = matchingVM.potentialMatches.count

        // If we have loaded matches, show the count
        if currentLoaded > 0 {
            return "\(currentLoaded)+"
        } else if matchingVM.isLoading {
            return "..."
        } else {
            return "0"
        }
    }

    /// Helper function to get the best available profile image URL
    /// Prioritizes profileImageUrls array over legacy profileImageUrl
    private func getProfileImageUrl(from profile: UserModel) -> String? {
        // First, try to get from the newer profileImageUrls array
        if let imageUrls = profile.profileImageUrls,
           !imageUrls.isEmpty,
           let firstUrl = imageUrls.first,
           !firstUrl.isEmpty {
            return firstUrl
        }

        // Fallback to legacy profileImageUrl
        if let legacyUrl = profile.profileImageUrl, !legacyUrl.isEmpty {
            return legacyUrl
        }

        // No image available
        return nil
    }




}

/// A reusable ring view that displays the user’s image in the center
/// and a circular progress stroke around it representing profile completion (0–100).
struct ProfileCompletionRingView: View {
    let completion: Double      // 0–100
    let imageUrl: String?       // Optional image URL
    let size: CGFloat           // Ring size

    @State private var animatedCompletion: Double = 0

    var body: some View {
        let strokeWidth = size * 0.075 // 7.5% of ring size
        let imageSize = size * 0.75    // 75% of ring size

        ZStack {
            // Background circle
            Circle()
                .stroke(AppTheme.surfaceSecondary, lineWidth: strokeWidth)

            // Progress circle with gradient
            Circle()
                .trim(from: 0, to: CGFloat(animatedCompletion / 100))
                .stroke(
                    LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: strokeWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: animatedCompletion)

            // Profile image - responsive size
            ProfileImageView(imageUrl: imageUrl, size: imageSize)
        }
        .frame(width: size, height: size)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).delay(0.2)) {
                animatedCompletion = completion
            }
        }
    }
}
