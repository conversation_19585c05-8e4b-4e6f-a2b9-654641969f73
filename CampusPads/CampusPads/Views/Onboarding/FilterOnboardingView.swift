// FilterOnboardingView.swift

import SwiftUI

struct FilterOnboardingView: View {
    @StateObject private var viewModel = AdvancedFilterViewModel()
    @StateObject private var databaseService = RealtimeDatabaseService.shared
    @StateObject private var profileViewModel = ProfileViewModel.shared
    @State private var currentStep = 0
    @State private var animateContent = false
    @State private var showParticles = false
    @Environment(\.dismiss) private var dismiss

    // Completion callback
    let onComplete: () -> Void
    
    // Filter onboarding steps
    private let steps = [
        FilterOnboardingStep(
            icon: "graduationcap.circle.fill",
            title: "Welcome to Discovery!",
            subtitle: "Let's set up your preferences to find perfect roommates",
            description: "We'll help you configure filters to match with compatible people at your college."
        ),
        FilterOnboardingStep(
            icon: "house.circle.fill",
            title: "Housing Status",
            subtitle: "What's your housing situation?",
            description: "This helps us match you with people who have compatible housing needs."
        ),
        FilterOnboardingStep(
            icon: "building.2.crop.circle.fill",
            title: "Your College",
            subtitle: "Which college do you attend?",
            description: "We'll show you roommates from your college for the best matches."
        ),
        FilterOnboardingStep(
            icon: "person.2.circle.fill",
            title: "Gender Preference",
            subtitle: "Who would you prefer as a roommate?",
            description: "Choose your preferred roommate gender for comfortable living."
        ),
        FilterOnboardingStep(
            icon: "calendar.circle.fill",
            title: "Age Range",
            subtitle: "What age difference are you comfortable with?",
            description: "Set the maximum age difference for potential roommates."
        ),
        FilterOnboardingStep(
            icon: "checkmark.circle.fill",
            title: "All Set!",
            subtitle: "Your filters are configured",
            description: "You're ready to discover amazing roommates! You can always adjust these later."
        )
    ]
    
    var body: some View {
        ZStack {
            // Sexy dynamic background with particles
            AppTheme.dynamicBackgroundGradient.ignoresSafeArea()

            // Animated particles
            if showParticles {
                ParticleEffectView()
                    .ignoresSafeArea()
            }

            // Skip button for users who already have filter settings
            if hasExistingFilterSettings() {
                VStack {
                    HStack {
                        Spacer()
                        Button("Skip") {
                            skipOnboarding()
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .padding(.top, 60)
                        .padding(.trailing, 20)
                    }
                    Spacer()
                }
            }
            
            VStack(spacing: 0) {
                // Progress indicator
                progressIndicator
                    .padding(.top, 60)
                    .padding(.horizontal, AppTheme.spacing20)
                
                // Main content
                TabView(selection: $currentStep) {
                    ForEach(0..<steps.count, id: \.self) { index in
                        stepContent(for: index)
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.5), value: currentStep)
                
                // Navigation buttons
                navigationButtons
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, 50)
            }
        }
        .onAppear {
            // Load user's existing profile data for auto-population
            loadUserProfileData()
            
            // Start animations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                animateContent = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                showParticles = true
            }
        }
    }
    
    // MARK: - Progress Indicator
    
    private var progressIndicator: some View {
        VStack(spacing: AppTheme.spacing12) {
            HStack {
                Text("Filter Setup")
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Step \(currentStep + 1) of \(steps.count)")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }
            
            // Progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(.ultraThinMaterial.opacity(0.3))
                        .frame(height: 8)
                    
                    RoundedRectangle(cornerRadius: 4)
                        .fill(AppTheme.sexyGradient)
                        .frame(width: geometry.size.width * CGFloat(currentStep + 1) / CGFloat(steps.count), height: 8)
                        .animation(.easeInOut(duration: 0.5), value: currentStep)
                }
            }
            .frame(height: 8)
        }
    }
    
    // MARK: - Step Content
    
    private func stepContent(for index: Int) -> some View {
        let step = steps[index]

        return ScrollView(.vertical, showsIndicators: false) {
            VStack(spacing: AppTheme.spacing24) {
                // Top spacing
                Spacer()
                    .frame(height: 20)

                // Icon
                Image(systemName: step.icon)
                    .font(.system(size: 80, weight: .light))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .scaleEffect(animateContent ? 1.0 : 0.8)
                    .opacity(animateContent ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.2), value: animateContent)

                // Title and subtitle
                VStack(spacing: AppTheme.spacing8) {
                    Text(step.title)
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)

                    Text(step.subtitle)
                        .font(.custom("AvenirNext-Medium", size: 18))
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)

                    Text(step.description)
                        .font(.custom("AvenirNext-Regular", size: 16))
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppTheme.spacing20)
                }
                .opacity(animateContent ? 1.0 : 0.0)
                .offset(y: animateContent ? 0 : 30)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateContent)

                // Filter input for current step
                filterInputView(for: index)
                    .opacity(animateContent ? 1.0 : 0.0)
                    .offset(y: animateContent ? 0 : 50)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.6), value: animateContent)

                // Bottom spacing for safe scrolling
                Spacer()
                    .frame(height: 120)
            }
            .padding(.horizontal, AppTheme.spacing20)
        }
    }
    
    // MARK: - Filter Input Views
    
    @ViewBuilder
    private func filterInputView(for step: Int) -> some View {
        switch step {
        case 0: // Welcome
            EmptyView()
        case 1: // Housing Status
            housingStatusPicker
        case 2: // College
            collegePicker
        case 3: // Gender Preference
            genderPreferencePicker
        case 4: // Age Range
            ageRangePicker
        case 5: // Complete
            completionView
        default:
            EmptyView()
        }
    }
    
    // MARK: - Navigation Buttons
    
    private var navigationButtons: some View {
        HStack(spacing: AppTheme.spacing16) {
            // Back button
            if currentStep > 0 && currentStep < steps.count - 1 {
                Button(action: previousStep) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                        Text("Back")
                            .font(.custom("AvenirNext-Medium", size: 16))
                    }
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.vertical, AppTheme.spacing12)
                    .padding(.horizontal, AppTheme.spacing20)
                    .background(
                        RoundedRectangle(cornerRadius: 25)
                            .fill(.ultraThinMaterial.opacity(0.3))
                            .overlay(
                                RoundedRectangle(cornerRadius: 25)
                                    .stroke(.white.opacity(0.2), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Spacer()
            
            // Next/Complete button
            Button(action: nextStep) {
                HStack {
                    Text(currentStep == steps.count - 1 ? "Start Discovering!" : 
                         currentStep == 0 ? "Let's Begin" : "Continue")
                        .font(.custom("AvenirNext-Bold", size: 16))
                    
                    if currentStep < steps.count - 1 {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 16, weight: .semibold))
                    } else {
                        Image(systemName: "heart.fill")
                            .font(.system(size: 16, weight: .semibold))
                    }
                }
                .foregroundColor(.white)
                .padding(.vertical, AppTheme.spacing16)
                .padding(.horizontal, AppTheme.spacing24)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(AppTheme.sexyGradient)
                        .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(animateContent ? 1.0 : 0.9)
            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.8), value: animateContent)
            .disabled(!canProceed)
            .opacity(canProceed ? 1.0 : 0.6)
        }
    }
    
    // MARK: - Helper Functions
    
    private var canProceed: Bool {
        switch currentStep {
        case 0: return true // Welcome
        case 1: return true // Housing - Any option (nil) is now valid
        case 2: return !viewModel.filterCollegeName.isEmpty // College
        case 3: return !viewModel.filterPreferredGender.isEmpty // Gender
        case 4: return viewModel.maxAgeDifference > 0 // Age
        case 5: return true // Complete
        default: return false
        }
    }
    
    private func nextStep() {
        HapticFeedbackManager.shared.generateImpact(style: .medium)
        
        if currentStep < steps.count - 1 {
            withAnimation(.easeInOut(duration: 0.5)) {
                currentStep += 1
            }
        } else {
            // Complete onboarding
            completeOnboarding()
        }
    }
    
    private func previousStep() {
        HapticFeedbackManager.shared.generateImpact(style: .light)
        
        if currentStep > 0 {
            withAnimation(.easeInOut(duration: 0.5)) {
                currentStep -= 1
            }
        }
    }
    
    private func completeOnboarding() {
        // Save filters
        viewModel.saveFiltersToUserDoc()

        // Mark onboarding as complete
        UserDefaults.standard.set(true, forKey: "hasCompletedFilterOnboarding")

        print("✅ FilterOnboardingView: Filter onboarding completed and saved")
        print("   - College: \(viewModel.filterCollegeName)")
        print("   - Gender: \(viewModel.filterPreferredGender)")
        print("   - Age: \(viewModel.maxAgeDifference)")
        print("   - Housing: \(viewModel.filterHousingPreference?.rawValue ?? "nil")")

        // Trigger completion
        onComplete()

        // Dismiss
        dismiss()
    }

    private func skipOnboarding() {
        print("⏭️ FilterOnboardingView: User skipped onboarding (already has filter settings)")

        // Mark onboarding as complete even though skipped
        UserDefaults.standard.set(true, forKey: "hasCompletedFilterOnboarding")

        // Trigger completion
        onComplete()

        // Dismiss
        dismiss()
    }

    private func hasExistingFilterSettings() -> Bool {
        guard let userProfile = profileViewModel.userProfile,
              let filterSettings = userProfile.filterSettings else {
            return false
        }

        // Check if user has the mandatory filter settings already configured
        let hasCollegeName = !(filterSettings.collegeName?.isEmpty ?? true)
        let hasPreferredGender = !(filterSettings.preferredGender?.isEmpty ?? true)
        let hasMaxAgeDifference = (filterSettings.maxAgeDifference ?? 0) > 0
        let hasHousingStatus = !(filterSettings.housingStatus?.isEmpty ?? true)

        return hasCollegeName && hasPreferredGender && hasMaxAgeDifference && hasHousingStatus
    }

    private func loadUserProfileData() {
        guard let userProfile = ProfileViewModel.shared.userProfile else { return }

        // Auto-populate college from user profile
        if let userCollege = userProfile.collegeName, !userCollege.isEmpty {
            viewModel.filterCollegeName = userCollege
        }

        // Auto-populate housing status with CORRECT compatibility logic
        if let userHousingStatus = userProfile.housingStatus,
           let userPreference = PrimaryHousingPreference(rawValue: userHousingStatus) {
            // Set the RECOMMENDED filter (not the user's own status)
            let recommendedFilter = getRecommendedFilter(for: userPreference)
            viewModel.filterHousingPreference = recommendedFilter
        }
    }

    // MARK: - Housing Compatibility Helper Functions

    /// Returns the recommended filter based on user's housing status using proper compatibility logic
    private func getRecommendedFilter(for userStatus: PrimaryHousingPreference) -> PrimaryHousingPreference {
        switch userStatus {
        case .lookingForRoommate:
            // User has a place, should filter for people who need a place
            return .lookingForLease
        case .lookingForLease:
            // User needs a place, should filter for people who have a place
            return .lookingForRoommate
        case .lookingToFindTogether:
            // User wants to find together, should filter for similar people (default to same)
            return .lookingToFindTogether
        }
    }

    /// Checks if the given housing preference is the recommended filter for the current user
    private func isRecommendedFilter(_ housing: PrimaryHousingPreference) -> Bool {
        guard let userProfile = profileViewModel.userProfile,
              let userHousingStatus = userProfile.housingStatus,
              let userPreference = PrimaryHousingPreference(rawValue: userHousingStatus) else {
            return false
        }

        let recommendedFilter = getRecommendedFilter(for: userPreference)
        return housing == recommendedFilter
    }

    /// Provides explanation for why this filter makes sense
    private func getCompatibilityExplanation(userStatus: PrimaryHousingPreference, filterFor: PrimaryHousingPreference) -> String {
        switch (userStatus, filterFor) {
        case (.lookingForRoommate, .lookingForLease):
            return "You have a place, they need a place - perfect match!"
        case (.lookingForLease, .lookingForRoommate):
            return "You need a place, they have a place - perfect match!"
        case (.lookingToFindTogether, .lookingToFindTogether):
            return "Both want to search for housing together"
        case (.lookingToFindTogether, .lookingForLease):
            return "You can help them find a place together"
        default:
            return "Compatible housing preferences"
        }
    }

    // MARK: - Filter Input Components

    private var housingStatusPicker: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Housing explanation
            VStack(spacing: AppTheme.spacing8) {
                Text("Choose your housing situation:")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.9))

                Text("• Looking for Roommate: You have a place, need someone to share\n• Looking for Lease: You need a place and a roommate\n• Looking to Find Together: You want to find a place together")
                    .font(.custom("AvenirNext-Regular", size: 14))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.leading)
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
            )

            // User's current housing status hint with proper compatibility logic
            if let userProfile = profileViewModel.userProfile,
               let userHousingStatus = userProfile.housingStatus,
               let userPreference = PrimaryHousingPreference(rawValue: userHousingStatus) {

                let recommendedFilter = getRecommendedFilter(for: userPreference)

                VStack(spacing: AppTheme.spacing8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.yellow)

                        Text("Smart Recommendation:")
                            .font(.custom("AvenirNext-Medium", size: 14))
                            .foregroundColor(.white.opacity(0.9))

                        Spacer()
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Your profile: \"\(userPreference.rawValue)\"")
                                .font(.custom("AvenirNext-Regular", size: 13))
                                .foregroundColor(.white.opacity(0.7))

                            Spacer()
                        }

                        HStack {
                            Text("Filter for: \"\(recommendedFilter.rawValue)\"")
                                .font(.custom("AvenirNext-Bold", size: 14))
                                .foregroundColor(.yellow)

                            Spacer()
                        }

                        HStack {
                            Text(getCompatibilityExplanation(userStatus: userPreference, filterFor: recommendedFilter))
                                .font(.custom("AvenirNext-Regular", size: 12))
                                .foregroundColor(.white.opacity(0.6))

                            Spacer()
                        }
                    }
                }
                .padding(AppTheme.spacing12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.yellow.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(.yellow.opacity(0.3), lineWidth: 1)
                        )
                )
            }

            // Housing options (including "Any")
            VStack(spacing: AppTheme.spacing12) {
                // Add "Any" option first
                anyHousingOptionButton()

                // Then add specific housing preferences
                ForEach(PrimaryHousingPreference.allCases, id: \.self) { housing in
                    housingOptionButton(for: housing)
                }
            }
        }
    }

    private func anyHousingOptionButton() -> some View {
        let isSelected = viewModel.filterHousingPreference == nil

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterHousingPreference = nil
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Any Housing Status")
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(isSelected ? .white : .white.opacity(0.9))

                    Text("Show all users regardless of housing situation")
                        .font(.custom("AvenirNext-Regular", size: 14))
                        .foregroundColor(isSelected ? .white.opacity(0.9) : .white.opacity(0.7))
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private func housingOptionButton(for housing: PrimaryHousingPreference) -> some View {
        let isSelected = viewModel.filterHousingPreference == housing
        let isRecommended = isRecommendedFilter(housing)

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterHousingPreference = housing
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(housing.rawValue)
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(isSelected ? .white : .white.opacity(0.9))

                        if isRecommended {
                            HStack(spacing: 4) {
                                Image(systemName: "star.fill")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.yellow)

                                Text("Recommended")
                                    .font(.custom("AvenirNext-Bold", size: 10))
                                    .foregroundColor(.yellow)
                            }
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(.yellow.opacity(0.2))
                            )
                        }
                    }

                    Text(housingDescription(for: housing))
                        .font(.custom("AvenirNext-Regular", size: 14))
                        .foregroundColor(isSelected ? .white.opacity(0.9) : .white.opacity(0.7))
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) :
                          isRecommended ? AnyShapeStyle(.yellow.opacity(0.1)) :
                          AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.clear :
                                   isRecommended ? .yellow.opacity(0.4) :
                                   .white.opacity(0.2), lineWidth: isRecommended ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : isRecommended ? 1.01 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private func housingDescription(for housing: PrimaryHousingPreference) -> String {
        switch housing {
        case .lookingForRoommate:
            return "I have a place and need someone to share it"
        case .lookingForLease:
            return "I need both a place and a roommate"
        case .lookingToFindTogether:
            return "Let's find a place together"
        }
    }

    private var collegePicker: some View {
        VStack(spacing: AppTheme.spacing16) {
            // College search/selection
            VStack(spacing: AppTheme.spacing12) {
                Text("Search for your college:")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.9))

                CollegeSearchField(
                    selectedCollege: Binding(
                        get: { viewModel.filterCollegeName },
                        set: { newValue in
                            viewModel.filterCollegeName = newValue
                        }
                    ),
                    label: "",
                    placeholder: "Start typing your college name..."
                )
            }
        }
    }



    private var genderPreferencePicker: some View {
        VStack(spacing: AppTheme.spacing16) {
            Text("Who would you prefer as a roommate?")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.9))

            VStack(spacing: AppTheme.spacing12) {
                ForEach(["Any", "Male", "Female", "Non-binary"], id: \.self) { gender in
                    genderOptionButton(for: gender)
                }
            }
        }
    }

    private func genderOptionButton(for gender: String) -> some View {
        let isSelected = viewModel.filterPreferredGender == gender

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterPreferredGender = gender
        }) {
            HStack {
                Text(gender)
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.9))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var ageRangePicker: some View {
        VStack(spacing: AppTheme.spacing20) {
            Text("Maximum age difference you're comfortable with:")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.9))
                .multilineTextAlignment(.center)

            VStack(spacing: AppTheme.spacing16) {
                Text("\(Int(viewModel.maxAgeDifference)) years")
                    .font(.custom("AvenirNext-Bold", size: 32))
                    .foregroundColor(.white)

                Slider(value: $viewModel.maxAgeDifference, in: 1...10, step: 1)
                    .tint(.white)
                    .scaleEffect(x: 1, y: 2, anchor: .center)

                HStack {
                    Text("1 year")
                        .font(.custom("AvenirNext-Regular", size: 14))
                        .foregroundColor(.white.opacity(0.7))

                    Spacer()

                    Text("10 years")
                        .font(.custom("AvenirNext-Regular", size: 14))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .padding(AppTheme.spacing20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
            )
        }
    }

    private var completionView: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Success animation
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 120, height: 120)
                    .scaleEffect(showParticles ? 1.0 : 0.8)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6), value: showParticles)

                Image(systemName: "checkmark")
                    .font(.system(size: 50, weight: .bold))
                    .foregroundColor(.white)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("Perfect!")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundColor(.white)

                Text("Your filters are set up and ready to find amazing roommates.")
                    .font(.custom("AvenirNext-Regular", size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
        }
    }
}

// MARK: - Particle Effect View

struct ParticleEffectView: View {
    @State private var particles: [FilterOnboardingParticle] = []

    var body: some View {
        ZStack {
            ForEach(particles, id: \.id) { particle in
                Circle()
                    .fill(Color.white.opacity(0.3))
                    .frame(width: particle.size, height: particle.size)
                    .position(particle.position)
                    .opacity(particle.opacity)
            }
        }
        .onAppear {
            createParticles()
        }
    }

    private func createParticles() {
        for _ in 0..<20 {
            let particle = FilterOnboardingParticle(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                ),
                size: CGFloat.random(in: 2...8),
                opacity: Double.random(in: 0.1...0.6)
            )
            particles.append(particle)
        }

        // Animate particles
        withAnimation(.linear(duration: 10).repeatForever(autoreverses: false)) {
            for i in particles.indices {
                particles[i].position.y -= 1000
            }
        }
    }
}

struct FilterOnboardingParticle {
    let id: UUID
    var position: CGPoint
    let size: CGFloat
    let opacity: Double
}

// MARK: - Supporting Types

struct FilterOnboardingStep {
    let icon: String
    let title: String
    let subtitle: String
    let description: String
}

// MARK: - Preview

#Preview {
    FilterOnboardingView {
        print("Onboarding completed!")
    }
}
