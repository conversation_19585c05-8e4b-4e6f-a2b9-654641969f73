//
//  MessageModel.swift
//  CampusPads
//
//  Created by [Your Name] on [Date].
//

import Foundation
import FirebaseFirestoreCombineSwift

struct MessageModel: Identifiable, Codable {
    var id: String?
    let senderID: String
    let text: String
    let timestamp: Date
    var isRead: Bool?
    var reactions: [String: Int]? = nil

    // Media message properties
    var messageType: MessageType = .text
    var mediaUrl: String?
    var mediaType: String? // MIME type like "image/jpeg", "video/mp4"
    var thumbnailUrl: String? // For videos
    var fileName: String? // For documents
    var fileSize: Int? // In bytes
    var uploadProgress: Double? // 0.0 to 1.0 for upload progress

    enum CodingKeys: String, CodingKey {
        case senderID, text, timestamp, isRead, reactions
        case messageType, mediaUrl, mediaType, thumbnailUrl, fileName, fileSize, uploadProgress
    }
}

enum MessageType: String, Codable, CaseIterable {
    case text = "text"
    case image = "image"
    case video = "video"
    case document = "document"

    var displayName: String {
        switch self {
        case .text: return "Text"
        case .image: return "Image"
        case .video: return "Video"
        case .document: return "Document"
        }
    }
}
