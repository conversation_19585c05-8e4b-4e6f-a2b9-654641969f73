import SwiftUI

struct SignUpView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Binding var showSignIn: Bool
    @State private var confirmPassword: String = ""
    @State private var hasAttemptedSignUp: Bool = false

    // Computed property for form validity using enhanced validation.
    private var isFormValid: Bool {
        return authViewModel.isEmailValid &&
               authViewModel.isPasswordValid &&
               !confirmPassword.isEmpty &&
               authViewModel.password == confirmPassword
    }

    private var passwordsMatch: Bool {
        return authViewModel.password == confirmPassword
    }

    private var confirmPasswordError: String? {
        if hasAttemptedSignUp && !confirmPassword.isEmpty && !passwordsMatch {
            return "Passwords do not match"
        }
        return nil
    }

    var body: some View {
        ScrollView {
                VStack(spacing: AppTheme.spacing24) {
                    Spacer(minLength: AppTheme.spacing40)

                    // Header
                    VStack(spacing: AppTheme.spacing8) {
                        Text("Join CampusPads")
                            .font(AppTheme.title1)
                            .foregroundColor(AppTheme.textPrimary)

                        Text("Create your account to get started")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                            .multilineTextAlignment(.center)
                    }

                    // Form
                    VStack(spacing: AppTheme.spacing20) {
                        EnhancedTextField(
                            title: "Email Address",
                            text: $authViewModel.email,
                            placeholder: "Enter your email address",
                            keyboardType: .emailAddress,
                            errorMessage: hasAttemptedSignUp && !authViewModel.isEmailValid && !authViewModel.email.isEmpty ?
                                "Please use a valid email address" : nil
                        )
                        .overlay(
                            HStack {
                                Spacer()
                                if !authViewModel.email.isEmpty {
                                    Image(systemName: authViewModel.isEmailValid ? "checkmark.circle.fill" : "xmark.circle.fill")
                                        .foregroundColor(authViewModel.isEmailValid ? AppTheme.successColor : AppTheme.errorColor)
                                        .font(.system(size: 20, weight: .medium))
                                        .padding(.trailing, AppTheme.spacing16)
                                }
                            }
                            .padding(.top, 32) // Offset to align with text field center (title height + text field padding)
                        )

                        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                            EnhancedTextField(
                                title: "Password",
                                text: $authViewModel.password,
                                placeholder: "Create a secure password (min 8 characters)",
                                isSecure: true,
                                errorMessage: hasAttemptedSignUp && !authViewModel.isPasswordValid && !authViewModel.password.isEmpty ?
                                    "Password must be at least 8 characters with uppercase, lowercase, number, and special character" : nil
                            )

                            // Password strength indicator
                            if !authViewModel.password.isEmpty {
                                PasswordStrengthIndicator(password: authViewModel.password)
                            }
                        }

                        EnhancedTextField(
                            title: "Confirm Password",
                            text: $confirmPassword,
                            placeholder: "Confirm your password",
                            isSecure: true,
                            errorMessage: confirmPasswordError
                        )
                        .overlay(
                            HStack {
                                Spacer()
                                if !confirmPassword.isEmpty {
                                    Image(systemName: passwordsMatch ? "checkmark.circle.fill" : "xmark.circle.fill")
                                        .foregroundColor(passwordsMatch ? AppTheme.successColor : AppTheme.errorColor)
                                        .font(.system(size: 20, weight: .medium))
                                        .padding(.trailing, AppTheme.spacing16)
                                }
                            }
                            .padding(.top, 32) // Offset to align with text field center (title height + text field padding)
                        )
                    }
                    .padding(.horizontal, AppTheme.spacing20)

                    // Sexy error message display
                    if hasAttemptedSignUp,
                       let errorMessage = authViewModel.errorMessage,
                       !errorMessage.isEmpty,
                       !errorMessage.contains("email") && !errorMessage.contains("password") {
                        HStack(spacing: AppTheme.spacing12) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(AppTheme.errorColor)
                                .font(.system(size: 16, weight: .medium))

                            Text(errorMessage)
                                .font(AppTheme.body)
                                .foregroundColor(AppTheme.textPrimary)
                                .multilineTextAlignment(.leading)

                            Spacer()
                        }
                        .padding(AppTheme.spacing16)
                        .background(
                            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                                .fill(AppTheme.errorColor.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                                        .stroke(AppTheme.errorColor.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .padding(.horizontal, AppTheme.spacing20)
                        .transition(.scale.combined(with: .opacity))
                    }

                    // Sign Up Button
                    Button(action: {
                        hasAttemptedSignUp = true
                        authViewModel.signUp()
                    }) {
                        if authViewModel.isLoading {
                            HStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("Creating Account...")
                            }
                        } else {
                            Text("Create Account")
                        }
                    }
                    .buttonStyle(PrimaryButtonStyle(
                        backgroundColor: isFormValid ? AppTheme.primaryColor : AppTheme.primaryColor.opacity(0.5),
                        isLoading: authViewModel.isLoading
                    ))
                    .disabled(!isFormValid || authViewModel.isLoading)
                    .padding(.horizontal, AppTheme.spacing20)

                    // Sign In Link
                    Button("Already have an account? Sign In") {
                        authViewModel.errorMessage = nil
                        authViewModel.email = ""
                        authViewModel.password = ""
                        confirmPassword = ""
                        showSignIn = true
                    }
                    .buttonStyle(TertiaryButtonStyle())

                    Spacer(minLength: AppTheme.spacing40)
                }
            }
        .sheet(isPresented: $authViewModel.showAgeVerification) {
            AgeVerificationView(
                onAgeVerified: { dateOfBirth in
                    authViewModel.completeSignUpWithAge(dateOfBirth: dateOfBirth)
                },
                onAgeRejected: {
                    authViewModel.cancelAgeVerification()
                }
            )
        }
    }
}

// MARK: - Password Strength Indicator

struct PasswordStrengthIndicator: View {
    let password: String

    private var strength: PasswordStrength {
        calculatePasswordStrength(password)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack(spacing: AppTheme.spacing4) {
                ForEach(0..<4, id: \.self) { index in
                    Rectangle()
                        .frame(height: 4)
                        .foregroundColor(index < strength.level ? strength.color : AppTheme.surfaceSecondary)
                        .cornerRadius(2)
                }
            }

            Text(strength.text)
                .font(AppTheme.caption)
                .foregroundColor(strength.color)
        }
        .padding(.horizontal, AppTheme.spacing16)
    }

    private func calculatePasswordStrength(_ password: String) -> PasswordStrength {
        var score = 0

        if password.count >= 8 { score += 1 }
        if password.range(of: "[A-Z]", options: .regularExpression) != nil { score += 1 }
        if password.range(of: "[a-z]", options: .regularExpression) != nil { score += 1 }
        if password.range(of: "[0-9]", options: .regularExpression) != nil { score += 1 }
        if password.range(of: "[!@#$%^&*(),.?\":{}|<>]", options: .regularExpression) != nil { score += 1 }

        switch score {
        case 0...1:
            return PasswordStrength(level: 1, color: AppTheme.errorColor, text: "Weak")
        case 2...3:
            return PasswordStrength(level: 2, color: AppTheme.warningColor, text: "Fair")
        case 4:
            return PasswordStrength(level: 3, color: .blue, text: "Good")
        case 5:
            return PasswordStrength(level: 4, color: AppTheme.successColor, text: "Strong")
        default:
            return PasswordStrength(level: 1, color: AppTheme.errorColor, text: "Weak")
        }
    }
}

struct PasswordStrength {
    let level: Int
    let color: Color
    let text: String
}

struct SignUpView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            SignUpView(showSignIn: .constant(true))
                .environmentObject(AuthViewModel())
        }
    }
}
