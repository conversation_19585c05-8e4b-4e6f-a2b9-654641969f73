import SwiftUI

// MARK: - Shadow Style Enum
enum CardShadowStyle {
    case light, medium, heavy, none
}

// MARK: - Ultra-Modern Enhanced Card Component
struct EnhancedCard<Content: View>: View {
    let content: Content
    var padding: CGFloat = AppTheme.spacing16
    var cornerRadius: CGFloat = AppTheme.radiusXLarge
    var shadowStyle: CardShadowStyle = .medium
    @State private var isPressed = false

    init(padding: CGFloat = AppTheme.spacing16,
         cornerRadius: CGFloat = AppTheme.radiusXLarge,
         shadowStyle: CardShadowStyle = .medium,
         @ViewBuilder content: () -> Content) {
        self.content = content()
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.shadowStyle = shadowStyle
    }

    var body: some View {
        content
            .padding(padding)
            .background(
                ZStack {
                    // Advanced glassmorphism background
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: cornerRadius)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.25),
                                            Color.white.opacity(0.1),
                                            Color.clear
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                        )

                    // Sexy border glow
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.5),
                                    Color.white.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                }
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
            .applyShadow(shadowStyle)
            .onTapGesture {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                        isPressed = false
                    }
                }
                HapticFeedbackManager.shared.generateImpact(style: .light)
            }
    }
}

// MARK: - Loading State Component
struct LoadingView: View {
    var message: String = "Loading..."
    var size: CGFloat = 40

    @State private var isAnimating = false

    var body: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 3)
                    .frame(width: size, height: size)

                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(AppTheme.primaryColor, style: StrokeStyle(lineWidth: 3, lineCap: .round))
                    .frame(width: size, height: size)
                    .rotationEffect(.degrees(isAnimating ? 360 : 0))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: isAnimating)
            }

            Text(message)
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
                .opacity(isAnimating ? 1 : 0.7)
                .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: isAnimating)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundGradient.ignoresSafeArea())
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Shimmer Loading Component
struct ShimmerView: View {
    @State private var isAnimating = false

    var body: some View {
        Rectangle()
            .fill(
                LinearGradient(
                    colors: [
                        AppTheme.surfaceSecondary,
                        AppTheme.surfacePrimary,
                        AppTheme.surfaceSecondary
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .mask(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [.clear, .black, .clear],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(70))
                    .offset(x: isAnimating ? 200 : -200)
            )
            .onAppear {
                withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                    isAnimating = true
                }
            }
    }
}

// MARK: - Empty State Component
struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    var actionTitle: String? = nil
    var action: (() -> Void)? = nil

    var body: some View {
        VStack(spacing: AppTheme.spacing20) {
            Image(systemName: icon)
                .font(.system(size: 60, weight: .light))
                .foregroundColor(AppTheme.primaryColor.opacity(0.6))

            VStack(spacing: AppTheme.spacing8) {
                Text(title)
                    .font(AppTheme.title3)
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)

                Text(subtitle)
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }

            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle, action: action)
                    .buttonStyle(PrimaryButtonStyle())
                    .padding(.horizontal, AppTheme.spacing32)
            }
        }
        .padding(AppTheme.spacing40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Enhanced Text Field
struct EnhancedTextField: View {
    let title: String
    @Binding var text: String
    var placeholder: String = ""
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var errorMessage: String? = nil

    @FocusState private var isFocused: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(title)
                .font(AppTheme.subheadline.weight(.medium))
                .foregroundColor(AppTheme.textPrimary)

            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .font(AppTheme.body)
            .padding(AppTheme.spacing16)
            .background(AppTheme.surfacePrimary)
            .cornerRadius(AppTheme.radiusSmall)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusSmall)
                    .stroke(borderColor, lineWidth: 1)
            )
            .focused($isFocused)
            .keyboardType(keyboardType)

            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.errorColor)
            }
        }
    }

    private var borderColor: Color {
        if let _ = errorMessage {
            return AppTheme.errorColor
        } else if isFocused {
            return AppTheme.primaryColor
        } else {
            return AppTheme.textTertiary
        }
    }
}

// MARK: - Ultra-Sexy Modern Stat Card Component
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    var color: Color = AppTheme.primaryColor
    var isPremium: Bool = false
    @State private var isHovered = false

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            HStack {
                // Enhanced icon with glow effect
                Image(systemName: icon)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        ZStack {
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: isPremium ? [
                                            Color.yellow.opacity(0.9),
                                            Color.orange.opacity(0.8),
                                            Color.yellow
                                        ] : [
                                            color,
                                            color.opacity(0.8),
                                            color
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )

                            Circle()
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                .blur(radius: 1)
                        }
                    )
                    .shadow(color: isPremium ? Color.yellow.opacity(0.4) : color.opacity(0.4), radius: 12, x: 0, y: 6)
                    .scaleEffect(isHovered ? 1.1 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isHovered)

                Spacer()

                // Premium crown or regular sparkle
                Image(systemName: isPremium ? "crown.fill" : "sparkles")
                    .font(.system(size: isPremium ? 16 : 12, weight: .medium))
                    .foregroundColor(isPremium ? Color.yellow.opacity(0.8) : color.opacity(0.6))
                    .scaleEffect(isHovered ? 1.2 : 0.8)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isHovered)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(value)
                    .font(.system(size: 24, weight: .black, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)
                    .shadow(color: color.opacity(0.2), radius: 2, x: 0, y: 1)

                Text(title)
                    .font(.system(size: 14, weight: .semibold, design: .rounded))
                    .foregroundColor(AppTheme.textSecondary)
                    .tracking(0.5)
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            ZStack {
                // Advanced glassmorphism background
                RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                            .fill(
                                LinearGradient(
                                    colors: isPremium ? [
                                        Color.yellow.opacity(0.1),
                                        Color.orange.opacity(0.05),
                                        Color.clear
                                    ] : [
                                        Color.white.opacity(0.2),
                                        Color.white.opacity(0.1),
                                        Color.clear
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                    )

                // Sexy border glow
                RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                    .stroke(
                        LinearGradient(
                            colors: isPremium ? [
                                Color.yellow.opacity(0.6),
                                Color.orange.opacity(0.4),
                                Color.yellow.opacity(0.2)
                            ] : [
                                Color.white.opacity(0.4),
                                Color.white.opacity(0.2),
                                Color.clear
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: isPremium ? 2 : 1.5
                    )
            }
        )
        .shadow(color: color.opacity(0.1), radius: 20, x: 0, y: 10)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isHovered)
        .onTapGesture {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isHovered.toggle()
            }
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
        .onAppear {
            // Subtle entrance animation
            withAnimation(.easeOut(duration: 0.6).delay(Double.random(in: 0...0.3))) {
                isHovered = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                withAnimation(.easeInOut(duration: 0.4)) {
                    isHovered = false
                }
            }
        }
    }
}

// MARK: - Premium Stat Card Component
struct PremiumStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    var isPremium: Bool = true
    @State private var isHovered = false
    @State private var glowIntensity: Double = 0.3

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Header with icon and premium badge
            HStack {
                // Premium icon with enhanced glow
                Image(systemName: icon)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 52, height: 52)
                    .background(
                        ZStack {
                            Circle()
                                .fill(premiumGradient)

                            Circle()
                                .stroke(Color.white.opacity(0.4), lineWidth: 2)
                                .blur(radius: 1)
                        }
                    )
                    .shadow(color: Color.yellow.opacity(glowIntensity), radius: 16, x: 0, y: 8)
                    .scaleEffect(isHovered ? 1.1 : 1.0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isHovered)

                Spacer()

                // Premium badge
                premiumBadge
            }

            // Content section
            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                Text(title)
                    .font(.system(size: 18, weight: .bold, design: .rounded))
                    .foregroundColor(AppTheme.textPrimary)

                Text(value)
                    .font(.system(size: 28, weight: .black, design: .rounded))
                    .foregroundStyle(premiumTextGradient)

                Text(subtitle)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)
            }

            // Navigation indicator
            HStack {
                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(Color.yellow.opacity(0.8))
                    .scaleEffect(isHovered ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isHovered)
            }
        }
        .padding(AppTheme.spacing20)
        .background(premiumCardBackground)
        .cornerRadius(AppTheme.radiusXLarge)
        .overlay(premiumBorder)
        .shadow(color: Color.yellow.opacity(0.3), radius: 20, x: 0, y: 10)
        .onHover { hovering in
            isHovered = hovering
        }
        .onAppear {
            // Subtle breathing glow animation
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                glowIntensity = 0.6
            }
        }
    }

    // MARK: - Premium Styling Components
    private var premiumGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color.yellow.opacity(0.9),
                Color.orange.opacity(0.8),
                Color.yellow
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var premiumTextGradient: LinearGradient {
        LinearGradient(
            colors: [Color.yellow, Color.orange, Color.yellow],
            startPoint: .leading,
            endPoint: .trailing
        )
    }

    private var premiumBadge: some View {
        HStack(spacing: AppTheme.spacing6) {
            Image(systemName: "star.fill")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.white)

            Text("PREMIUM")
                .font(.system(size: 11, weight: .black, design: .rounded))
                .foregroundColor(.white)
        }
        .padding(.horizontal, AppTheme.spacing12)
        .padding(.vertical, AppTheme.spacing8)
        .background(premiumGradient)
        .cornerRadius(AppTheme.radiusMedium)
        .shadow(color: Color.yellow.opacity(0.4), radius: 8, x: 0, y: 4)
    }

    private var premiumCardBackground: some View {
        ZStack {
            // Base glassmorphism
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)

            // Premium overlay
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.yellow.opacity(0.15),
                            Color.orange.opacity(0.08),
                            Color.clear,
                            Color.yellow.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.yellow.opacity(0.8),
                        Color.orange.opacity(0.6),
                        Color.yellow.opacity(0.4)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 2.5
            )
    }
}

// MARK: - Simple Profile Image Component
struct ProfileImageView: View {
    let imageUrl: String?
    let size: CGFloat
    var placeholder: String = "person.crop.circle"

    var body: some View {
        ZStack {
            Circle()
                .fill(AppTheme.surfaceSecondary)

            if let urlStr = imageUrl, !urlStr.isEmpty, let url = URL(string: urlStr) {
                RobustAsyncImage(url: url)
                    .frame(width: size, height: size)
                    .clipShape(Circle())
            } else {
                Image(systemName: placeholder)
                    .font(.system(size: size * 0.4))
                    .foregroundColor(AppTheme.textTertiary)
            }
        }
        .frame(width: size, height: size)
        .overlay(
            Circle()
                .stroke(AppTheme.surfaceSecondary.opacity(0.3), lineWidth: 1)
        )
        .lightShadow()
    }
}

// MARK: - Enhanced Profile Image Component with UserModel Support
struct EnhancedProfileImageView: View {
    let user: UserModel?
    let size: CGFloat
    var placeholder: String = "person.crop.circle"

    var body: some View {
        ProfileImageView(
            imageUrl: getBestProfileImageUrl(from: user),
            size: size,
            placeholder: placeholder
        )
    }

    /// Helper function to get the best available profile image URL from UserModel
    /// Prioritizes profileImageUrls array over legacy profileImageUrl
    private func getBestProfileImageUrl(from user: UserModel?) -> String? {
        guard let user = user else { return nil }

        // First, try to get from the newer profileImageUrls array
        if let imageUrls = user.profileImageUrls,
           !imageUrls.isEmpty,
           let firstUrl = imageUrls.first,
           !firstUrl.isEmpty {
            return firstUrl
        }

        // Fallback to legacy profileImageUrl
        if let legacyUrl = user.profileImageUrl, !legacyUrl.isEmpty {
            return legacyUrl
        }

        // No image available
        return nil
    }
}

// MARK: - Toast Notification Component
struct ToastView: View {
    let message: String
    let type: ToastType
    @Binding var isShowing: Bool

    enum ToastType {
        case success, error, warning, info

        var color: Color {
            switch self {
            case .success: return AppTheme.successColor
            case .error: return AppTheme.errorColor
            case .warning: return AppTheme.warningColor
            case .info: return AppTheme.infoColor
            }
        }

        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .error: return "xmark.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .info: return "info.circle.fill"
            }
        }
    }

    var body: some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: type.icon)
                .foregroundColor(type.color)
                .font(.system(size: 20, weight: .medium))

            Text(message)
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textPrimary)
                .multilineTextAlignment(.leading)

            Spacer()

            Button(action: { isShowing = false }) {
                Image(systemName: "xmark")
                    .foregroundColor(AppTheme.textSecondary)
                    .font(.system(size: 14, weight: .medium))
            }
        }
        .padding(AppTheme.spacing16)
        .background(AppTheme.surfacePrimary)
        .cornerRadius(AppTheme.radiusMedium)
        .mediumShadow()
        .transition(.move(edge: .top).combined(with: .opacity))
    }
}

// MARK: - Sexy Modern Search Bar
struct SearchBar: View {
    @Binding var text: String
    var placeholder: String = "Search..."
    var onSearchButtonClicked: (() -> Void)? = nil

    @FocusState private var isFocused: Bool
    @State private var isAnimating = false
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        HStack(spacing: AppTheme.spacing12) {
            HStack(spacing: AppTheme.spacing12) {
                // Animated search icon
                Image(systemName: isFocused ? "magnifyingglass.circle.fill" : "magnifyingglass")
                    .foregroundColor(isFocused ? AppTheme.primaryColor : AppTheme.textSecondary)
                    .font(.system(size: 18, weight: .medium))
                    .scaleEffect(isFocused ? 1.1 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isFocused)

                TextField(placeholder, text: $text)
                    .font(AppTheme.body)
                    .focused($isFocused)
                    .onSubmit {
                        onSearchButtonClicked?()
                    }

                if !text.isEmpty {
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                            text = ""
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppTheme.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding(.horizontal, AppTheme.spacing16)
            .padding(.vertical, AppTheme.spacing12)
            .background(
                ZStack {
                    // Glass morphism background
                    AppTheme.modernCardGradient
                    AppTheme.glassEffect
                }
            )
            .cornerRadius(AppTheme.radiusXLarge)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                isFocused ? AppTheme.primaryColor : Color.white.opacity(0.2),
                                isFocused ? AppTheme.accentColor : Color.white.opacity(0.1),
                                isFocused ? AppTheme.primaryColor.opacity(0.6) : Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: isFocused ? 2 : 1
                    )
                    .animation(.easeInOut(duration: 0.2), value: isFocused)
            )
            .shadow(
                color: isFocused ? AppTheme.primaryColor.opacity(0.2) : Color.black.opacity(0.05),
                radius: isFocused ? 12 : 6,
                x: 0,
                y: isFocused ? 6 : 3
            )
            .scaleEffect(isFocused ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isFocused)
        }
    }
}

// MARK: - Badge Component
struct BadgeView: View {
    let text: String
    var backgroundColor: Color = AppTheme.primaryColor
    var textColor: Color = .white
    var size: BadgeSize = .medium

    enum BadgeSize {
        case small, medium, large

        var font: Font {
            switch self {
            case .small: return AppTheme.caption
            case .medium: return AppTheme.footnote
            case .large: return AppTheme.subheadline
            }
        }

        var padding: CGFloat {
            switch self {
            case .small: return AppTheme.spacing4
            case .medium: return AppTheme.spacing8
            case .large: return AppTheme.spacing12
            }
        }
    }

    var body: some View {
        Text(text)
            .font(size.font.weight(.medium))
            .foregroundColor(textColor)
            .padding(.horizontal, size.padding * 1.5)
            .padding(.vertical, size.padding)
            .background(backgroundColor)
            .cornerRadius(AppTheme.radiusLarge)
            .lightShadow()
    }
}

// MARK: - View Extensions
extension View {
    func applyShadow(_ style: CardShadowStyle) -> some View {
        switch style {
        case .light:
            return AnyView(self.lightShadow())
        case .medium:
            return AnyView(self.mediumShadow())
        case .heavy:
            return AnyView(self.heavyShadow())
        case .none:
            return AnyView(self.shadow(radius: 0))
        }
    }

    // Toast modifier
    func toast<Content: View>(
        isShowing: Binding<Bool>,
        @ViewBuilder content: () -> Content
    ) -> some View {
        ZStack(alignment: .top) {
            self

            if isShowing.wrappedValue {
                content()
                    .padding(.horizontal, AppTheme.spacing16)
                    .padding(.top, AppTheme.spacing8)
                    .zIndex(1000)
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation(.easeInOut(duration: AppTheme.animationMedium)) {
                                isShowing.wrappedValue = false
                            }
                        }
                    }
            }
        }
    }

    // Shimmer effect modifier
    func shimmer(isLoading: Bool = true) -> some View {
        self.overlay(
            Group {
                if isLoading {
                    ShimmerView()
                        .cornerRadius(AppTheme.radiusSmall)
                }
            }
        )
    }
}

// MARK: - Preview
struct UIComponents_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(spacing: AppTheme.spacing20) {
                EnhancedCard {
                    Text("Enhanced Card")
                        .font(AppTheme.headline)
                }

                StatCard(title: "Matches", value: "42", icon: "heart.fill", color: .red)

                ProfileImageView(imageUrl: nil, size: 80)

                EnhancedTextField(title: "Email", text: .constant(""), placeholder: "Enter your email")

                EmptyStateView(
                    icon: "heart.slash",
                    title: "No matches yet",
                    subtitle: "Keep swiping to find your perfect roommate!",
                    actionTitle: "Start Swiping",
                    action: {}
                )
            }
            .padding()
        }
        .background(AppTheme.backgroundGradient)
    }
}

// MARK: - Modern Toggle Style
struct ModernToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack {
            configuration.label

            Spacer()

            ZStack {
                // Background track
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        configuration.isOn ?
                        LinearGradient(
                            colors: [AppTheme.primaryColor, AppTheme.accentColor],
                            startPoint: .leading,
                            endPoint: .trailing
                        ) :
                        LinearGradient(
                            colors: [AppTheme.surfaceSecondary],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: 50, height: 30)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                configuration.isOn ?
                                Color.white.opacity(0.3) :
                                AppTheme.textTertiary.opacity(0.3),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: configuration.isOn ?
                        AppTheme.primaryColor.opacity(0.3) :
                        .black.opacity(0.1),
                        radius: configuration.isOn ? 8 : 4,
                        x: 0,
                        y: configuration.isOn ? 4 : 2
                    )

                // Toggle thumb
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white,
                                Color.white.opacity(0.95)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 24, height: 24)
                    .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.8), lineWidth: 1)
                    )
                    .offset(x: configuration.isOn ? 10 : -10)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: configuration.isOn)
            }
            .onTapGesture {
                configuration.isOn.toggle()
                HapticFeedbackManager.shared.generateImpact(style: .light)
            }
        }
    }
}
