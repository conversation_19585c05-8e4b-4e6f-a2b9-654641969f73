import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift
import FirebaseAuth
import Combine

struct MatchItem: Identifiable, Codable {
    let id: String
    let participants: [String]
    let createdAt: Date
    let hasConversationStarted: Bool?
    let isNewMatch: Bool?
    let chatID: String?

    // Custom initializer for backward compatibility
    init(id: String, participants: [String], createdAt: Date, hasConversationStarted: Bool? = nil, isNewMatch: Bool? = nil, chatID: String? = nil) {
        self.id = id
        self.participants = participants
        self.createdAt = createdAt
        self.hasConversationStarted = hasConversationStarted
        self.isNewMatch = isNewMatch
        self.chatID = chatID
    }
}

class MatchesDashboardViewModel: ObservableObject {
    @Published var matches: [MatchItem] = []
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false
    @Published var profilesCache: [String: UserModel] = [:]

    private var cancellables = Set<AnyCancellable>()
    private let db = Firestore.firestore()
    private var matchesListener: ListenerRegistration?
    private let profileLoader = ProfileLoaderService.shared
    private var refreshTimer: Timer?

    var currentUserID: String? {
        Auth.auth().currentUser?.uid
    }

    // MARK: - Real-time Match Loading with Profile Preloading

    func startRealtimeMatchLoading() {
        guard let uid = currentUserID else {
            self.errorMessage = "User not authenticated."
            return
        }

        // Remove existing listener
        stopRealtimeMatchLoading()

        isLoading = true
        print("🔄 MatchesDashboardViewModel: Starting real-time match loading for user: \(uid)")

        // Set up real-time listener for immediate updates
        matchesListener = db.collection("matches")
            .whereField("participants", arrayContains: uid)
            .addSnapshotListener { [weak self] snapshot, error in
                guard let self = self else { return }

                DispatchQueue.main.async {
                    self.isLoading = false
                }

                if let error = error {
                    print("❌ MatchesDashboardViewModel: Real-time listener error: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        self.errorMessage = error.localizedDescription
                    }
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ MatchesDashboardViewModel: No snapshot received")
                    return
                }

                print("📊 MatchesDashboardViewModel: Real-time update - \(snapshot.documents.count) match documents")

                let matchItems = snapshot.documents.compactMap { doc -> MatchItem? in
                    let data = doc.data()
                    guard let participants = data["participants"] as? [String],
                          let timestamp = (data["createdAt"] as? Timestamp)?.dateValue() else {
                        print("❌ MatchesDashboardViewModel: Missing required fields in match: \(doc.documentID)")
                        return nil
                    }

                    // Validate match integrity
                    guard participants.count == 2,
                          participants.contains(uid),
                          !participants.isEmpty else {
                        print("❌ MatchesDashboardViewModel: Invalid match participants in \(doc.documentID): \(participants)")
                        return nil
                    }

                    // Validate timestamp is reasonable (not in future, not too old)
                    let now = Date()
                    let oneYearAgo = Calendar.current.date(byAdding: .year, value: -1, to: now) ?? now
                    guard timestamp <= now && timestamp >= oneYearAgo else {
                        print("❌ MatchesDashboardViewModel: Invalid timestamp in match \(doc.documentID): \(timestamp)")
                        return nil
                    }

                    // Extract optional fields for enhanced match tracking
                    let hasConversationStarted = data["hasConversationStarted"] as? Bool
                    let isNewMatch = data["isNewMatch"] as? Bool
                    let chatID = data["chatID"] as? String

                    print("✅ MatchesDashboardViewModel: Validated match \(doc.documentID) with participants: \(participants)")

                    return MatchItem(
                        id: doc.documentID,
                        participants: participants,
                        createdAt: timestamp,
                        hasConversationStarted: hasConversationStarted,
                        isNewMatch: isNewMatch,
                        chatID: chatID
                    )
                }

                // Sort client-side by creation date (most recent first)
                let sortedMatches = matchItems.sorted { $0.createdAt > $1.createdAt }

                DispatchQueue.main.async {
                    self.matches = sortedMatches

                    // Enhanced logging for debugging
                    print("✅ MatchesDashboardViewModel: Real-time update - \(sortedMatches.count) matches loaded")
                    if sortedMatches.isEmpty {
                        print("📊 MatchesDashboardViewModel: No matches found")
                    } else {
                        print("📊 MatchesDashboardViewModel: Match details:")
                        for (index, match) in sortedMatches.enumerated() {
                            print("   \(index + 1). ID: \(match.id), Participants: \(match.participants), Created: \(match.createdAt)")
                        }
                    }

                    // Preload profiles for all matches
                    self.preloadMatchProfiles(for: sortedMatches)
                }
            }
    }

    func stopRealtimeMatchLoading() {
        matchesListener?.remove()
        matchesListener = nil
        print("🛑 MatchesDashboardViewModel: Stopped real-time match loading")
    }

    // MARK: - Profile Preloading for Performance

    private func preloadMatchProfiles(for matches: [MatchItem]) {
        guard let currentUID = currentUserID else { return }

        print("🚀 MatchesDashboardViewModel: Preloading profiles for \(matches.count) matches")

        // Get all candidate IDs that need profile loading
        let candidateIDs = matches.compactMap { match in
            match.participants.first { $0 != currentUID }
        }

        // Batch load profiles in background
        Task {
            await withTaskGroup(of: Void.self) { group in
                for candidateID in candidateIDs {
                    group.addTask { [weak self] in
                        await self?.loadProfileForCache(candidateID: candidateID)
                    }
                }
            }

            DispatchQueue.main.async {
                print("✅ MatchesDashboardViewModel: Completed preloading \(candidateIDs.count) profiles")
            }
        }
    }

    private func loadProfileForCache(candidateID: String) async {
        // Check if already cached
        if profilesCache[candidateID] != nil {
            return
        }

        do {
            let snapshot = try await db.collection("users").document(candidateID).getDocument()
            guard snapshot.exists else {
                print("❌ MatchesDashboardViewModel: Profile not found for \(candidateID)")
                return
            }

            let profile = try snapshot.data(as: UserModel.self)

            DispatchQueue.main.async {
                self.profilesCache[candidateID] = profile
                print("✅ MatchesDashboardViewModel: Cached profile for \(profile.firstName ?? "Unknown")")
            }
        } catch {
            print("❌ MatchesDashboardViewModel: Error loading profile for \(candidateID): \(error.localizedDescription)")
        }
    }

    // MARK: - Legacy Support (Deprecated)

    @available(*, deprecated, message: "Use startRealtimeMatchLoading() instead")
    func loadMatches() {
        startRealtimeMatchLoading()
    }

    // MARK: - Helper Methods for Enhanced Match Filtering

    /// Get matches that haven't started a conversation yet
    var newMatches: [MatchItem] {
        matches.filter { match in
            // Consider a match "new" if:
            // 1. hasConversationStarted is false or nil
            // 2. Created within the last 7 days
            let hasStartedConversation = match.hasConversationStarted ?? false
            let isRecent = Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.contains(match.createdAt) ?? false

            return !hasStartedConversation && isRecent
        }
    }

    /// Get matches that have active conversations
    var activeMatches: [MatchItem] {
        matches.filter { match in
            match.hasConversationStarted == true && match.chatID != nil
        }
    }

    /// Get all matches sorted by most recent
    var sortedMatches: [MatchItem] {
        matches.sorted { $0.createdAt > $1.createdAt }
    }

    /// Force refresh matches data
    func refreshMatches() {
        print("🔄 MatchesDashboardViewModel: Force refreshing matches...")
        loadMatches()
    }

    // MARK: - Automatic Refresh Management (DISABLED FOR BETTER UX)

    func startAutoRefresh() {
        // DISABLED: Auto-refresh was causing constant UI updates and poor UX
        // Users can manually refresh with pull-to-refresh instead
        print("🔄 MatchesDashboardViewModel: Auto-refresh disabled - use manual refresh instead")
    }

    func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
        print("🛑 MatchesDashboardViewModel: Auto-refresh stopped")
    }

    func manualRefresh() {
        print("🔄 MatchesDashboardViewModel: Manual refresh triggered")
        loadMatches()
    }

    /// Force refresh when messages tab is opened - always gets fresh data
    func forceRefreshOnTabOpen() {
        print("🔄 MatchesDashboardViewModel: Force refresh on tab open - ensuring fresh data")

        // Clear current data to show loading state
        DispatchQueue.main.async {
            self.matches = []
            self.errorMessage = nil
            self.isLoading = true
        }

        // Force fresh data load
        loadMatches()
    }

    /// Get the other participant's ID for a given match
    func getOtherParticipantID(for match: MatchItem) -> String? {
        guard let uid = currentUserID else { return nil }
        return match.participants.first { $0 != uid }
    }

    /// Ensure chat exists for a match and return chat ID
    func ensureChatExists(for match: MatchItem) async -> String? {
        // If chat ID already exists, return it
        if let existingChatID = match.chatID {
            return existingChatID
        }

        // Create new chat
        guard let otherUserID = getOtherParticipantID(for: match),
              let currentUID = currentUserID else {
            print("❌ MatchesDashboardViewModel: Cannot create chat - missing user IDs")
            return nil
        }

        do {
            let chatID = "\(currentUID)_\(otherUserID)"

            // Update match with chat ID
            try await db.collection("matches").document(match.id).updateData([
                "chatID": chatID,
                "hasConversationStarted": true
            ])

            print("✅ MatchesDashboardViewModel: Created chat \(chatID) for match \(match.id)")
            return chatID

        } catch {
            print("❌ MatchesDashboardViewModel: Error creating chat: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - Lifecycle Management

    deinit {
        stopAutoRefresh()
        cancellables.removeAll()
    }
}
