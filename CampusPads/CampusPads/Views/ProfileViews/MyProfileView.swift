import SwiftUI
import PhotosUI
import FirebaseAuth
import MapKit
import FirebaseFirestore
import Combine

// MARK: — Section styling modifier
struct SectionContainer: ViewModifier {
    func body(content: Content) -> some View {
        content
            .padding()
            .background(AppTheme.cardBackground.opacity(0.8))
            .cornerRadius(15)
            .shadow(radius: 5)
    }
}
extension View {
    /// Apply the standard “card” look
    func sectionStyle() -> some View {
        modifier(SectionContainer())
    }
}

// MARK: - Missing Enum Definitions
enum PrimaryHousingPreference: String, CaseIterable, Identifiable {
    case lookingToFindTogether = "Looking to Find Together"
    case lookingForLease = "Looking for Lease"
    case lookingForRoommate = "Looking for Roommate"
    var id: String { self.rawValue }
}

enum PropertyMediaType: String, CaseIterable, Identifiable {
    case propertyImage = "Property Image"
    case floorplan = "Floorplan"
    case document = "Document"
    var id: String { self.rawValue }
}

// MARK: - Quiz Data Structures & Other Quiz-Related Code
struct QuizQuestion: Identifiable {
    let id = UUID()
    let question: String
    let options: [String]
}

let goingOutQuizQuestions: [QuizQuestion] = [
    QuizQuestion(question: "You can find me...", options: ["Dancing 💃", "Socializing 🗣️"]),
    QuizQuestion(question: "I like to...", options: ["Dress Up 👗", "Dress Down 👕"]),
    QuizQuestion(question: "I tend to arrive...", options: ["Early ⏰", "Fashionably Late 🕒"]),
    QuizQuestion(question: "My exit strategy looks like...", options: ["Say Bye First 👋", "Disappear 🕶️"])
]

let weekendsQuizQuestions: [QuizQuestion] = [
    QuizQuestion(question: "Weekends are for...", options: ["Recharging 😴", "Socializing 🥳"]),
    QuizQuestion(question: "Saturday night looks like...", options: ["Cozy nights in 🏡", "Fun nights out 🎊"]),
    QuizQuestion(question: "A typical Sunday looks like...", options: ["Self care 💆", "Sunday fun day 🎈"])
]

let myPhoneQuizQuestions: [QuizQuestion] = [
    QuizQuestion(question: "I'm the kind of person who...", options: ["Replies quickly ⚡", "Forgets to reply 💤"]),
    QuizQuestion(question: "I prefer receiving...", options: ["Text messages 📱", "Phone calls 📞"]),
    QuizQuestion(question: "My phone is always...", options: ["Fully charged 🔋", "Low on battery 🪫"])
]

// MARK: - MyProfileView
struct MyProfileView: View {
    @StateObject private var viewModel = ProfileViewModel.shared

    // MARK: - Mode Selection
    @State private var isPreviewMode = false

    // MARK: - Photo Picker State (for profile images)
    @State private var showingPhotoPicker = false
    @State private var newProfileImage: UIImage? = nil
    @State private var tappedImageIndex: Int? = nil
    @State private var isPickerActive = false

    // MARK: - Property Media Picker State (for property media uploads)
    @State private var showingPropertyMediaPicker = false
    @State private var newPropertyMediaImage: UIImage? = nil
    @State private var tappedPropertyMediaIndex: Int? = nil



    // MARK: - Housing Tiered State
    @State private var primaryHousingPreference: PrimaryHousingPreference? = nil
    @State private var secondaryHousingType: String = ""

    // MARK: - Profile Fields
    @State private var aboutMe = ""
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var dateOfBirth = ""
    @State private var gender = "Other"
    @State private var selectedHeight = ""

    @State private var selectedGradeLevel: GradeLevel = .freshman
    @State private var major = ""
    @State private var collegeName = ""
    @State private var collegeSearchQuery: String = ""
    @State private var filteredColleges: [String] = []
    @State private var budgetMin: Double = 0
    @State private var budgetMax: Double = 5000
    @State private var cleanliness = 3
    @State private var sleepSchedule = "Flexible"
    @State private var smoker = false
    @State private var petFriendly = false
    @State private var interestsText = ""

    // MARK: - Dropdown Selection State
    @State private var selectedInterests: [String] = []



    // New state variables for roommate count inputs
    @State private var roommateCountNeeded: Int = 0
    @State private var roommateCountExisting: Int = 0

    // MARK: - Property Details & Media State
    @State private var propertyDetails: String = ""
    // Use only one unified array for property/floorplan images.
    @State private var propertyImageUrls: [String] = []
    // NEW: State for property address entry (only used for roommate mode)
    @State private var propertyAddress: String = ""
    @State private var propertyCoordinate: CLLocationCoordinate2D? = nil
    @StateObject private var addressLocationService = AddressLocationService()

    // MARK: - Amenities State (New Multi-Select)
    @State private var selectedAmenities: [String] = []
    private let propertyAmenitiesOptions = [
        "In-Unit Laundry", "On-Site Laundry", "Air Conditioning", "Heating",
        "Furnished", "Unfurnished", "High-Speed Internet", "Utilities Included",
        "Pet Friendly", "Parking Available", "Garage Parking", "Balcony / Patio",
        "Private Bathroom", "Shared Bathroom", "Gym / Fitness Center", "Common Area / Lounge",
        "Pool Access", "Rooftop Access", "Bike Storage", "Dishwasher", "Microwave",
        "Elevator Access", "Wheelchair Accessible", "24/7 Security", "Gated Community",
        "Study Rooms", "Game Room", "Smoke-Free", "Quiet Hours Enforced"
    ]

    // MARK: - Lease & Pricing Details State (for Lease/Sublease users)
    @State private var leaseStartDate: Date = Date()
    @State private var leaseDurationText: String = ""
    @State private var rentMin: Double = 0
    @State private var rentMax: Double = 5000
    @State private var selectedSpecialLeaseConditions: [String] = []
    private let specialLeaseConditionsOptions: [String] = [
        "Start date negotiable", "Early move-in available", "Late move-out allowed",
        "Rent negotiable", "First month free", "Utilities included", "Partial months prorated",
        "Furnished room", "Unfurnished but furniture available for purchase", "Room includes mattress/desk/chair",
        "Must be approved by landlord", "Temporary sublease only", "Must sign roommate agreement",
        "Deposit required", "No deposit required", "Split rent with roommate", "Venmo/Zelle accepted",
        "Pet allowed (with conditions)", "No smoking", "Must be okay with overnight guests",
        "Cleanliness expectations", "Quiet hours after 10 PM", "No parties", "Gated entry",
        "Keycard access only", "Limited guest parking"
    ]

    // MARK: - Room Type State
    @State private var roomType: String = ""

    // MARK: - Lifestyle State
    @State private var selectedPets: [String] = []
    @State private var selectedDrinking: String = ""
    @State private var selectedSmoking: String = ""
    @State private var selectedCannabis: String = ""
    @State private var selectedWorkout: String = ""
    @State private var selectedDietaryPreferences: [String] = []
    @State private var selectedSocialMedia: String = ""
    @State private var selectedSleepingHabits: String = ""

    // MARK: - Quiz State (matching UserModel exactly)
    @State private var goingOutQuizAnswers: [String] = []
    @State private var weekendQuizAnswers: [String] = []
    @State private var phoneQuizAnswers: [String] = []


    // MARK: - Options
    private let petOptions = ["Dog","Cat","Reptile","Amphibian","Bird","Fish","Don't have but love others","Turtle","Hamster","Rabbit","Pet-free","Want a pet","Allergic to pets"]
    private let drinkingOptions = ["Not for me","Sober","Sober curious","On special occasions","Socially","Rarely"]
    private let smokingOptions = ["Non-smoker","Prefer non-smoking environment","Smoking friendly","Trying to quit"]
    private let cannabisOptions = ["420 friendly","Occasionally","Socially","Never"]
    private let workoutOptions = ["Everyday","Often","Sometimes","Never"]
    private let dietaryOptions = ["Vegan","Vegetarian","Pescatarian","Kosher","Halal","Carnivore","Omnivore","Other"]
    private let socialMediaOptions = ["Influencer status","Socially active","Off the grid","Passive scroller"]
    private let sleepingHabitsOptions = ["Early bird","Night owl","In a spectrum"]

    // New desired lease housing type options
    private let leaseTypeForLease = ["Dorm","Apartment","House"]
    private let leaseTypeForRoommate = ["Dorm","Apartment","House","Subleasing"]

    // MARK: - Height Options
    private let heightOptions: [String] = {
        var result: [String] = []
        for ft in 3...8 {
            for inch in 0...11 {
                if ft == 8 && inch > 0 { break }
                result.append("\(ft)'\(inch)\"")
            }
        }
        return result
    }()

    // MARK: - College Search
    @State private var validColleges: [String] = []
    let sleepScheduleOptions = ["Early Bird","Night Owl","Flexible"]

    // MARK: - Cleanliness Descriptions
    private let cleanlinessDescriptions: [Int:String] = [
        1:"Very Messy",2:"Messy",3:"Average",4:"Tidy",5:"Very Tidy"
    ]

    // MARK: - Auto-Save Debouncer
    @State private var autoSaveWorkItem: DispatchWorkItem?

    // MARK: - Animation States
    @State private var animateCards = false
    @State private var cardAnimationDelay: Double = 0

    // MARK: - Enumerations
    enum GradeLevel: String, CaseIterable, Identifiable {
        case freshman="Freshman", sophomore="Sophomore", junior="Junior",
             senior="Senior", graduate="Graduate", phd="PhD", other="Other"
        var id: String { self.rawValue }
    }
    enum LeaseDuration: String, CaseIterable, Identifiable {
        case current="Current Lease", shortTerm="Short Term (<6 months)",
             mediumTerm="6-12 months", longTerm="1 year+", futureNextYear="Future: Next Year",
             futureTwoPlus="Future: 2+ Years", notApplicable="Not Applicable"
        var id: String { self.rawValue }
    }

    // Updated: Lease & Pricing Details are always saved
    private var isLeaseOrSublease: Bool {
        primaryHousingPreference == .lookingForRoommate
    }

    // MARK: - Body
    var body: some View {
        mainContentView
            .onReceive(viewModel.$userProfile.compactMap { $0 }) { profile in
                populateLocalFields(from: profile)
            }
            .onAppear(perform: handleViewAppear)
            .sheet(isPresented: $showingPhotoPicker) {
                CustomImagePicker(image: $newProfileImage)
            }
            .sheet(isPresented: $showingPropertyMediaPicker) {
                CustomImagePicker(image: $newPropertyMediaImage)
            }
            .onChange(of: newProfileImage) { _, image in
                if image != nil { handlePhotoSelected() }
            }
            .onChange(of: newPropertyMediaImage) { _, image in
                if image != nil { handlePropertyMediaSelected() }
            }
            .onChange(of: selectedInterests) { _, _ in scheduleAutoSave() }
            .onChange(of: major) { _, _ in scheduleAutoSave() }
            .onDisappear {
                autoSaveWorkItem?.cancel()
            }
    }

    // MARK: - Main Content View
    private var mainContentView: some View {
        GeometryReader { geometry in
            ZStack {
                AppTheme.dynamicBackgroundGradient.ignoresSafeArea()

                if DeviceInfo.isIPad && DeviceInfo.isLandscape {
                    iPadLandscapeLayout(geometry: geometry)
                } else {
                    standardProfileLayout(geometry: geometry)
                }
            }
        }
    }

    // MARK: - Layout Variants

    private func standardProfileLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            modernHeaderSection

            if isPreviewMode {
                previewModeContent
            } else {
                editModeContent
            }
        }
    }

    private func iPadLandscapeLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            modernHeaderSection

            HStack(spacing: ResponsiveSpacing.xl) {
                // Left side: Main content
                VStack {
                    if isPreviewMode {
                        previewModeContent
                    } else {
                        editModeContent
                    }
                }
                .frame(maxWidth: LayoutHelpers.maxContentWidth)

                // Right side: Quick actions or preview (for larger iPads)
                if DeviceInfo.screenSize == .iPadPro12_9 {
                    VStack(spacing: ResponsiveSpacing.lg) {
                        if !isPreviewMode {
                            // Quick actions panel - simplified for now
                            VStack(spacing: ResponsiveSpacing.md) {
                                Button("Preview Profile") {
                                    isPreviewMode.toggle()
                                }
                                .buttonStyle(ResponsivePrimaryButtonStyle())
                            }
                            .responsiveCardStyle()
                        }
                        Spacer()
                    }
                    .frame(width: 250)
                    .responsivePadding()
                }
            }
            .responsiveHorizontalPadding()
        }
    }

    private var previewModeContent: some View {
        Group {
            if let profile = viewModel.userProfile {
                ProfilePreviewView(user: profile)
                    .transition(.opacity)
            } else {
                modernLoadingView
            }
        }
    }

    private var editModeContent: some View {
        ScrollView(.vertical, showsIndicators: false) {
            AdaptiveContainer {
                VStack(spacing: ResponsiveSpacing.lg) {
                    Spacer().frame(height: ResponsiveSpacing.sm)

                    animatedSection(profileCompletionSection, delay: 0.1)
                    animatedSection(mediaSection, delay: 0.2)
                    animatedSection(modernAboutMeSection, delay: 0.3)
                    animatedSection(modernBasicsSection, delay: 0.4)
                    animatedSection(modernAcademicsSection, delay: 0.5)
                    animatedSection(housingRelatedSections, delay: 0.6)
                    animatedSection(modernLifestyleSection, delay: 0.7)
                    animatedSection(quizSection, delay: 0.8)
                    animatedSection(modernInterestsSection, delay: 0.9)

                    Spacer().frame(height: ResponsiveSpacing.xxl * 2)
                }
            }
        }
    }

    private func animatedSection<Content: View>(_ content: Content, delay: Double) -> some View {
        content
            .opacity(animateCards ? 1.0 : 0.0)
            .offset(y: animateCards ? 0 : 30)
            .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(delay), value: animateCards)
    }

    private func handleViewAppear() {
        UniversityDataProvider.shared.loadUniversities { colleges in
            validColleges = colleges
            print("[MyProfileView] Loaded \(colleges.count) colleges.")
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            animateCards = true
        }
    }

    // MARK: - Profile Completion Helper Methods

    private func getMissingFields(for user: UserModel) -> [MissingField] {
        var missing: [MissingField] = []

        // Basic Info
        if user.firstName?.isEmpty != false {
            missing.append(MissingField(name: "First Name", points: 5, category: .basic))
        }
        if user.lastName?.isEmpty != false {
            missing.append(MissingField(name: "Last Name", points: 5, category: .basic))
        }
        if user.dateOfBirth == nil {
            missing.append(MissingField(name: "Date of Birth", points: 5, category: .basic))
        }
        if user.gender?.isEmpty != false {
            missing.append(MissingField(name: "Gender", points: 5, category: .basic))
        }
        if user.height?.isEmpty != false {
            missing.append(MissingField(name: "Height", points: 5, category: .basic))
        }

        // Academics
        if user.gradeLevel?.isEmpty != false {
            missing.append(MissingField(name: "Grade Level", points: 5, category: .academic))
        }
        if user.major?.isEmpty != false {
            missing.append(MissingField(name: "Major", points: 5, category: .academic))
        }
        if user.collegeName?.isEmpty != false {
            missing.append(MissingField(name: "College Name", points: 5, category: .academic))
        }

        // About Me
        if user.aboutMe?.isEmpty != false {
            missing.append(MissingField(name: "About Me", points: 10, category: .profile))
        }

        // Lifestyle
        if user.cleanliness == nil || user.cleanliness! <= 0 {
            missing.append(MissingField(name: "Cleanliness Level", points: 3, category: .lifestyle))
        }
        if user.sleepSchedule?.isEmpty != false {
            missing.append(MissingField(name: "Sleep Schedule", points: 3, category: .lifestyle))
        }
        if user.interests?.isEmpty != false {
            missing.append(MissingField(name: "Interests", points: 5, category: .lifestyle))
        }

        // Housing
        if user.desiredLeaseHousingType?.isEmpty != false {
            missing.append(MissingField(name: "Desired Housing Type", points: 5, category: .housing))
        }

        // Images
        let imgCount = user.profileImageUrls?.count ?? (user.profileImageUrl != nil ? 1 : 0)
        if imgCount == 0 {
            missing.append(MissingField(name: "Profile Photos", points: 10, category: .media))
        } else if imgCount < 5 {
            missing.append(MissingField(name: "More Photos (\(5-imgCount) more)", points: (5-imgCount)*2, category: .media))
        }

        // Quizzes
        if user.goingOutQuizAnswers?.isEmpty != false {
            missing.append(MissingField(name: "Going Out Quiz", points: 3, category: .quiz))
        }
        if user.weekendQuizAnswers?.isEmpty != false {
            missing.append(MissingField(name: "Weekend Quiz", points: 3, category: .quiz))
        }
        if user.phoneQuizAnswers?.isEmpty != false {
            missing.append(MissingField(name: "Phone Quiz", points: 3, category: .quiz))
        }

        // Room Type (not for Find-Together)
        if user.housingStatus != PrimaryHousingPreference.lookingToFindTogether.rawValue,
           user.roomType?.isEmpty != false {
            missing.append(MissingField(name: "Room Type", points: 5, category: .housing))
        }

        // Lifestyle bonus check
        let hasLifestyle = (user.pets?.isEmpty == false) ||
                          (user.drinking?.isEmpty == false) ||
                          (user.smoking?.isEmpty == false) ||
                          (user.cannabis?.isEmpty == false) ||
                          (user.workout?.isEmpty == false) ||
                          (user.dietaryPreferences?.isEmpty == false) ||
                          (user.socialMedia?.isEmpty == false) ||
                          (user.sleepingHabits?.isEmpty == false)

        if !hasLifestyle {
            missing.append(MissingField(name: "Lifestyle Preferences", points: 4, category: .lifestyle))
        }

        // Mode-specific fields
        if let status = user.housingStatus,
           let pref = PrimaryHousingPreference(rawValue: status) {

            switch pref {
            case .lookingForLease, .lookingToFindTogether:
                if user.budgetMin == nil || user.budgetMax == nil || user.budgetMax! <= user.budgetMin! {
                    missing.append(MissingField(name: "Budget Range", points: 5, category: .housing))
                }

            case .lookingForRoommate:
                if user.roommateCountNeeded == nil || user.roommateCountNeeded! <= 0 {
                    missing.append(MissingField(name: "Roommates Needed", points: 5, category: .housing))
                }
                if user.roommateCountExisting == nil || user.roommateCountExisting! <= 0 {
                    missing.append(MissingField(name: "Current Roommates", points: 5, category: .housing))
                }
                if user.propertyDetails?.isEmpty != false {
                    missing.append(MissingField(name: "Property Details", points: 5, category: .housing))
                }
                if user.propertyAddress?.isEmpty != false {
                    missing.append(MissingField(name: "Property Address", points: 5, category: .housing))
                }
                if user.propertyImageUrls?.isEmpty != false {
                    missing.append(MissingField(name: "Property Photos", points: 5, category: .media))
                }
                if user.leaseStartDate == nil {
                    missing.append(MissingField(name: "Lease Start Date", points: 5, category: .housing))
                }
                if user.leaseDuration?.isEmpty != false {
                    missing.append(MissingField(name: "Lease Duration", points: 5, category: .housing))
                }
                if user.monthlyRentMin == nil || user.monthlyRentMax == nil || user.monthlyRentMax! <= user.monthlyRentMin! {
                    missing.append(MissingField(name: "Monthly Rent Range", points: 5, category: .housing))
                }
                if user.specialLeaseConditions?.isEmpty != false {
                    missing.append(MissingField(name: "Special Lease Conditions", points: 3, category: .housing))
                }
            }
        }

        return missing.sorted { $0.points > $1.points }
    }

    private func missingFieldsView(missingFields: [MissingField]) -> some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.orange)

                Text("Missing for 100%")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Spacer()

                Text("+\(missingFields.reduce(0) { $0 + $1.points }) pts")
                    .font(.custom("AvenirNext-Bold", size: 14))
                    .foregroundColor(.orange)
                    .padding(.horizontal, AppTheme.spacing8)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(.orange.opacity(0.2))
                    )
            }

            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: DeviceInfo.isIPad ? 180 : 140), spacing: ResponsiveSpacing.sm)
            ], spacing: ResponsiveSpacing.sm) {
                ForEach(missingFields.prefix(6), id: \.name) { field in
                    missingFieldChip(field: field)
                }
            }

            if missingFields.count > 6 {
                Text("+ \(missingFields.count - 6) more fields")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.white.opacity(0.6))
                    .padding(.top, AppTheme.spacing4)
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private func missingFieldChip(field: MissingField) -> some View {
        HStack(spacing: AppTheme.spacing6) {
            Image(systemName: field.category.icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(field.category.color)

            VStack(alignment: .leading, spacing: 2) {
                Text(field.name)
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.white)
                    .lineLimit(1)

                Text("+\(field.points) pts")
                    .font(.custom("AvenirNext-Bold", size: 10))
                    .foregroundColor(field.category.color)
            }

            Spacer()
        }
        .padding(.vertical, AppTheme.spacing6)
        .padding(.horizontal, AppTheme.spacing8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.ultraThinMaterial.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(field.category.color.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var perfectProfileView: some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: "checkmark.seal.fill")
                .font(.system(size: 24, weight: .bold))
                .foregroundStyle(AppTheme.sexyGradient)

            VStack(alignment: .leading, spacing: 2) {
                Text("Perfect Profile!")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Text("Your profile is 100% complete")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppTheme.sexyGradient.opacity(0.2))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppTheme.sexyGradient.opacity(0.5), lineWidth: 1)
                )
        )
    }

    // MARK: - Modern Subviews

    private var modernHeaderSection: some View {
        VStack(spacing: AppTheme.spacing12) {
            HStack {
                // Back button for iPad navigation
                if DeviceInfo.isIPad {
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        // Dismiss the view
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let window = windowScene.windows.first {
                            window.rootViewController?.dismiss(animated: true)
                        }
                    }) {
                        HStack(spacing: ResponsiveSpacing.xs) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: ResponsiveTypography.fontSize(16), weight: .semibold))
                                .foregroundColor(.white)

                            Text("Back")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                                .foregroundColor(.white)
                        }
                        .padding(.horizontal, ResponsiveSpacing.sm)
                        .padding(.vertical, ResponsiveSpacing.xs)
                        .background(
                            RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                                .fill(.ultraThinMaterial.opacity(0.6))
                                .overlay(
                                    RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Text("My Profile")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(28)))
                    .foregroundColor(.white)

                Spacer()
            }
            .padding(.horizontal, AppTheme.spacing20)
            .padding(.top, AppTheme.spacing8)

            // Modern segmented picker
            HStack(spacing: AppTheme.spacing4) {
                ForEach([false, true], id: \.self) { mode in
                    let isSelected = isPreviewMode == mode
                    let title = mode ? "Preview" : "Edit"

                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        isPreviewMode = mode
                    }) {
                        Text(title)
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                            .padding(.vertical, AppTheme.spacing12)
                            .padding(.horizontal, AppTheme.spacing16)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .scaleEffect(isSelected ? 1.02 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                }
            }
            .padding(AppTheme.spacing4)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial.opacity(0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .padding(.horizontal, AppTheme.spacing20)
            .padding(.bottom, AppTheme.spacing8)
        }
    }

    private var modernLoadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .scaleEffect(1.5)
                .tint(.white)

            Text("Loading preview...")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.8))
        }
        .padding(AppTheme.spacing40)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
        .modernCardStyle()
    }

    private var profileCompletionSection: some View {
        Group {
            if let profile = viewModel.userProfile {
                VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                    // Section header
                    modernSectionHeader(
                        icon: "chart.pie.fill",
                        title: "Profile Completion",
                        subtitle: "Complete your profile to attract better matches"
                    )

                    let completion = ProfileCompletionCalculator.calculateCompletion(for: profile)
                    let missingFields = getMissingFields(for: profile)

                    VStack(spacing: AppTheme.spacing12) {
                        HStack {
                            Text("\(Int(completion))% Complete")
                                .font(.custom("AvenirNext-Bold", size: 18))
                                .foregroundColor(.white)

                            Spacer()

                            Text(completion >= 80 ? "Excellent!" : completion >= 60 ? "Good" : "Needs Work")
                                .font(.custom("AvenirNext-Medium", size: 14))
                                .foregroundColor(.white.opacity(0.8))
                                .padding(.horizontal, AppTheme.spacing12)
                                .padding(.vertical, AppTheme.spacing6)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(completion >= 80 ? Color.green : completion >= 60 ? Color.orange : Color.red)
                                )
                        }

                        ProgressView(value: completion, total: 100)
                            .tint(.white)
                            .scaleEffect(x: 1, y: 2, anchor: .center)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(.ultraThinMaterial.opacity(0.4))
                            )

                        // Missing fields section
                        if !missingFields.isEmpty && completion < 100 {
                            missingFieldsView(missingFields: missingFields)
                        } else if completion >= 100 {
                            perfectProfileView
                        }
                    }
                }
                .padding(AppTheme.spacing20)
                .background(modernCardBackground)
                .modernCardStyle()
            }
        }
    }

    private var mediaSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "photo.on.rectangle.angled",
                title: "Profile Photos",
                subtitle: "Add up to 9 photos to showcase yourself"
            )

            MediaGridView(
                imageUrls: viewModel.userProfile?.profileImageUrls ?? [],
                onTapAddOrEdit: { index in
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    tappedImageIndex = index
                    newProfileImage = nil
                    isPickerActive = true
                    showingPhotoPicker = true
                },
                onRemoveImage: { index in
                    HapticFeedbackManager.shared.generateImpact(style: .medium)
                    removeImage(at: index)
                }
            )
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernAboutMeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            HStack {
                modernSectionHeader(
                    icon: "person.text.rectangle.fill",
                    title: "About Me",
                    subtitle: "Tell others about yourself"
                )

                Spacer()

                Text("\(aboutMe.count)/500")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(aboutMe.count > 500 ? .red : .white.opacity(0.7))
                    .padding(.horizontal, AppTheme.spacing8)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.ultraThinMaterial.opacity(0.4))
                    )
            }

            TextEditor(text: $aboutMe)
                .scrollContentBackground(.hidden)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .frame(minHeight: 120, maxHeight: 180)
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
                .onChange(of: aboutMe) { _, newValue in
                    // OPTIMIZATION: Limit character count
                    if newValue.count > 500 {
                        aboutMe = String(newValue.prefix(500))
                    }
                    scheduleAutoSave()
                }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernBasicsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "person.circle.fill",
                title: "Basic Information",
                subtitle: "Your essential details"
            )

            VStack(spacing: AppTheme.spacing16) {
                modernLabeledField(label: "First Name", text: $firstName, icon: "person.fill")
                modernLabeledField(label: "Last Name", text: $lastName, icon: "person.fill")
                modernLabeledField(label: "Date of Birth (YYYY-MM-DD)", text: $dateOfBirth, icon: "calendar")

                // Gender picker
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Gender", icon: "figure.dress.line.vertical.figure")

                    HStack(spacing: AppTheme.spacing4) {
                        ForEach(["Male", "Female", "Other"], id: \.self) { genderOption in
                            let isSelected = gender == genderOption

                            Button(action: {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                gender = genderOption
                                scheduleAutoSave()
                            }) {
                                Text(genderOption)
                                    .font(.custom("AvenirNext-Medium", size: 14))
                                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))
                                    .padding(.vertical, AppTheme.spacing12)
                                    .padding(.horizontal, AppTheme.spacing8)
                                    .frame(maxWidth: .infinity)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(isSelected ? Color.clear : .white.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                            .scaleEffect(isSelected ? 1.02 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                        }
                    }
                }

                // Height picker
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Height", icon: "ruler")

                    Menu {
                        ForEach(heightOptions, id: \.self) { height in
                            Button(height) {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                selectedHeight = height
                                scheduleAutoSave()
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedHeight.isEmpty ? "Select Height" : selectedHeight)
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white)

                            Spacer()

                            Image(systemName: "chevron.down")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(AppTheme.spacing12)
                        .background(modernInputBackground)
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var basicsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("BASICS").font(.headline)
            Group {
                LabeledField(label: "First Name", text: $firstName)
                LabeledField(label: "Last Name", text: $lastName)
                LabeledField(label: "Date of Birth (YYYY-MM-DD)", text: $dateOfBirth)
                Picker("Gender", selection: $gender) {
                    Text("Male").tag("Male")
                    Text("Female").tag("Female")
                    Text("Other").tag("Other")
                }
                .pickerStyle(.segmented)
                .onChange(of: gender) { _, _ in scheduleAutoSave() }
                Picker("Height", selection: $selectedHeight) {
                    Text("Select Height").tag("")
                    ForEach(heightOptions, id: \.self) { h in Text(h).tag(h) }
                }
                .pickerStyle(.menu)
                .onChange(of: selectedHeight) { _, _ in scheduleAutoSave() }
            }
        }
        .sectionStyle()
    }

    private var academicsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("ACADEMICS").font(.headline)
            Picker("Grade Level", selection: $selectedGradeLevel) {
                ForEach(GradeLevel.allCases) { level in Text(level.rawValue).tag(level) }
            }
            .onChange(of: selectedGradeLevel) { _, _ in scheduleAutoSave() }
            LabeledField(label: "Major", text: $major)
            CollegeSearchField(
                selectedCollege: $collegeName,
                label: "College",
                placeholder: "Search for your college"
            )
            .onChange(of: collegeName) { _, _ in
                scheduleAutoSave()
            }
        }
        .sectionStyle()
    }

    private var housingSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("HOUSING").font(.headline)
            Picker("Primary Preference", selection: $primaryHousingPreference) {
                ForEach(PrimaryHousingPreference.allCases) { pref in
                    Text(pref.rawValue).tag(Optional(pref))
                }
            }
            .pickerStyle(.segmented)
            .onChange(of: primaryHousingPreference) { _, _ in
                scheduleAutoSave()
                secondaryHousingType = ""
            }

            if let primary = primaryHousingPreference {
                Picker("Housing Type", selection: $secondaryHousingType) {
                    Text("Select Type").tag("")
                    if primary == .lookingForLease || primary == .lookingToFindTogether {
                        ForEach(leaseTypeForLease, id: \.self) { type in Text(type).tag(type) }
                    } else {
                        ForEach(leaseTypeForRoommate, id: \.self) { type in Text(type).tag(type) }
                    }
                }
                .pickerStyle(.menu)
                .onChange(of: secondaryHousingType) { _, _ in scheduleAutoSave() }
            }

            if primaryHousingPreference == .lookingForLease
                || primaryHousingPreference == .lookingToFindTogether {
                VStack(alignment: .leading) {
                    Text("Budget: \(Int(budgetMin))–\(Int(budgetMax)) USD")
                        .font(.headline)
                    HStack {
                        Text("Min: \(Int(budgetMin))")
                        Slider(value: $budgetMin, in: 0...5000, step: 50)
                            .onChange(of: budgetMin) { _, _ in scheduleAutoSave() }
                    }
                    HStack {
                        Text("Max: \(Int(budgetMax))")
                        Slider(value: $budgetMax, in: 0...5000, step: 50)
                            .onChange(of: budgetMax) { _, _ in scheduleAutoSave() }
                    }
                }
            }
        }
        .sectionStyle()
    }

    private var propertyDetailsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("PROPERTY DETAILS").font(.headline)
                Spacer()
                Text("\(propertyDetails.count)/300")
                    .font(.caption)
                    .foregroundColor(propertyDetails.count > 300 ? .red : .secondary)
            }
            TextEditor(text: $propertyDetails)
                .scrollContentBackground(.hidden)
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.defaultCornerRadius)
                .frame(minHeight: 100, maxHeight: 120)
                .padding(8)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .onChange(of: propertyDetails) { _, newValue in
                    // OPTIMIZATION: Limit character count
                    if newValue.count > 300 {
                        propertyDetails = String(newValue.prefix(300))
                    }
                    scheduleAutoSave()
                }

            VStack(alignment: .leading, spacing: 4) {
                Text("Property Address").foregroundColor(.secondary)
                TextField("Enter property address", text: $propertyAddress)
                    .padding(8)
                    .background(AppTheme.cardBackground)
                    .cornerRadius(8)
                    .onChange(of: propertyAddress) { _, newValue in
                        addressLocationService.queryFragment = newValue
                        scheduleAutoSave()
                    }
                if !addressLocationService.suggestions.isEmpty {
                    ScrollView(.vertical) {
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(addressLocationService.suggestions, id: \.self) { suggestion in
                                Button(action: {
                                    let fullAddress = suggestion.title + " " + suggestion.subtitle
                                    propertyAddress = fullAddress
                                    addressLocationService.suggestions = []
                                    addressLocationService.getCoordinate(for: fullAddress) { coordinate in
                                        DispatchQueue.main.async {
                                            propertyCoordinate = coordinate
                                            scheduleAutoSave()
                                        }
                                    }
                                }) {
                                    Text(suggestion.title + " " + suggestion.subtitle)
                                        .foregroundColor(.primary)
                                        .padding(8)
                                }
                            }
                        }
                    }
                    .frame(maxHeight: 150)
                    .background(AppTheme.cardBackground.opacity(0.8))
                    .cornerRadius(8)
                }
            }

            Text("Property & Floorplan Images").font(.subheadline)
            SinglePropertyMediaGridView(
                imageUrls: $propertyImageUrls,
                onAddMedia: {
                    tappedPropertyMediaIndex = nil
                    newPropertyMediaImage = nil
                    showingPropertyMediaPicker = true
                },
                onRemoveMedia: { index in removePropertyMedia(at: index) }
            )
        }
        .sectionStyle()
    }

    private var roomTypeSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("ROOM TYPE").font(.headline)
            Picker("Room Type", selection: $roomType) {
                Text("Private Room").tag("Private Room")
                Text("Shared Room").tag("Shared Room")
                Text("Studio").tag("Studio")
            }
            .pickerStyle(.segmented)
            .onChange(of: roomType) { _, _ in scheduleAutoSave() }
        }
        .sectionStyle()
    }

    private var leasePricingSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("LEASE & PRICING DETAILS").font(.headline)
            DatePicker("Lease Start Date", selection: $leaseStartDate, displayedComponents: .date)
                .datePickerStyle(.compact)
            LabeledField(label: "Lease Duration", text: $leaseDurationText)
            VStack(alignment: .leading) {
                Text("Monthly Rent: \(Int(rentMin))–\(Int(rentMax)) USD")
                    .font(.headline)
                HStack {
                    Text("Min: \(Int(rentMin))")
                    Slider(value: $rentMin, in: 0...5000, step: 50)
                        .onChange(of: rentMin) { _, _ in scheduleAutoSave() }
                }
                HStack {
                    Text("Max: \(Int(rentMax))")
                    Slider(value: $rentMax, in: 0...5000, step: 50)
                        .onChange(of: rentMax) { _, _ in scheduleAutoSave() }
                }
            }
            .keyboardType(.decimalPad)
            Text("Special Lease Conditions").font(.subheadline)
            MultiSelectChipView(
                options: specialLeaseConditionsOptions,
                selectedItems: $selectedSpecialLeaseConditions,
                onSelectionChanged: { scheduleAutoSave() }
            )
        }
        .sectionStyle()
    }

    private var amenitiesSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("AMENITIES").font(.headline)
            MultiSelectChipView(
                options: propertyAmenitiesOptions,
                selectedItems: $selectedAmenities,
                onSelectionChanged: { scheduleAutoSave() }
            )
        }
        .sectionStyle()
    }

    private var lifestyleSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("LIFESTYLE").font(.headline).padding(.bottom, 4)
            VStack(alignment: .leading, spacing: 4) {
                Text("Cleanliness").font(AppTheme.bodyFont)
                Picker("Cleanliness", selection: $cleanliness) {
                    ForEach(1..<6) { number in
                        Text("\(number) - \(cleanlinessDescriptions[number] ?? "")").tag(number)
                    }
                }
                .pickerStyle(.menu)
                .onChange(of: cleanliness) { _, _ in scheduleAutoSave() }
            }
            Text("Do you have any pets?").font(AppTheme.bodyFont)
            MultiSelectChipView(
                options: petOptions,
                selectedItems: $selectedPets,
                onSelectionChanged: { scheduleAutoSave() }
            )
            .padding(.bottom, 8)
            Text("Social drinking preference?").font(AppTheme.bodyFont)
            Picker("Drinking", selection: $selectedDrinking) {
                ForEach(drinkingOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedDrinking) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
            Text("Smoking environment preference?").font(AppTheme.bodyFont)
            Picker("Smoking", selection: $selectedSmoking) {
                ForEach(smokingOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedSmoking) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
            Text("Cannabis environment preference?").font(AppTheme.bodyFont)
            Picker("Cannabis", selection: $selectedCannabis) {
                ForEach(cannabisOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedCannabis) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
            Text("Do you workout?").font(AppTheme.bodyFont)
            Picker("Workout", selection: $selectedWorkout) {
                ForEach(workoutOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedWorkout) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
            Text("What are your dietary preferences?").font(AppTheme.bodyFont)
            MultiSelectChipView(
                options: dietaryOptions,
                selectedItems: $selectedDietaryPreferences,
                onSelectionChanged: { scheduleAutoSave() }
            )
            .padding(.bottom, 8)
            Text("How active are you on social media?").font(AppTheme.bodyFont)
            Picker("Social Media", selection: $selectedSocialMedia) {
                ForEach(socialMediaOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedSocialMedia) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
            Text("What are your sleeping habits?").font(AppTheme.bodyFont)
            Picker("Sleeping Habits", selection: $selectedSleepingHabits) {
                ForEach(sleepingHabitsOptions, id: \.self) { Text($0).tag($0) }
            }
            .pickerStyle(.menu)
            .onChange(of: selectedSleepingHabits) { _, _ in scheduleAutoSave() }
            .padding(.bottom, 8)
        }
        .sectionStyle()
    }

    private var interestsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("INTERESTS").font(.headline)
                Spacer()
                Text("\(interestsText.count)/200")
                    .font(.caption)
                    .foregroundColor(interestsText.count > 200 ? .red : .secondary)
            }
            TextField("Interests (comma-separated)", text: $interestsText)
                .autocapitalization(.none)
                .padding(AppTheme.defaultPadding)
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.defaultCornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .onChange(of: interestsText) { _, newValue in
                    // OPTIMIZATION: Limit character count
                    if newValue.count > 200 {
                        interestsText = String(newValue.prefix(200))
                    }
                    scheduleAutoSave()
                }
        }
        .sectionStyle()
    }

    // MARK: - Missing Modern Sections

    private var modernAcademicsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "graduationcap.fill",
                title: "Academic Information",
                subtitle: "Your educational background"
            )

            VStack(spacing: AppTheme.spacing16) {
                // Grade Level picker
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Grade Level", icon: "book.fill")

                    Menu {
                        ForEach(GradeLevel.allCases) { level in
                            Button(level.rawValue) {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                selectedGradeLevel = level
                                scheduleAutoSave()
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedGradeLevel.rawValue)
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white)

                            Spacer()

                            Image(systemName: "chevron.down")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(AppTheme.spacing12)
                        .background(modernInputBackground)
                    }
                }

                // Major dropdown selection (dropdown-only, no manual input)
                MajorDropdownSelection(
                    selectedMajor: $major,
                    placeholder: "Select Major",
                    label: "Major"
                )

                // College search with forced selection
                CollegeSearchField(
                    selectedCollege: $collegeName,
                    label: "College",
                    placeholder: "Search for your college"
                )
                .onChange(of: collegeName) { _, _ in
                    scheduleAutoSave()
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var housingRelatedSections: some View {
        VStack(spacing: AppTheme.spacing20) {
            if let pref = primaryHousingPreference {
                if pref == .lookingForLease {
                    modernHousingSection
                    modernRoomTypeSection
                    modernAmenitiesSection
                } else if pref == .lookingForRoommate {
                    modernHousingSection
                    modernPropertyDetailsSection
                    modernRoomTypeSection
                    modernLeasePricingSection
                    modernAmenitiesSection
                } else if pref == .lookingToFindTogether {
                    modernHousingSection
                }
            } else {
                modernHousingSection
            }
        }
    }

    private var modernHousingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "house.fill",
                title: "Housing Preferences",
                subtitle: "Your living situation and preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                // Primary housing preference
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Primary Preference", icon: "house.circle.fill")

                    VStack(spacing: AppTheme.spacing8) {
                        ForEach(PrimaryHousingPreference.allCases) { pref in
                            let isSelected = primaryHousingPreference == pref

                            Button(action: {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                primaryHousingPreference = pref
                                secondaryHousingType = ""
                                scheduleAutoSave()
                            }) {
                                HStack {
                                    Text(pref.rawValue)
                                        .font(.custom("AvenirNext-Medium", size: 14))
                                        .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                                    Spacer()

                                    if isSelected {
                                        Image(systemName: "checkmark.circle.fill")
                                            .font(.system(size: 16, weight: .bold))
                                            .foregroundColor(.white)
                                    }
                                }
                                .padding(.vertical, AppTheme.spacing12)
                                .padding(.horizontal, AppTheme.spacing16)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                            .scaleEffect(isSelected ? 1.02 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                        }
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    // MARK: - Additional Modern Sections

    private var modernLifestyleSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "heart.circle.fill",
                title: "Lifestyle & Preferences",
                subtitle: "Your daily habits and preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                // Cleanliness picker
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Cleanliness Level", icon: "sparkles")

                    Menu {
                        ForEach(1..<6) { level in
                            Button("\(level) - \(cleanlinessDescriptions[level] ?? "")") {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                cleanliness = level
                                scheduleAutoSave()
                            }
                        }
                    } label: {
                        HStack {
                            Text("\(cleanliness) - \(cleanlinessDescriptions[cleanliness] ?? "")")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white)

                            Spacer()

                            Image(systemName: "chevron.down")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(AppTheme.spacing12)
                        .background(modernInputBackground)
                    }
                }

                // Lifestyle preferences with enhanced chips
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    modernFieldLabel("Pets", icon: "pawprint.fill")
                    EnhancedMultiSelectChipView(
                        options: petOptions,
                        selectedItems: $selectedPets,
                        onSelectionChanged: {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            scheduleAutoSave()
                        }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernInterestsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            HStack {
                modernSectionHeader(
                    icon: "star.circle.fill",
                    title: "Interests & Hobbies",
                    subtitle: "What you're passionate about"
                )

                Spacer()

                Text("\(interestsText.count)/200")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(interestsText.count > 200 ? .red : .white.opacity(0.7))
                    .padding(.horizontal, AppTheme.spacing8)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.ultraThinMaterial.opacity(0.4))
                    )
            }

            // Interests dropdown selection (dropdown-only, no manual input)
            InterestsDropdownSelection(
                selectedInterests: $selectedInterests,
                placeholder: "Select Interests",
                label: "Interests",
                maxSelections: 10
            )
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var quizSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "questionmark.circle.fill",
                title: "Personality Quizzes",
                subtitle: "Help others get to know you better"
            )

            CombinedQuizzesSection(
                goingOutQuizAnswers: $goingOutQuizAnswers,
                weekendQuizAnswers: $weekendQuizAnswers,
                phoneQuizAnswers: $phoneQuizAnswers,
                onQuizComplete: { scheduleAutoSave() }
            )
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    // MARK: - Complete Modern Sections

    private var modernRoomTypeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "bed.double.fill",
                title: "Room Type",
                subtitle: "What type of room are you offering?"
            )

            VStack(spacing: AppTheme.spacing8) {
                ForEach(["Private Room", "Shared Room", "Studio"], id: \.self) { type in
                    let isSelected = roomType == type

                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        roomType = type
                        scheduleAutoSave()
                    }) {
                        HStack {
                            Text(type)
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                            Spacer()

                            if isSelected {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 18, weight: .bold))
                                    .foregroundColor(.white)
                            }
                        }
                        .padding(.vertical, AppTheme.spacing12)
                        .padding(.horizontal, AppTheme.spacing16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .scaleEffect(isSelected ? 1.02 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernPropertyDetailsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            HStack {
                modernSectionHeader(
                    icon: "building.2.fill",
                    title: "Property Details",
                    subtitle: "Describe your property and location"
                )

                Spacer()

                Text("\(propertyDetails.count)/300")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(propertyDetails.count > 300 ? .red : .white.opacity(0.7))
                    .padding(.horizontal, AppTheme.spacing8)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.ultraThinMaterial.opacity(0.4))
                    )
            }

            VStack(spacing: AppTheme.spacing16) {
                // Property description
                TextEditor(text: $propertyDetails)
                    .scrollContentBackground(.hidden)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(minHeight: 120, maxHeight: 180)
                    .padding(AppTheme.spacing12)
                    .background(modernInputBackground)
                    .onChange(of: propertyDetails) { _, newValue in
                        if newValue.count > 300 {
                            propertyDetails = String(newValue.prefix(300))
                        }
                        scheduleAutoSave()
                    }

                // Property address
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Property Address", icon: "location.fill")

                    TextField("Enter property address", text: $propertyAddress)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(.white)
                        .padding(AppTheme.spacing12)
                        .background(modernInputBackground)
                        .onChange(of: propertyAddress) { _, newValue in
                            addressLocationService.queryFragment = newValue
                            scheduleAutoSave()
                        }

                    if !addressLocationService.suggestions.isEmpty {
                        ScrollView {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(addressLocationService.suggestions, id: \.self) { suggestion in
                                    Button(action: {
                                        let fullAddress = suggestion.title + " " + suggestion.subtitle
                                        propertyAddress = fullAddress
                                        addressLocationService.suggestions = []
                                        addressLocationService.getCoordinate(for: fullAddress) { coordinate in
                                            DispatchQueue.main.async {
                                                propertyCoordinate = coordinate
                                                scheduleAutoSave()
                                            }
                                        }
                                    }) {
                                        Text(suggestion.title + " " + suggestion.subtitle)
                                            .foregroundColor(.white)
                                            .padding(AppTheme.spacing8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .background(
                                                RoundedRectangle(cornerRadius: 8)
                                                    .fill(.ultraThinMaterial.opacity(0.4))
                                            )
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 150)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.ultraThinMaterial.opacity(0.8))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                }

                // Property media
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Property & Floorplan Images", icon: "photo.stack.fill")

                    SinglePropertyMediaGridView(
                        imageUrls: $propertyImageUrls,
                        onAddMedia: {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            tappedPropertyMediaIndex = nil
                            newPropertyMediaImage = nil
                            showingPropertyMediaPicker = true
                        },
                        onRemoveMedia: { index in
                            HapticFeedbackManager.shared.generateImpact(style: .medium)
                            removePropertyMedia(at: index)
                        }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernLeasePricingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "dollarsign.circle.fill",
                title: "Lease & Pricing Details",
                subtitle: "Set your lease terms and pricing"
            )

            VStack(spacing: AppTheme.spacing16) {
                // Lease start date
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    modernFieldLabel("Lease Start Date", icon: "calendar")

                    DatePicker("", selection: $leaseStartDate, displayedComponents: .date)
                        .datePickerStyle(.compact)
                        .accentColor(.white)
                        .colorScheme(.dark)
                        .padding(AppTheme.spacing8)
                        .background(modernInputBackground)
                        .onChange(of: leaseStartDate) { _, _ in scheduleAutoSave() }
                }

                // Lease duration
                modernLabeledField(label: "Lease Duration", text: $leaseDurationText, icon: "clock.fill")

                // Monthly rent range
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    modernFieldLabel("Monthly Rent Range", icon: "banknote.fill")

                    VStack(spacing: AppTheme.spacing8) {
                        HStack {
                            Text("$\(Int(rentMin)) - $\(Int(rentMax))")
                                .font(.custom("AvenirNext-Bold", size: 18))
                                .foregroundColor(.white)

                            Spacer()
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            HStack {
                                Text("Min: $\(Int(rentMin))")
                                    .font(.custom("AvenirNext-Medium", size: 14))
                                    .foregroundColor(.white.opacity(0.8))

                                Spacer()
                            }

                            Slider(value: $rentMin, in: 0...5000, step: 50)
                                .tint(.white)
                                .onChange(of: rentMin) { _, _ in scheduleAutoSave() }
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            HStack {
                                Text("Max: $\(Int(rentMax))")
                                    .font(.custom("AvenirNext-Medium", size: 14))
                                    .foregroundColor(.white.opacity(0.8))

                                Spacer()
                            }

                            Slider(value: $rentMax, in: 0...5000, step: 50)
                                .tint(.white)
                                .onChange(of: rentMax) { _, _ in scheduleAutoSave() }
                        }
                    }
                    .padding(AppTheme.spacing12)
                    .background(modernInputBackground)
                }

                // Special lease conditions
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    modernFieldLabel("Special Lease Conditions", icon: "doc.text.fill")

                    EnhancedMultiSelectChipView(
                        options: specialLeaseConditionsOptions,
                        selectedItems: $selectedSpecialLeaseConditions,
                        onSelectionChanged: {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            scheduleAutoSave()
                        }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var modernAmenitiesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            modernSectionHeader(
                icon: "house.and.flag.fill",
                title: "Property Amenities",
                subtitle: "What amenities does your property offer?"
            )

            EnhancedMultiSelectChipView(
                options: propertyAmenitiesOptions,
                selectedItems: $selectedAmenities,
                onSelectionChanged: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    scheduleAutoSave()
                }
            )
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    // MARK: - Modern Helper Methods

    private func modernSectionHeader(icon: String, title: String, subtitle: String) -> some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()
        }
    }

    private func modernFieldLabel(_ text: String, icon: String) -> some View {
        HStack(spacing: AppTheme.spacing8) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.8))

            Text(text)
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)
        }
    }

    private func modernLabeledField(label: String, text: Binding<String>, icon: String) -> some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            modernFieldLabel(label, icon: icon)

            TextField(label, text: text)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
                .onChange(of: text.wrappedValue) { _, _ in
                    scheduleAutoSave()
                }
        }
    }

    private var modernCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }

    private var modernInputBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial.opacity(0.6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }

    @ViewBuilder
    func LabeledField(label: String, text: Binding<String>) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(label).font(.subheadline).foregroundColor(.secondary)
            TextField(label, text: text)
                .padding(AppTheme.defaultPadding)
                .background(AppTheme.cardBackground)
                .cornerRadius(AppTheme.defaultCornerRadius)
                .onChange(of: text.wrappedValue) { _, _ in scheduleAutoSave() }
        }
    }

    struct ProfileCompletionView: View {
        let completion: Double
        var body: some View {
            VStack(alignment: .leading, spacing: 8) {
                Text("Profile Completion: \(Int(completion))%")
                    .font(AppTheme.bodyFont)
                ProgressView(value: completion, total: 100)
                    .accentColor(AppTheme.primaryColor)
                    .scaleEffect(x: 1, y: 2, anchor: .center)
            }
            .padding()
            .background(AppTheme.cardBackground)
            .cornerRadius(15)
            .shadow(radius: 5)
        }
    }

    struct MediaGridView: View {
        let imageUrls: [String]
        let onTapAddOrEdit: (Int) -> Void
        let onRemoveImage: (Int) -> Void

        // OPTIMIZATION: Use flexible columns with minimum size for better layout
        private let columns = Array(repeating: GridItem(.flexible(minimum: 100)), count: 3)

        var body: some View {
            LazyVGrid(columns: columns, spacing: 8) {
                ForEach(Array(0..<9), id: \.self) { index in
                    ZStack(alignment: .topTrailing) {
                        if index < imageUrls.count, let url = URL(string: imageUrls[index]) {
                            // OPTIMIZATION: Add cache busting for newly uploaded images
                            let cacheBustedURL = addCacheBuster(to: url)
                            RobustAsyncImage(url: cacheBustedURL)
                                .frame(width: 100, height: 100)
                                .cornerRadius(8)
                                .onAppear {
                                    print("✅ MediaGridView: Loading image at index \(index)")
                                    print("🔗 MediaGridView: Original URL: \(imageUrls[index])")
                                    print("🔗 MediaGridView: Cache-busted URL: \(cacheBustedURL.absoluteString)")
                                }
                            .onTapGesture { onTapAddOrEdit(index) }
                            Button(action: { onRemoveImage(index) }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 20, weight: .medium))
                                    .padding(4)
                            }
                            .background(Color.red.opacity(0.8))
                            .clipShape(Circle())
                            .offset(x: -6, y: 6)
                            .accessibilityLabel("Remove image")
                            .accessibilityHint("Double tap to remove this profile image")
                        } else {
                            ZStack {
                                Rectangle()
                                    .fill(AppTheme.cardBackground)
                                    .frame(width: 100, height: 100)
                                    .cornerRadius(8)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                                VStack(spacing: 4) {
                                    Image(systemName: "plus.circle")
                                        .font(.system(size: 24, weight: .light))
                                        .foregroundColor(AppTheme.primaryColor)
                                    Text("Add Photo")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .onTapGesture { onTapAddOrEdit(index) }
                            .accessibilityLabel("Add profile image")
                            .accessibilityHint("Double tap to select a new profile image")
                        }
                    }
                }
            }
            .padding(.horizontal, 8)
        }

        /// Add cache busting parameter to force image refresh
        private func addCacheBuster(to url: URL) -> URL {
            guard var components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
                return url
            }

            // Add timestamp as cache buster for newly uploaded images
            let timestamp = Int(Date().timeIntervalSince1970)
            let cacheBusterItem = URLQueryItem(name: "cb", value: "\(timestamp)")

            if components.queryItems == nil {
                components.queryItems = [cacheBusterItem]
            } else {
                // Remove existing cache buster if present
                components.queryItems = components.queryItems?.filter { $0.name != "cb" }
                components.queryItems?.append(cacheBusterItem)
            }

            return components.url ?? url
        }
    }

    struct SinglePropertyMediaGridView: View {
        @Binding var imageUrls: [String]
        let onAddMedia: () -> Void
        let onRemoveMedia: (Int) -> Void

        // OPTIMIZATION: Use flexible columns with minimum size for better layout
        private let columns = Array(repeating: GridItem(.flexible(minimum: 100)), count: 3)

        var body: some View {
            LazyVGrid(columns: columns, spacing: 8) {
                ForEach(Array(0..<9), id: \.self) { index in
                    ZStack(alignment: .topTrailing) {
                        if index < imageUrls.count, let url = URL(string: imageUrls[index]) {
                            // OPTIMIZATION: Add cache busting for newly uploaded property media
                            let cacheBustedURL = addCacheBuster(to: url)
                            RobustAsyncImage(url: cacheBustedURL)
                                .frame(width: 100, height: 100)
                                .cornerRadius(8)
                                .onAppear {
                                    print("✅ PropertyMediaGridView: Loading image at index \(index) with RobustAsyncImage")
                                }
                            .overlay(
                                Group {
                                    if index == 0 {
                                        Text("Floorplan")
                                            .font(.caption)
                                            .padding(4)
                                            .background(Color.black.opacity(0.7))
                                            .foregroundColor(.white)
                                            .cornerRadius(4)
                                            .padding(4)
                                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottomLeading)
                                    }
                                }
                            )
                            .onTapGesture { onAddMedia() }
                            Button(action: { onRemoveMedia(index) }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 20, weight: .medium))
                                    .padding(4)
                            }
                            .background(Color.red.opacity(0.8))
                            .clipShape(Circle())
                            .offset(x: -6, y: 6)
                            .accessibilityLabel("Remove property media")
                            .accessibilityHint("Double tap to remove this property image")
                        } else {
                            ZStack {
                                Rectangle()
                                    .fill(AppTheme.cardBackground)
                                    .frame(width: 100, height: 100)
                                    .cornerRadius(8)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                                if index == 0 {
                                    VStack(spacing: 4) {
                                        Image(systemName: "doc.text")
                                            .font(.system(size: 20, weight: .light))
                                            .foregroundColor(AppTheme.primaryColor)
                                        Text("Floorplan")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                } else {
                                    VStack(spacing: 4) {
                                        Image(systemName: "plus.circle")
                                            .font(.system(size: 20, weight: .light))
                                            .foregroundColor(AppTheme.primaryColor)
                                        Text("Add Media")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                            .onTapGesture { onAddMedia() }
                            .accessibilityLabel(index == 0 ? "Add floorplan" : "Add property media")
                            .accessibilityHint("Double tap to select property media")
                        }
                    }
                }
            }
            .padding(.horizontal, 8)
        }

        /// Add cache busting parameter to force image refresh
        private func addCacheBuster(to url: URL) -> URL {
            guard var components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
                return url
            }

            // Add timestamp as cache buster for newly uploaded images
            let timestamp = Int(Date().timeIntervalSince1970)
            let cacheBusterItem = URLQueryItem(name: "cb", value: "\(timestamp)")

            if components.queryItems == nil {
                components.queryItems = [cacheBusterItem]
            } else {
                // Remove existing cache buster if present
                components.queryItems = components.queryItems?.filter { $0.name != "cb" }
                components.queryItems?.append(cacheBusterItem)
            }

            return components.url ?? url
        }
    }

    private func handlePhotoSelected() {
        guard let newImg = newProfileImage,
              var updatedProfile = viewModel.userProfile,
              let index = tappedImageIndex else {
            print("[MyProfileView] Error: userProfile is nil while handling photo selection.")
            return
        }
        viewModel.uploadProfileImage(image: newImg) { result in
            switch result {
            case .success(let downloadURL):
                print("✅ MyProfileView: Profile image uploaded successfully: \(downloadURL)")
                var urls = updatedProfile.profileImageUrls ?? []
                if index < urls.count {
                    urls[index] = downloadURL
                } else {
                    urls.append(downloadURL)
                }
                updatedProfile.profileImageUrls = Array(urls.prefix(9))
                updatedProfile.profileImageUrl = updatedProfile.profileImageUrls?.first

                // OPTIMIZATION: Update profile and refresh UI immediately
                viewModel.updateUserProfile(updatedProfile: updatedProfile) { updateResult in
                    DispatchQueue.main.async {
                        switch updateResult {
                        case .success:
                            print("✅ MyProfileView: Profile updated successfully with new image")
                            // Force UI refresh by updating the viewModel's userProfile
                            viewModel.userProfile = updatedProfile
                        case .failure(let updateError):
                            print("❌ MyProfileView: Failed to update profile: \(updateError.localizedDescription)")
                        }
                    }
                }
            case .failure(let error):
                print("❌ MyProfileView: Error uploading profile image: \(error.localizedDescription)")
            }
            DispatchQueue.main.async {
                showingPhotoPicker = false
                isPickerActive = false
                newProfileImage = nil
            }
        }
    }

    private func handlePropertyMediaSelected() {
        guard let newImg = newPropertyMediaImage,
              var updatedProfile = viewModel.userProfile else {
            print("❌ MyProfileView: Error: userProfile is nil while handling property media selection.")
            return
        }

        print("📸 MyProfileView: Uploading property media...")
        let folder = "propertyMedia"
        viewModel.uploadPropertyMedia(image: newImg, folder: folder) { result in
            switch result {
            case .success(let downloadURL):
                print("✅ MyProfileView: Property media uploaded successfully: \(downloadURL)")
                var arr = updatedProfile.propertyImageUrls ?? []
                arr.append(downloadURL)
                updatedProfile.propertyImageUrls = arr

                // OPTIMIZATION: Update both local state and profile immediately
                DispatchQueue.main.async {
                    self.propertyImageUrls = arr
                }

                viewModel.updateUserProfile(updatedProfile: updatedProfile) { updateResult in
                    DispatchQueue.main.async {
                        switch updateResult {
                        case .success:
                            print("✅ MyProfileView: Property media profile updated successfully")
                            // Force UI refresh by updating the viewModel's userProfile
                            self.viewModel.userProfile = updatedProfile
                        case .failure(let updateError):
                            print("❌ MyProfileView: Failed to update profile with property media: \(updateError.localizedDescription)")
                        }
                    }
                }
            case .failure(let error):
                print("❌ MyProfileView: Error uploading property media: \(error.localizedDescription)")
            }
            DispatchQueue.main.async {
                showingPropertyMediaPicker = false
                newPropertyMediaImage = nil
            }
        }
    }

    private func removeImage(at index: Int) {
        guard var profile = viewModel.userProfile,
              var urls = profile.profileImageUrls, index < urls.count else { return }

        print("🗑️ MyProfileView: Removing image at index \(index)")
        urls.remove(at: index)
        profile.profileImageUrls = urls
        profile.profileImageUrl = urls.first

        // OPTIMIZATION: Update UI immediately and then sync to Firestore
        viewModel.updateUserProfile(updatedProfile: profile) { updateResult in
            DispatchQueue.main.async {
                switch updateResult {
                case .success:
                    print("✅ MyProfileView: Image removed successfully")
                    // Force UI refresh by updating the viewModel's userProfile
                    viewModel.userProfile = profile
                case .failure(let error):
                    print("❌ MyProfileView: Failed to remove image: \(error.localizedDescription)")
                }
            }
        }
    }

    private func removePropertyMedia(at index: Int) {
        guard var profile = viewModel.userProfile,
              var urls = profile.propertyImageUrls, index < urls.count else { return }

        print("🗑️ MyProfileView: Removing property media at index \(index)")
        urls.remove(at: index)
        profile.propertyImageUrls = urls

        // OPTIMIZATION: Update local state immediately
        propertyImageUrls = urls

        viewModel.updateUserProfile(updatedProfile: profile) { updateResult in
            DispatchQueue.main.async {
                switch updateResult {
                case .success:
                    print("✅ MyProfileView: Property media removed successfully")
                    // Force UI refresh by updating the viewModel's userProfile
                    self.viewModel.userProfile = profile
                case .failure(let error):
                    print("❌ MyProfileView: Failed to remove property media: \(error.localizedDescription)")
                    // Revert local state on failure
                    self.propertyImageUrls = profile.propertyImageUrls ?? []
                }
            }
        }
    }

    /// Add cache busting parameter to force image refresh
    private func addCacheBuster(to url: URL) -> URL {
        guard var components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
            return url
        }

        // Add timestamp as cache buster for newly uploaded images
        let timestamp = Int(Date().timeIntervalSince1970)
        let cacheBusterItem = URLQueryItem(name: "cb", value: "\(timestamp)")

        if components.queryItems == nil {
            components.queryItems = [cacheBusterItem]
        } else {
            // Remove existing cache buster if present
            components.queryItems = components.queryItems?.filter { $0.name != "cb" }
            components.queryItems?.append(cacheBusterItem)
        }

        return components.url ?? url
    }

    private func populateLocalFields(from profile: UserModel) {
        aboutMe = profile.aboutMe ?? ""
        firstName = profile.firstName ?? ""
        lastName = profile.lastName ?? ""
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        dateOfBirth = profile.dateOfBirth != nil ? dateFormatter.string(from: profile.dateOfBirth!) : ""
        gender = profile.gender ?? "Other"
        selectedHeight = profile.height ?? ""
        major = profile.major ?? ""
        collegeName = profile.collegeName ?? ""
        collegeSearchQuery = profile.collegeName ?? ""
        secondaryHousingType = profile.desiredLeaseHousingType ?? ""
        roommateCountNeeded = profile.roommateCountNeeded ?? 0
        roommateCountExisting = profile.roommateCountExisting ?? 0
        budgetMin = profile.budgetMin ?? 0
        budgetMax = profile.budgetMax ?? 5000
        cleanliness = profile.cleanliness ?? 3
        sleepSchedule = profile.sleepSchedule ?? "Flexible"
        smoker = profile.smoker ?? false
        petFriendly = profile.petFriendly ?? false
        interestsText = (profile.interests ?? []).joined(separator: ", ")
        selectedInterests = profile.interests ?? []
        selectedGradeLevel = GradeLevel(rawValue: profile.gradeLevel ?? "") ?? .freshman

        if let primary = profile.housingStatus,
           let pref = PrimaryHousingPreference(rawValue: primary) {
            primaryHousingPreference = pref
        } else {
            primaryHousingPreference = nil
        }
        secondaryHousingType = profile.desiredLeaseHousingType ?? ""
        propertyDetails = profile.propertyDetails ?? ""
        propertyImageUrls = profile.propertyImageUrls ?? []
        propertyAddress = profile.propertyAddress ?? ""
        if let loc = profile.location {
            propertyCoordinate = CLLocationCoordinate2D(latitude: loc.latitude, longitude: loc.longitude)
        } else {
            propertyCoordinate = nil
        }
        selectedAmenities = profile.amenities ?? []

        leaseStartDate = profile.leaseStartDate ?? Date()
        leaseDurationText = profile.leaseDuration ?? ""
        selectedSpecialLeaseConditions = profile.specialLeaseConditions ?? []
        roomType = profile.roomType ?? ""
        rentMin = profile.monthlyRentMin ?? 0
        rentMax = profile.monthlyRentMax ?? 5000
        selectedPets = profile.pets ?? []
        selectedDrinking = profile.drinking ?? ""
        selectedSmoking = profile.smoking ?? ""
        selectedCannabis = profile.cannabis ?? ""
        selectedWorkout = profile.workout ?? ""
        selectedDietaryPreferences = profile.dietaryPreferences ?? []
        selectedSocialMedia = profile.socialMedia ?? ""
        selectedSleepingHabits = profile.sleepingHabits ?? ""

        goingOutQuizAnswers = profile.goingOutQuizAnswers ?? []
        weekendQuizAnswers = profile.weekendQuizAnswers ?? []
        phoneQuizAnswers = profile.phoneQuizAnswers ?? []
    }

    private func scheduleAutoSave() {
        // OPTIMIZATION: Cancel previous auto-save to prevent conflicts
        autoSaveWorkItem?.cancel()

        // OPTIMIZATION: Longer debounce time to reduce frequent saves
        // Note: No weak reference needed for structs - they don't create retain cycles
        autoSaveWorkItem = DispatchWorkItem {
            self.autoSaveProfile()
        }

        // OPTIMIZATION: Safe execution with nil check
        if let workItem = autoSaveWorkItem {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0, execute: workItem)
        }
    }

    private func autoSaveProfile() {
        // OPTIMIZATION: Guard against multiple conditions that could cause issues
        guard !isPickerActive,
              var updatedProfile = viewModel.userProfile,
              autoSaveWorkItem?.isCancelled == false else {
            print("🔄 MyProfileView: Auto-save skipped - picker active or profile unavailable")
            return
        }

        print("💾 MyProfileView: Auto-saving profile...")

        // OPTIMIZATION: Use selectedInterests array directly
        let interestsArray = selectedInterests.filter { !$0.isEmpty }

        // Update all profile fields
        updatedProfile.aboutMe = aboutMe.isEmpty ? nil : aboutMe
        updatedProfile.firstName = firstName.isEmpty ? nil : firstName
        updatedProfile.lastName = lastName.isEmpty ? nil : lastName
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        updatedProfile.dateOfBirth = dateOfBirth.isEmpty ? nil : dateFormatter.date(from: dateOfBirth)
        updatedProfile.gender = gender
        updatedProfile.height = selectedHeight.isEmpty ? nil : selectedHeight
        updatedProfile.gradeLevel = selectedGradeLevel.rawValue
        updatedProfile.major = major.isEmpty ? nil : major
        updatedProfile.collegeName = collegeName.isEmpty ? nil : collegeName
        updatedProfile.budgetMin = budgetMin
        updatedProfile.budgetMax = budgetMax
        updatedProfile.cleanliness = cleanliness
        updatedProfile.sleepSchedule = sleepSchedule
        updatedProfile.smoker = smoker
        updatedProfile.petFriendly = petFriendly
        updatedProfile.interests = interestsArray.isEmpty ? nil : interestsArray

        updatedProfile.housingStatus = primaryHousingPreference?.rawValue
        updatedProfile.desiredLeaseHousingType = secondaryHousingType.isEmpty ? nil : secondaryHousingType

        updatedProfile.roommateCountNeeded = roommateCountNeeded
        updatedProfile.roommateCountExisting = roommateCountExisting

        updatedProfile.propertyDetails = propertyDetails.isEmpty ? nil : propertyDetails
        updatedProfile.propertyImageUrls = propertyImageUrls.isEmpty ? nil : propertyImageUrls
        updatedProfile.propertyAddress = propertyAddress.isEmpty ? nil : propertyAddress
        updatedProfile.location = propertyCoordinate != nil
            ? GeoPoint(latitude: propertyCoordinate!.latitude, longitude: propertyCoordinate!.longitude)
            : nil

        updatedProfile.amenities = selectedAmenities.isEmpty ? nil : selectedAmenities

        updatedProfile.leaseStartDate = leaseStartDate
        updatedProfile.leaseDuration = leaseDurationText.isEmpty ? nil : leaseDurationText
        updatedProfile.monthlyRentMin = rentMin
        updatedProfile.monthlyRentMax = rentMax
        updatedProfile.specialLeaseConditions = selectedSpecialLeaseConditions.isEmpty ? nil : selectedSpecialLeaseConditions
        updatedProfile.roomType = roomType.isEmpty ? nil : roomType

        updatedProfile.pets = selectedPets.isEmpty ? nil : selectedPets
        updatedProfile.drinking = selectedDrinking.isEmpty ? nil : selectedDrinking
        updatedProfile.smoking = selectedSmoking.isEmpty ? nil : selectedSmoking
        updatedProfile.cannabis = selectedCannabis.isEmpty ? nil : selectedCannabis
        updatedProfile.workout = selectedWorkout.isEmpty ? nil : selectedWorkout
        updatedProfile.dietaryPreferences = selectedDietaryPreferences.isEmpty ? nil : selectedDietaryPreferences
        updatedProfile.socialMedia = selectedSocialMedia.isEmpty ? nil : selectedSocialMedia
        updatedProfile.sleepingHabits = selectedSleepingHabits.isEmpty ? nil : selectedSleepingHabits

        updatedProfile.goingOutQuizAnswers = goingOutQuizAnswers.isEmpty ? nil : goingOutQuizAnswers
        updatedProfile.weekendQuizAnswers = weekendQuizAnswers.isEmpty ? nil : weekendQuizAnswers
        updatedProfile.phoneQuizAnswers = phoneQuizAnswers.isEmpty ? nil : phoneQuizAnswers

        // OPTIMIZATION: Preserve original timestamps and improve error handling
        let originalCreatedAt = updatedProfile.createdAt
        viewModel.updateUserProfile(updatedProfile: updatedProfile) { result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    print("✅ MyProfileView: Auto-save completed successfully")
                    // Preserve original creation timestamp
                    updatedProfile.createdAt = originalCreatedAt
                case .failure(let error):
                    print("❌ MyProfileView: Auto-save failed: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - MultiSelectChipView (For Pets, Dietary Preferences, and Amenities)
struct MultiSelectChipView: View {
    let options: [String]
    @Binding var selectedItems: [String]
    var onSelectionChanged: () -> Void = {}
    var maxSelection: Int? = nil

    var body: some View {
        FlowLayout(options,
                   selectedItems: $selectedItems,
                   onSelectionChanged: onSelectionChanged,
                   maxSelection: maxSelection)
            .padding(6)
            .background(Color.clear)
    }
}

// MARK: - FlowLayout for Multi-Select Chips
struct FlowLayout: View {
    let data: [String]
    @Binding var selectedItems: [String]
    var onSelectionChanged: () -> Void
    var maxSelection: Int? = nil

    @State private var totalHeight: CGFloat = .zero

    init(_ data: [String],
         selectedItems: Binding<[String]>,
         onSelectionChanged: @escaping () -> Void,
         maxSelection: Int? = nil) {
        self.data = data
        self._selectedItems = selectedItems
        self.onSelectionChanged = onSelectionChanged
        self.maxSelection = maxSelection
    }

    var body: some View {
        GeometryReader { geo in content(in: geo) }
        .frame(minHeight: totalHeight)
    }

    private func content(in g: GeometryProxy) -> some View {
        var widthAccumulator: CGFloat = 0
        var rows: [RowItem] = []
        var currentRow = RowItem()

        for text in data {
            let chipSize = chipSize(for: text)
            if widthAccumulator + chipSize.width > g.size.width {
                rows.append(currentRow)
                currentRow = RowItem()
                widthAccumulator = 0
            }
            currentRow.items.append(text)
            widthAccumulator += chipSize.width
        }
        if !currentRow.items.isEmpty { rows.append(currentRow) }
        DispatchQueue.main.async { totalHeight = CGFloat(rows.count) * 40 }

        return VStack(alignment: .leading, spacing: 8) {
            ForEach(rows.indices, id: \.self) { rowIndex in
                HStack(spacing: 8) {
                    ForEach(rows[rowIndex].items, id: \.self) { item in chipView(item) }
                }
            }
        }
    }

    private func chipSize(for text: String) -> CGSize {
        let padding: CGFloat = 44
        let font = UIFont.systemFont(ofSize: 14)
        let attributes = [NSAttributedString.Key.font: font]
        let textSize = (text as NSString).size(withAttributes: attributes)
        return CGSize(width: textSize.width + padding, height: 36)
    }

    private func chipView(_ item: String) -> some View {
        let isSelected = selectedItems.contains(item)
        let isDisabled = !isSelected && (maxSelection != nil && selectedItems.count >= maxSelection!)
        return Text(item)
            .font(.system(size: 14))
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.vertical, 6)
            .padding(.horizontal, 12)
            .background(isSelected ? AppTheme.accentColor : AppTheme.cardBackground)
            .cornerRadius(16)
            .opacity(isDisabled ? 0.5 : 1.0)
            .onTapGesture {
                if isDisabled { return }
                if isSelected {
                    selectedItems.removeAll { $0 == item }
                } else {
                    selectedItems.append(item)
                }
                onSelectionChanged()
            }
    }

    struct RowItem {
        var items: [String] = []
    }
}

// MARK: - Quiz Support Structure
struct Question {
    let text: String
    let options: [String]
}

// MARK: - CombinedQuizzesSection Definition
struct CombinedQuizzesSection: View {
    @Binding var goingOutQuizAnswers: [String]
    @Binding var weekendQuizAnswers: [String]
    @Binding var phoneQuizAnswers: [String]

    let onQuizComplete: () -> Void

    @State private var showingGoingOutQuiz = false
    @State private var showingWeekendsQuiz = false
    @State private var showingMyPhoneQuiz = false

    var body: some View {
        VStack(spacing: 24) {
            VStack(alignment: .leading, spacing: 12) {
                Text("GOING OUT QUIZ")
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.primaryColor)
                if goingOutQuizAnswers.isEmpty {
                    Text("Not taken yet")
                        .font(AppTheme.bodyFont)
                        .foregroundColor(.gray)
                } else {
                    ForEach(goingOutQuizAnswers, id: \.self) { answer in
                        Text(answer)
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.primary)
                    }
                }
                Button(action: { showingGoingOutQuiz = true }) {
                    Text(goingOutQuizAnswers.isEmpty ? "Take Quiz" : "Retake Quiz")
                        .font(AppTheme.bodyFont)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(AppTheme.primaryColor)
                        .foregroundColor(.white)
                        .cornerRadius(AppTheme.defaultCornerRadius)
                }
            }
            .padding(AppTheme.defaultPadding)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                    .fill(AppTheme.cardBackground.opacity(0.8))
            )
            .shadow(radius: 5)

            VStack(alignment: .leading, spacing: 12) {
                Text("WEEKENDS QUIZ")
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.primaryColor)
                if weekendQuizAnswers.isEmpty {
                    Text("Not taken yet")
                        .font(AppTheme.bodyFont)
                        .foregroundColor(.gray)
                } else {
                    ForEach(weekendQuizAnswers, id: \.self) { answer in
                        Text(answer)
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.primary)
                    }
                }
                Button(action: { showingWeekendsQuiz = true }) {
                    Text(weekendQuizAnswers.isEmpty ? "Take Quiz" : "Retake Quiz")
                        .font(AppTheme.bodyFont)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(AppTheme.primaryColor)
                        .foregroundColor(.white)
                        .cornerRadius(AppTheme.defaultCornerRadius)
                }
            }
            .padding(AppTheme.defaultPadding)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                    .fill(AppTheme.cardBackground.opacity(0.8))
            )
            .shadow(radius: 5)

            VStack(alignment: .leading, spacing: 12) {
                Text("+ MY PHONE QUIZ")
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.primaryColor)
                if phoneQuizAnswers.isEmpty {
                    Text("Not taken yet")
                        .font(AppTheme.bodyFont)
                        .foregroundColor(.gray)
                } else {
                    ForEach(phoneQuizAnswers, id: \.self) { answer in
                        Text(answer)
                            .font(AppTheme.bodyFont)
                            .foregroundColor(.primary)
                    }
                }
                Button(action: { showingMyPhoneQuiz = true }) {
                    Text(phoneQuizAnswers.isEmpty ? "Take Quiz" : "Retake Quiz")
                        .font(AppTheme.bodyFont)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(AppTheme.primaryColor)
                        .foregroundColor(.white)
                        .cornerRadius(AppTheme.defaultCornerRadius)
                }
            }
            .padding(AppTheme.defaultPadding)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.defaultCornerRadius)
                    .fill(AppTheme.cardBackground.opacity(0.8))
            )
            .shadow(radius: 5)
        }
        .sheet(isPresented: $showingGoingOutQuiz) {
            QuizView(
                quizTitle: "Going Out Quiz",
                quizQuestions: goingOutQuizQuestions,
                onComplete: { answers in
                    goingOutQuizAnswers = answers
                    showingGoingOutQuiz = false
                    onQuizComplete()
                }
            )
        }
        .sheet(isPresented: $showingWeekendsQuiz) {
            QuizView(
                quizTitle: "Weekends Quiz",
                quizQuestions: weekendsQuizQuestions,
                onComplete: { answers in
                    weekendQuizAnswers = answers
                    showingWeekendsQuiz = false
                    onQuizComplete()
                }
            )
        }
        .sheet(isPresented: $showingMyPhoneQuiz) {
            QuizView(
                quizTitle: "+ My Phone Quiz",
                quizQuestions: myPhoneQuizQuestions,
                onComplete: { answers in
                    phoneQuizAnswers = answers
                    showingMyPhoneQuiz = false
                    onQuizComplete()
                }
            )
        }
    }
}

// MARK: - QuizView Definition
struct QuizView: View {
    let quizTitle: String
    let quizQuestions: [QuizQuestion]
    let onComplete: ([String]) -> Void

    @State private var currentQuestionIndex = 0
    @State private var selectedAnswers: [String] = []

    var body: some View {
        Group {
            if currentQuestionIndex < quizQuestions.count {
                VStack(spacing: 20) {
                    Text(quizTitle)
                        .font(AppTheme.titleFont)
                        .padding(.top)

                    Text(quizQuestions[currentQuestionIndex].question)
                        .font(AppTheme.subtitleFont)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    ForEach(quizQuestions[currentQuestionIndex].options, id: \.self) { option in
                        Button(action: {
                            selectedAnswers.append(option)
                            if currentQuestionIndex < quizQuestions.count - 1 {
                                withAnimation { currentQuestionIndex += 1 }
                            } else {
                                onComplete(selectedAnswers)
                            }
                        }) {
                            Text(option)
                                .font(AppTheme.bodyFont)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(AppTheme.primaryColor)
                                .foregroundColor(.white)
                                .cornerRadius(AppTheme.defaultCornerRadius)
                        }
                        .padding(.horizontal)
                    }

                    Spacer()
                }
                .padding()
            } else {
                EmptyView()
            }
        }
    }

}

// MARK: - Supporting Data Structures

struct MissingField {
    let name: String
    let points: Int
    let category: FieldCategory
}

enum FieldCategory {
    case basic, academic, profile, lifestyle, housing, media, quiz

    var icon: String {
        switch self {
        case .basic: return "person.fill"
        case .academic: return "graduationcap.fill"
        case .profile: return "text.alignleft"
        case .lifestyle: return "heart.fill"
        case .housing: return "house.fill"
        case .media: return "photo.fill"
        case .quiz: return "questionmark.circle.fill"
        }
    }

    var color: Color {
        switch self {
        case .basic: return .blue
        case .academic: return .purple
        case .profile: return .green
        case .lifestyle: return .orange
        case .housing: return .red
        case .media: return .pink
        case .quiz: return .yellow
        }
    }
}

