// MajorSelectionView.swift
// Modern UI component for selecting college majors

import SwiftUI

struct MajorSelectionView: View {
    @StateObject private var majorsService = MajorsService.shared
    @Binding var selectedMajor: String
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var selectedCategory: String? = nil
    @State private var showingCategories = true
    
    private var filteredMajors: [CollegeMajor] {
        let majors = selectedCategory != nil ? 
            majorsService.getMajors(for: selectedCategory!) : 
            majorsService.majors
        
        if searchText.isEmpty {
            return majors
        } else {
            return majors.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }
    
    private var categories: [String] {
        majorsService.getCategories()
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Search bar
                    searchSection
                    
                    // Category/Major selection
                    if showingCategories && selectedCategory == nil {
                        categorySelectionView
                    } else {
                        majorSelectionView
                    }
                }
            }
            .navigationTitle("Select Major")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                if selectedCategory != nil {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Categories") {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                selectedCategory = nil
                                searchText = ""
                            }
                        }
                        .foregroundColor(.white)
                    }
                }
            }
        }
        .onAppear {
            if majorsService.majors.isEmpty {
                majorsService.loadMajorsFromFirebase()
            }
        }
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: AppTheme.spacing12) {
            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
                
                TextField("Search majors...", text: $searchText)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
            
            // Quick filters
            if selectedCategory == nil && searchText.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: AppTheme.spacing8) {
                        quickFilterButton("Popular", icon: "star.fill") {
                            // Show popular majors
                            showingCategories = false
                        }
                        
                        quickFilterButton("STEM", icon: "atom") {
                            selectedCategory = "Engineering & Technology"
                        }
                        
                        quickFilterButton("Business", icon: "briefcase.fill") {
                            selectedCategory = "Business & Economics"
                        }
                        
                        quickFilterButton("Health", icon: "cross.fill") {
                            selectedCategory = "Health & Medicine"
                        }
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                }
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing8)
    }
    
    private func quickFilterButton(_ title: String, icon: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .bold))
                
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 12))
            }
            .foregroundColor(.white)
            .padding(.horizontal, AppTheme.spacing12)
            .padding(.vertical, AppTheme.spacing8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial.opacity(0.4))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Category Selection View
    
    private var categorySelectionView: some View {
        ScrollView {
            LazyVStack(spacing: AppTheme.spacing12) {
                ForEach(categories, id: \.self) { category in
                    categoryCard(category)
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func categoryCard(_ category: String) -> some View {
        let majorCount = majorsService.getMajors(for: category).count
        
        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                selectedCategory = category
                showingCategories = false
            }
        }) {
            HStack(spacing: AppTheme.spacing16) {
                // Category icon
                Image(systemName: iconForCategory(category))
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(AppTheme.sexyGradient)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(category)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Text("\(majorCount) majors")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white.opacity(0.6))
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Major Selection View
    
    private var majorSelectionView: some View {
        ScrollView {
            LazyVStack(spacing: AppTheme.spacing8) {
                if majorsService.isLoading {
                    loadingView
                } else if filteredMajors.isEmpty {
                    emptyStateView
                } else {
                    ForEach(filteredMajors, id: \.id) { major in
                        majorRow(major)
                    }
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func majorRow(_ major: CollegeMajor) -> some View {
        let isSelected = selectedMajor == major.name
        
        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .medium)
            selectedMajor = major.name
            dismiss()
        }) {
            HStack(spacing: AppTheme.spacing12) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(major.name)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(isSelected ? .white : .white.opacity(0.9))
                        .multilineTextAlignment(.leading)
                    
                    if let description = major.description, !description.isEmpty {
                        Text(description)
                            .font(.custom("AvenirNext-Regular", size: 12))
                            .foregroundColor(isSelected ? .white.opacity(0.8) : .white.opacity(0.6))
                            .lineLimit(2)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    // MARK: - Helper Views
    
    private var loadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
            
            Text("Loading majors...")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white.opacity(0.6))
            
            Text("No majors found")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(.white)
            
            Text("Try adjusting your search or browse categories")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    // MARK: - Helper Methods
    
    private func iconForCategory(_ category: String) -> String {
        switch category {
        case "Business & Economics": return "briefcase.fill"
        case "Engineering & Technology": return "gear"
        case "Health & Medicine": return "cross.fill"
        case "Computer Science & Information Technology": return "laptopcomputer"
        case "Natural Sciences": return "atom"
        case "Arts & Humanities": return "paintbrush.fill"
        case "Social Sciences": return "person.3.fill"
        case "Mathematics & Statistics": return "function"
        case "Education": return "graduationcap.fill"
        case "Communications & Media": return "megaphone.fill"
        case "Law & Criminal Justice": return "scale.3d"
        case "Agriculture & Environmental Studies": return "leaf.fill"
        case "Architecture & Design": return "building.2.fill"
        case "Performing Arts": return "music.note"
        case "Languages & Literature": return "book.fill"
        case "Religion & Philosophy": return "brain.head.profile"
        case "Military & Security Studies": return "shield.fill"
        case "Interdisciplinary Studies": return "network"
        default: return "book.fill"
        }
    }
}
