// InterestsDropdownSelection.swift
// Multi-select dropdown-only interests selection component (no manual text input allowed)

import SwiftUI

struct InterestsDropdownSelection: View {
    @Binding var selectedInterests: [String]
    @StateObject private var databaseService = RealtimeDatabaseService.shared
    
    @State private var searchText = ""
    @State private var showingDropdown = false
    
    let placeholder: String
    let label: String?
    let maxSelections: Int
    
    init(selectedInterests: Binding<[String]>, placeholder: String = "Select Interests", label: String? = nil, maxSelections: Int = 10) {
        self._selectedInterests = selectedInterests
        self.placeholder = placeholder
        self.label = label
        self.maxSelections = maxSelections
    }
    
    private var filteredInterests: [String] {
        if searchText.isEmpty {
            return databaseService.interests
        } else {
            return databaseService.searchInterests(query: searchText)
        }
    }
    
    private var canSelectMore: Bool {
        selectedInterests.count < maxSelections
    }
    
    private var displayText: String {
        if selectedInterests.isEmpty {
            return placeholder
        } else if selectedInterests.count == 1 {
            return selectedInterests.first!
        } else {
            return "\(selectedInterests.count) interests selected"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            // Label
            if let label = label {
                modernFieldLabel(label, icon: "heart.fill")
            }
            
            // Main selection button
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .light)
                showingDropdown = true
            }) {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(displayText)
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(selectedInterests.isEmpty ? .white.opacity(0.6) : .white)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        Image(systemName: "chevron.down")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                            .rotationEffect(.degrees(showingDropdown ? 180 : 0))
                            .animation(.easeInOut(duration: 0.2), value: showingDropdown)
                    }
                    
                    // Show preview of selected interests
                    if selectedInterests.count > 1 {
                        Text(selectedInterests.prefix(3).joined(separator: ", ") + (selectedInterests.count > 3 ? "..." : ""))
                            .font(.custom("AvenirNext-Regular", size: 14))
                            .foregroundColor(.white.opacity(0.7))
                            .lineLimit(2)
                    }
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Selection counter
            if !selectedInterests.isEmpty {
                Text("\(selectedInterests.count) of \(maxSelections) selected")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .sheet(isPresented: $showingDropdown) {
            interestsSelectionSheet
        }
    }
    
    // MARK: - Interests Selection Sheet
    
    private var interestsSelectionSheet: some View {
        NavigationView {
            ZStack {
                // Background
                AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Selection counter
                    selectionCounter
                    
                    // Search bar
                    searchSection
                    
                    // Interests list
                    if databaseService.isLoadingInterests {
                        loadingView
                    } else if filteredInterests.isEmpty {
                        emptyStateView
                    } else {
                        interestsList
                    }
                }
            }
            .navigationTitle("Select Interests")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        showingDropdown = false
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        showingDropdown = false
                    }
                    .foregroundColor(.white)
                    .font(.custom("AvenirNext-Bold", size: 16))
                }
            }
        }
    }
    
    // MARK: - Selection Counter
    
    private var selectionCounter: some View {
        HStack {
            Text("\(selectedInterests.count) of \(maxSelections) selected")
                .font(.custom("AvenirNext-Bold", size: 14))
                .foregroundColor(.white)
            
            Spacer()
            
            if !selectedInterests.isEmpty {
                Button("Clear All") {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        selectedInterests.removeAll()
                    }
                }
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.vertical, AppTheme.spacing8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial.opacity(0.3))
        )
        .padding(.horizontal, AppTheme.spacing20)
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
            
            TextField("Search interests...", text: $searchText)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing8)
    }
    
    // MARK: - Interests List
    
    private var interestsList: some View {
        ScrollView {
            LazyVStack(spacing: AppTheme.spacing8) {
                ForEach(filteredInterests, id: \.self) { interest in
                    interestRow(interest)
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func interestRow(_ interest: String) -> some View {
        let isSelected = selectedInterests.contains(interest)
        let isDisabled = !isSelected && !canSelectMore
        
        return Button(action: {
            toggleInterest(interest)
        }) {
            HStack {
                Text(interest)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.9))
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                } else if isDisabled {
                    Image(systemName: "minus.circle")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .opacity(isDisabled ? 0.5 : 1.0)
        .disabled(isDisabled)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    // MARK: - Helper Views
    
    private var loadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
            
            Text("Loading interests...")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white.opacity(0.6))
            
            Text("No interests found")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(.white)
            
            Text("Try adjusting your search")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    // MARK: - Helper Methods
    
    private func toggleInterest(_ interest: String) {
        HapticFeedbackManager.shared.generateImpact(style: .light)
        
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            if selectedInterests.contains(interest) {
                selectedInterests.removeAll { $0 == interest }
            } else if canSelectMore {
                selectedInterests.append(interest)
            }
        }
    }
    
    private func modernFieldLabel(_ text: String, icon: String) -> some View {
        HStack(spacing: AppTheme.spacing8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            Text(text)
                .font(.custom("AvenirNext-Bold", size: 14))
                .foregroundColor(.white.opacity(0.9))
        }
    }
    
    private var modernInputBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial.opacity(0.4))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.2), lineWidth: 1)
            )
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
        
        VStack(spacing: 20) {
            InterestsDropdownSelection(
                selectedInterests: .constant([]),
                placeholder: "Select Interests",
                label: "Interests"
            )
            
            InterestsDropdownSelection(
                selectedInterests: .constant(["Music", "Sports", "Technology"]),
                placeholder: "Select Interests",
                label: "Interests"
            )
        }
        .padding()
    }
}
