import Foundation
import FirebaseFirestore
import FirebaseAuth
import Combine

// MARK: - Matching Service
class MatchingService: ObservableObject {
    static let shared = MatchingService()
    private let db = Firestore.firestore()

    private init() {}

    // MARK: - Match Creation
    func createMatch(user1ID: String, user2ID: String, isPremiumMatch: Bool = false) async throws {
        print("💕 MatchingService: Creating match between \(user1ID) and \(user2ID) (Premium: \(isPremiumMatch))")

        // Validate inputs
        guard !user1ID.isEmpty && !user2ID.isEmpty && user1ID != user2ID else {
            throw MatchingError.invalidUserIDs
        }

        // Check if match already exists
        let existingMatch = try await findExistingMatch(user1ID: user1ID, user2ID: user2ID)
        if existingMatch != nil {
            print("⚠️ MatchingService: Match already exists between users")
            return
        }

        // Create match model
        let matchType: MatchType = isPremiumMatch ? .premium : .regular
        let match = MatchModel(
            participants: [user1ID, user2ID].sorted(), // Sort for consistency
            createdAt: Date(),
            hasConversationStarted: false,
            isNewMatch: true,
            initiatedBy: user1ID,
            matchType: matchType
        )

        // Store in Firestore
        try await storeMatch(match)

        // Create corresponding swipe records
        try await createSwipeRecords(user1ID: user1ID, user2ID: user2ID, isPremiumMatch: isPremiumMatch)

        // Track analytics
        trackMatchCreation(match: match, isPremiumMatch: isPremiumMatch)

        print("✅ MatchingService: Successfully created match")
    }

    private func storeMatch(_ match: MatchModel) async throws {
        do {
            let _ = try db.collection("matches").addDocument(from: match) { error in
                if let error = error {
                    print("❌ MatchingService: Failed to store match: \(error)")
                }
            }
            print("✅ MatchingService: Match document stored in Firestore")
        } catch {
            print("❌ MatchingService: Failed to encode match: \(error)")
            throw MatchingError.firestoreError(error)
        }
    }

    private func createSwipeRecords(user1ID: String, user2ID: String, isPremiumMatch: Bool) async throws {
        let batch = db.batch()

        // Create swipe record for user1 -> user2
        let swipe1Data: [String: Any] = [
            "swipedBy": user1ID,
            "swipedOn": user2ID,
            "liked": true,
            "timestamp": FieldValue.serverTimestamp(),
            "isPremiumSwipe": isPremiumMatch
        ]

        let swipe1Ref = db.collection("swipes").document()
        batch.setData(swipe1Data, forDocument: swipe1Ref)

        // Create swipe record for user2 -> user1 (mutual like)
        let swipe2Data: [String: Any] = [
            "swipedBy": user2ID,
            "swipedOn": user1ID,
            "liked": true,
            "timestamp": FieldValue.serverTimestamp(),
            "isPremiumSwipe": false // Only the initiator gets premium credit
        ]

        let swipe2Ref = db.collection("swipes").document()
        batch.setData(swipe2Data, forDocument: swipe2Ref)

        try await batch.commit()
        print("✅ MatchingService: Swipe records created")
    }

    private func findExistingMatch(user1ID: String, user2ID: String) async throws -> MatchModel? {
        let participants = [user1ID, user2ID].sorted()

        let snapshot = try await db.collection("matches")
            .whereField("participants", isEqualTo: participants)
            .limit(to: 1)
            .getDocuments()

        if let document = snapshot.documents.first {
            return try document.data(as: MatchModel.self)
        }

        return nil
    }

    private func trackMatchCreation(match: MatchModel, isPremiumMatch: Bool) {
        let eventName = isPremiumMatch ? "premium_match_created" : "regular_match_created"

        AnalyticsManager.shared.trackEvent(eventName, parameters: [
            "match_id": match.id ?? "unknown",
            "participants": match.participants,
            "match_type": match.matchType.rawValue,
            "is_premium": isPremiumMatch
        ])
    }

    // MARK: - Match Queries
    func getUserMatches(userID: String) async throws -> [MatchModel] {
        let snapshot = try await db.collection("matches")
            .whereField("participants", arrayContains: userID)
            .order(by: "createdAt", descending: true)
            .getDocuments()

        var matches: [MatchModel] = []
        for document in snapshot.documents {
            do {
                let match = try document.data(as: MatchModel.self)
                matches.append(match)
            } catch {
                print("❌ MatchingService: Failed to decode match: \(error)")
            }
        }

        return matches
    }

    func getNewMatches(userID: String) async throws -> [MatchModel] {
        let allMatches = try await getUserMatches(userID: userID)
        return allMatches.filter { $0.isNewMatch }
    }

    func markMatchAsViewed(matchID: String, userID: String) async throws {
        guard let matchID = matchID as String? else {
            throw MatchingError.invalidMatchID
        }

        let matchRef = db.collection("matches").document(matchID)

        try await matchRef.updateData([
            "lastViewedBy.\(userID)": FieldValue.serverTimestamp(),
            "isNewMatch": false
        ])

        print("✅ MatchingService: Marked match \(matchID) as viewed by \(userID)")
    }

    // MARK: - Chat Integration
    func startConversation(matchID: String, participants: [String]) async throws -> String {
        guard participants.count == 2 else {
            throw MatchingError.invalidParticipants
        }

        // Create chat document
        let chatData: [String: Any] = [
            "participants": participants.sorted(),
            "createdAt": FieldValue.serverTimestamp(),
            "lastMessageAt": FieldValue.serverTimestamp(),
            "lastMessage": "",
            "unreadCount": participants.reduce(into: [String: Int]()) { result, userID in
                result[userID] = 0
            },
            "isTyping": false,
            "matchID": matchID
        ]

        let chatRef = try await db.collection("chats").addDocument(data: chatData)
        let chatID = chatRef.documentID

        // Update match with chat ID
        try await db.collection("matches").document(matchID).updateData([
            "chatID": chatID,
            "hasConversationStarted": true
        ])

        // Track conversation start
        AnalyticsManager.shared.trackConversationStarted()

        print("✅ MatchingService: Started conversation \(chatID) for match \(matchID)")
        return chatID
    }
}

// MARK: - Matching Errors
enum MatchingError: LocalizedError {
    case invalidUserIDs
    case invalidMatchID
    case invalidParticipants
    case firestoreError(Error)
    case matchAlreadyExists

    var errorDescription: String? {
        switch self {
        case .invalidUserIDs:
            return "Invalid user IDs provided"
        case .invalidMatchID:
            return "Invalid match ID"
        case .invalidParticipants:
            return "Invalid participants for conversation"
        case .firestoreError(let error):
            return "Firestore error: \(error.localizedDescription)"
        case .matchAlreadyExists:
            return "Match already exists between these users"
        }
    }
}


