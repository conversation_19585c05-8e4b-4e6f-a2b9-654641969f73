import SwiftUI
import CoreLocation

struct SwipeDeckView: View {
    @StateObject private var viewModel = MatchingViewModel()
    @StateObject private var profileVM = ProfileViewModel.shared
    @StateObject private var swipeLimitManager = DailySwipeLimitManager.shared
    @StateObject private var superLikeManager = SuperLikeManager.shared
    @State private var currentIndex: Int = 0
    @State private var showFilter: Bool = false
    @State private var showSwipeLimitPaywall = false
    @State private var showSuperLikePaywall = false

    // Track swipes that resulted in matches to prevent rewinding them
    @State private var matchedSwipeIndices: Set<Int> = []
    @State private var showPremiumUpgrade = false
    @State private var showFilterOnboarding = false

    @State private var showChatView = false
    @State private var selectedChatPartner: UserModel?

    // Check if user has completed filter onboarding OR has required filter settings
    private var hasCompletedFilterOnboarding: Bool {
        // First check the flag
        let hasCompletedFlag = UserDefaults.standard.bool(forKey: "hasCompletedFilterOnboarding")

        // If flag is true, they've completed it
        if hasCompletedFlag {
            return true
        }

        // If flag is false, check if they have the required filter settings already
        return hasRequiredFilterSettings()
    }

    // Check if user has the mandatory filter settings configured
    private func hasRequiredFilterSettings() -> Bool {
        guard let userProfile = profileVM.userProfile,
              let filterSettings = userProfile.filterSettings else {
            print("🔍 SwipeDeckView: No user profile or filter settings found")
            return false
        }

        // Check mandatory filters (based on FilterSettings model)
        let hasCollegeName = !(filterSettings.collegeName?.isEmpty ?? true)
        let hasPreferredGender = !(filterSettings.preferredGender?.isEmpty ?? true)
        let hasMaxAgeDifference = (filterSettings.maxAgeDifference ?? 0) > 0
        let hasHousingStatus = !(filterSettings.housingStatus?.isEmpty ?? true)

        let hasAllRequired = hasCollegeName && hasPreferredGender && hasMaxAgeDifference && hasHousingStatus

        print("🔍 SwipeDeckView: Filter settings check:")
        print("   - College: \(hasCollegeName) (\(filterSettings.collegeName ?? "nil"))")
        print("   - Gender: \(hasPreferredGender) (\(filterSettings.preferredGender ?? "nil"))")
        print("   - Age: \(hasMaxAgeDifference) (\(filterSettings.maxAgeDifference ?? 0))")
        print("   - Housing: \(hasHousingStatus) (\(filterSettings.housingStatus ?? "nil"))")
        print("   - All required: \(hasAllRequired)")

        return hasAllRequired
    }

    // MARK: - Computed Properties

    /// Current filtered matches - now simplified to show all SmartMatchingEngine results
    private var filteredMatches: [UserModel] {
        let allMatches = viewModel.potentialMatches

        print("🔍 SwipeDeckView: filteredMatches computation")
        print("   - Total potentialMatches: \(allMatches.count)")
        print("   - Using simplified filtering (all SmartMatchingEngine results)")

        // Return all SmartMatchingEngine results to preserve ranking and maximize matches
        // Advanced filtering is now handled through the AdvancedFilterView instead
        print("   - Final result count: \(allMatches.count)")
        return allMatches
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                if DeviceInfo.isIPad && DeviceInfo.isLandscape {
                    // iPad Landscape: Side-by-side layout
                    iPadLandscapeLayout(geometry: geometry)
                } else {
                    // iPhone and iPad Portrait: Standard layout
                    standardLayout(geometry: geometry)
                }
            }
        }
        .background(AppTheme.backgroundGradient.ignoresSafeArea())
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarHidden(true)
        .onAppear {
            // Check if user needs filter onboarding
            if !hasCompletedFilterOnboarding {
                // Show onboarding after a brief delay for smooth transition
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showFilterOnboarding = true
                }
            } else {
                // OPTIMIZATION: Use initial load instead of force fetch
                viewModel.initialLoad()
            }

            // Initialize managers - with retry logic for profile loading
            initializeManagers()
        }
        .onChange(of: profileVM.userProfile) { _, newProfile in
            // Re-initialize managers when profile becomes available or changes
            if newProfile != nil {
                initializeManagers()
            }
        }
        .onChange(of: viewModel.lastMatchCreatedForUserID) { _, matchedUserID in
            // Track matches to prevent rewind
            if let matchedUserID = matchedUserID {
                matchedSwipeIndices.insert(currentIndex)
                print("🚫 SwipeDeckView: Added index \(currentIndex) to matched swipes (user: \(matchedUserID))")
            }
        }
        .onChange(of: viewModel.potentialMatches) { _, newMatches in
            print("🔍 SwipeDeckView: potentialMatches changed")
            print("   - New matches count: \(newMatches.count)")
            print("   - Current index: \(currentIndex)")
            print("   - Filtered matches count: \(filteredMatches.count)")

            // Safety check: if currentIndex is out of bounds, reset it
            if currentIndex >= filteredMatches.count && !filteredMatches.isEmpty {
                print("   - Resetting currentIndex from \(currentIndex) to 0")
                withAnimation {
                    currentIndex = 0
                }
            }
        }
        .sheet(isPresented: $showFilter) {AdvancedFilterView() }
        .onChange(of: showFilter) { _, isPresented in
            // when filter sheet dismisses:
            if !isPresented {
                viewModel.fetchPotentialMatches()
                currentIndex = 0
            }
        }
        .fullScreenCover(isPresented: $showFilterOnboarding) {
            FilterOnboardingView {
                // On completion callback
                showFilterOnboarding = false

                // Start loading matches after onboarding
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    viewModel.initialLoad()
                }
            }
        }
        .addSwipeViewSheets()
    }

    // MARK: - Layout Variants

    private func standardLayout(geometry: GeometryProxy) -> some View {
        ZStack {
            VStack(spacing: 0) {
                // Main card display area - maximized for full-screen swipe interface
                cardDisplayArea(matches: filteredMatches, geometry: geometry)

                // Bottom controls - positioned at bottom for optimal swipe experience
                bottomControls
            }

            // Top-right utility controls overlay - optimized for full-screen layout
            VStack {
                HStack {
                    // Swipe counter (left side)
                    swipeCounterView

                    Spacer()

                    HStack(spacing: ResponsiveSpacing.sm) {
                        // Debug buttons (only in debug builds)
                        #if DEBUG
                        HStack(spacing: 8) {
                            // Debug discovery pipeline
                            Button(action: { viewModel.quickDebugDiscovery() }) {
                                Text("🔍")
                                    .font(.system(size: 16, weight: .bold))
                            }
                            .buttonStyle(IconButtonStyle(
                                backgroundColor: Color.red.opacity(0.8),
                                iconColor: .white,
                                size: 40
                            ))

                            // Force fetch matches (bypass rate limit)
                            Button(action: { viewModel.forceFetchPotentialMatches() }) {
                                Text("🔄")
                                    .font(.system(size: 16, weight: .bold))
                            }
                            .buttonStyle(IconButtonStyle(
                                backgroundColor: Color.blue.opacity(0.8),
                                iconColor: .white,
                                size: 40
                            ))

                            // Debug user ID issues
                            Button(action: { viewModel.debugUserIDIssues() }) {
                                Text("🔧")
                                    .font(.system(size: 16, weight: .bold))
                            }
                            .buttonStyle(IconButtonStyle(
                                backgroundColor: Color.green.opacity(0.8),
                                iconColor: .white,
                                size: 40
                            ))
                        }
                        #endif

                        Button(action: refresh) {
                            Image(systemName: "arrow.clockwise")
                        }
                        .buttonStyle(IconButtonStyle(
                            backgroundColor: AppTheme.surfacePrimary.opacity(0.95),
                            iconColor: AppTheme.primaryColor,
                            size: 40
                        ))
                        .scaleEffect(viewModel.isLoading ? 0.9 : 1.0)
                        .animation(.easeInOut(duration: AppTheme.animationFast), value: viewModel.isLoading)

                        Button(action: { showFilter = true }) {
                            Image(systemName: "slider.horizontal.3")
                        }
                        .buttonStyle(IconButtonStyle(
                            backgroundColor: AppTheme.surfacePrimary.opacity(0.95),
                            iconColor: AppTheme.primaryColor,
                            size: 40
                        ))
                        .scaleEffect(showFilter ? 0.9 : 1.0)
                        .animation(.easeInOut(duration: AppTheme.animationFast), value: showFilter)
                    }
                }
                .responsivePadding(.top, ResponsiveSpacing.sm)
                .responsiveHorizontalPadding()

                Spacer()
            }
        }
    }

    private func iPadLandscapeLayout(geometry: GeometryProxy) -> some View {
        HStack(spacing: ResponsiveSpacing.xl) {
            // Left side: Card display
            VStack(spacing: 0) {
                cardDisplayArea(matches: filteredMatches, geometry: geometry)
                    .frame(maxWidth: geometry.size.width * 0.6)
            }

            // Right side: Controls and info
            VStack(spacing: ResponsiveSpacing.lg) {
                // Top controls
                VStack(spacing: ResponsiveSpacing.md) {
                    swipeCounterView

                    HStack(spacing: ResponsiveSpacing.md) {
                        Button(action: refresh) {
                            Image(systemName: "arrow.clockwise")
                        }
                        .buttonStyle(ResponsivePrimaryButtonStyle())

                        Button(action: { showFilter = true }) {
                            Image(systemName: "slider.horizontal.3")
                        }
                        .buttonStyle(ResponsiveSecondaryButtonStyle())
                    }
                }

                Spacer()

                // Bottom controls (vertical layout for iPad)
                VStack(spacing: ResponsiveSpacing.lg) {
                    bottomControlsVertical
                }

                Spacer()
            }
            .frame(maxWidth: geometry.size.width * 0.35)
            .responsivePadding()
        }
        .responsiveHorizontalPadding()
    }



    // MARK: - Computed Properties



    /// Main card display area with proper spacing and layout
    private func cardDisplayArea(matches: [UserModel], geometry: GeometryProxy) -> some View {
        Group {
            if viewModel.isLoading {
                loadingStateView
            } else if matches.isEmpty {
                emptyStateView
            } else {
                cardStackView(matches: matches, geometry: geometry)
            }
        }
    }

    // MARK: - Swipe Counter View
    private var swipeCounterView: some View {
        let isPremium = profileVM.userProfile?.isPremium == true
        let swipeText = swipeLimitManager.getSwipeCountText(isPremium: isPremium)
        let remainingSwipes = swipeLimitManager.getRemainingSwipes(isPremium: isPremium)

        // Debug info
        let _ = print("🔍 SwipeCounterView update:")
        let _ = print("   - isPremium: \(isPremium)")
        let _ = print("   - dailySwipeCount: \(swipeLimitManager.dailySwipeCount)")
        let _ = print("   - remainingSwipes: \(remainingSwipes)")
        let _ = print("   - swipeText: \(swipeText)")

        return HStack(spacing: AppTheme.spacing8) {
            Image(systemName: isPremium ? "infinity" : "hand.raised.fill")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(isPremium ? .yellow : (remainingSwipes <= 5 ? .red : AppTheme.primaryColor))

            Text(swipeText)
                .font(.system(size: 14, weight: .bold, design: .rounded))
                .foregroundColor(isPremium ? .yellow : (remainingSwipes <= 5 ? .red : AppTheme.textPrimary))
        }
        .padding(.horizontal, AppTheme.spacing12)
        .padding(.vertical, AppTheme.spacing8)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(AppTheme.surfacePrimary.opacity(0.95))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(
                            isPremium ? Color.yellow.opacity(0.6) :
                            (remainingSwipes <= 5 ? Color.red.opacity(0.6) : AppTheme.primaryColor.opacity(0.3)),
                            lineWidth: 1
                        )
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        .scaleEffect(remainingSwipes <= 5 && !isPremium ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.3), value: remainingSwipes)
    }

    // MARK: - Broken Down View Components

    private var loadingStateView: some View {
        VStack(spacing: AppTheme.spacing20) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.primaryColor))

            Text("Finding your matches...")
                .font(AppTheme.subheadline)
                .foregroundColor(AppTheme.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyStateView: some View {
        EnhancedEmptyStateView(
            viewModel: viewModel,
            onRefresh: refresh
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, AppTheme.spacing24)
    }

    private func cardStackView(matches: [UserModel], geometry: GeometryProxy) -> some View {
        ZStack {
            ForEach(Array(matches.indices.reversed()), id: \.self) { index in
                cardAtIndex(index, matches: matches, geometry: geometry)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 12) // Horizontal padding for hue ring visibility
        .padding(.top, 20) // Minimal top padding for clean full-screen experience
        .padding(.bottom, 20) // Space for bottom controls
        .onAppear {
            logCardStackInfo(matches: matches, geometry: geometry)
        }
    }

    private func cardAtIndex(_ index: Int, matches: [UserModel], geometry: GeometryProxy) -> some View {
        let shouldShow = index >= currentIndex && index < currentIndex + 3
        let candidate = matches[index]

        return Group {
            if shouldShow {
                swipeCardView(for: candidate, at: index, matches: matches, geometry: geometry)
            }
        }
    }

    private func swipeCardView(for candidate: UserModel, at index: Int, matches: [UserModel], geometry: GeometryProxy) -> some View {
        SwipeCardView(user: candidate) { swipedUser, direction in
            handleSwipe(user: swipedUser, direction: direction)
        }
        .frame(
            width: cardWidth(for: geometry),
            height: cardHeight(for: geometry)
        )
        .scaleEffect(optimizedScale(for: index))
        .offset(x: optimizedHorizontalOffset(for: index), y: optimizedVerticalOffset(for: index))
        .rotationEffect(.degrees(optimizedRotation(for: index)))
        .allowsHitTesting(index == currentIndex)
        .zIndex(Double(matches.count - index))
        .transition(.asymmetric(insertion: .scale.combined(with: .opacity), removal: .scale.combined(with: .opacity)))
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentIndex)
    }

    // MARK: - Optimized Animation Calculations

    /// Optimized scale calculation with caching
    private func optimizedScale(for index: Int) -> CGFloat {
        let relativeIndex = index - currentIndex
        if relativeIndex < 0 { return 0 } // Hidden cards
        if relativeIndex == 0 { return 1.0 } // Current card
        if relativeIndex == 1 { return 0.95 } // Next card
        if relativeIndex == 2 { return 0.9 } // Third card
        return 0.85 // Further cards
    }

    /// Optimized horizontal offset with reduced calculations
    private func optimizedHorizontalOffset(for index: Int) -> CGFloat {
        let relativeIndex = index - currentIndex
        if relativeIndex <= 0 { return 0 }
        return CGFloat(relativeIndex) * 2 // Minimal offset for stacking
    }

    /// Optimized vertical offset with reduced calculations
    private func optimizedVerticalOffset(for index: Int) -> CGFloat {
        let relativeIndex = index - currentIndex
        if relativeIndex <= 0 { return 0 }
        return CGFloat(relativeIndex) * 4 // Subtle stacking effect
    }

    /// Optimized rotation with minimal calculations
    private func optimizedRotation(for index: Int) -> Double {
        let relativeIndex = index - currentIndex
        if relativeIndex <= 0 { return 0 }
        return Double(relativeIndex) * 1.5 // Subtle rotation for depth
    }

    // MARK: - Helper Properties

    private func cardWidth(for geometry: GeometryProxy) -> CGFloat {
        if DeviceInfo.isIPad && DeviceInfo.isLandscape {
            // iPad landscape: Use 60% of screen width
            return (geometry.size.width * 0.6) - ResponsiveSpacing.xl
        } else if DeviceInfo.isIPad {
            // iPad portrait: Use responsive sizing
            return min(500, geometry.size.width - ResponsiveSpacing.horizontalMargin * 2)
        } else {
            // iPhone: Full width minus margins
            return geometry.size.width - ResponsiveSpacing.lg
        }
    }

    private func cardHeight(for geometry: GeometryProxy) -> CGFloat {
        let safeAreaTop = geometry.safeAreaInsets.top
        let safeAreaBottom = geometry.safeAreaInsets.bottom

        // Responsive spacing for controls
        let bottomControlsSpace: CGFloat = DeviceInfo.isIPad ? 180 : 140
        let topUtilitySpace: CGFloat = DeviceInfo.isIPad ? 80 : 60

        // Calculate maximum available height accounting for safe areas and UI elements
        let totalAvailableHeight = geometry.size.height - safeAreaTop - safeAreaBottom
        let usableHeight = totalAvailableHeight - bottomControlsSpace - topUtilitySpace

        // Target height based on device
        let heightMultiplier: CGFloat = DeviceInfo.isIPad ? 0.85 : 0.97
        let targetHeight = usableHeight * heightMultiplier

        // Ensure proper aspect ratio
        let cardWidth = cardWidth(for: geometry)
        let aspectRatio: CGFloat = DeviceInfo.isIPad ? 1.4 : 1.25
        let minHeightForAspectRatio = cardWidth * aspectRatio

        return max(targetHeight, minHeightForAspectRatio)
    }



    private func logCardStackInfo(matches: [UserModel], geometry: GeometryProxy) {
        print("🔍 SwipeDeckView: Rendering card stack with \(matches.count) matches")
        print("   - Current index: \(currentIndex)")
        print("   - Geometry size: \(geometry.size)")

        // Log card visibility
        for (index, user) in matches.enumerated() {
            let shouldShow = index >= currentIndex && index < currentIndex + 3
            print("   - Card \(index): \(user.firstName ?? "Unknown") - shouldShow: \(shouldShow)")
        }
    }

    private var bottomControls: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Main action buttons row - Tinder-like layout with all buttons
            HStack(spacing: AppTheme.spacing20) {
                // Enhanced Rewind Button with modern effects
                Button(action: rewindSwipe) {
                    Image(systemName: "arrow.uturn.backward")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 50, height: 50)
                        .background(
                            ZStack {
                                Circle()
                                    .fill(
                                        canRewind ?
                                        LinearGradient(colors: [AppTheme.primaryColor, AppTheme.primaryColor.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                        LinearGradient(colors: [AppTheme.textTertiary.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing)
                                    )

                                // Subtle inner glow
                                if currentIndex > 0 {
                                    Circle()
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                        .blur(radius: 1)
                                }
                            }
                        )
                        .shadow(color: currentIndex > 0 ? AppTheme.primaryColor.opacity(0.4) : .clear, radius: 12, x: 0, y: 6)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                }
                .disabled(!canRewind)
                .scaleEffect(currentIndex > 0 ? 1.0 : 0.9)
                .cardTransform3D(isPressed: false)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentIndex > 0)

                // Enhanced Pass Button (X) with modern effects
                Button(action: passCurrentUser) {
                    Image(systemName: "xmark")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            ZStack {
                                Circle()
                                    .fill(
                                        currentIndex < filteredMatches.count ?
                                        LinearGradient(colors: [.red, .pink, .red.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                        LinearGradient(colors: [Color.gray.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing)
                                    )

                                // Subtle inner glow
                                Circle()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    .blur(radius: 1)
                            }
                        )
                        .shadow(color: .red.opacity(0.4), radius: 15, x: 0, y: 8)
                        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                }
                .disabled(currentIndex >= filteredMatches.count)
                .scaleEffect(currentIndex < filteredMatches.count ? 1.0 : 0.9)
                .cardTransform3D(isPressed: false)
                .pulsingNeonGlow(color: .red)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentIndex < filteredMatches.count)

                // Enhanced Super Like Button (Star) with premium gold effects
                Button(action: superLike) {
                    Image(systemName: "star.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 50, height: 50)
                        .background(
                            ZStack {
                                Circle()
                                    .fill(
                                        currentIndex < filteredMatches.count ?
                                        LinearGradient(colors: [.yellow, .orange, .yellow.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                        LinearGradient(colors: [Color.gray.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing)
                                    )

                                // Subtle inner glow
                                Circle()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    .blur(radius: 1)
                            }
                        )
                        .shadow(color: .yellow.opacity(0.6), radius: 12, x: 0, y: 6)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                }
                .disabled(currentIndex >= filteredMatches.count)
                .scaleEffect(currentIndex < filteredMatches.count ? 1.0 : 0.9)
                .cardTransform3D(isPressed: false)
                .pulsingNeonGlow(color: .yellow)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentIndex < filteredMatches.count)

                // Enhanced Like Button (Heart) with modern effects
                Button(action: likeCurrentUser) {
                    Image(systemName: "heart.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(
                            ZStack {
                                Circle()
                                    .fill(
                                        currentIndex < filteredMatches.count ?
                                        LinearGradient(colors: [.green, .mint, .green.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                        LinearGradient(colors: [Color.gray.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing)
                                    )

                                // Subtle inner glow
                                Circle()
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    .blur(radius: 1)
                            }
                        )
                        .shadow(color: .green.opacity(0.4), radius: 15, x: 0, y: 8)
                        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                }
                .disabled(currentIndex >= filteredMatches.count)
                .scaleEffect(currentIndex < filteredMatches.count ? 1.0 : 0.9)
                .cardTransform3D(isPressed: false)
                .pulsingNeonGlow(color: .green)
                .animation(.spring(response: 0.4, dampingFraction: 0.6), value: currentIndex < filteredMatches.count)
            }


        }
        .padding(.bottom, AppTheme.spacing40) // Optimized bottom padding for swipe interface
        .padding(.horizontal, AppTheme.spacing16) // Add horizontal padding for better layout
    }



    // MARK: - Computed Properties

    /// Check if rewind is allowed (not at start and previous swipe wasn't a match)
    private var canRewind: Bool {
        guard currentIndex > 0 else { return false }

        // Check if the previous swipe (currentIndex - 1) resulted in a match
        let previousSwipeIndex = currentIndex - 1
        let wasMatch = matchedSwipeIndices.contains(previousSwipeIndex)

        return !wasMatch
    }

    // MARK: - Manager Initialization
    private func initializeManagers() {
        guard let userID = profileVM.userProfile?.id else {
            print("⚠️ SwipeDeckView: Cannot initialize managers - no user profile available")
            return
        }

        print("🔄 SwipeDeckView: Initializing managers for user: \(userID)")

        Task {
            await swipeLimitManager.initialize(for: userID)
            await superLikeManager.initialize(for: userID)

            print("✅ SwipeDeckView: Managers initialized successfully")
            print("   - Daily swipes: \(swipeLimitManager.dailySwipeCount)/\(swipeLimitManager.maxDailySwipes)")
            print("   - Remaining swipes: \(swipeLimitManager.getRemainingSwipes(isPremium: profileVM.userProfile?.isPremium == true))")
        }
    }

    // MARK: - Swipe Logic
    private func handleSwipe(user: UserModel, direction: SwipeDirection) {
        print("🔍 SwipeDeckView: Handling swipe - User: \(user.firstName ?? "Unknown"), Direction: \(direction)")

        // Check swipe limit before processing
        let isPremium = profileVM.userProfile?.isPremium == true
        guard swipeLimitManager.canSwipe(isPremium: isPremium) else {
            print("⚠️ Swipe limit reached, showing paywall")
            showSwipeLimitPaywall = true

            // Track analytics
            if let userID = profileVM.userProfile?.id {
                swipeLimitManager.trackSwipeLimitReached(userID: userID)
            }
            return
        }

        // Process the swipe
        switch direction {
        case .right:
            viewModel.swipeRight(on: user)
        case .left:
            viewModel.swipeLeft(on: user)
        case .up:
            // CRITICAL FIX: Handle super likes properly
            viewModel.superLike(on: user)
        }

        // Record swipe for limit tracking
        if let userID = profileVM.userProfile?.id {
            print("🎯 SwipeDeckView: Recording swipe for limit tracking")
            print("   - UserID: \(userID)")
            print("   - isPremium: \(isPremium)")
            print("   - Current swipe count before: \(swipeLimitManager.dailySwipeCount)")

            Task {
                await swipeLimitManager.recordSwipe(userID: userID, isPremium: isPremium)

                await MainActor.run {
                    print("✅ SwipeDeckView: Swipe recorded, new count: \(swipeLimitManager.dailySwipeCount)")
                }
            }
        } else {
            print("❌ SwipeDeckView: Cannot record swipe - no user ID available")
        }

        withAnimation {
            currentIndex += 1
        }
    }

    private func rewindSwipe() {
        guard canRewind else {
            print("⚠️ SwipeDeckView: Cannot rewind - either at start or previous swipe was a match")
            return
        }

        print("↩️ SwipeDeckView: Rewinding swipe from index \(currentIndex) to \(currentIndex - 1)")

        withAnimation {
            currentIndex -= 1
        }
    }

    private func superLike() {
        let isPremium = profileVM.userProfile?.isPremium == true

        // Check if user is premium first
        guard isPremium else {
            showSuperLikePaywall = true
            if let userID = profileVM.userProfile?.id {
                superLikeManager.trackSuperLikeAttemptWithoutPremium(userID: userID)
            }
            return
        }

        // Check super like limit for premium users
        guard superLikeManager.canSuperLike(isPremium: isPremium) else {
            showSuperLikePaywall = true
            if let userID = profileVM.userProfile?.id {
                superLikeManager.trackSuperLikeLimitReached(userID: userID)
            }
            return
        }

        // Check regular swipe limit
        guard swipeLimitManager.canSwipe(isPremium: isPremium) else {
            showSwipeLimitPaywall = true
            if let userID = profileVM.userProfile?.id {
                swipeLimitManager.trackSwipeLimitReached(userID: userID)
            }
            return
        }

        // Get the current filtered matches
        let currentMatches = getCurrentFilteredMatches()
        if currentIndex < currentMatches.count {
            let user = currentMatches[currentIndex]
            viewModel.superLike(on: user)

            // Record super like and swipe for tracking
            if let userID = profileVM.userProfile?.id {
                Task {
                    await superLikeManager.recordSuperLike(userID: userID, isPremium: isPremium)
                    await swipeLimitManager.recordSwipe(userID: userID, isPremium: isPremium)
                }
            }

            withAnimation {
                currentIndex += 1
            }
        }
    }

    private func likeCurrentUser() {
        let isPremium = profileVM.userProfile?.isPremium == true
        guard swipeLimitManager.canSwipe(isPremium: isPremium) else {
            showSwipeLimitPaywall = true
            if let userID = profileVM.userProfile?.id {
                swipeLimitManager.trackSwipeLimitReached(userID: userID)
            }
            return
        }

        // Get the current filtered matches
        let currentMatches = getCurrentFilteredMatches()
        if currentIndex < currentMatches.count {
            let user = currentMatches[currentIndex]
            viewModel.swipeRight(on: user)

            // Record swipe for limit tracking
            if let userID = profileVM.userProfile?.id {
                Task {
                    await swipeLimitManager.recordSwipe(userID: userID, isPremium: isPremium)
                }
            }

            withAnimation {
                currentIndex += 1
            }
        }
    }

    private func passCurrentUser() {
        let isPremium = profileVM.userProfile?.isPremium == true
        guard swipeLimitManager.canSwipe(isPremium: isPremium) else {
            showSwipeLimitPaywall = true
            if let userID = profileVM.userProfile?.id {
                swipeLimitManager.trackSwipeLimitReached(userID: userID)
            }
            return
        }

        // Get the current filtered matches
        let currentMatches = getCurrentFilteredMatches()
        if currentIndex < currentMatches.count {
            let user = currentMatches[currentIndex]
            viewModel.swipeLeft(on: user)

            // Record swipe for limit tracking
            if let userID = profileVM.userProfile?.id {
                Task {
                    await swipeLimitManager.recordSwipe(userID: userID, isPremium: isPremium)
                }
            }

            withAnimation {
                currentIndex += 1
            }
        }
    }

    /// Helper function to get the current filtered matches
    private func getCurrentFilteredMatches() -> [UserModel] {
        return filteredMatches
    }

    /// Resets the deck and re‑fetches matches.
    private func refresh() {
        withAnimation {
            currentIndex = 0
        }

        // Use the improved force refresh method
        viewModel.forceRefresh()
    }

    // MARK: - Chat Navigation

    /// Navigate to chat with matched user - FIXED: Use proper chat creation
    private func navigateToChat(with user: UserModel) {
        print("💬 SwipeDeckView: Navigating to chat with \(user.firstName ?? "Unknown")")

        guard let currentUserID = ProfileViewModel.shared.userProfile?.id,
              let chatPartnerID = user.id else {
            print("❌ SwipeDeckView: Missing user IDs for chat navigation")
            return
        }

        // Use ChatManager to create or get existing chat
        ChatManager.shared.createOrGetChat(between: currentUserID, and: chatPartnerID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let chatID):
                    print("✅ SwipeDeckView: Successfully created/found chat: \(chatID)")

                    // Mark conversation as started
                    ChatManager.shared.markConversationStarted(for: [currentUserID, chatPartnerID], chatID: chatID)

                    // Set up navigation
                    selectedChatPartner = user
                    showChatView = true

                case .failure(let error):
                    print("❌ SwipeDeckView: Failed to create chat: \(error.localizedDescription)")
                    // Still try to show chat view as fallback
                    selectedChatPartner = user
                    showChatView = true
                }
            }
        }
    }

    /// Generate consistent chat ID for two users
    private func generateChatID(userA: String, userB: String) -> String {
        let sortedIDs = [userA, userB].sorted()
        return "chat_\(sortedIDs[0])_\(sortedIDs[1])"
    }



    // MARK: - Visual Helpers
    private func scale(for index: Int) -> CGFloat {
        let difference = index - currentIndex
        switch difference {
        case 0: return 1.0
        case 1: return 0.95
        case 2: return 0.90
        default: return 0.85
        }
    }

    private func horizontalOffset(for index: Int) -> CGFloat {
        let difference = index - currentIndex
        return CGFloat(difference) * 2
    }

    private func verticalOffset(for index: Int) -> CGFloat {
        let difference = index - currentIndex
        return CGFloat(difference) * 8
    }

    private func rotation(for index: Int) -> Double {
        let difference = index - currentIndex
        return Double(difference) * 1.5
    }
}

// MARK: - Enhanced Empty State View
struct EnhancedEmptyStateView: View {
    let viewModel: MatchingViewModel
    let onRefresh: () -> Void

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            // Icon and Title
            VStack(spacing: AppTheme.spacing16) {
                Image(systemName: "heart.slash")
                    .font(.system(size: 64, weight: .light))
                    .foregroundColor(AppTheme.textSecondary)

                Text("No Potential Matches")
                    .font(AppTheme.title2)
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)
            }

            // Diagnostic Information
            VStack(spacing: AppTheme.spacing12) {
                if let errorMessage = viewModel.errorMessage {
                    Text("Error: \(errorMessage)")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.errorColor)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppTheme.spacing16)
                } else {
                    Text("We couldn't find any matches for you right now.")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)

                    Text("This could be because:")
                        .font(AppTheme.subheadline)
                        .foregroundColor(AppTheme.textSecondary)
                        .padding(.top, AppTheme.spacing8)

                    VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                        Text("• You've already swiped on all available users")
                        Text("• No users match your current filters")
                        Text("• There are few users in your area")
                        Text("• Some users haven't completed their profiles")
                    }
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }

            // Action Buttons
            VStack(spacing: AppTheme.spacing12) {
                Button("Refresh Matches", action: onRefresh)
                    .buttonStyle(PrimaryButtonStyle())
            }
        }
        .padding(AppTheme.spacing24)
    }
}

// MARK: - View Extensions
extension View {
    func addSwipeViewSheets() -> some View {
        self
            .fullScreenCover(isPresented: .constant(false)) { // Match celebration - will be properly bound in actual implementation
                EmptyView()
            }
            .fullScreenCover(isPresented: .constant(false)) { // Chat view - will be properly bound in actual implementation
                EmptyView()
            }
            .overlay(
                Group {
                    EmptyView() // Paywall overlays - will be properly bound in actual implementation
                }
            )
            .sheet(isPresented: .constant(false)) { // Premium upgrade - will be properly bound in actual implementation
                EmptyView()
            }
    }
}

// MARK: - Responsive Bottom Controls
extension SwipeDeckView {
    var bottomControlsVertical: some View {
        VStack(spacing: ResponsiveSpacing.lg) {
            // Rewind button
            Button(action: rewindSwipe) {
                Image(systemName: "arrow.uturn.backward")
                    .font(.system(size: ResponsiveSizing.iconSize, weight: .bold))
            }
            .buttonStyle(ResponsiveSecondaryButtonStyle())
            .disabled(!canRewind)

            // Pass button
            Button(action: passCurrentUser) {
                Image(systemName: "xmark")
                    .font(.system(size: ResponsiveSizing.iconSize, weight: .bold))
            }
            .buttonStyle(ResponsiveSecondaryButtonStyle())

            // Super like button
            Button(action: superLike) {
                Image(systemName: "star.fill")
                    .font(.system(size: ResponsiveSizing.iconSize, weight: .bold))
            }
            .buttonStyle(ResponsivePrimaryButtonStyle())

            // Like button
            Button(action: likeCurrentUser) {
                Image(systemName: "heart.fill")
                    .font(.system(size: ResponsiveSizing.iconSize, weight: .bold))
            }
            .buttonStyle(ResponsivePrimaryButtonStyle())
        }
    }
}

struct SwipeDeckView_Previews: PreviewProvider {
    static var previews: some View {
        SwipeDeckView()
    }
}
