import SwiftUI

struct PremiumPaywallOverlay: View {
    let title: String
    let subtitle: String
    let onUpgrade: () -> Void
    let onDismiss: () -> Void

    @StateObject private var purchaseManager = PurchaseManager.shared
    @State private var animateGlow = false
    @State private var animateButton = false
    @State private var animateParticles = false
    @State private var animateShimmer = false
    @State private var pulseScale: CGFloat = 1.0
    @State private var showContent = false
    @State private var backgroundOffset: CGFloat = 0

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Use the same dynamic background as the rest of the app
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea(.all)
                    .overlay(
                        // Animated background overlay for depth
                        AnimatedBackground()
                            .opacity(0.4)
                            .offset(y: backgroundOffset)
                            .animation(.easeInOut(duration: 8).repeatForever(autoreverses: true), value: backgroundOffset)
                    )

                // Premium floating particles for luxury feel
                ForEach(0..<25, id: \.self) { index in
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.yellow.opacity(0.8),
                                    Color.orange.opacity(0.6),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 2,
                                endRadius: 15
                            )
                        )
                        .frame(width: CGFloat.random(in: 6...18))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .opacity(animateParticles ? 1.0 : 0.4)
                        .scaleEffect(animateParticles ? 1.6 : 0.8)
                        .animation(
                            .easeInOut(duration: Double.random(in: 2...5))
                            .repeatForever(autoreverses: true)
                            .delay(Double.random(in: 0...2)),
                            value: animateParticles
                        )
                }

                if DeviceInfo.isIPad {
                    // iPad: Centered modal-style layout
                    iPadPaywallLayout(geometry: geometry)
                } else {
                    // iPhone: Full-screen layout
                    iPhonePaywallLayout(geometry: geometry)
                }
            }
        }
        .onAppear {
            startAnimations()
        }
    }

    // MARK: - Layout Variants

    private func iPhonePaywallLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // Back button at the top
            HStack {
                backButton
                Spacer()
            }
            .padding(.horizontal, ResponsiveSpacing.lg)
            .padding(.top, max(10, geometry.safeAreaInsets.top + 10))

            // Minimal top spacing - moves content up significantly (1.5+ inches)
            Spacer()
                .frame(height: max(10, geometry.safeAreaInsets.top - 40))

            // Premium content card - full screen experience
            premiumContentCard
                .scaleEffect(showContent ? 1.0 : 0.9)
                .opacity(showContent ? 1.0 : 0.0)
                .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.1), value: showContent)

            // Minimal bottom spacing
            Spacer()
                .frame(height: max(20, geometry.safeAreaInsets.bottom + 10))
        }
    }

    private func iPadPaywallLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: ResponsiveSpacing.lg) {
            // Back button at the top for iPad
            HStack {
                backButton
                Spacer()
            }
            .padding(.horizontal, ResponsiveSpacing.lg)
            .padding(.top, ResponsiveSpacing.md)

            Spacer()

            // Centered modal card - much larger for iPad
            VStack(spacing: ResponsiveSpacing.lg) {
                premiumContentCard
                    .scaleEffect(showContent ? 1.0 : 0.9)
                    .opacity(showContent ? 1.0 : 0.0)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.1), value: showContent)
            }
            .frame(maxWidth: min(700, geometry.size.width * 0.9), maxHeight: geometry.size.height * 0.8)
            .background(paywallCardBackground)
            .overlay(premiumBorder)
            .shadow(color: Color.yellow.opacity(0.3), radius: 20, x: 0, y: 10)

            Spacer()

            // Dismiss button for iPad
            Button(action: onDismiss) {
                Text("Maybe Later")
                    .font(ResponsiveTypography.callout.weight(.medium))
                    .foregroundColor(.white.opacity(0.8))
                    .padding(.vertical, ResponsiveSpacing.sm)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.bottom, ResponsiveSpacing.lg)
        }
    }

    // MARK: - Premium Content Card (No Scroll - Single Screen)
    private var premiumContentCard: some View {
        VStack(spacing: DeviceInfo.isIPad ? ResponsiveSpacing.lg : ResponsiveSpacing.sm) {
            // Compact crown icon
            compactCrownIcon
                .padding(.top, ResponsiveSpacing.md)

            // Streamlined content
            VStack(spacing: ResponsiveSpacing.md) {
                Text(title)
                    .font(.system(size: DeviceInfo.isIPad ? 32 : 24, weight: .black, design: .rounded))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.yellow, Color.orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .multilineTextAlignment(.center)
                    .lineLimit(DeviceInfo.isIPad ? 3 : 2)
                    .shadow(color: Color.yellow.opacity(0.6), radius: 4, x: 0, y: 2)

                Text(subtitle)
                    .font(.system(size: DeviceInfo.isIPad ? 20 : 16, weight: .semibold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(DeviceInfo.isIPad ? 4 : 2)
                    .shadow(color: .black.opacity(0.6), radius: 3, x: 0, y: 1.5)
            }
            .responsivePadding(.horizontal)

            // Compact features grid (2x2)
            compactFeaturesGrid

            // Streamlined action buttons
            VStack(spacing: ResponsiveSpacing.md) {
                // Streamlined premium upgrade button
                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .heavy)
                    onUpgrade()
                }) {
                    HStack(spacing: ResponsiveSpacing.sm) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: DeviceInfo.isIPad ? 24 : 20, weight: .black))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.4), radius: 2, x: 0, y: 1)

                        VStack(spacing: ResponsiveSpacing.xs) {
                            Text("Unlock \(title)")
                                .font(.system(size: DeviceInfo.isIPad ? 22 : 18, weight: .black, design: .rounded))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.4), radius: 2, x: 0, y: 1)

                            Text(premiumSubtitleText)
                                .font(.system(size: DeviceInfo.isIPad ? 16 : 12, weight: .semibold))
                                .foregroundColor(.white.opacity(0.95))
                                .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                        }
                    }
                    .responsivePadding(.horizontal)
                    .padding(.vertical, DeviceInfo.isIPad ? ResponsiveSpacing.lg : ResponsiveSpacing.md)
                    .background(
                        RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                            .fill(
                                LinearGradient(
                                    colors: [Color.yellow, Color.orange],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(color: Color.yellow.opacity(0.6), radius: 12, x: 0, y: 6)
                    )
                    .scaleEffect(animateButton ? 0.98 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: animateButton)
                }
                .onTapGesture {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    withAnimation(.easeInOut(duration: 0.1)) {
                        animateButton.toggle()
                    }
                }
            }
            .responsivePadding(.horizontal)
        }
        .padding(.horizontal, AppTheme.spacing16)
        .padding(.vertical, AppTheme.spacing12)
    }

    // MARK: - Animation Functions
    private func startAnimations() {
        // Background animation
        backgroundOffset = -20

        // Content entrance
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7).delay(0.1)) {
            showContent = true
        }

        // Start all animations with staggered timing for dramatic effect
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            animateGlow = true
        }

        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true).delay(0.5)) {
            animateShimmer = true
        }

        withAnimation(.easeInOut(duration: 1.8).repeatForever(autoreverses: true).delay(0.2)) {
            animateParticles = true
        }

        withAnimation(.easeInOut(duration: 1.2).repeatForever(autoreverses: true).delay(0.8)) {
            pulseScale = 1.05
        }

        withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3)) {
            animateButton = true
        }
    }

    // MARK: - Back Button
    private var backButton: some View {
        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            onDismiss()
        }) {
            HStack(spacing: ResponsiveSpacing.xs) {
                Image(systemName: "chevron.left")
                    .font(.system(size: ResponsiveTypography.fontSize(16), weight: .semibold))
                    .foregroundColor(.white)

                Text("Back")
                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, ResponsiveSpacing.sm)
            .padding(.vertical, ResponsiveSpacing.xs)
            .background(
                RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                    .fill(.ultraThinMaterial.opacity(0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Compact Crown Icon
    private var compactCrownIcon: some View {
        let iconSize = DeviceInfo.isIPad ? 100 : 70
        let crownSize = DeviceInfo.isIPad ? 40 : 28

        return ZStack {
            // Subtle glow
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.yellow.opacity(0.3),
                            Color.orange.opacity(0.2),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: DeviceInfo.isIPad ? 15 : 10,
                        endRadius: DeviceInfo.isIPad ? 50 : 35
                    )
                )
                .frame(width: CGFloat(iconSize), height: CGFloat(iconSize))
                .scaleEffect(animateGlow ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateGlow)

            // Crown icon
            Image(systemName: "crown.fill")
                .font(.system(size: CGFloat(crownSize), weight: .black))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.yellow, Color.orange],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.yellow.opacity(0.6), radius: 6, x: 0, y: 3)
        }
    }

    // MARK: - Compact Features Grid
    private var compactFeaturesGrid: some View {
        let columns = DeviceInfo.isIPad ? 4 : 2

        return LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: ResponsiveSpacing.sm), count: columns), spacing: ResponsiveSpacing.sm) {
            compactFeatureItem(icon: "magnifyingglass.circle.fill", text: "Advanced Search")
            compactFeatureItem(icon: "infinity.circle.fill", text: "Unlimited Swipes")
            compactFeatureItem(icon: "crown.fill", text: "Top Matches")
            compactFeatureItem(icon: "sparkles", text: "Lifetime Access")
        }
        .responsivePadding()
        .background(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.yellow.opacity(0.4),
                                    Color.orange.opacity(0.3)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
        )
    }

    private func compactFeatureItem(icon: String, text: String) -> some View {
        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [Color.yellow, Color.orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 20, height: 20)

                Text(text)
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
            }
            .padding(.horizontal, AppTheme.spacing8)
            .padding(.vertical, AppTheme.spacing6)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(Color.black.opacity(0.2))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onTapGesture {
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
    }



    // MARK: - Computed Properties
    private var premiumSubtitleText: String {
        if let product = purchaseManager.getLifetimePremiumProduct() {
            return "\(product.displayPrice) • Lifetime access"
        } else {
            return "One-time payment • Lifetime access"
        }
    }

    // MARK: - Styling Components
    private var premiumGradient: LinearGradient {
        LinearGradient(
            colors: [Color.yellow, Color.orange, Color.yellow],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    private var premiumButtonGradient: LinearGradient {
        LinearGradient(
            colors: [Color.yellow, Color.orange],
            startPoint: .leading,
            endPoint: .trailing
        )
    }

    private var paywallCardBackground: some View {
        ZStack {
            // Base background with beautiful rounded corners
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)

            // Premium overlay with golden glow
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.yellow.opacity(0.1),
                            Color.orange.opacity(0.05),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.yellow.opacity(animateGlow ? 0.8 : 0.6),
                        Color.orange.opacity(animateGlow ? 0.6 : 0.4),
                        Color.yellow.opacity(animateGlow ? 0.4 : 0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 3
            )
            .shadow(color: Color.yellow.opacity(0.4), radius: 8, x: 0, y: 0)
            .shadow(color: Color.orange.opacity(0.3), radius: 16, x: 0, y: 0)
            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateGlow)
    }
}

// MARK: - Preview
struct PremiumPaywallOverlay_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            AppTheme.backgroundGradient
                .ignoresSafeArea()

            PremiumPaywallOverlay(
                title: "Premium Feature",
                subtitle: "Upgrade to premium to unlock this exclusive feature",
                onUpgrade: { print("Upgrade tapped") },
                onDismiss: { print("Dismiss tapped") }
            )
        }
    }
}
