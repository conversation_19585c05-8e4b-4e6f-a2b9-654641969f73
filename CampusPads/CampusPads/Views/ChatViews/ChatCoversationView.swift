import SwiftUI
import PhotosUI
import AVFoundation
import FirebaseAuth
import FirebaseFirestore

struct ChatConversationView: View {
    @StateObject var viewModel: ChatConversationViewModel
    var chatPartnerID: String?
    /// Optional match ID; only available after a match is made.
    var matchID: String? = nil

    @State private var messageText: String = ""
    @State private var showAgreementSheet: Bool = false
    @State private var showChatOptionsMenu: Bool = false
    @State private var showReportSheet: Bool = false
    @State private var showProfileView: Bool = false
    @State private var showUnmatchConfirmation: Bool = false
    @State private var showBlockConfirmation: Bool = false

    // Media sharing states
    @State private var showImagePicker: Bool = false
    @State private var showCamera: Bool = false
    @State private var showDocumentPicker: Bool = false
    @State private var selectedImage: UIImage?
    @State private var isUploadingMedia: Bool = false
    @ObservedObject private var profileLoader = ProfileLoaderService.shared
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Enhanced background with subtle animated gradient
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea()

                // Subtle animated overlay for depth
                AnimatedBackground()
                    .opacity(0.2)
                    .ignoresSafeArea()

                if DeviceInfo.isIPad && DeviceInfo.isLandscape {
                    // iPad Landscape: Optimized layout
                    iPadLandscapeLayout(geometry: geometry)
                } else {
                    // iPhone and iPad Portrait: Standard layout
                    standardChatLayout(geometry: geometry)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.markMessagesAsRead()
            // OPTIMIZED: Load partner profile for top bar with UI updates
            if let partnerID = chatPartnerID {
                print("🚀 ChatConversationView: Loading profile for partner: \(partnerID)")

                // Check cache first for instant display
                if let cachedProfile = ProfileLoaderService.shared.getCachedProfile(userID: partnerID) {
                    print("⚡ ChatConversationView: Using cached profile for \(cachedProfile.firstName ?? "Unknown")")
                    profileLoader.user = cachedProfile
                } else {
                    // Load with UI updates for loading state
                    profileLoader.loadProfileWithUIUpdates(candidateID: partnerID)
                }
            }
        }
        .alert(item: errorBinding) { alertError in
            Alert(
                title: Text("Error"),
                message: Text(alertError.message),
                dismissButton: .default(Text("OK"))
            )
        }

        // Present AgreementView - Always available for chat partners
        .sheet(isPresented: $showAgreementSheet) {
            if let partnerID = chatPartnerID,
               let currentUserID = ProfileViewModel.shared.userProfile?.id {
                // Use chat ID as agreement ID if no match ID available
                let agreementID = matchID ?? viewModel.chatID
                AgreementView(matchID: agreementID, userA: currentUserID, userB: partnerID)
            } else {
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)

                    Text("Unable to Create Agreement")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Please try again later.")
                        .foregroundColor(.secondary)

                    Button("Close") {
                        showAgreementSheet = false
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            }
        }
        // Present ReportUserView
        .sheet(isPresented: $showReportSheet) {
            if let partnerID = chatPartnerID {
                ReportUserView(reportedUserID: partnerID)
            } else {
                Text("Unable to report user.")
            }
        }
        // Present TinderStyleUserProfileSheet
        .fullScreenCover(isPresented: $showProfileView) {
            if let user = profileLoader.user {
                TinderStyleUserProfileSheet(
                    user: user,
                    onStartChat: nil, // No chat creation needed since we're already in a chat
                    onDismiss: {
                        showProfileView = false
                    }
                )
            } else {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                        .tint(AppTheme.primaryColor)

                    Text("Loading profile...")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(AppTheme.backgroundGradient)
            }
        }
        // Media sharing sheets
        .sheet(isPresented: $showImagePicker) {
            ChatImagePicker(selectedImage: $selectedImage, sourceType: .photoLibrary)
        }
        .sheet(isPresented: $showCamera) {
            ChatImagePicker(selectedImage: $selectedImage, sourceType: .camera)
        }
        .onChange(of: selectedImage) { _, image in
            if let image = image {
                uploadImage(image)
            }
        }
        // Unmatch confirmation dialog
        .alert("Unmatch User", isPresented: $showUnmatchConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Unmatch", role: .destructive) {
                unmatchUser()
            }
        } message: {
            Text("Are you sure you want to unmatch? This will remove the match and you won't be able to message each other anymore.")
        }
        // Block confirmation dialog
        .alert("Block User", isPresented: $showBlockConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Block", role: .destructive) {
                blockUser()
            }
        } message: {
            Text("Are you sure you want to block this user? They won't be able to contact you and you won't see them in discovery.")
        }
    }
}

extension ChatConversationView {

    // MARK: - Layout Variants

    private func standardChatLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // Enhanced custom top bar
            enhancedTopBar

            messagesScrollView

            // Enhanced ChatInputBar with media sharing
            ChatInputBar(messageText: $messageText, onSend: sendMessage, onMediaSelected: { mediaOption in
                handleMediaSelection(mediaOption)
            })
            .padding(.bottom, ResponsiveSpacing.xs)
        }
    }

    private func iPadLandscapeLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // Enhanced custom top bar
            enhancedTopBar

            HStack(spacing: 0) {
                // Messages area - takes most of the space
                messagesScrollView
                    .frame(maxWidth: LayoutHelpers.maxContentWidth)

                // Side panel for additional info (optional)
                if DeviceInfo.screenSize == .iPadPro12_9 {
                    VStack {
                        // Could add user info, shared media, etc.
                        Spacer()
                    }
                    .frame(width: 200)
                    .background(AppTheme.glassEffect.opacity(0.3))
                }
            }

            // Enhanced ChatInputBar with media sharing
            ChatInputBar(messageText: $messageText, onSend: sendMessage, onMediaSelected: { mediaOption in
                handleMediaSelection(mediaOption)
            })
            .limitContentWidth()
            .padding(.bottom, ResponsiveSpacing.xs)
        }
    }

    // MARK: - Enhanced Top Bar
    private var enhancedTopBar: some View {
        VStack(spacing: 0) {
            // Main top bar content
            HStack(spacing: ResponsiveSpacing.md) {
                // Back button with modern styling and proper navigation
                Button(action: {
                    // Handle back navigation properly - try both methods for compatibility
                    if #available(iOS 15.0, *) {
                        dismiss()
                    } else {
                        presentationMode.wrappedValue.dismiss()
                    }
                }) {
                    ZStack {
                        Circle()
                            .fill(AppTheme.glassEffect)
                            .frame(width: ResponsiveSizing.buttonHeight * 0.7, height: ResponsiveSizing.buttonHeight * 0.7)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )

                        Image(systemName: "chevron.left")
                            .font(.system(size: ResponsiveSizing.iconSize * 0.9, weight: .semibold))
                            .foregroundStyle(AppTheme.sexyGradient)
                    }
                }
                .buttonStyle(PlainButtonStyle())

                // Enhanced profile section - larger and tappable
                Button(action: {
                    showProfileView = true
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                }) {
                    HStack(spacing: ResponsiveSpacing.md) {
                        // Profile image with beautiful styling - larger size
                        ZStack {
                            if let imageUrl = profileLoader.user?.profileImageUrls?.first,
                               let url = URL(string: imageUrl) {
                                AsyncImage(url: url) { image in
                                    image
                                        .resizable()
                                        .scaledToFill()
                                } placeholder: {
                                    ZStack {
                                        Circle()
                                            .fill(AppTheme.sexyGradient)

                                        ProgressView()
                                            .tint(.white)
                                            .scaleEffect(0.8)
                                    }
                                    .shimmerEffect()
                                }
                            } else {
                                ZStack {
                                    Circle()
                                        .fill(AppTheme.sexyGradient)

                                    Image(systemName: "person.fill")
                                        .font(.system(size: 24, weight: .medium))
                                        .foregroundColor(.white)
                                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                                }
                                .shimmerEffect()
                            }
                        }
                        .frame(width: ResponsiveSizing.profileImageMedium, height: ResponsiveSizing.profileImageMedium)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.white.opacity(0.8),
                                            AppTheme.primaryColor.opacity(0.4),
                                            Color.white.opacity(0.2)
                                        ]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 2
                                )
                        )
                        .neonGlow(color: AppTheme.primaryColor)

                        // Name and status with beautiful typography - larger spacing
                        VStack(alignment: .leading, spacing: 4) {
                            Text(profileLoader.user?.firstName ?? "Loading...")
                                .font(ResponsiveTypography.title3.weight(.bold))
                                .foregroundStyle(AppTheme.sexyGradient)
                                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)

                            HStack(spacing: ResponsiveSpacing.xs) {
                                Circle()
                                    .fill(AppTheme.successColor)
                                    .frame(width: ResponsiveSpacing.spacing(10), height: ResponsiveSpacing.spacing(10))
                                    .shadow(color: AppTheme.successColor.opacity(0.6), radius: 2, x: 0, y: 1)

                                Text("Active now")
                                    .font(ResponsiveTypography.subheadline.weight(.medium))
                                    .foregroundColor(AppTheme.textSecondary)
                            }
                        }

                        Spacer()
                    }
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()

                // Enhanced three-dot menu with roommate options
                Button(action: {
                    showChatOptionsMenu = true
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                }) {
                    ZStack {
                        Circle()
                            .fill(AppTheme.glassEffect)
                            .frame(width: 40, height: 40)
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )

                        Image(systemName: "ellipsis")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundStyle(AppTheme.sexyGradient)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .confirmationDialog("Chat Options", isPresented: $showChatOptionsMenu, titleVisibility: .visible) {
                    // Roommate Agreement - Always available for matched users
                    Button("📋 Create Roommate Agreement") {
                        showAgreementSheet = true
                    }

                    // Unmatch option
                    Button("💔 Unmatch", role: .destructive) {
                        showUnmatchConfirmation = true
                    }

                    // Block option
                    Button("🚫 Block User", role: .destructive) {
                        showBlockConfirmation = true
                    }

                    // Report option
                    Button("⚠️ Report User", role: .destructive) {
                        showReportSheet = true
                    }

                    Button("Cancel", role: .cancel) { }
                }
            }
            .padding(.horizontal, AppTheme.spacing20)
            .padding(.vertical, AppTheme.spacing16) // Increased vertical padding for less compression
            .background(
                ZStack {
                    // Glass morphism background
                    AppTheme.glassEffect

                    // Subtle gradient overlay
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.1),
                            Color.clear
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                }
            )
            .overlay(
                // Bottom border with gradient
                VStack {
                    Spacer()
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.clear,
                                    AppTheme.primaryColor.opacity(0.3),
                                    AppTheme.accentColor.opacity(0.2),
                                    Color.clear
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(height: 1)
                }
            )
        }
    }

    private var messagesScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(viewModel.messages) { msg in
                        MessageBubble(
                            message: msg,
                            isCurrentUser: msg.senderID == viewModel.currentUserID,
                            onReact: { emoji in
                                viewModel.addReaction(to: msg.id ?? "", emoji: emoji)
                            }
                        )
                        .id(msg.id)
                    }
                    if viewModel.isTyping {
                        HStack {
                            ZStack {
                                Capsule()
                                    .fill(AppTheme.glassEffect)
                                    .frame(width: 60, height: 32)
                                    .overlay(
                                        Capsule()
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                    )

                                TypingIndicatorView()
                            }
                            Spacer()
                        }
                        .padding(.leading, AppTheme.spacing16)
                    }
                }
                .padding(.horizontal, AppTheme.spacing16)
                .padding(.top, AppTheme.spacing8)
            }
            .onChange(of: viewModel.messages.count) { _, _ in
                scrollToLastMessage(proxy: proxy)
            }
        }
    }

    // Removed old toolbar content - using custom enhancedTopBar instead

    private var errorBinding: Binding<GenericAlertError?> {
        Binding(
            get: {
                if let errorMessage = viewModel.errorMessage {
                    return GenericAlertError(message: errorMessage)
                }
                return nil
            },
            set: { _ in viewModel.errorMessage = nil }
        )
    }

    // alertContent function removed - now using inline alert closure

    private func sendMessage() {
        let trimmed = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return }
        viewModel.sendMessage(text: trimmed)
        messageText = ""
    }

    private func scrollToLastMessage(proxy: ScrollViewProxy) {
        if let lastMessage = viewModel.messages.last, let id = lastMessage.id {
            withAnimation {
                proxy.scrollTo(id, anchor: .bottom)
            }
        }
    }

    // MARK: - Media Handling
    private func handleMediaSelection(_ option: MediaOption) {
        switch option {
        case .camera:
            checkCameraPermission()
        case .photoLibrary:
            showImagePicker = true
        case .document:
            showDocumentPicker = true
        }
    }

    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            showCamera = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.showCamera = true
                    }
                }
            }
        case .denied, .restricted:
            // Show alert to go to settings
            break
        @unknown default:
            break
        }
    }

    private func uploadImage(_ image: UIImage) {
        print("📸 Uploading image...")
        isUploadingMedia = true

        // Send image message using the view model
        viewModel.sendImageMessage(image)

        // Reset selected image
        selectedImage = nil
        isUploadingMedia = false
    }

    // MARK: - User Actions
    private func unmatchUser() {
        guard let currentUserID = ProfileViewModel.shared.userProfile?.id,
              let partnerID = chatPartnerID else {
            print("❌ Missing user IDs for unmatch")
            return
        }

        Task {
            do {
                // Remove match from Firestore
                let db = Firestore.firestore()

                // Delete match document
                if let matchID = matchID {
                    try await db.collection("matches").document(matchID).delete()
                }

                // Remove from user's matches list
                try await db.collection("users").document(currentUserID).updateData([
                    "matches": FieldValue.arrayRemove([partnerID])
                ])

                // Remove from partner's matches list
                try await db.collection("users").document(partnerID).updateData([
                    "matches": FieldValue.arrayRemove([currentUserID])
                ])

                // Delete chat conversation
                try await db.collection("chats").document(viewModel.chatID).delete()

                print("✅ Successfully unmatched user")

                // Navigate back
                DispatchQueue.main.async {
                    if #available(iOS 15.0, *) {
                        dismiss()
                    } else {
                        presentationMode.wrappedValue.dismiss()
                    }
                }

            } catch {
                print("❌ Failed to unmatch user: \(error)")
                viewModel.errorMessage = "Failed to unmatch user. Please try again."
            }
        }
    }

    private func blockUser() {
        guard let currentUserID = ProfileViewModel.shared.userProfile?.id,
              let partnerID = chatPartnerID else {
            print("❌ Missing user IDs for block")
            return
        }

        Task {
            do {
                let db = Firestore.firestore()

                print("🚫 Starting comprehensive block process for user: \(partnerID)")

                // 1. Add to current user's blocked list (prevents them from seeing blocked user)
                try await db.collection("users").document(currentUserID).updateData([
                    "blockedUserIDs": FieldValue.arrayUnion([partnerID])
                ])
                print("✅ Added \(partnerID) to current user's blocked list")

                // 2. Add current user to blocked user's blocked list (mutual blocking for complete isolation)
                try await db.collection("users").document(partnerID).updateData([
                    "blockedUserIDs": FieldValue.arrayUnion([currentUserID])
                ])
                print("✅ Added current user to \(partnerID)'s blocked list (mutual block)")

                // 3. Remove match if exists
                if let matchID = matchID {
                    try await db.collection("matches").document(matchID).delete()
                    print("✅ Deleted match document: \(matchID)")
                }

                // 4. Remove from both users' matches lists
                try await db.collection("users").document(currentUserID).updateData([
                    "matches": FieldValue.arrayRemove([partnerID])
                ])

                try await db.collection("users").document(partnerID).updateData([
                    "matches": FieldValue.arrayRemove([currentUserID])
                ])
                print("✅ Removed from both users' matches lists")

                // 5. Delete chat conversation completely
                try await db.collection("chats").document(viewModel.chatID).delete()
                print("✅ Deleted chat conversation")

                // 6. Update local ProfileViewModel to immediately reflect the block
                DispatchQueue.main.async {
                    if var currentProfile = ProfileViewModel.shared.userProfile {
                        var blockedUsers = currentProfile.blockedUserIDs ?? []
                        if !blockedUsers.contains(partnerID) {
                            blockedUsers.append(partnerID)
                            currentProfile.blockedUserIDs = blockedUsers
                            ProfileViewModel.shared.userProfile = currentProfile
                        }
                    }
                }

                print("✅ Successfully blocked user with comprehensive coverage")
                print("   - User will not appear in discovery")
                print("   - User will not appear in search results")
                print("   - Mutual blocking prevents any future interactions")
                print("   - All existing connections removed")

                // Navigate back
                DispatchQueue.main.async {
                    if #available(iOS 15.0, *) {
                        dismiss()
                    } else {
                        presentationMode.wrappedValue.dismiss()
                    }
                }

            } catch {
                print("❌ Failed to block user: \(error)")
                viewModel.errorMessage = "Failed to block user. Please try again."
            }
        }
    }
}

// MARK: - Typing Indicator Component
struct TypingIndicatorView: View {
    @State private var isAnimating = false

    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(AppTheme.primaryColor)
                    .frame(width: 6, height: 6)
                    .scaleEffect(isAnimating ? 1.3 : 1.0)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever()
                        .delay(Double(index) * 0.2),
                        value: isAnimating
                    )
            }
        }
        .onAppear {
            isAnimating = true
        }
        .onDisappear {
            isAnimating = false
        }
    }
}

struct ChatConversationView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ChatConversationView(
                viewModel: ChatConversationViewModel(chatID: "dummyChatID"),
                chatPartnerID: "dummyPartnerID",
                matchID: "dummyMatchID"
            )
        }
    }
}
