//
//  DataMigrationService.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseFirestore
import FirebaseAuth

/// Service for handling data migrations and legacy field updates
class DataMigrationService {
    static let shared = DataMigrationService()
    
    private let db = Firestore.firestore()
    
    private init() {}
    
    /// CRITICAL FIX: Migrate legacy isProfilePublic to hideFromDiscovery
    /// This ensures all users have proper discovery visibility settings
    func migrateDiscoveryVisibilitySettings() {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            print("❌ DataMigrationService: User not authenticated")
            return
        }
        
        print("🔄 DataMigrationService: Checking discovery visibility settings for user: \(currentUserID)")
        
        let userRef = db.collection("users").document(currentUserID)
        
        userRef.getDocument { [weak self] snapshot, error in
            if let error = error {
                print("❌ DataMigrationService: Failed to fetch user document: \(error.localizedDescription)")
                return
            }
            
            guard let document = snapshot, document.exists,
                  let data = document.data() else {
                print("❌ DataMigrationService: User document does not exist")
                return
            }
            
            self?.processDiscoveryVisibilityMigration(userRef: userRef, userData: data)
        }
    }
    
    private func processDiscoveryVisibilityMigration(userRef: DocumentReference, userData: [String: Any]) {
        let hideFromDiscovery = userData["hideFromDiscovery"] as? Bool
        let isProfilePublic = userData["isProfilePublic"] as? Bool
        
        print("🔍 DataMigrationService: Current settings:")
        print("   - hideFromDiscovery: \(hideFromDiscovery?.description ?? "nil")")
        print("   - isProfilePublic: \(isProfilePublic?.description ?? "nil")")
        
        var needsUpdate = false
        var updateData: [String: Any] = [:]
        
        // CRITICAL FIX: Ensure hideFromDiscovery is properly set
        if hideFromDiscovery == nil {
            // If hideFromDiscovery is not set, derive it from isProfilePublic
            let shouldHideFromDiscovery: Bool
            
            if let isPublic = isProfilePublic {
                // If user explicitly set profile to private, hide from discovery
                // If user set profile to public, show in discovery
                shouldHideFromDiscovery = !isPublic
                print("🔄 DataMigrationService: Deriving hideFromDiscovery from isProfilePublic: \(!isPublic)")
            } else {
                // If neither field is set, default to visible in discovery (false = not hidden)
                shouldHideFromDiscovery = false
                print("🔄 DataMigrationService: Setting default hideFromDiscovery: false (visible)")
            }
            
            updateData["hideFromDiscovery"] = shouldHideFromDiscovery
            needsUpdate = true
        }
        
        // Ensure isEmailVerified is set (required for discovery)
        if userData["isEmailVerified"] == nil {
            print("🔄 DataMigrationService: Setting default isEmailVerified: false")
            updateData["isEmailVerified"] = false
            needsUpdate = true
        }
        
        // Apply updates if needed
        if needsUpdate {
            print("🔄 DataMigrationService: Updating user document with: \(updateData)")
            
            userRef.updateData(updateData) { error in
                if let error = error {
                    print("❌ DataMigrationService: Failed to update user document: \(error.localizedDescription)")
                } else {
                    print("✅ DataMigrationService: Successfully updated discovery visibility settings")
                    
                    // Log final state
                    let finalHideFromDiscovery = updateData["hideFromDiscovery"] as? Bool ?? hideFromDiscovery ?? false
                    if finalHideFromDiscovery {
                        print("   - User is HIDDEN from discovery")
                    } else {
                        print("   - User is VISIBLE in discovery")
                    }
                }
            }
        } else {
            print("✅ DataMigrationService: Discovery visibility settings are already correct")
            
            // Log current state
            let currentHideFromDiscovery = hideFromDiscovery ?? false
            if currentHideFromDiscovery {
                print("   - User is HIDDEN from discovery")
            } else {
                print("   - User is VISIBLE in discovery")
            }
        }
    }
    
    /// Check if current user should be visible in discovery
    func checkCurrentUserDiscoveryStatus(completion: @escaping (Bool) -> Void) {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            print("❌ DataMigrationService: User not authenticated")
            completion(false)
            return
        }
        
        db.collection("users").document(currentUserID).getDocument { snapshot, error in
            if let error = error {
                print("❌ DataMigrationService: Failed to check discovery status: \(error.localizedDescription)")
                completion(false)
                return
            }
            
            guard let document = snapshot, document.exists,
                  let data = document.data() else {
                print("❌ DataMigrationService: User document does not exist")
                completion(false)
                return
            }
            
            let isEmailVerified = data["isEmailVerified"] as? Bool ?? false
            let hideFromDiscovery = data["hideFromDiscovery"] as? Bool ?? false
            
            let isDiscoverable = isEmailVerified && !hideFromDiscovery
            
            print("🔍 DataMigrationService: Discovery status check:")
            print("   - Email verified: \(isEmailVerified)")
            print("   - Hidden from discovery: \(hideFromDiscovery)")
            print("   - Is discoverable: \(isDiscoverable)")
            
            completion(isDiscoverable)
        }
    }
    
    /// Force enable discovery for current user (for testing)
    func enableDiscoveryForCurrentUser(completion: @escaping (Bool) -> Void) {
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            print("❌ DataMigrationService: User not authenticated")
            completion(false)
            return
        }
        
        print("🔄 DataMigrationService: Force enabling discovery for user: \(currentUserID)")
        
        let updateData: [String: Any] = [
            "hideFromDiscovery": false,
            "isEmailVerified": true
        ]
        
        db.collection("users").document(currentUserID).updateData(updateData) { error in
            if let error = error {
                print("❌ DataMigrationService: Failed to enable discovery: \(error.localizedDescription)")
                completion(false)
            } else {
                print("✅ DataMigrationService: Successfully enabled discovery for user")
                completion(true)
            }
        }
    }
}
