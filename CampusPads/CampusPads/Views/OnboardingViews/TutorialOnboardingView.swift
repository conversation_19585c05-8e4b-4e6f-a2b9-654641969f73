import SwiftUI

/// Data model for tutorial pages.
struct OnboardingPage {
    let title: String
    let description: String
    let imageName: String
}

/// TutorialOnboardingView – displays pages 0–2.
/// This view is shown when no user is signed in.
struct TutorialOnboardingView: View {
    @Environment(\.presentationMode) var presentationMode

    // For this part, only the tutorial content is needed.
    private let tutorialPages: [OnboardingPage] = [
        OnboardingPage(title: "Welcome to CampusPads",
                       description: "The modern way to find your perfect college roommate. Swipe, match, and connect with compatible students!",
                       imageName: "graduationcap.fill"),
        OnboardingPage(title: "Smart Matching",
                       description: "Our algorithm matches you based on lifestyle, budget, and housing preferences. Swipe right to like, left to pass!",
                       imageName: "heart.fill"),
        OnboardingPage(title: "Safe & Secure",
                       description: "Chat safely with verified users. All profiles are verified with email addresses for your security.",
                       imageName: "shield.checkered")
    ]

    // Tracks current page.
    @State private var currentPage: Int = 0

    var body: some View {
        ZStack {
            // Sexy animated background
            OnboardingAnimatedBackground()

            VStack(spacing: 0) {
                // Skip button
                HStack {
                    Spacer()
                    Button("Skip") {
                        withAnimation(.easeOut(duration: 0.3)) {
                            presentationMode.wrappedValue.dismiss()
                        }
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                    }
                    .buttonStyle(TertiaryButtonStyle())
                    .padding(.top, AppTheme.spacing16)
                    .padding(.trailing, AppTheme.spacing20)
                }
                // App branding header
                VStack(spacing: AppTheme.spacing20) {
                    Spacer()

                    VStack(spacing: AppTheme.spacing8) {
                        Text("CampusPads")
                            .font(.custom("AvenirNext-Bold", size: 28))
                            .foregroundStyle(AppTheme.sexyGradient)

                        Text("Find Your Perfect Roommate")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    Spacer()
                }
                .frame(maxHeight: 150)

                // Tutorial content
                TabView(selection: $currentPage) {
                    ForEach(0..<tutorialPages.count, id: \.self) { index in
                        OnboardingCard {
                            VStack(spacing: AppTheme.spacing24) {
                                // Enhanced icon with gradient background
                                ZStack {
                                    Circle()
                                        .fill(AppTheme.primaryGradient)
                                        .frame(width: 120, height: 120)
                                        .blur(radius: 20)
                                        .opacity(0.6)

                                    Image(systemName: tutorialPages[index].imageName)
                                        .font(.system(size: 50, weight: .medium))
                                        .foregroundColor(.white)
                                }

                                VStack(spacing: AppTheme.spacing12) {
                                    Text(tutorialPages[index].title)
                                        .font(.custom("AvenirNext-Bold", size: 24))
                                        .foregroundStyle(AppTheme.sexyGradient)
                                        .multilineTextAlignment(.center)

                                    Text(tutorialPages[index].description)
                                        .font(AppTheme.body)
                                        .foregroundColor(AppTheme.textSecondary)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal, AppTheme.spacing16)
                                }
                            }
                        }
                        .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(maxHeight: 400)

                // Navigation controls
                VStack(spacing: AppTheme.spacing20) {
                    // Sexy custom page indicators
                    HStack(spacing: AppTheme.spacing12) {
                        ForEach(0..<tutorialPages.count, id: \.self) { index in
                            ZStack {
                                // Background circle
                                Circle()
                                    .fill(AppTheme.textTertiary.opacity(0.2))
                                    .frame(width: 12, height: 12)

                                // Active indicator with gradient
                                if index == currentPage {
                                    Circle()
                                        .fill(AppTheme.primaryGradient)
                                        .frame(width: 12, height: 12)
                                        .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 4, x: 0, y: 2)
                                } else {
                                    Circle()
                                        .fill(AppTheme.textTertiary.opacity(0.4))
                                        .frame(width: 8, height: 8)
                                }
                            }
                            .scaleEffect(index == currentPage ? 1.0 : 0.8)
                            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: currentPage)
                        }
                    }
                    .padding(.top, AppTheme.spacing20)

                    // Navigation buttons
                    HStack(spacing: AppTheme.spacing16) {
                        if currentPage > 0 {
                            Button("Back") {
                                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                    currentPage -= 1
                                }
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                            .buttonStyle(SecondaryButtonStyle())
                            .frame(maxWidth: .infinity)
                        }

                        Button(currentPage == tutorialPages.count - 1 ? "Get Started" : "Next") {
                            if currentPage == tutorialPages.count - 1 {
                                // Properly dismiss the tutorial onboarding
                                // This will return the user to the AuthenticationView
                                withAnimation(.easeOut(duration: 0.3)) {
                                    presentationMode.wrappedValue.dismiss()
                                }
                                HapticFeedbackManager.shared.generateNotification(.success)
                            } else {
                                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                    currentPage += 1
                                }
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                        }
                        .buttonStyle(PrimaryButtonStyle())
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing32)
                }
            }
        }
        .navigationBarHidden(true)
    }
}

struct TutorialOnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        TutorialOnboardingView()
    }
}
