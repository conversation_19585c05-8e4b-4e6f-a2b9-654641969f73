//
//  TinderStyleMatchProfileSheet.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI
import FirebaseAuth

// MARK: - Enhanced Tinder-Style Match Profile Sheet
struct TinderStyleMatchProfileSheet: View {
    let match: MatchItem
    let onStartChat: (String) -> Void
    let onDismiss: () -> Void

    @State private var profile: UserModel?
    @State private var isLoading = true
    @State private var currentImageIndex = 0
    @State private var loadingError: String?
    @Environment(\.dismiss) private var dismiss

    private var candidateID: String {
        guard let currentUID = Auth.auth().currentUser?.uid else {
            print("❌ TinderStyleMatchProfileSheet: No authenticated user found")
            return "unknown"
        }
        let candidate = match.participants.first(where: { $0 != currentUID }) ?? "unknown"
        print("🔍 TinderStyleMatchProfileSheet: Extracted candidateID: \(candidate) from participants: \(match.participants)")
        return candidate
    }
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    AppTheme.backgroundGradient.ignoresSafeArea()

                    if isLoading {
                        loadingView
                    } else if let profile = profile {
                        ScrollView {
                            AdaptiveContainer {
                                VStack(spacing: ResponsiveSpacing.lg) {
                                    // Hero Image Section
                                    heroImageSection(profile: profile)

                                    // Profile Content
                                    VStack(spacing: ResponsiveSpacing.lg) {
                                        // Basic Info
                                        basicInfoSection(profile: profile)

                                        // About Me
                                        if let aboutMe = profile.aboutMe, !aboutMe.isEmpty {
                                            aboutMeSection(aboutMe: aboutMe)
                                        }

                                        // Academic Info
                                        academicInfoSection(profile: profile)

                                        // Housing Preferences
                                        housingPreferencesSection(profile: profile)

                                        // Lifestyle & Interests
                                        lifestyleSection(profile: profile)

                                        // Action Buttons
                                        actionButtonsSection
                                    }
                                    .responsivePadding(.horizontal)
                                    .padding(.top, ResponsiveSpacing.lg)
                                    .padding(.bottom, ResponsiveSpacing.xxl)
                                }
                            }
                        }
                    } else {
                        enhancedErrorView
                    }
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .navigationTitle("New Match")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                if DeviceInfo.isIPad {
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        onDismiss()
                    }) {
                        HStack(spacing: ResponsiveSpacing.xs) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: ResponsiveTypography.fontSize(16), weight: .semibold))
                                .foregroundColor(AppTheme.primaryColor)

                            Text("Back")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Close") {
                    onDismiss()
                }
                .foregroundColor(AppTheme.primaryColor)
            }
        }
        .onAppear {
            loadProfileWithEnhancedLogging()
        }
    }
    
    // MARK: - Helper Views
    
    private var loadingView: some View {
        VStack(spacing: ResponsiveSpacing.lg) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: DeviceInfo.isIPad ? 80 : 60, height: DeviceInfo.isIPad ? 80 : 60)

                ProgressView()
                    .tint(.white)
                    .scaleEffect(DeviceInfo.isIPad ? 1.5 : 1.2)
            }

            Text("Loading profile...")
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                .foregroundColor(AppTheme.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundGradient)
    }
    
    private var enhancedErrorView: some View {
        VStack(spacing: ResponsiveSpacing.lg) {
            Image(systemName: "person.crop.circle.badge.exclamationmark")
                .font(.system(size: DeviceInfo.isIPad ? 80 : 60))
                .foregroundColor(AppTheme.textSecondary)

            VStack(spacing: ResponsiveSpacing.sm) {
                Text("Profile Loading Failed")
                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Text(loadingError ?? "This profile may have been removed or is temporarily unavailable.")
                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(5)
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .medium)
                    retryProfileLoad()
                }) {
                    HStack(spacing: ResponsiveSpacing.xs) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: ResponsiveTypography.fontSize(14), weight: .medium))
                        Text("Try Again")
                            .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                    }
                    .foregroundColor(.white)
                    .responsivePadding(.horizontal)
                    .padding(.vertical, ResponsiveSpacing.sm)
                    .background(AppTheme.primaryColor)
                    .cornerRadius(ResponsiveRadius.medium)
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    onDismiss()
                }) {
                    Text("Go Back")
                        .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                        .foregroundColor(AppTheme.textSecondary)
                        .responsivePadding(.horizontal)
                        .padding(.vertical, ResponsiveSpacing.sm)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .responsivePadding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundGradient)
    }
    
    private func heroImageSection(profile: UserModel) -> some View {
        ZStack {
            if let imageUrls = profile.profileImageUrls, !imageUrls.isEmpty {
                TabView(selection: $currentImageIndex) {
                    ForEach(Array(imageUrls.enumerated()), id: \.offset) { index, imageUrl in
                        if let url = URL(string: imageUrl) {
                            AsyncImage(url: url) { image in
                                image
                                    .resizable()
                                    .scaledToFill()
                            } placeholder: {
                                ZStack {
                                    AppTheme.sexyGradient
                                    ProgressView()
                                        .tint(.white)
                                }
                            }
                            .tag(index)
                        }
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                .frame(height: DeviceInfo.isIPad ? 500 : 400)
                .clipped()
            } else {
                ZStack {
                    AppTheme.sexyGradient
                        .frame(height: DeviceInfo.isIPad ? 500 : 400)

                    VStack(spacing: ResponsiveSpacing.md) {
                        Image(systemName: "person.fill")
                            .font(.system(size: DeviceInfo.isIPad ? 100 : 80))
                            .foregroundColor(.white)

                        Text(profile.firstName ?? "Unknown")
                            .font(.custom("AvenirNext-Bold", size: DeviceInfo.isIPad ? 32 : 24))
                            .foregroundColor(.white)
                    }
                }
            }
            
            // Gradient overlay for better text readability
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.clear,
                    Color.black.opacity(0.3)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .frame(height: DeviceInfo.isIPad ? 500 : 400)
        }
    }
    
    private func basicInfoSection(profile: UserModel) -> some View {
        VStack(spacing: ResponsiveSpacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: ResponsiveSpacing.xs) {
                    HStack(spacing: ResponsiveSpacing.sm) {
                        Text(profile.firstName ?? "Unknown")
                            .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(32)))
                            .foregroundStyle(AppTheme.sexyGradient)

                        if let age = profile.age {
                            Text("\(age)")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(24)))
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }

                    Text("Matched \(timeAgoString)")
                        .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                        .foregroundColor(AppTheme.textSecondary)
                }

                Spacer()
            }
        }
    }

    private func aboutMeSection(aboutMe: String) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.sm) {
            HStack {
                Image(systemName: "quote.bubble.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("About Me")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            Text(aboutMe)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                .foregroundColor(AppTheme.textSecondary)
                .lineLimit(nil)
                .multilineTextAlignment(.leading)
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func academicInfoSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Academic Info")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let college = profile.collegeName {
                    infoRow(icon: "building.2.fill", title: "College", value: college)
                }

                if let major = profile.major {
                    infoRow(icon: "book.fill", title: "Major", value: major)
                }

                if let gradeLevel = profile.gradeLevel {
                    infoRow(icon: "person.fill", title: "Grade Level", value: gradeLevel)
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func housingPreferencesSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "house.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Housing Preferences")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let housingStatus = profile.housingStatus {
                    infoRow(icon: "house.circle.fill", title: "Housing Status", value: housingStatus)
                }

                if let budgetMin = profile.budgetMin, let budgetMax = profile.budgetMax {
                    infoRow(icon: "dollarsign.circle.fill", title: "Budget", value: "$\(Int(budgetMin)) - $\(Int(budgetMax))")
                }

                if let roomType = profile.roomType {
                    infoRow(icon: "bed.double.fill", title: "Room Type", value: roomType)
                }

                if let cleanliness = profile.cleanliness {
                    infoRow(icon: "sparkles", title: "Cleanliness", value: "\(cleanliness)/5")
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func lifestyleSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "heart.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Lifestyle")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let sleepSchedule = profile.sleepSchedule {
                    infoRow(icon: "moon.fill", title: "Sleep Schedule", value: sleepSchedule)
                }

                if let smoking = profile.smoking {
                    infoRow(icon: "smoke.fill", title: "Smoking", value: smoking)
                }

                if let drinking = profile.drinking {
                    infoRow(icon: "wineglass.fill", title: "Drinking", value: drinking)
                }

                if let workout = profile.workout {
                    infoRow(icon: "figure.run", title: "Workout", value: workout)
                }

                if let interests = profile.interests, !interests.isEmpty {
                    VStack(alignment: .leading, spacing: ResponsiveSpacing.xs) {
                        HStack {
                            Image(systemName: "star.fill")
                                .font(.system(size: ResponsiveTypography.fontSize(12)))
                                .foregroundColor(AppTheme.primaryColor)

                            Text("Interests")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                                .foregroundColor(AppTheme.textPrimary)
                        }

                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: ResponsiveSpacing.xs) {
                            ForEach(interests.prefix(6), id: \.self) { interest in
                                Text(interest)
                                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(12)))
                                    .foregroundColor(AppTheme.primaryColor)
                                    .padding(.horizontal, ResponsiveSpacing.sm)
                                    .padding(.vertical, ResponsiveSpacing.xs)
                                    .background(AppTheme.primaryColor.opacity(0.1))
                                    .cornerRadius(ResponsiveRadius.medium)
                            }
                        }
                    }
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private var actionButtonsSection: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            // Start Chat Button
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .medium)
                handleStartChat()
            }) {
                HStack(spacing: ResponsiveSpacing.sm) {
                    Image(systemName: "message.fill")
                        .font(.system(size: ResponsiveTypography.fontSize(18), weight: .semibold))
                    Text("Start Conversation")
                        .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                }
                .foregroundColor(.white)
                .frame(maxWidth: DeviceInfo.isIPad ? 400 : .infinity)
                .responsivePadding(.vertical)
                .background(AppTheme.sexyGradient)
                .cornerRadius(ResponsiveRadius.large)
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
                .overlay(
                    RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())

            // Maybe Later Button
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .light)
                onDismiss()
            }) {
                Text("Maybe Later")
                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.textSecondary)
                    .responsivePadding(.vertical)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    private func infoRow(icon: String, title: String, value: String) -> some View {
        HStack(spacing: ResponsiveSpacing.sm) {
            Image(systemName: icon)
                .font(.system(size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.primaryColor)
                .frame(width: DeviceInfo.isIPad ? 24 : 20)

            Text(title)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.textPrimary)
                .frame(width: DeviceInfo.isIPad ? 140 : 100, alignment: .leading)

            Text(value)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.textSecondary)

            Spacer()
        }
    }

    // MARK: - Helper Methods

    private var timeAgoString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: match.createdAt, relativeTo: Date())
    }

    private func loadProfileWithEnhancedLogging() {
        print("🔄 TinderStyleMatchProfileSheet: Starting enhanced profile load")
        print("📊 TinderStyleMatchProfileSheet: Match details - ID: \(match.id), Participants: \(match.participants)")
        print("🔍 TinderStyleMatchProfileSheet: Extracted candidateID: '\(candidateID)'")

        // Validate candidateID
        guard !candidateID.isEmpty && candidateID != "unknown" else {
            print("❌ TinderStyleMatchProfileSheet: Invalid candidateID - cannot load profile")
            loadingError = "Unable to identify the user profile. Please try again."
            isLoading = false
            return
        }

        // Check cache first for instant loading
        if let cachedProfile = ProfileLoaderService.shared.getCachedProfile(userID: candidateID) {
            print("✅ TinderStyleMatchProfileSheet: Using cached profile for \(cachedProfile.firstName ?? "Unknown")")
            profile = cachedProfile
            isLoading = false
            loadingError = nil
            return
        }

        print("📡 TinderStyleMatchProfileSheet: Loading profile from Firestore for candidateID: '\(candidateID)'")

        // Load from Firestore using the same method as chat views
        ProfileLoaderService.shared.loadUserProfile(userID: candidateID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let loadedProfile):
                    print("✅ TinderStyleMatchProfileSheet: Successfully loaded profile for \(loadedProfile.firstName ?? "Unknown")")
                    self.profile = loadedProfile
                    self.isLoading = false
                    self.loadingError = nil
                case .failure(let error):
                    print("❌ TinderStyleMatchProfileSheet: Failed to load profile for '\(self.candidateID)': \(error.localizedDescription)")
                    self.loadingError = "Failed to load profile: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    private func retryProfileLoad() {
        print("🔄 TinderStyleMatchProfileSheet: Retrying profile load")
        isLoading = true
        loadingError = nil
        loadProfileWithEnhancedLogging()
    }

    private func handleStartChat() {
        print("💬 TinderStyleMatchProfileSheet: Starting chat creation process")

        // Validate current user
        guard let currentUserID = Auth.auth().currentUser?.uid else {
            print("❌ TinderStyleMatchProfileSheet: No authenticated user for chat creation")
            return
        }

        // Validate candidate ID
        guard !candidateID.isEmpty && candidateID != "unknown" else {
            print("❌ TinderStyleMatchProfileSheet: Invalid candidateID for chat creation")
            return
        }

        print("🔄 TinderStyleMatchProfileSheet: Creating chat between \(currentUserID) and \(candidateID)")

        ChatManager.shared.createOrGetChat(between: currentUserID, and: candidateID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let chatID):
                    print("✅ TinderStyleMatchProfileSheet: Successfully created/retrieved chat: \(chatID)")
                    ChatManager.shared.markConversationStarted(for: [currentUserID, candidateID], chatID: chatID)
                    onStartChat(chatID)
                case .failure(let error):
                    print("❌ TinderStyleMatchProfileSheet: Failed to create chat: \(error.localizedDescription)")
                    // Still dismiss on error for now - could show alert in future
                    onDismiss()
                }
            }
        }
    }
}

// MARK: - User Profile Variant (for Chat Profile Views)
struct TinderStyleUserProfileSheet: View {
    let user: UserModel
    let onStartChat: ((String) -> Void)?
    let onDismiss: () -> Void

    @State private var currentImageIndex = 0
    @State private var isLoading = false
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    AppTheme.backgroundGradient.ignoresSafeArea()

                    if isLoading {
                        loadingView
                    } else {
                        ScrollView {
                            AdaptiveContainer {
                                VStack(spacing: ResponsiveSpacing.lg) {
                                    // Hero Image Section
                                    heroImageSection(profile: user)

                                    // Profile Content
                                    VStack(spacing: ResponsiveSpacing.lg) {
                                        // Basic Info
                                        basicInfoSection(profile: user)

                                        // About Me
                                        if let aboutMe = user.aboutMe, !aboutMe.isEmpty {
                                            aboutMeSection(aboutMe: aboutMe)
                                        }

                                        // Academic Info
                                        academicInfoSection(profile: user)

                                        // Housing Preferences
                                        housingPreferencesSection(profile: user)

                                        // Lifestyle & Interests
                                        lifestyleSection(profile: user)

                                        // Action Buttons (only if chat callback provided)
                                        if onStartChat != nil {
                                            actionButtonsSection
                                        }
                                    }
                                    .responsivePadding(.horizontal)
                                    .padding(.top, ResponsiveSpacing.lg)
                                    .padding(.bottom, ResponsiveSpacing.xxl)
                                }
                            }
                        }
                    }
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .navigationTitle("Profile")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                if DeviceInfo.isIPad {
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        onDismiss()
                    }) {
                        HStack(spacing: ResponsiveSpacing.xs) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: ResponsiveTypography.fontSize(16), weight: .semibold))
                                .foregroundColor(AppTheme.primaryColor)

                            Text("Back")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Close") {
                    onDismiss()
                }
                .foregroundColor(AppTheme.primaryColor)
            }
        }
        .onAppear {
            // Any initialization if needed
        }
    }

    // MARK: - Helper Views for TinderStyleUserProfileSheet

    private var loadingView: some View {
        VStack(spacing: ResponsiveSpacing.lg) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: DeviceInfo.isIPad ? 80 : 60, height: DeviceInfo.isIPad ? 80 : 60)

                ProgressView()
                    .tint(.white)
                    .scaleEffect(DeviceInfo.isIPad ? 1.5 : 1.2)
            }

            Text("Loading profile...")
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                .foregroundColor(AppTheme.textSecondary)
        }
    }

    private func heroImageSection(profile: UserModel) -> some View {
        ZStack {
            if let imageUrls = profile.profileImageUrls, !imageUrls.isEmpty {
                TabView(selection: $currentImageIndex) {
                    ForEach(Array(imageUrls.enumerated()), id: \.offset) { index, imageUrl in
                        if let url = URL(string: imageUrl) {
                            AsyncImage(url: url) { image in
                                image
                                    .resizable()
                                    .scaledToFill()
                            } placeholder: {
                                ZStack {
                                    AppTheme.sexyGradient
                                    ProgressView()
                                        .tint(.white)
                                }
                            }
                            .tag(index)
                        }
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                .frame(height: DeviceInfo.isIPad ? 500 : 400)
                .clipped()
            } else {
                ZStack {
                    AppTheme.sexyGradient
                        .frame(height: DeviceInfo.isIPad ? 500 : 400)

                    VStack(spacing: ResponsiveSpacing.md) {
                        Image(systemName: "person.fill")
                            .font(.system(size: DeviceInfo.isIPad ? 100 : 80))
                            .foregroundColor(.white)

                        Text(profile.firstName ?? "Unknown")
                            .font(.custom("AvenirNext-Bold", size: DeviceInfo.isIPad ? 32 : 24))
                            .foregroundColor(.white)
                    }
                }
            }

            // Gradient overlay for better text readability
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.clear,
                    Color.clear,
                    Color.black.opacity(0.3)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .frame(height: DeviceInfo.isIPad ? 500 : 400)
        }
    }

    private func basicInfoSection(profile: UserModel) -> some View {
        VStack(spacing: ResponsiveSpacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: ResponsiveSpacing.xs) {
                    HStack(spacing: ResponsiveSpacing.sm) {
                        Text(profile.firstName ?? "Unknown")
                            .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(32)))
                            .foregroundStyle(AppTheme.sexyGradient)

                        if let age = profile.age {
                            Text("\(age)")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(24)))
                                .foregroundColor(AppTheme.textSecondary)
                        }
                    }

                    if let lastActive = profile.lastActiveAt {
                        Text("Last active \(timeAgoString(for: lastActive))")
                            .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                }

                Spacer()
            }
        }
    }

    private var actionButtonsSection: some View {
        VStack(spacing: ResponsiveSpacing.md) {
            // Start Chat Button (only if callback provided)
            if onStartChat != nil {
                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .medium)
                    handleStartChat()
                }) {
                    HStack(spacing: ResponsiveSpacing.sm) {
                        Image(systemName: "message.fill")
                            .font(.system(size: ResponsiveTypography.fontSize(18), weight: .semibold))
                        Text("Start Conversation")
                            .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: DeviceInfo.isIPad ? 400 : .infinity)
                    .responsivePadding(.vertical)
                    .background(AppTheme.sexyGradient)
                    .cornerRadius(ResponsiveRadius.large)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
                    .overlay(
                        RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }

            // Close Button
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .light)
                onDismiss()
            }) {
                Text("Close")
                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.textSecondary)
                    .responsivePadding(.vertical)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - Helper Methods for TinderStyleUserProfileSheet

    private func timeAgoString(for date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: date, relativeTo: Date())
    }

    private func handleStartChat() {
        guard let currentUserID = Auth.auth().currentUser?.uid,
              let userID = user.id,
              let onStartChat = onStartChat else { return }

        ChatManager.shared.createOrGetChat(between: currentUserID, and: userID) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let chatID):
                    ChatManager.shared.markConversationStarted(for: [currentUserID, userID], chatID: chatID)
                    onStartChat(chatID)
                case .failure(let error):
                    print("❌ Failed to create chat: \(error.localizedDescription)")
                    // Still dismiss on error
                    onDismiss()
                }
            }
        }
    }

    // Reuse section methods from TinderStyleMatchProfileSheet by calling them
    private func aboutMeSection(aboutMe: String) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.sm) {
            HStack {
                Image(systemName: "quote.bubble.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("About Me")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            Text(aboutMe)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(16)))
                .foregroundColor(AppTheme.textSecondary)
                .lineLimit(nil)
                .multilineTextAlignment(.leading)
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func academicInfoSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "graduationcap.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Academic Info")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let college = profile.collegeName {
                    infoRow(icon: "building.2.fill", title: "College", value: college)
                }

                if let major = profile.major {
                    infoRow(icon: "book.fill", title: "Major", value: major)
                }

                if let gradeLevel = profile.gradeLevel {
                    infoRow(icon: "person.fill", title: "Grade Level", value: gradeLevel)
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func housingPreferencesSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "house.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Housing Preferences")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let housingStatus = profile.housingStatus {
                    infoRow(icon: "house.circle.fill", title: "Housing Status", value: housingStatus)
                }

                if let budgetMin = profile.budgetMin, let budgetMax = profile.budgetMax {
                    infoRow(icon: "dollarsign.circle.fill", title: "Budget", value: "$\(Int(budgetMin)) - $\(Int(budgetMax))")
                }

                if let roomType = profile.roomType {
                    infoRow(icon: "bed.double.fill", title: "Room Type", value: roomType)
                }

                if let cleanliness = profile.cleanliness {
                    infoRow(icon: "sparkles", title: "Cleanliness", value: "\(cleanliness)/5")
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func lifestyleSection(profile: UserModel) -> some View {
        VStack(alignment: .leading, spacing: ResponsiveSpacing.md) {
            HStack {
                Image(systemName: "heart.fill")
                    .font(.system(size: ResponsiveTypography.fontSize(16)))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Lifestyle")
                    .font(.custom("AvenirNext-Bold", size: ResponsiveTypography.fontSize(18)))
                    .foregroundColor(AppTheme.textPrimary)

                Spacer()
            }

            VStack(spacing: ResponsiveSpacing.sm) {
                if let sleepSchedule = profile.sleepSchedule {
                    infoRow(icon: "moon.fill", title: "Sleep Schedule", value: sleepSchedule)
                }

                if let smoking = profile.smoking {
                    infoRow(icon: "smoke.fill", title: "Smoking", value: smoking)
                }

                if let drinking = profile.drinking {
                    infoRow(icon: "wineglass.fill", title: "Drinking", value: drinking)
                }

                if let workout = profile.workout {
                    infoRow(icon: "figure.run", title: "Workout", value: workout)
                }

                if let interests = profile.interests, !interests.isEmpty {
                    VStack(alignment: .leading, spacing: ResponsiveSpacing.xs) {
                        HStack {
                            Image(systemName: "star.fill")
                                .font(.system(size: ResponsiveTypography.fontSize(12)))
                                .foregroundColor(AppTheme.primaryColor)

                            Text("Interests")
                                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                                .foregroundColor(AppTheme.textPrimary)
                        }

                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: ResponsiveSpacing.xs) {
                            ForEach(interests.prefix(6), id: \.self) { interest in
                                Text(interest)
                                    .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(12)))
                                    .foregroundColor(AppTheme.primaryColor)
                                    .padding(.horizontal, ResponsiveSpacing.sm)
                                    .padding(.vertical, ResponsiveSpacing.xs)
                                    .background(AppTheme.primaryColor.opacity(0.1))
                                    .cornerRadius(ResponsiveRadius.medium)
                            }
                        }
                    }
                }
            }
        }
        .responsivePadding()
        .background(AppTheme.modernCardGradient)
        .cornerRadius(ResponsiveRadius.large)
        .overlay(
            RoundedRectangle(cornerRadius: ResponsiveRadius.large)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    private func infoRow(icon: String, title: String, value: String) -> some View {
        HStack(spacing: ResponsiveSpacing.sm) {
            Image(systemName: icon)
                .font(.system(size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.primaryColor)
                .frame(width: 20)

            Text(title)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.textPrimary)
                .frame(width: DeviceInfo.isIPad ? 120 : 100, alignment: .leading)

            Text(value)
                .font(.custom("AvenirNext-Medium", size: ResponsiveTypography.fontSize(14)))
                .foregroundColor(AppTheme.textSecondary)

            Spacer()
        }
    }
}
