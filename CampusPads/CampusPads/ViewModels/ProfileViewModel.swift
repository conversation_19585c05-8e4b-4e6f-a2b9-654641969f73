import Foundation
import FirebaseFirestore
import FirebaseAuth
import FirebaseFirestoreCombineSwift
import FirebaseStorage
import Combine

class ProfileViewModel: ObservableObject {
    @Published var userProfile: UserModel?
    @Published var errorMessage: String?
    @Published var isLoading: Bool = false

    private let db = Firestore.firestore()
    private var authListener: AuthStateDidChangeListenerHandle?
    private var profileListenerCancellables = Set<AnyCancellable>()

    static let shared = ProfileViewModel()

    var userID: String? {
        Auth.auth().currentUser?.uid
    }

    private init() {
        Logger.shared.info("Initializing ProfileViewModel singleton")
        authListener = Auth.auth().addStateDidChangeListener { [weak self] _, user in
            guard let self = self else {
                Logger.shared.warning("Self is nil in auth listener")
                return
            }

            Logger.shared.info("Auth state changed - User exists: \(user != nil)")
            Logger.shared.debug("User UID: \(user?.uid ?? "nil"), Email verified: \(user?.isEmailVerified ?? false)")

            guard let uid = user?.uid else {
                Logger.shared.info("No authenticated user, clearing profile state")
                DispatchQueue.main.async {
                    self.userProfile = nil
                    self.errorMessage = nil
                    self.isLoading = false
                }
                return
            }

            Logger.shared.info("Setting up profile listener for authenticated user: \(uid)")
            Logger.shared.debug("Current profile loaded: \(self.userProfile != nil), Loading: \(self.isLoading)")

            // Set loading state
            DispatchQueue.main.async {
                print("🔄 ProfileViewModel: Setting loading state to true")
                self.isLoading = true
                self.errorMessage = nil
            }

            // live-update our profile as it changes in Firestore
            // cancel *only* the profile snapshot listener:
            print("🧹 ProfileViewModel: Cancelling existing profile listeners (\(self.profileListenerCancellables.count) active)")
            self.profileListenerCancellables
                .forEach { $0.cancel() }
            self.profileListenerCancellables.removeAll()

            // CRITICAL FIX: Add retry logic and better error handling for profile loading
            print("🚀 ProfileViewModel: Starting profile load process for \(uid)")
            self.loadProfileWithRetry(uid: uid)
        }
        print("✅ ProfileViewModel.init: Auth listener setup complete")
    }

    /// CRITICAL FIX: Load profile with retry logic and fallback to one-time fetch
    private func loadProfileWithRetry(uid: String, attempt: Int = 1) {
        print("🔄 ProfileViewModel.loadProfileWithRetry: Starting attempt \(attempt) for user: \(uid)")
        print("   - Current loading state: \(isLoading)")
        print("   - Current error message: \(errorMessage ?? "none")")
        print("   - Current profile loaded: \(userProfile != nil)")
        print("   - Active listeners: \(profileListenerCancellables.count)")

        // Try real-time listener first
        print("📡 ProfileViewModel.loadProfileWithRetry: Setting up real-time listener for \(uid)")
        self.db
            .collection("users")
            .document(uid)
            .snapshotPublisher()
            .tryMap { snapshot -> UserModel in
                print("📄 ProfileViewModel.loadProfileWithRetry: Received snapshot for \(uid)")
                print("   - Snapshot exists: \(snapshot.exists)")
                print("   - Snapshot metadata: \(snapshot.metadata)")
                print("   - Snapshot data keys: \(snapshot.data()?.keys.sorted() ?? [])")

                guard snapshot.exists else {
                    print("❌ ProfileViewModel.loadProfileWithRetry: Document does not exist for \(uid)")
                    throw NSError(domain: "ProfileViewModel", code: 404,
                                userInfo: [NSLocalizedDescriptionKey: "Profile document does not exist"])
                }

                print("🔄 ProfileViewModel.loadProfileWithRetry: Attempting to decode UserModel for \(uid)")
                do {
                    let profile = try snapshot.data(as: UserModel.self)
                    print("✅ ProfileViewModel.loadProfileWithRetry: Successfully decoded profile")
                    print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                    print("   - Email: \(profile.email)")
                    print("   - Email Domain: \(profile.emailDomain ?? "nil")")
                    print("   - Email Verified: \(profile.isEmailVerified)")
                    print("   - Hidden from Discovery: \(profile.hideFromDiscovery ?? false)")
                    print("   - Profile Image URL: \(profile.profileImageUrl ?? "nil")")
                    print("   - Profile Image URLs: \(profile.profileImageUrls?.count ?? 0) images")
                    print("   - College: \(profile.collegeName ?? "nil")")
                    print("   - Housing Status: \(profile.housingStatus ?? "nil")")
                    print("   - Created At: \(profile.createdAt?.description ?? "nil")")
                    return profile
                } catch {
                    print("❌ ProfileViewModel.loadProfileWithRetry: Failed to decode UserModel for \(uid)")
                    print("   - Error: \(error.localizedDescription)")
                    print("   - Error Type: \(type(of: error))")
                    if let decodingError = error as? DecodingError {
                        print("   - Decoding Error Details: \(decodingError)")
                        switch decodingError {
                        case .typeMismatch(let type, let context):
                            print("     * Type Mismatch: Expected \(type)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                        case .valueNotFound(let type, let context):
                            print("     * Value Not Found: \(type)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                        case .keyNotFound(let key, let context):
                            print("     * Key Not Found: \(key)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                        case .dataCorrupted(let context):
                            print("     * Data Corrupted: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                        @unknown default:
                            print("     * Unknown Decoding Error")
                        }
                    }
                    throw error
                }
            }
            .receive(on: DispatchQueue.main)
            .sink { [weak self] completion in
                if case let .failure(err) = completion {
                    print("❌ ProfileViewModel.loadProfileWithRetry: Real-time listener failed (attempt \(attempt))")
                    print("   - Error: \(err.localizedDescription)")
                    print("   - Error Type: \(type(of: err))")

                    // If real-time listener fails, try one-time fetch as fallback
                    if attempt <= 2 {
                        print("🔄 ProfileViewModel.loadProfileWithRetry: Scheduling fallback one-time fetch")
                        DispatchQueue.main.asyncAfter(deadline: .now() + Double(attempt)) {
                            self?.loadProfileWithOneTimeFetch(uid: uid, attempt: attempt)
                        }
                    } else {
                        print("❌ ProfileViewModel.loadProfileWithRetry: Max attempts reached, setting error state")
                        DispatchQueue.main.async {
                            self?.errorMessage = "Failed to load profile: \(err.localizedDescription)"
                            self?.isLoading = false
                        }
                    }
                }
            } receiveValue: { [weak self] profile in
                print("✅ ProfileViewModel.loadProfileWithRetry: Successfully received profile via real-time listener")
                print("   - Profile ID: \(profile.id ?? "nil")")
                print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                print("   - Email: \(profile.email)")
                DispatchQueue.main.async {
                    print("🔄 ProfileViewModel.loadProfileWithRetry: Updating UI state with loaded profile")

                    // CRITICAL FIX: Migrate legacy profile image data if needed
                    let migratedProfile = self?.migrateProfileImageData(profile) ?? profile

                    self?.userProfile = migratedProfile
                    self?.errorMessage = nil
                    self?.isLoading = false
                    print("✅ ProfileViewModel.loadProfileWithRetry: UI state updated successfully")
                }

                // OPTIMIZATION: Removed automatic data migration to prevent unnecessary operations
            }
            .store(in: &self.profileListenerCancellables)

        print("📝 ProfileViewModel.loadProfileWithRetry: Real-time listener setup complete, stored in cancellables")
    }

    /// CRITICAL FIX: Fallback one-time fetch if real-time listener fails
    private func loadProfileWithOneTimeFetch(uid: String, attempt: Int) {
        print("🔄 ProfileViewModel.loadProfileWithOneTimeFetch: Starting fallback fetch (attempt \(attempt)) for user: \(uid)")
        print("   - Current loading state: \(isLoading)")
        print("   - Current error message: \(errorMessage ?? "none")")

        db.collection("users").document(uid).getDocument { [weak self] snapshot, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ ProfileViewModel.loadProfileWithOneTimeFetch: Firestore fetch failed (attempt \(attempt))")
                    print("   - Error: \(error.localizedDescription)")
                    print("   - Error Type: \(type(of: error))")
                    print("   - Error Code: \((error as NSError).code)")
                    print("   - Error Domain: \((error as NSError).domain)")

                    if attempt <= 3 {
                        print("🔄 ProfileViewModel.loadProfileWithOneTimeFetch: Scheduling retry with exponential backoff")
                        // Retry with exponential backoff
                        DispatchQueue.main.asyncAfter(deadline: .now() + Double(attempt * 2)) {
                            self?.loadProfileWithRetry(uid: uid, attempt: attempt + 1)
                        }
                    } else {
                        print("❌ ProfileViewModel.loadProfileWithOneTimeFetch: Max attempts reached, setting final error state")
                        self?.errorMessage = "Failed to load profile after multiple attempts: \(error.localizedDescription)"
                        self?.isLoading = false
                    }
                    return
                }

                print("📄 ProfileViewModel.loadProfileWithOneTimeFetch: Received snapshot from Firestore")
                print("   - Snapshot exists: \(snapshot?.exists ?? false)")
                print("   - Snapshot metadata: \(snapshot?.metadata.description ?? "nil")")

                guard let snapshot = snapshot, snapshot.exists else {
                    print("❌ ProfileViewModel.loadProfileWithOneTimeFetch: Profile document does not exist for user: \(uid)")
                    print("   - This indicates the user completed onboarding but no profile was created in Firestore")
                    print("   - Possible causes:")
                    print("     * Onboarding completion flag was set but profile save failed")
                    print("     * Profile was created with different UID")
                    print("     * Profile was deleted after creation")
                    print("     * Firestore rules preventing access")
                    self?.errorMessage = "Profile not found. Please complete your profile setup."
                    self?.isLoading = false
                    return
                }

                print("📊 ProfileViewModel.loadProfileWithOneTimeFetch: Document exists, analyzing data structure")
                let data = snapshot.data() ?? [:]
                print("   - Document data keys: \(data.keys.sorted())")
                print("   - Document size: \(data.count) fields")

                // Log critical fields for debugging
                print("   - email: \(data["email"] ?? "missing")")
                print("   - firstName: \(data["firstName"] ?? "missing")")
                print("   - isEmailVerified: \(data["isEmailVerified"] ?? "missing")")
                print("   - createdAt: \(data["createdAt"] ?? "missing")")
                print("   - createdAt type: \(type(of: data["createdAt"]))")

                do {
                    print("🔄 ProfileViewModel.loadProfileWithOneTimeFetch: Attempting to decode UserModel")
                    let profile = try snapshot.data(as: UserModel.self)
                    print("✅ ProfileViewModel.loadProfileWithOneTimeFetch: Successfully decoded profile via one-time fetch")
                    print("   - Profile ID: \(profile.id ?? "nil")")
                    print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
                    print("   - Email: \(profile.email)")
                    print("   - Email Domain: \(profile.emailDomain ?? "nil")")
                    print("   - Email Verified: \(profile.isEmailVerified)")
                    print("   - Created At: \(profile.createdAt?.description ?? "nil")")

                    // CRITICAL FIX: Migrate legacy profile image data if needed
                    let migratedProfile = self?.migrateProfileImageData(profile) ?? profile

                    self?.userProfile = migratedProfile
                    self?.errorMessage = nil
                    self?.isLoading = false
                    print("✅ ProfileViewModel.loadProfileWithOneTimeFetch: UI state updated with loaded profile")

                    // Start online status tracking when profile is loaded
                    Task { @MainActor in
                        OnlineStatusManager.shared.startTracking()
                    }

                    // After successful one-time fetch, try to re-establish real-time listener
                    if attempt <= 2 {
                        print("🔄 ProfileViewModel.loadProfileWithOneTimeFetch: Scheduling real-time listener re-establishment")
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            self?.loadProfileWithRetry(uid: uid, attempt: attempt + 1)
                        }
                    }
                } catch {
                    print("❌ ProfileViewModel.loadProfileWithOneTimeFetch: Failed to decode profile from document")
                    print("   - Error: \(error.localizedDescription)")
                    print("   - Error Type: \(type(of: error))")
                    if let decodingError = error as? DecodingError {
                        print("   - Decoding Error Details: \(decodingError)")
                        switch decodingError {
                        case .typeMismatch(let type, let context):
                            print("     * Type Mismatch: Expected \(type)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                            print("     * Debug Description: \(context.debugDescription)")
                        case .valueNotFound(let type, let context):
                            print("     * Value Not Found: \(type)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                            print("     * Debug Description: \(context.debugDescription)")
                        case .keyNotFound(let key, let context):
                            print("     * Key Not Found: \(key)")
                            print("     * Context: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                            print("     * Debug Description: \(context.debugDescription)")
                        case .dataCorrupted(let context):
                            print("     * Data Corrupted: \(context)")
                            print("     * Coding Path: \(context.codingPath)")
                            print("     * Debug Description: \(context.debugDescription)")
                        @unknown default:
                            print("     * Unknown Decoding Error")
                        }
                    }
                    self?.errorMessage = "Failed to decode profile: \(error.localizedDescription)"
                    self?.isLoading = false
                }
            }
        }
    }
    /// Force refresh the current user's profile
    func forceRefreshProfile() {
        guard let uid = Auth.auth().currentUser?.uid else {
            print("❌ ProfileViewModel: Cannot refresh profile - no authenticated user")
            return
        }

        print("🔄 ProfileViewModel: Force refreshing profile for user: \(uid)")

        // Clear current state
        DispatchQueue.main.async {
            self.errorMessage = nil
            self.isLoading = true
        }

        // Cancel existing listeners
        profileListenerCancellables.forEach { $0.cancel() }
        profileListenerCancellables.removeAll()

        // Start fresh load
        loadProfileWithRetry(uid: uid, attempt: 1)
    }

    /// Clears all user data from the view model (used during account deletion)
    func clearUserData() {
        print("🗑️ ProfileViewModel: Clearing all user data")

        // Clear published properties
        DispatchQueue.main.async {
            self.userProfile = nil
            self.errorMessage = nil
            self.isLoading = false
        }

        // Cancel all listeners and subscriptions
        if let handle = authListener {
            Auth.auth().removeStateDidChangeListener(handle)
            authListener = nil
        }

        profileListenerCancellables.forEach { $0.cancel() }
        profileListenerCancellables.removeAll()

        print("✅ ProfileViewModel: User data cleared")
    }

    deinit {
        // CRITICAL FIX: Proper cleanup to prevent memory leaks
        if let handle = authListener {
            Auth.auth().removeStateDidChangeListener(handle)
        }

        // Cancel all profile listeners
        profileListenerCancellables.removeAll()

        print("🧹 ProfileViewModel: Cleaned up all listeners and cancellables")
    }


    /// Updates the current user's profile in Firestore with content moderation.
    func updateUserProfile(updatedProfile: UserModel, completion: @escaping (Result<Void, Error>) -> Void) {
        guard let uid = userID else {
            print("[ProfileViewModel] updateUserProfile: User not authenticated")
            completion(.failure(NSError(domain: "ProfileUpdate", code: 0,
                                        userInfo: [NSLocalizedDescriptionKey: "User not authenticated"])))
            return
        }
        print("[ProfileViewModel] updateUserProfile: Updating profile for uid: \(uid)")

        // CRITICAL: Content moderation before saving
        let moderationResult = ContentModerationService.shared.validateProfileContent(
            firstName: updatedProfile.firstName,
            lastName: updatedProfile.lastName,
            aboutMe: updatedProfile.aboutMe,
            interests: updatedProfile.interests
        )

        if !moderationResult.isApproved {
            print("❌ ProfileViewModel: Content moderation failed")
            let violationMessages = moderationResult.violations.map { "\($0.field): \($0.type.rawValue)" }
            let errorMessage = "Content not allowed: \(violationMessages.joined(separator: ", "))"

            // Record violations for user
            Task {
                for violation in moderationResult.violations {
                    await ContentModerationService.shared.recordViolation(userID: uid, violation: violation)
                }
            }

            completion(.failure(NSError(domain: "ContentModeration", code: 1001,
                                        userInfo: [NSLocalizedDescriptionKey: errorMessage])))
            return
        }

        // Apply filtered content if any profanity was censored
        var profileToSave = updatedProfile
        if let filteredFirstName = moderationResult.filteredContent["firstName"] {
            profileToSave.firstName = filteredFirstName
        }
        if let filteredLastName = moderationResult.filteredContent["lastName"] {
            profileToSave.lastName = filteredLastName
        }
        if let filteredAboutMe = moderationResult.filteredContent["aboutMe"] {
            profileToSave.aboutMe = filteredAboutMe
        }

        // Ensure derived fields are updated before saving
        profileToSave.firstName_lowercase = profileToSave.firstName?.lowercased()
        profileToSave.lastName_lowercase = profileToSave.lastName?.lowercased()
        profileToSave.collegeName_lowercase = profileToSave.collegeName?.lowercased()
        if let atIndex = profileToSave.email.lastIndex(of: "@") {
            profileToSave.emailDomain = String(profileToSave.email.suffix(from: profileToSave.email.index(after: atIndex)))
        } else {
            profileToSave.emailDomain = nil
        }

        do {
            try db.collection("users").document(uid)
                .setData(from: profileToSave, merge: true) { error in
                    if let error = error {
                        DispatchQueue.main.async {
                            print("[ProfileViewModel] updateUserProfile error: \(error.localizedDescription)")
                            completion(.failure(error))
                        }
                    } else {
                        DispatchQueue.main.async {
                            var newProfile = profileToSave
                            newProfile.id = uid
                            self.userProfile = newProfile
                            print("[ProfileViewModel] updateUserProfile: Successfully updated profile: \(newProfile)")
                            completion(.success(()))
                        }
                    }
                }
        } catch {
            print("[ProfileViewModel] updateUserProfile: Exception caught: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }

    /// Uploads a profile image to Firebase Storage with enhanced error handling and content moderation.
    func uploadProfileImage(image: UIImage, completion: @escaping (Result<String, Error>) -> Void) {
        guard let uid = userID else {
            let error = NSError(domain: "ProfileViewModel", code: 1001,
                               userInfo: [NSLocalizedDescriptionKey: "User not authenticated"])
            print("[ProfileViewModel] uploadProfileImage: User not authenticated")
            DispatchQueue.main.async { completion(.failure(error)) }
            return
        }

        // Compress image with better quality control
        guard let imageData = compressImageForUpload(image) else {
            let error = NSError(domain: "ProfileViewModel", code: 1002,
                               userInfo: [NSLocalizedDescriptionKey: "Failed to process image data"])
            print("[ProfileViewModel] uploadProfileImage: Failed to compress image")
            DispatchQueue.main.async { completion(.failure(error)) }
            return
        }

        // CRITICAL: Enhanced image content moderation
        let imageModeration = ContentModerationService.shared.validateImageContent(imageData, imageType: .profile)
        if !imageModeration.isApproved {
            // If image should be quarantined, handle it
            if imageModeration.shouldQuarantine {
                Task {
                    await ContentModerationService.shared.quarantineImage(
                        imageData: imageData,
                        originalPath: "profileImages/\(uid)",
                        userID: uid,
                        reason: imageModeration.reason ?? "Inappropriate content detected"
                    )
                }
            }

            let error = NSError(domain: "ContentModeration", code: 1003,
                               userInfo: [NSLocalizedDescriptionKey: imageModeration.reason ?? "Image content not allowed"])
            print("[ProfileViewModel] uploadProfileImage: Image blocked by content moderation")
            DispatchQueue.main.async { completion(.failure(error)) }
            return
        }

        let fileName = "profile_\(uid)_\(UUID().uuidString).jpg"
        let storageRef = Storage.storage().reference().child("profileImages/\(fileName)")
        let metadata = StorageMetadata()
        metadata.contentType = "image/jpeg"
        metadata.customMetadata = [
            "uploadedBy": uid,
            "uploadedAt": ISO8601DateFormatter().string(from: Date())
        ]

        print("[ProfileViewModel] uploadProfileImage: Starting upload for uid: \(uid), size: \(imageData.count) bytes")

        let uploadTask = storageRef.putData(imageData, metadata: metadata) { _, error in
            if let error = error {
                print("[ProfileViewModel] uploadProfileImage: Upload failed: \(error.localizedDescription)")
                DispatchQueue.main.async { completion(.failure(error)) }
                return
            }

            // Get download URL
            storageRef.downloadURL { url, urlError in
                if let urlError = urlError {
                    print("[ProfileViewModel] uploadProfileImage: Failed to get download URL: \(urlError.localizedDescription)")
                    DispatchQueue.main.async { completion(.failure(urlError)) }
                    return
                }

                guard let downloadURL = url?.absoluteString else {
                    let error = NSError(domain: "ProfileViewModel", code: 1003,
                                       userInfo: [NSLocalizedDescriptionKey: "Download URL not available"])
                    print("[ProfileViewModel] uploadProfileImage: Download URL is nil")
                    DispatchQueue.main.async { completion(.failure(error)) }
                    return
                }

                print("[ProfileViewModel] uploadProfileImage: Successfully uploaded. URL: \(downloadURL)")
                DispatchQueue.main.async { completion(.success(downloadURL)) }
            }
        }

        // Monitor upload progress
        uploadTask.observe(.progress) { snapshot in
            guard let progress = snapshot.progress else { return }
            let percentComplete = 100.0 * Double(progress.completedUnitCount) / Double(progress.totalUnitCount)
            Logger.shared.debug("Upload progress: \(Int(percentComplete))%")
        }

        uploadTask.observe(.failure) { snapshot in
            if let error = snapshot.error {
                print("[ProfileViewModel] Upload failed with error: \(error.localizedDescription)")
            }
        }
    }

    /// Compresses image for optimal upload size and quality
    private func compressImageForUpload(_ image: UIImage) -> Data? {
        // Resize image if too large
        let maxDimension: CGFloat = 1024
        let resizedImage: UIImage

        if max(image.size.width, image.size.height) > maxDimension {
            let scale = maxDimension / max(image.size.width, image.size.height)
            let newSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)

            UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
            image.draw(in: CGRect(origin: .zero, size: newSize))
            resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
            UIGraphicsEndImageContext()
        } else {
            resizedImage = image
        }

        // Compress with quality control
        var compression: CGFloat = 0.8
        var imageData = resizedImage.jpegData(compressionQuality: compression)

        // Reduce quality if still too large (max 2MB)
        let maxSize = 2 * 1024 * 1024
        while let data = imageData, data.count > maxSize && compression > 0.1 {
            compression -= 0.1
            imageData = resizedImage.jpegData(compressionQuality: compression)
        }

        return imageData
    }

    /// Uploads a property media file (image, floorplan, or document) to Firebase Storage.
    func uploadPropertyMedia(image: UIImage, folder: String, completion: @escaping (Result<String, Error>) -> Void) {
        guard let uid = userID,
              let imageData = image.jpegData(compressionQuality: 0.8) else {
            print("[ProfileViewModel] uploadPropertyMedia: Invalid user or image data.")
            completion(.failure(NSError(domain: "UploadError", code: 0,
                                        userInfo: [NSLocalizedDescriptionKey: "Invalid user or image data."])))
            return
        }

        let storageRef = Storage.storage().reference().child("\(folder)/\(uid)_\(UUID().uuidString).jpg")
        let metadata = StorageMetadata()
        metadata.contentType = "image/jpeg"

        print("[ProfileViewModel] uploadPropertyMedia: Uploading media to folder \(folder) for uid: \(uid)")
        storageRef.putData(imageData, metadata: metadata) { _, error in
            if let error = error {
                print("[ProfileViewModel] uploadPropertyMedia: Error uploading media: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            storageRef.downloadURL { url, error in
                if let error = error {
                    print("[ProfileViewModel] uploadPropertyMedia: Error getting download URL: \(error.localizedDescription)")
                    completion(.failure(error))
                } else if let downloadURL = url?.absoluteString {
                    print("[ProfileViewModel] uploadPropertyMedia: Successfully uploaded media. URL: \(downloadURL)")
                    completion(.success(downloadURL))
                }
            }
        }
    }

    /// Removes a blocked user ID from the profile (in memory).
    func removeBlockedUser(with uid: String) {
        if var blocked = userProfile?.blockedUserIDs {
            blocked.removeAll { $0 == uid }
            userProfile?.blockedUserIDs = blocked
            print("[ProfileViewModel] removeBlockedUser: Removed blocked user with uid: \(uid)")
        }
    }

    /// Debug method to analyze current user's profile loading issues
    func debugCurrentUserProfile() {
        guard let uid = Auth.auth().currentUser?.uid else {
            print("❌ ProfileViewModel.debugCurrentUserProfile: No authenticated user")
            return
        }

        print("🔍 ProfileViewModel.debugCurrentUserProfile: Starting debug for current user: \(uid)")
        print("   - Current profile loaded: \(userProfile != nil)")
        print("   - Current loading state: \(isLoading)")
        print("   - Current error: \(errorMessage ?? "none")")
        print("   - Active listeners: \(profileListenerCancellables.count)")

        // Use ProfileLoaderService for detailed debugging
        ProfileLoaderService.shared.debugProfileEligibility(userID: uid) { report in
            print("📊 ProfileViewModel.debugCurrentUserProfile: Debug report received")
        }
    }

    /// Migrates legacy profile image data to ensure compatibility
    /// Fixes the issue where profileImageUrl is not set when profileImageUrls exists
    private func migrateProfileImageData(_ profile: UserModel) -> UserModel {
        var migratedProfile = profile

        // Check if we have profileImageUrls but no profileImageUrl
        if let imageUrls = profile.profileImageUrls,
           !imageUrls.isEmpty,
           let firstImageUrl = imageUrls.first,
           !firstImageUrl.isEmpty,
           (profile.profileImageUrl?.isEmpty != false) {

            print("🔧 ProfileViewModel: Migrating profile image data")
            print("   - Found profileImageUrls: \(imageUrls.count) images")
            print("   - Missing profileImageUrl, setting to first image: \(firstImageUrl)")

            migratedProfile.profileImageUrl = firstImageUrl

            // Save the migration to Firestore
            updateUserProfile(updatedProfile: migratedProfile) { result in
                switch result {
                case .success:
                    print("✅ ProfileViewModel: Profile image migration saved successfully")
                case .failure(let error):
                    print("❌ ProfileViewModel: Failed to save profile image migration: \(error.localizedDescription)")
                }
            }
        }

        return migratedProfile
    }

    /// Helper to create a default user profile if none exists.
    private func defaultUserProfile() -> UserModel {
        let user = UserModel(
            email: Auth.auth().currentUser?.email ?? "<EMAIL>",
            isEmailVerified: false,
            // 2. Personal Info
            aboutMe: nil,
            firstName: nil,
            lastName: nil,
            dateOfBirth: nil,
            gender: nil,
            height: nil,
            // 3. Academic Info
            gradeLevel: nil,
            major: nil,
            collegeName: nil,
            // 4. Housing & Lease Info
            housingStatus: nil,
            desiredLeaseHousingType: nil,
            roommateCountNeeded: 0,
            roommateCountExisting: 0,
            // 5. Property Details
            propertyDetails: nil,
            propertyAddress: nil, // NEW
            propertyImageUrls: nil,
            floorplanUrls: nil,
            documentUrls: nil,
            // 6. Room Type Selector
            roomType: nil,
            // 7. Lease & Pricing Details
            leaseStartDate: nil,
            leaseDuration: nil,
            monthlyRentMin: nil,
            monthlyRentMax: nil,
            specialLeaseConditions: nil,
            // 8. Amenities Multi-Select Field
            amenities: nil,
            // 9. Additional Housing Fields
            budgetMin: nil,
            budgetMax: nil,
            cleanliness: nil,
            sleepSchedule: nil,
            smoker: nil,
            petFriendly: nil,
            livingStyle: nil,
            // 10. Interests
            socialLevel: nil,
            studyHabits: nil,
            interests: nil,
            // 11. Media & Location
            profileImageUrl: nil,
            profileImageUrls: nil,
            location: nil,

            // 13. Blocked Users
            blockedUserIDs: nil,
            // 14. Advanced Filter Settings
            filterSettings: nil,
            // 15. Lifestyle Fields
            pets: nil,
            drinking: nil,
            smoking: nil,
            cannabis: nil,
            workout: nil,
            dietaryPreferences: nil,
            socialMedia: nil,
            sleepingHabits: nil,
            // 16. Quiz Answers
            goingOutQuizAnswers: nil,
            weekendQuizAnswers: nil,
            phoneQuizAnswers: nil
        )
        var mutableUser = user
        mutableUser.id = Auth.auth().currentUser?.uid
        return mutableUser
    }
}
