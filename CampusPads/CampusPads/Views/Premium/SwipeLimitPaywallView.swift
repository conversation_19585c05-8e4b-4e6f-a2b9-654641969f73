import SwiftUI

struct SwipeLimitPaywallView: View {
    let onUpgrade: () -> Void
    let onDismiss: () -> Void

    @StateObject private var swipeLimitManager = DailySwipeLimitManager.shared
    @StateObject private var purchaseManager = PurchaseManager.shared
    @State private var animateGlow = false
    @State private var animateButton = false
    @State private var timeUntilReset = ""
    @State private var timer: Timer?

    var body: some View {
        ZStack {
            // Full-screen content masking background - completely opaque to hide content behind
            ZStack {
                // Solid background to completely hide content behind
                Color.black.opacity(0.95)
                    .ignoresSafeArea()

                // Blurred background for visual effect
                Color.black.opacity(0.6)
                    .ignoresSafeArea()
                    .blur(radius: 20)
            }

            // Paywall content
            VStack(spacing: AppTheme.spacing24) {
                // Swipe limit icon with animation
                swipeLimitIcon

                // Content
                VStack(spacing: AppTheme.spacing16) {
                    Text("Daily Swipe Limit Reached")
                        .font(.system(size: 26, weight: .bold, design: .rounded))
                        .foregroundColor(AppTheme.textPrimary)
                        .multilineTextAlignment(.center)

                    Text("You've used all 25 of your daily swipes! Upgrade to premium for unlimited swipes or wait until tomorrow.")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                }

                // Reset timer
                resetTimerView

                // Premium benefits
                premiumBenefitsView

                // Action buttons
                VStack(spacing: AppTheme.spacing12) {
                    // Upgrade button
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                        onUpgrade()
                    }) {
                        HStack(spacing: AppTheme.spacing12) {
                            Image(systemName: "infinity")
                                .font(.system(size: 18, weight: .bold))

                            Text(upgradeButtonText)
                                .font(.system(size: 18, weight: .bold, design: .rounded))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, AppTheme.spacing24)
                        .padding(.vertical, AppTheme.spacing16)
                        .background(premiumButtonGradient)
                        .cornerRadius(AppTheme.radiusLarge)
                        .shadow(color: Color.yellow.opacity(0.4), radius: 12, x: 0, y: 6)
                        .scaleEffect(animateButton ? 1.05 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: animateButton)
                    }

                    // Wait button
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        onDismiss()
                    }) {
                        Text("Wait Until Tomorrow")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                }
            }
            .padding(AppTheme.spacing32)
            .background(paywallCardBackground)
            .cornerRadius(AppTheme.radiusXLarge)
            .overlay(premiumBorder)
            .shadow(color: .black.opacity(0.2), radius: 24, x: 0, y: 12)
            .padding(AppTheme.spacing20)
        }
        .onAppear {
            startAnimations()
            startResetTimer()
        }
        .onDisappear {
            timer?.invalidate()
        }
    }

    // MARK: - Swipe Limit Icon
    private var swipeLimitIcon: some View {
        ZStack {
            // Glow effect
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.red.opacity(animateGlow ? 0.6 : 0.3),
                            Color.orange.opacity(animateGlow ? 0.4 : 0.2),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 20,
                        endRadius: 60
                    )
                )
                .frame(width: 120, height: 120)
                .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateGlow)

            // Swipe limit icon
            ZStack {
                Image(systemName: "hand.raised.fill")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.red)

                Image(systemName: "25.circle.fill")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)
                    .background(Color.red)
                    .clipShape(Circle())
                    .offset(x: 20, y: -20)
            }
            .shadow(color: Color.red.opacity(0.6), radius: 8, x: 0, y: 4)
        }
    }

    // MARK: - Reset Timer View
    private var resetTimerView: some View {
        VStack(spacing: AppTheme.spacing8) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: "clock.fill")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppTheme.primaryColor)

                Text("Resets in:")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppTheme.textSecondary)

                Text(timeUntilReset)
                    .font(.system(size: 16, weight: .bold, design: .monospaced))
                    .foregroundColor(AppTheme.primaryColor)
            }
        }
        .padding(AppTheme.spacing12)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                .fill(Color.black.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                        .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 1)
                )
        )
    }

    // MARK: - Premium Benefits
    private var premiumBenefitsView: some View {
        VStack(spacing: AppTheme.spacing12) {
            Text("Premium Benefits")
                .font(.system(size: 18, weight: .bold, design: .rounded))
                .foregroundColor(AppTheme.textPrimary)

            VStack(spacing: AppTheme.spacing8) {
                benefitRow(icon: "infinity", text: "Unlimited daily swipes")
                benefitRow(icon: "crown.fill", text: "Premium top matches")
                benefitRow(icon: "eye.fill", text: "See who viewed you")
                benefitRow(icon: "star.fill", text: "One-time payment, lifetime access")
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .fill(Color.black.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private func benefitRow(icon: String, text: String) -> some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.yellow)
                .frame(width: 24, height: 24)

            Text(text)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.textPrimary)

            Spacer()
        }
    }

    // MARK: - Styling Components
    private var premiumButtonGradient: LinearGradient {
        LinearGradient(
            colors: [Color.yellow, Color.orange],
            startPoint: .leading,
            endPoint: .trailing
        )
    }

    private var paywallCardBackground: some View {
        ZStack {
            // Base background
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)

            // Premium overlay
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.red.opacity(0.1),
                            Color.orange.opacity(0.05),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        }
    }

    private var premiumBorder: some View {
        RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
            .stroke(
                LinearGradient(
                    colors: [
                        Color.red.opacity(0.6),
                        Color.orange.opacity(0.4),
                        Color.yellow.opacity(0.2)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ),
                lineWidth: 2
            )
    }

    // MARK: - Computed Properties
    private var upgradeButtonText: String {
        if let product = purchaseManager.getLifetimePremiumProduct() {
            return "Get Unlimited Swipes - \(product.displayPrice)"
        } else {
            return "Get Unlimited Swipes - $4.99"
        }
    }

    // MARK: - Helper Methods
    private func startAnimations() {
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            animateGlow = true
        }

        withAnimation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3)) {
            animateButton = true
        }
    }

    private func startResetTimer() {
        updateTimeUntilReset()

        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            updateTimeUntilReset()
        }
    }

    private func updateTimeUntilReset() {
        let calendar = Calendar.current
        let now = Date()
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now)!
        let midnight = calendar.startOfDay(for: tomorrow)

        let timeInterval = midnight.timeIntervalSince(now)
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60

        timeUntilReset = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
}

// MARK: - Preview
struct SwipeLimitPaywallView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            AppTheme.backgroundGradient
                .ignoresSafeArea()

            SwipeLimitPaywallView(
                onUpgrade: { print("Upgrade tapped") },
                onDismiss: { print("Dismiss tapped") }
            )
        }
    }
}
