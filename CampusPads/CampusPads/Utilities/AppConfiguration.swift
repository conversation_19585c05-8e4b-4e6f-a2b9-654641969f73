//
//  AppConfiguration.swift
//  CampusPads
//
//  Centralized configuration management for the app
//

import Foundation

/// Centralized configuration for the entire app
struct AppConfiguration {
    
    // MARK: - Website & Legal URLs
    struct URLs {
        static let baseURL = "https://campuspadsapp.com"
        static let privacyPolicy = "\(baseURL)/privacy-policy"
        static let termsOfService = "\(baseURL)/terms-of-service"
        static let support = "\(baseURL)/support"
        static let communityGuidelines = "\(baseURL)/community-guidelines"
        static let contactUs = "\(baseURL)/contact"
        static let faq = "\(baseURL)/faq"
    }

    // MARK: - App Information
    struct App {
        static let name = "CampusPads™"
        static let displayName = "CampusPads" // For places where ™ symbol might cause issues
        static let version = "1.0"
        static let bundleIdentifier = "com.CampusPads.CampusPadsApp"
        static let minimumIOSVersion = "17.0"
        static let supportEmail = "<EMAIL>"
        static let copyrightNotice = "© 2024 CampusPads™. All rights reserved."
    }
    
    // MARK: - Firebase Configuration
    struct Firebase {
        static let projectID = "collegepads-1"
        static let storageBucket = "collegepads-1.firebasestorage.app"
        
        // Collection Names
        struct Collections {
            static let users = "users"
            static let matches = "matches"
            static let chats = "chats"
            static let messages = "messages"
            static let swipes = "swipes"
            static let reports = "reports"
            static let contentViolations = "contentViolations"
            static let quarantinedImages = "quarantinedImages"
            static let adminReviews = "adminReviews"
        }
    }
    
    // MARK: - Network Configuration
    struct Network {
        static let requestTimeout: TimeInterval = 15.0
        static let resourceTimeout: TimeInterval = 60.0
        static let maxConnectionsPerHost = 4
        static let retryAttempts = 3
        static let retryDelay: TimeInterval = 1.0
    }
    
    // MARK: - Cache Configuration
    struct Cache {
        static let imageCountLimit = 100
        static let imageSizeLimit = 50 * 1024 * 1024 // 50MB
        static let profileCacheLimit = 50
        static let messageCacheLimit = 1000
    }
    
    // MARK: - Content Moderation
    struct ContentModeration {
        static let maxViolationsBeforeWarning = 3
        static let maxViolationsBeforeReview = 5
        static let violationHistoryDays = 30
        static let imageQuarantineThreshold = 0.8
    }
    
    // MARK: - Matching Algorithm
    struct Matching {
        static let maxPotentialMatches = 50
        static let fetchCooldownMinutes = 5
        static let maxDistanceKm = 50.0
        static let minAgeGap = 1
        static let maxAgeGap = 10
    }
    
    // MARK: - UI Configuration
    struct UI {
        static let animationDuration: TimeInterval = 0.3
        static let longAnimationDuration: TimeInterval = 0.6
        static let hapticFeedbackEnabled = true
        static let maxProfileImages = 6
        static let maxBioLength = 500
        static let maxMessageLength = 1000
    }
    
    // MARK: - Debug Configuration
    struct Debug {
        #if DEBUG
        static let isEnabled = true
        static let verboseLogging = true
        static let showDebugInfo = true
        #else
        static let isEnabled = false
        static let verboseLogging = false
        static let showDebugInfo = false
        #endif
        
        static let logLevel: LogLevel = Debug.isEnabled ? .debug : .error
    }
    
    // MARK: - Feature Flags
    struct Features {
        static let premiumMatchingEnabled = true
        static let videoCallsEnabled = false
        static let groupChatsEnabled = false
        static let locationSharingEnabled = true
        static let pushNotificationsEnabled = true
        static let analyticsEnabled = false // Privacy-focused
    }
}

// MARK: - Log Level Enum
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
    
    var emoji: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .critical: return "🚨"
        }
    }
}

// MARK: - Environment Detection
extension AppConfiguration {
    static var isDebugBuild: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
    
    static var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    static var isTestFlight: Bool {
        guard let appStoreReceiptURL = Bundle.main.appStoreReceiptURL else {
            return false
        }
        return appStoreReceiptURL.lastPathComponent == "sandboxReceipt"
    }
    
    static var isAppStore: Bool {
        return !isDebugBuild && !isTestFlight
    }
}

// MARK: - Validation
extension AppConfiguration {
    /// Validates that all required configuration values are properly set
    static func validateConfiguration() -> [String] {
        var issues: [String] = []
        
        // Validate URLs
        if !URLs.baseURL.hasPrefix("https://") {
            issues.append("Base URL must use HTTPS")
        }
        
        // Validate timeouts
        if Network.requestTimeout <= 0 {
            issues.append("Request timeout must be positive")
        }
        
        // Validate cache limits
        if Cache.imageCountLimit <= 0 {
            issues.append("Image cache count limit must be positive")
        }
        
        return issues
    }
}
