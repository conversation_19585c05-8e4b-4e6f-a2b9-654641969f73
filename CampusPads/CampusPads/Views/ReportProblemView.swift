import SwiftUI
import FirebaseFirestore

struct ReportProblemView: View {
    @StateObject private var viewModel = ProblemReportViewModel()
    
    @State private var selectedCategory: ProblemCategory = .bug
    @State private var description: String = ""
    @State private var userEmail: String = ""
    @State private var isSubmitting: Bool = false
    @State private var showSuccessMessage: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                // Sexy gradient background
                AppTheme.dynamicBackgroundGradient
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Header with icon
                        headerSection
                        
                        // Problem categories
                        categorySection
                        
                        // Description section
                        descriptionSection
                        
                        // Contact section
                        contactSection
                        
                        // Submit button
                        submitSection
                        
                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        if #available(iOS 15.0, *) {
                            dismiss()
                        } else {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                    .foregroundStyle(AppTheme.sexyGradient)
                    .font(.custom("AvenirNext-Medium", size: 16))
                }
            }
            .alert("Thank You!", isPresented: $showSuccessMessage) {
                Button("OK") {
                    if #available(iOS 15.0, *) {
                        dismiss()
                    } else {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            } message: {
                Text("Your feedback helps us improve CampusPads. We'll look into this issue and get back to you if needed.")
            }
            .alert(item: Binding(
                get: {
                    if let errorMessage = viewModel.errorMessage {
                        return GenericAlertError(message: errorMessage)
                    }
                    return nil
                },
                set: { _ in viewModel.errorMessage = nil }
            )) { alertError in
                Alert(title: Text("Error"), message: Text(alertError.message), dismissButton: .default(Text("OK")))
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)
                
                Image(systemName: "exclamationmark.bubble.fill")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            
            VStack(spacing: AppTheme.spacing8) {
                Text("Report a Problem")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)
                
                Text("Help us make CampusPads better for everyone")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var categorySection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("What type of issue are you experiencing?")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: AppTheme.spacing12) {
                ForEach(ProblemCategory.allCases, id: \.self) { category in
                    ProblemCategoryCard(
                        category: category,
                        isSelected: selectedCategory == category,
                        onSelect: { selectedCategory = category }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var descriptionSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("Describe the Problem")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            Text("Please provide as much detail as possible to help us understand and fix the issue.")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)
            
            ZStack(alignment: .topLeading) {
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(.ultraThinMaterial)
                    .frame(height: 120)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                
                TextEditor(text: $description)
                    .background(Color.clear)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textPrimary)
                    .padding(AppTheme.spacing12)
                
                if description.isEmpty {
                    Text("What happened? What were you trying to do?")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textTertiary)
                        .padding(.horizontal, AppTheme.spacing16)
                        .padding(.vertical, AppTheme.spacing20)
                        .allowsHitTesting(false)
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var contactSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            Text("Contact Information (Optional)")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(AppTheme.textPrimary)
            
            Text("Leave your email if you'd like us to follow up with you about this issue.")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)
            
            TextField("<EMAIL>", text: $userEmail)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .font(.custom("AvenirNext-Medium", size: 16))
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var submitSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            Button(action: submitProblemReport) {
                HStack(spacing: AppTheme.spacing12) {
                    if isSubmitting {
                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 18, weight: .bold))
                    }
                    
                    Text(isSubmitting ? "Sending Report..." : "Send Report")
                        .font(.custom("AvenirNext-Bold", size: 18))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, AppTheme.spacing16)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .fill(canSubmit ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.gray))
                )
                .shadow(color: canSubmit ? AppTheme.primaryColor.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
            }
            .disabled(!canSubmit || isSubmitting)
            .scaleEffect(isSubmitting ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSubmitting)
            
            Text("Your feedback is valuable and helps us improve the app for everyone.")
                .font(.custom("AvenirNext-Medium", size: 12))
                .foregroundColor(AppTheme.textTertiary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var canSubmit: Bool {
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func submitProblemReport() {
        isSubmitting = true
        HapticFeedbackManager.shared.generateImpact(style: .medium)
        
        viewModel.submitProblemReport(
            category: selectedCategory,
            description: description,
            userEmail: userEmail.isEmpty ? nil : userEmail
        ) { result in
            DispatchQueue.main.async {
                isSubmitting = false
                switch result {
                case .success:
                    HapticFeedbackManager.shared.generateNotification(.success)
                    showSuccessMessage = true
                case .failure(let error):
                    HapticFeedbackManager.shared.generateNotification(.error)
                    print("Problem report submission failed: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - Supporting Types

enum ProblemCategory: String, CaseIterable {
    case bug = "Bug/Crash"
    case performance = "Performance"
    case ui = "UI/Design Issue"
    case feature = "Feature Request"
    case account = "Account Issue"
    case other = "Other"

    var icon: String {
        switch self {
        case .bug: return "ladybug.fill"
        case .performance: return "speedometer"
        case .ui: return "paintbrush.fill"
        case .feature: return "lightbulb.fill"
        case .account: return "person.circle.fill"
        case .other: return "ellipsis.circle.fill"
        }
    }

    var description: String {
        switch self {
        case .bug: return "App crashes, errors, or unexpected behavior"
        case .performance: return "Slow loading, freezing, or lag issues"
        case .ui: return "Layout problems, visual glitches, or design issues"
        case .feature: return "Suggestions for new features or improvements"
        case .account: return "Login, profile, or account-related problems"
        case .other: return "Other issues not listed above"
        }
    }
}

struct ProblemCategoryCard: View {
    let category: ProblemCategory
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        let circleFill = isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial)
        let circleStroke = isSelected ? Color.clear : Color.white.opacity(0.3)
        let iconColor = isSelected ? Color.white : AppTheme.textSecondary
        let textColor = isSelected ? AppTheme.textPrimary : AppTheme.textSecondary
        let backgroundFill = isSelected ? AnyShapeStyle(.ultraThinMaterial) : AnyShapeStyle(Color.clear)
        let borderStroke = isSelected ? AppTheme.primaryColor.opacity(0.5) : Color.white.opacity(0.2)

        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            onSelect()
        }) {
            VStack(spacing: AppTheme.spacing8) {
                ZStack {
                    Circle()
                        .fill(circleFill)
                        .frame(width: 40, height: 40)
                        .overlay(
                            Circle()
                                .stroke(circleStroke, lineWidth: 1)
                        )

                    Image(systemName: category.icon)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(iconColor)
                }

                Text(category.rawValue)
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(textColor)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(AppTheme.spacing12)
            .frame(maxWidth: .infinity, minHeight: 80)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .fill(backgroundFill)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                            .stroke(borderStroke, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - View Model

class ProblemReportViewModel: ObservableObject {
    @Published var errorMessage: String?

    func submitProblemReport(
        category: ProblemCategory,
        description: String,
        userEmail: String?,
        completion: @escaping (Result<Void, Error>) -> Void
    ) {
        guard let currentUserID = ProfileViewModel.shared.userProfile?.id else {
            completion(.failure(NSError(domain: "ProblemReport", code: 0, userInfo: [NSLocalizedDescriptionKey: "User not authenticated"])))
            return
        }

        let reportData: [String: Any] = [
            "userID": currentUserID,
            "category": category.rawValue,
            "description": description,
            "userEmail": userEmail ?? "",
            "timestamp": Timestamp(),
            "status": "open",
            "appVersion": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown",
            "deviceInfo": UIDevice.current.model
        ]

        Firestore.firestore().collection("problemReports").addDocument(data: reportData) { error in
            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to submit report: \(error.localizedDescription)"
                }
                completion(.failure(error))
            } else {
                completion(.success(()))
            }
        }
    }
}

struct ReportProblemView_Previews: PreviewProvider {
    static var previews: some View {
        ReportProblemView()
    }
}
