import SwiftUI
import FirebaseAuth
import FirebaseFirestore

// MARK: - Enhanced Onboarding Flow
struct EnhancedOnboardingFlow: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = ProfileViewModel.shared
    @State private var currentStep: OnboardingStep = .emailVerification
    @State private var progress: Double = 0.0

    // Profile data collection
    @State private var firstName: String = ""
    @State private var lastName: String = ""
    @State private var birthDate: Date = Calendar.current.date(byAdding: .year, value: -20, to: Date()) ?? Date()
    @State private var gender: String = ""
    @State private var height: String = ""
    @State private var aboutMe: String = ""
    @State private var gradeLevel: String = ""
    @State private var major: String = ""
    @State private var collegeName: String = ""
    @State private var housingStatus: String = ""
    @State private var dormType: String = ""
    @State private var monthlyRentMin: Int = 500
    @State private var monthlyRentMax: Int = 1500
    @State private var budgetMin: Int = 400
    @State private var budgetMax: Int = 1200
    @State private var cleanliness: Int = 3
    @State private var sleepSchedule: String = ""
    @State private var smoker: Bool = false
    @State private var petFriendly: Bool = false
    @State private var selectedImages: [UIImage] = []
    @State private var isEmailVerified: Bool = false
    @State private var isSavingProfile: Bool = false
    @State private var saveError: String? = nil
    @State private var isSavingStep: Bool = false
    @State private var stepSaveError: String? = nil

    // Enhanced housing preferences data to match MyProfileView
    @State private var primaryHousingPreference: PrimaryHousingPreference? = nil
    @State private var secondaryHousingType: String = ""
    @State private var roommateCountNeeded: Int = 0
    @State private var roommateCountExisting: Int = 0
    @State private var propertyDetails: String = ""
    @State private var propertyAddress: String = ""
    @State private var propertyImageUrls: [String] = []
    @State private var roomType: String = ""
    @State private var leaseStartDate: Date = Date()
    @State private var leaseDurationText: String = ""
    @State private var rentMin: Double = 0
    @State private var rentMax: Double = 5000
    @State private var selectedSpecialLeaseConditions: [String] = []
    @State private var selectedAmenities: [String] = []

    // Enhanced lifestyle data to match profile setup
    @State private var selectedPets: [String] = []
    @State private var selectedDrinking: String = ""
    @State private var selectedSmoking: String = ""
    @State private var selectedCannabis: String = ""
    @State private var selectedWorkout: String = ""
    @State private var selectedDietaryPreferences: [String] = []
    @State private var selectedSocialMedia: String = ""
    @State private var selectedSleepingHabits: String = ""

    // Quiz and interests data
    @State private var selectedInterests: [String] = []
    @State private var goingOutQuizAnswers: [String] = []
    @State private var weekendQuizAnswers: [String] = []
    @State private var phoneQuizAnswers: [String] = []

    var body: some View {
        ZStack {
            // Stunning animated background
            AnimatedBackground()

            VStack(spacing: 0) {
                // Progress bar
                progressBar

                // Content area with scroll to top functionality
                ScrollViewReader { proxy in
                    ScrollView {
                        VStack(spacing: AppTheme.spacing24) {
                            currentStepView
                        }
                        .padding(.horizontal, AppTheme.spacing20)
                        .padding(.vertical, AppTheme.spacing32)
                        .id("top")
                    }
                    .onChange(of: currentStep) { _, _ in
                        // Scroll to top when step changes
                        withAnimation(.easeInOut(duration: 0.5)) {
                            proxy.scrollTo("top", anchor: .top)
                        }
                    }
                }

                // Navigation buttons
                navigationButtons

                // Step save error display
                if let stepSaveError = stepSaveError {
                    VStack(spacing: AppTheme.spacing8) {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text(stepSaveError)
                                .font(AppTheme.caption)
                                .foregroundColor(.orange)
                            Spacer()
                            Button("Dismiss") {
                                self.stepSaveError = nil
                            }
                            .font(AppTheme.caption)
                            .foregroundColor(.orange)
                        }
                        .padding(.horizontal, AppTheme.spacing16)
                        .padding(.vertical, AppTheme.spacing8)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(AppTheme.radiusSmall)
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .transition(.slide)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            updateProgress()
            checkEmailVerification()
            startEmailVerificationTimer()
            loadExistingProgress()
        }
    }

    // MARK: - Progress Bar
    private var progressBar: some View {
        VStack(spacing: AppTheme.spacing8) {
            HStack {
                Text("Step \(currentStep.stepNumber) of \(OnboardingStep.totalSteps)")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
                Spacer()
                Text("\(Int(progress * 100))%")
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }

            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppTheme.primaryColor))
                .scaleEffect(y: 2)
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing16)
    }

    // MARK: - Current Step View
    @ViewBuilder
    private var currentStepView: some View {
        switch currentStep {
        case .emailVerification:
            CustomEmailVerificationView(isEmailVerified: $isEmailVerified)
        case .welcome:
            WelcomeStepView()
        case .personalInfo:
            PersonalInfoStepView(
                firstName: $firstName,
                lastName: $lastName,
                birthDate: $birthDate,
                gender: $gender
            )
        case .physicalInfo:
            PhysicalInfoStepView(
                height: $height,
                aboutMe: $aboutMe
            )
        case .academicInfo:
            AcademicInfoStepView(
                gradeLevel: $gradeLevel,
                major: $major,
                collegeName: $collegeName
            )
        case .housingPreferences:
            HousingPreferencesStepView(
                primaryHousingPreference: $primaryHousingPreference,
                secondaryHousingType: $secondaryHousingType,
                roommateCountNeeded: $roommateCountNeeded,
                roommateCountExisting: $roommateCountExisting,
                propertyDetails: $propertyDetails,
                propertyAddress: $propertyAddress,
                propertyImageUrls: $propertyImageUrls,
                roomType: $roomType,
                leaseStartDate: $leaseStartDate,
                leaseDurationText: $leaseDurationText,
                rentMin: $rentMin,
                rentMax: $rentMax,
                selectedSpecialLeaseConditions: $selectedSpecialLeaseConditions,
                selectedAmenities: $selectedAmenities,
                budgetMin: Binding(
                    get: { Double(budgetMin) },
                    set: { budgetMin = Int($0) }
                ),
                budgetMax: Binding(
                    get: { Double(budgetMax) },
                    set: { budgetMax = Int($0) }
                )
            )

        case .lifestylePreferences:
            EnhancedLifestylePreferencesStepView(
                cleanliness: $cleanliness,
                sleepSchedule: $sleepSchedule,
                smoker: $smoker,
                petFriendly: $petFriendly,
                selectedPets: $selectedPets,
                selectedDrinking: $selectedDrinking,
                selectedSmoking: $selectedSmoking,
                selectedCannabis: $selectedCannabis,
                selectedWorkout: $selectedWorkout,
                selectedDietaryPreferences: $selectedDietaryPreferences,
                selectedSocialMedia: $selectedSocialMedia,
                selectedSleepingHabits: $selectedSleepingHabits
            )
        case .interests:
            InterestsStepView(selectedInterests: $selectedInterests)
        case .goingOutQuiz:
            GoingOutQuizStepView(answers: $goingOutQuizAnswers)
        case .weekendsQuiz:
            WeekendsQuizStepView(answers: $weekendQuizAnswers)
        case .phoneQuiz:
            PhoneQuizStepView(answers: $phoneQuizAnswers)
        case .profilePhotos:
            ProfilePhotosStepView(selectedImages: $selectedImages)
        case .completion:
            CompletionStepView(
                isSaving: isSavingProfile,
                saveError: saveError
            )
        }
    }

    // MARK: - Navigation Buttons
    private var navigationButtons: some View {
        HStack(spacing: AppTheme.spacing16) {
            if currentStep != .emailVerification {
                Button("Back") {
                    goToPreviousStep()
                }
                .buttonStyle(SecondaryButtonStyle())
                .frame(maxWidth: .infinity)
            }

            Button(action: {
                print("🔘 EnhancedOnboardingFlow: Continue button tapped for step: \(currentStep)")
                if currentStep == .completion {
                    completeOnboarding()
                } else {
                    goToNextStep()
                }
            }) {
                HStack {
                    if isSavingStep {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                        Text("Saving...")
                    } else {
                        Text(currentStep == .completion ? "Get Started" : "Continue")
                    }
                }
            }
            .buttonStyle(PrimaryButtonStyle(
                backgroundColor: (canProceed && !isSavingStep) ? AppTheme.primaryColor : AppTheme.primaryColor.opacity(0.3)
            ))
            .frame(maxWidth: .infinity)
            .disabled(!canProceed || isSavingStep)
            .opacity((canProceed && !isSavingStep) ? 1.0 : 0.5)
            .animation(.easeInOut(duration: 0.3), value: canProceed)
            .onAppear {
                print("🔍 EnhancedOnboardingFlow: Button state - Step: \(currentStep), canProceed: \(canProceed)")
            }

            // Show step save error if any
            if let error = stepSaveError {
                Text(error)
                    .font(AppTheme.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.top, AppTheme.spacing8)
            }
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.bottom, AppTheme.spacing32)
    }

    // MARK: - Helper Methods
    private func updateProgress() {
        withAnimation(.easeInOut(duration: 0.3)) {
            progress = Double(currentStep.stepNumber) / Double(OnboardingStep.totalSteps)
        }
    }

    private func goToNextStep() {
        print("🔄 EnhancedOnboardingFlow: Moving from \(currentStep) to next step")

        // Set loading state
        isSavingStep = true
        stepSaveError = nil

        // Save current step data before proceeding
        saveCurrentStepData { success in
            DispatchQueue.main.async {
                self.isSavingStep = false

                if success {
                    print("✅ EnhancedOnboardingFlow: Step data saved successfully, proceeding to next step")
                    self.stepSaveError = nil

                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        self.currentStep = self.currentStep.next()
                        self.updateProgress()
                    }

                    // Haptic feedback for success
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                } else {
                    print("❌ EnhancedOnboardingFlow: Failed to save step data, but allowing progression")
                    self.stepSaveError = "Failed to save data. Your progress will be saved at the end."

                    // Still allow progression (data will be saved at completion)
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        self.currentStep = self.currentStep.next()
                        self.updateProgress()
                    }

                    // Warning haptic feedback
                    HapticFeedbackManager.shared.generateNotification(.warning)
                }
            }
        }
    }

    private func goToPreviousStep() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            currentStep = currentStep.previous()
            updateProgress()
        }

        // Haptic feedback
        HapticFeedbackManager.shared.generateImpact(style: .light)
    }

    private var canProceed: Bool {
        let result: Bool
        switch currentStep {
        case .emailVerification:
            result = isEmailVerified
            print("🔍 canProceed - emailVerification: isVerified=\(result) (state: \(isEmailVerified), auth: \(Auth.auth().currentUser?.isEmailVerified ?? false))")
        case .welcome:
            result = true
            print("🔍 canProceed - welcome: always true")
        case .personalInfo:
            result = !firstName.isEmpty && !lastName.isEmpty && !gender.isEmpty
            print("🔍 canProceed - personalInfo: firstName='\(firstName)', lastName='\(lastName)', gender='\(gender)' -> \(result)")
        case .physicalInfo:
            result = !height.isEmpty && !aboutMe.isEmpty
            print("🔍 canProceed - physicalInfo: height='\(height)', aboutMe='\(aboutMe)' -> \(result)")
        case .academicInfo:
            result = !gradeLevel.isEmpty && !major.isEmpty && !collegeName.isEmpty
            print("🔍 canProceed - academicInfo: gradeLevel='\(gradeLevel)', major='\(major)', collegeName='\(collegeName)' -> \(result)")
        case .housingPreferences:
            // Basic validation: must have primary housing preference
            let hasBasicPreference = primaryHousingPreference != nil

            // Additional validation based on preference type
            var additionalValidation = true
            if let pref = primaryHousingPreference {
                switch pref {
                case .lookingForLease:
                    // Must have room type and budget range
                    additionalValidation = !roomType.isEmpty && budgetMin > 0 && budgetMax > budgetMin
                case .lookingForRoommate:
                    // Must have property details, room type, and rent range
                    additionalValidation = !propertyDetails.isEmpty && !roomType.isEmpty && rentMin > 0 && rentMax > rentMin
                case .lookingToFindTogether:
                    // Must have budget range
                    additionalValidation = budgetMin > 0 && budgetMax > budgetMin
                }
            }

            result = hasBasicPreference && additionalValidation
            print("🔍 canProceed - housingPreferences: preference='\(primaryHousingPreference?.rawValue ?? "none")', additional=\(additionalValidation) -> \(result)")

        case .lifestylePreferences:
            result = !selectedSleepingHabits.isEmpty && !selectedDrinking.isEmpty && !selectedSmoking.isEmpty
            print("🔍 canProceed - lifestylePreferences: sleepingHabits='\(selectedSleepingHabits)', drinking='\(selectedDrinking)', smoking='\(selectedSmoking)' -> \(result)")
        case .interests:
            result = !selectedInterests.isEmpty
            print("🔍 canProceed - interests: count=\(selectedInterests.count) -> \(result)")
        case .goingOutQuiz:
            result = goingOutQuizAnswers.count >= 4 // All 4 questions answered
            print("🔍 canProceed - goingOutQuiz: answersCount=\(goingOutQuizAnswers.count) -> \(result)")
        case .weekendsQuiz:
            result = weekendQuizAnswers.count >= 4 // All 4 questions answered
            print("🔍 canProceed - weekendsQuiz: answersCount=\(weekendQuizAnswers.count) -> \(result)")
        case .phoneQuiz:
            result = phoneQuizAnswers.count >= 4 // All 4 questions answered
            print("🔍 canProceed - phoneQuiz: answersCount=\(phoneQuizAnswers.count) -> \(result)")
        case .profilePhotos:
            result = selectedImages.count >= 1
            print("🔍 canProceed - profilePhotos: imageCount=\(selectedImages.count) -> \(result)")
        case .completion:
            result = true
            print("🔍 canProceed - completion: always true")
        }
        return result
    }

    private func completeOnboarding() {
        print("🏁 EnhancedOnboardingFlow: Starting onboarding completion...")

        // Save all profile data first
        isSavingProfile = true
        saveError = nil

        saveProfileData { success in
            DispatchQueue.main.async {
                self.isSavingProfile = false

                if success {
                    // Mark onboarding as completed only after successful save
                    print("🏁 EnhancedOnboardingFlow: Setting onboarding completed to TRUE")
                    UserDefaults.standard.set(true, forKey: "onboardingCompleted")
                    let flagAfterSet = UserDefaults.standard.bool(forKey: "onboardingCompleted")
                    print("🔍 EnhancedOnboardingFlow: Flag after setting to true: \(flagAfterSet)")

                    // Dismiss onboarding
                    self.presentationMode.wrappedValue.dismiss()

                    // Success haptic
                    HapticFeedbackManager.shared.generateNotification(.success)
                } else {
                    print("❌ EnhancedOnboardingFlow: Failed to save profile data, not completing onboarding")
                    // Show error to user - they can try again
                    HapticFeedbackManager.shared.generateNotification(.error)
                }
            }
        }
    }

    // MARK: - Load Existing Progress
    private func loadExistingProgress() {
        print("🔄 EnhancedOnboardingFlow: Loading existing onboarding progress...")

        guard let currentUser = Auth.auth().currentUser else {
            print("❌ EnhancedOnboardingFlow: No authenticated user found for progress loading")
            return
        }

        let db = Firestore.firestore()
        let userRef = db.collection("users").document(currentUser.uid)

        userRef.getDocument { document, error in
            if let error = error {
                print("❌ EnhancedOnboardingFlow: Error loading progress: \(error.localizedDescription)")
                return
            }

            guard let document = document, document.exists, let data = document.data() else {
                print("📝 EnhancedOnboardingFlow: No existing user document found, starting fresh")
                return
            }

            DispatchQueue.main.async {
                // Load existing data if available
                if let firstName = data["firstName"] as? String { self.firstName = firstName }
                if let lastName = data["lastName"] as? String { self.lastName = lastName }
                if let gender = data["gender"] as? String { self.gender = gender }
                if let height = data["height"] as? String { self.height = height }
                if let aboutMe = data["aboutMe"] as? String { self.aboutMe = aboutMe }
                if let gradeLevel = data["gradeLevel"] as? String { self.gradeLevel = gradeLevel }
                if let major = data["major"] as? String { self.major = major }
                if let collegeName = data["collegeName"] as? String { self.collegeName = collegeName }

                // Load housing preferences
                if let housingStatus = data["housingStatus"] as? String {
                    self.primaryHousingPreference = PrimaryHousingPreference(rawValue: housingStatus)
                }
                if let secondaryType = data["desiredLeaseHousingType"] as? String { self.secondaryHousingType = secondaryType }
                if let roommateNeeded = data["roommateCountNeeded"] as? Int { self.roommateCountNeeded = roommateNeeded }
                if let roommateExisting = data["roommateCountExisting"] as? Int { self.roommateCountExisting = roommateExisting }

                // Load lifestyle preferences
                if let cleanliness = data["cleanliness"] as? Int { self.cleanliness = cleanliness }
                if let sleepSchedule = data["sleepSchedule"] as? String { self.sleepSchedule = sleepSchedule }
                if let smoker = data["smoker"] as? Bool { self.smoker = smoker }
                if let petFriendly = data["petFriendly"] as? Bool { self.petFriendly = petFriendly }

                // Load enhanced lifestyle data
                if let pets = data["pets"] as? [String] { self.selectedPets = pets }
                if let drinking = data["drinking"] as? String { self.selectedDrinking = drinking }
                if let smoking = data["smoking"] as? String { self.selectedSmoking = smoking }
                if let cannabis = data["cannabis"] as? String { self.selectedCannabis = cannabis }
                if let workout = data["workout"] as? String { self.selectedWorkout = workout }
                if let dietary = data["dietaryPreferences"] as? [String] { self.selectedDietaryPreferences = dietary }
                if let socialMedia = data["socialMedia"] as? String { self.selectedSocialMedia = socialMedia }
                if let sleepingHabits = data["sleepingHabits"] as? String { self.selectedSleepingHabits = sleepingHabits }

                // Determine where to resume based on last completed step
                if let lastStep = data["lastOnboardingStep"] as? Int {
                    let resumeStep = min(lastStep + 1, OnboardingStep.allCases.count - 1)
                    if let step = OnboardingStep(rawValue: resumeStep) {
                        print("🔄 EnhancedOnboardingFlow: Resuming from step \(step) (last completed: \(lastStep))")
                        self.currentStep = step
                        self.updateProgress()
                    }
                }

                print("✅ EnhancedOnboardingFlow: Successfully loaded existing progress")
            }
        }
    }

    // MARK: - Incremental Step Data Saving
    private func saveCurrentStepData(completion: @escaping (Bool) -> Void) {
        print("💾 EnhancedOnboardingFlow: Saving data for step: \(currentStep)")

        guard let currentUser = Auth.auth().currentUser else {
            print("❌ EnhancedOnboardingFlow: No authenticated user found")
            completion(false)
            return
        }

        // Check network connectivity
        if !NetworkMonitor.shared.isConnected {
            print("⚠️ EnhancedOnboardingFlow: No network connection, skipping incremental save")
            completion(true) // Allow progression offline, will save at completion
            return
        }

        let db = Firestore.firestore()
        let userRef = db.collection("users").document(currentUser.uid)

        var updateData: [String: Any] = [:]

        // Prepare data based on current step
        switch currentStep {
        case .emailVerification:
            updateData["isEmailVerified"] = Auth.auth().currentUser?.isEmailVerified ?? false
            updateData["emailVerificationTimestamp"] = FieldValue.serverTimestamp()

        case .welcome:
            // No data to save for welcome step
            completion(true)
            return

        case .personalInfo:
            updateData["firstName"] = firstName
            updateData["lastName"] = lastName
            updateData["gender"] = gender

            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyy-MM-dd"
            updateData["dateOfBirth"] = dateFormatter.string(from: birthDate)

        case .physicalInfo:
            updateData["height"] = height
            updateData["aboutMe"] = aboutMe

        case .academicInfo:
            updateData["gradeLevel"] = gradeLevel
            updateData["major"] = major
            updateData["collegeName"] = collegeName

        case .housingPreferences:
            updateData["housingStatus"] = primaryHousingPreference?.rawValue
            updateData["desiredLeaseHousingType"] = secondaryHousingType
            updateData["roommateCountNeeded"] = roommateCountNeeded
            updateData["roommateCountExisting"] = roommateCountExisting
            updateData["propertyDetails"] = propertyDetails
            updateData["propertyAddress"] = propertyAddress
            updateData["roomType"] = roomType
            updateData["leaseDuration"] = leaseDurationText
            updateData["monthlyRentMin"] = rentMin
            updateData["monthlyRentMax"] = rentMax
            updateData["specialLeaseConditions"] = selectedSpecialLeaseConditions
            updateData["amenities"] = selectedAmenities
            updateData["budgetMin"] = Double(budgetMin)
            updateData["budgetMax"] = Double(budgetMax)

        case .lifestylePreferences:
            updateData["cleanliness"] = cleanliness
            updateData["sleepSchedule"] = sleepSchedule
            updateData["smoker"] = smoker
            updateData["petFriendly"] = petFriendly
            updateData["pets"] = selectedPets
            updateData["drinking"] = selectedDrinking
            updateData["smoking"] = selectedSmoking
            updateData["cannabis"] = selectedCannabis
            updateData["workout"] = selectedWorkout
            updateData["dietaryPreferences"] = selectedDietaryPreferences
            updateData["socialMedia"] = selectedSocialMedia
            updateData["sleepingHabits"] = selectedSleepingHabits

        case .interests:
            updateData["interests"] = selectedInterests

        case .goingOutQuiz:
            updateData["goingOutQuizAnswers"] = goingOutQuizAnswers

        case .weekendsQuiz:
            updateData["weekendQuizAnswers"] = weekendQuizAnswers

        case .phoneQuiz:
            updateData["phoneQuizAnswers"] = phoneQuizAnswers

        case .profilePhotos:
            // Handle image uploads separately if needed
            // For now, just mark that photos step was completed
            updateData["profilePhotosStepCompleted"] = true

        case .completion:
            // This will be handled by the full save function
            completion(true)
            return
        }

        // Add step completion timestamp
        updateData["step_\(currentStep.rawValue)_completed"] = FieldValue.serverTimestamp()
        updateData["lastOnboardingStep"] = currentStep.rawValue
        updateData["onboardingProgress"] = Double(currentStep.stepNumber) / Double(OnboardingStep.totalSteps)

        print("💾 EnhancedOnboardingFlow: Updating Firestore with data: \(updateData)")

        userRef.updateData(updateData) { error in
            if let error = error {
                print("❌ EnhancedOnboardingFlow: Failed to save step data: \(error.localizedDescription)")
                // For non-critical errors, still allow progression
                let errorCode = (error as NSError).code
                if errorCode == 7 { // PERMISSION_DENIED - allow progression, will retry at completion
                    print("⚠️ EnhancedOnboardingFlow: Permission denied, allowing progression")
                    completion(true)
                } else {
                    completion(false)
                }
            } else {
                print("✅ EnhancedOnboardingFlow: Successfully saved step \(self.currentStep) data")
                completion(true)
            }
        }
    }

    private func saveProfileData(completion: @escaping (Bool) -> Void) {
        print("💾 Saving complete profile data...")
        debugPrintAllData()

        // Create comprehensive profile with all collected data
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"

        if var profile = viewModel.userProfile {
            // Update existing profile with all new data
            profile.firstName = firstName
            profile.lastName = lastName
            profile.dateOfBirth = birthDate
            profile.gender = gender
            profile.height = height
            profile.aboutMe = aboutMe
            profile.gradeLevel = gradeLevel
            profile.major = major
            profile.collegeName = collegeName

            // Enhanced housing preferences
            profile.housingStatus = primaryHousingPreference?.rawValue
            profile.desiredLeaseHousingType = secondaryHousingType
            profile.roommateCountNeeded = roommateCountNeeded
            profile.roommateCountExisting = roommateCountExisting
            profile.propertyDetails = propertyDetails
            profile.propertyAddress = propertyAddress
            profile.propertyImageUrls = propertyImageUrls
            profile.roomType = roomType
            profile.leaseStartDate = leaseStartDate
            profile.leaseDuration = leaseDurationText
            profile.monthlyRentMin = rentMin
            profile.monthlyRentMax = rentMax
            profile.specialLeaseConditions = selectedSpecialLeaseConditions
            profile.amenities = selectedAmenities

            // Budget preferences
            profile.budgetMin = Double(budgetMin)
            profile.budgetMax = Double(budgetMax)
            profile.cleanliness = cleanliness
            profile.sleepSchedule = sleepSchedule
            profile.smoker = smoker
            profile.petFriendly = petFriendly

            // Enhanced lifestyle data
            profile.pets = selectedPets
            profile.drinking = selectedDrinking
            profile.smoking = selectedSmoking
            profile.cannabis = selectedCannabis
            profile.workout = selectedWorkout
            profile.dietaryPreferences = selectedDietaryPreferences
            profile.socialMedia = selectedSocialMedia
            profile.sleepingHabits = selectedSleepingHabits

            // Update derived fields for search
            profile.firstName_lowercase = firstName.lowercased()
            profile.lastName_lowercase = lastName.lowercased()
            profile.collegeName_lowercase = collegeName.lowercased()

            print("💾 Updating existing profile with data:")
            print("   - Name: \(profile.firstName ?? "nil") \(profile.lastName ?? "nil")")
            print("   - Housing: \(profile.housingStatus ?? "nil")")
            print("   - Room Type: \(profile.roomType ?? "nil")")
            print("   - Budget: \(profile.budgetMin ?? 0) - \(profile.budgetMax ?? 0)")

            viewModel.updateUserProfile(updatedProfile: profile) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        print("✅ Enhanced profile updated successfully!")
                        // Upload profile images if any
                        self.uploadProfileImages()
                        completion(true)
                    case .failure(let error):
                        print("❌ Error updating profile: \(error.localizedDescription)")
                        self.saveError = error.localizedDescription
                        completion(false)
                    }
                }
            }
        } else {
            // Create new comprehensive profile
            let newProfile = UserModel(
                email: Auth.auth().currentUser?.email ?? "<EMAIL>",
                isEmailVerified: Auth.auth().currentUser?.isEmailVerified ?? false,
                aboutMe: aboutMe,
                firstName: firstName,
                lastName: lastName,
                dateOfBirth: birthDate,
                gender: gender,
                height: height,
                gradeLevel: gradeLevel,
                major: major,
                collegeName: collegeName,
                housingStatus: primaryHousingPreference?.rawValue,
                desiredLeaseHousingType: secondaryHousingType,
                roommateCountNeeded: roommateCountNeeded,
                roommateCountExisting: roommateCountExisting,
                propertyDetails: propertyDetails,
                propertyAddress: propertyAddress,
                propertyImageUrls: propertyImageUrls,
                roomType: roomType,
                leaseStartDate: leaseStartDate,
                leaseDuration: leaseDurationText,
                monthlyRentMin: rentMin,
                monthlyRentMax: rentMax,
                specialLeaseConditions: selectedSpecialLeaseConditions,
                amenities: selectedAmenities,
                budgetMin: Double(budgetMin),
                budgetMax: Double(budgetMax),
                cleanliness: cleanliness,
                sleepSchedule: sleepSchedule,
                smoker: smoker,
                petFriendly: petFriendly,
                // Enhanced lifestyle fields
                pets: selectedPets,
                drinking: selectedDrinking,
                smoking: selectedSmoking,
                cannabis: selectedCannabis,
                workout: selectedWorkout,
                dietaryPreferences: selectedDietaryPreferences,
                socialMedia: selectedSocialMedia,
                sleepingHabits: selectedSleepingHabits
            )

            print("💾 Creating new profile with data:")
            print("   - Email: \(newProfile.email)")
            print("   - Name: \(newProfile.firstName ?? "nil") \(newProfile.lastName ?? "nil")")
            print("   - Housing: \(newProfile.housingStatus ?? "nil")")
            print("   - Room Type: \(newProfile.roomType ?? "nil")")
            print("   - Budget: \(newProfile.budgetMin ?? 0) - \(newProfile.budgetMax ?? 0)")

            viewModel.updateUserProfile(updatedProfile: newProfile) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        print("✅ Enhanced profile created successfully!")
                        // Upload profile images if any
                        self.uploadProfileImages()
                        completion(true)
                    case .failure(let error):
                        print("❌ Error creating profile: \(error.localizedDescription)")
                        self.saveError = error.localizedDescription
                        completion(false)
                    }
                }
            }
        }
    }

    private func uploadProfileImages() {
        guard !selectedImages.isEmpty else {
            print("📸 No profile images to upload")
            return
        }

        print("📸 Uploading \(selectedImages.count) profile images...")

        // Upload images one by one
        var uploadedUrls: [String] = []
        let group = DispatchGroup()

        for (index, image) in selectedImages.enumerated() {
            group.enter()
            viewModel.uploadProfileImage(image: image) { result in
                switch result {
                case .success(let url):
                    print("✅ Image \(index + 1) uploaded: \(url)")
                    uploadedUrls.append(url)
                case .failure(let error):
                    print("❌ Failed to upload image \(index + 1): \(error.localizedDescription)")
                }
                group.leave()
            }
        }

        group.notify(queue: .main) {
            if !uploadedUrls.isEmpty {
                // Update profile with image URLs
                if var profile = self.viewModel.userProfile {
                    profile.profileImageUrls = uploadedUrls
                    // CRITICAL FIX: Also update the legacy profileImageUrl field for backward compatibility
                    profile.profileImageUrl = uploadedUrls.first
                    print("🔧 EnhancedOnboardingFlow: Updating profile with \(uploadedUrls.count) images")
                    print("   - profileImageUrls: \(uploadedUrls)")
                    print("   - profileImageUrl (legacy): \(uploadedUrls.first ?? "nil")")

                    self.viewModel.updateUserProfile(updatedProfile: profile) { result in
                        switch result {
                        case .success:
                            print("✅ Profile updated with \(uploadedUrls.count) image URLs")
                            print("✅ Legacy profileImageUrl field also updated for progress ring compatibility")
                        case .failure(let error):
                            print("❌ Failed to update profile with image URLs: \(error.localizedDescription)")
                        }
                    }
                }
            }
        }
    }

    private func showSaveError(_ error: Error) {
        // You can implement a proper error alert here
        print("💥 Save Error: \(error.localizedDescription)")
        // For now, just print the error. In a real app, you'd show an alert to the user
    }

    // MARK: - Debug Functions
    private func debugPrintAllData() {
        print("🔍 === ONBOARDING DATA DEBUG ===")
        print("📧 Auth User: \(Auth.auth().currentUser?.email ?? "nil")")
        print("📧 Email Verified: \(Auth.auth().currentUser?.isEmailVerified ?? false)")
        print("👤 Name: \(firstName) \(lastName)")
        print("🎓 College: \(collegeName)")
        print("🏠 Housing Preference: \(primaryHousingPreference?.rawValue ?? "nil")")
        print("🏠 Room Type: \(roomType)")
        print("🏠 Property Details: \(propertyDetails)")
        print("🏠 Property Address: \(propertyAddress)")
        print("💰 Budget: \(budgetMin) - \(budgetMax)")
        print("💰 Rent: \(rentMin) - \(rentMax)")
        print("🏷️ Amenities: \(selectedAmenities)")
        print("📋 Lease Conditions: \(selectedSpecialLeaseConditions)")
        print("🍺 Drinking: \(selectedDrinking)")
        print("🚬 Smoking: \(selectedSmoking)")
        print("🌿 Cannabis: \(selectedCannabis)")
        print("💪 Workout: \(selectedWorkout)")
        print("🐕 Pets: \(selectedPets)")
        print("🥗 Diet: \(selectedDietaryPreferences)")
        print("📱 Social Media: \(selectedSocialMedia)")
        print("😴 Sleep: \(selectedSleepingHabits)")
        print("📸 Images: \(selectedImages.count)")
        print("🔍 === END DEBUG ===")
    }

    // MARK: - Email Verification Methods

    private func checkEmailVerification() {
        Auth.auth().currentUser?.reload { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ Error reloading user: \(error.localizedDescription)")
                } else {
                    let verified = Auth.auth().currentUser?.isEmailVerified ?? false
                    print("📧 Email verification check: \(verified)")
                    isEmailVerified = verified
                }
            }
        }
    }

    private func startEmailVerificationTimer() {
        // Only start timer if we're on email verification step and not verified yet
        guard currentStep == .emailVerification && !isEmailVerified else { return }

        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { timer in
            // Stop timer if we're no longer on email verification step
            if currentStep != .emailVerification {
                timer.invalidate()
                return
            }

            // Check verification status
            Auth.auth().currentUser?.reload { error in
                DispatchQueue.main.async {
                    if error == nil {
                        let verified = Auth.auth().currentUser?.isEmailVerified ?? false
                        print("🔄 Timer check - Email verified: \(verified)")
                        isEmailVerified = verified

                        // Stop timer if verified
                        if verified {
                            timer.invalidate()
                            print("✅ Email verified! Timer stopped.")
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Custom Email Verification View
struct CustomEmailVerificationView: View {
    @Binding var isEmailVerified: Bool
    @State private var isChecking = false
    @State private var status = "Checking verification..."
    @State private var showResend = false
    @State private var showBackToLogin = false
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            Spacer()

            // Icon with status
            VStack(spacing: AppTheme.spacing24) {
                ZStack {
                    Circle()
                        .fill(AppTheme.primaryGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 20)
                        .opacity(0.6)

                    Image(systemName: isEmailVerified ? "checkmark.circle.fill" : "envelope.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.white)
                        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isEmailVerified)
                }

                VStack(spacing: AppTheme.spacing12) {
                    Text("Verify Your Email")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text("We've sent a verification email to:")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)

                    Text(Auth.auth().currentUser?.email ?? "your email")
                        .font(AppTheme.subtitleFont)
                        .foregroundColor(AppTheme.primaryColor)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.center)
                }
            }

            // Status and actions
            VStack(spacing: AppTheme.spacing20) {
                Text(status)
                    .font(AppTheme.body)
                    .foregroundColor(isEmailVerified ? .green : AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .animation(.easeInOut(duration: 0.3), value: isEmailVerified)

                if !isEmailVerified {
                    VStack(spacing: AppTheme.spacing16) {
                        Button("Check Verification") {
                            checkVerification()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                        .disabled(isChecking)
                        .opacity(isChecking ? 0.6 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isChecking)

                        if showResend {
                            Button("Resend Email") {
                                resendEmail()
                            }
                            .buttonStyle(TertiaryButtonStyle())
                        }

                        // Back to Login button
                        Button("Back to Login") {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            showBackToLogin = true
                        }
                        .buttonStyle(TertiaryButtonStyle())
                        .foregroundColor(AppTheme.textSecondary)
                    }
                } else {
                    VStack(spacing: AppTheme.spacing16) {
                        Text("✅ Email Verified!")
                            .font(AppTheme.subtitleFont)
                            .foregroundColor(.green)
                            .fontWeight(.semibold)

                        Text("You can now continue to the next step")
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)

                        // Back to Login button (also available after verification)
                        Button("Back to Login") {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            showBackToLogin = true
                        }
                        .buttonStyle(TertiaryButtonStyle())
                        .foregroundColor(AppTheme.textSecondary)
                    }
                }
            }

            Spacer()
        }
        .onAppear {
            checkVerification()
            // Show resend button after 30 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                showResend = true
            }
        }
        .alert("Return to Login?", isPresented: $showBackToLogin) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                signOutAndReturnToLogin()
            }
        } message: {
            Text("This will sign you out and return you to the login screen. You can sign in with a different email or try again.")
        }
    }

    private func checkVerification() {
        isChecking = true
        status = "Checking verification status..."

        Auth.auth().currentUser?.reload { error in
            DispatchQueue.main.async {
                isChecking = false

                if let error = error {
                    status = "Error checking verification: \(error.localizedDescription)"
                    print("❌ Email verification check error: \(error.localizedDescription)")
                } else {
                    let verified = Auth.auth().currentUser?.isEmailVerified ?? false
                    isEmailVerified = verified

                    if verified {
                        status = "✅ Email verified! You can continue."
                        HapticFeedbackManager.shared.generateNotification(.success)
                        print("✅ Email verification successful!")
                    } else {
                        status = "Email not yet verified. Please check your inbox and click the verification link."
                        print("⏳ Email not yet verified")
                    }
                }
            }
        }
    }

    private func resendEmail() {
        Auth.auth().currentUser?.sendEmailVerification { error in
            DispatchQueue.main.async {
                if let error = error {
                    status = "Error sending email: \(error.localizedDescription)"
                    print("❌ Error resending verification email: \(error.localizedDescription)")
                } else {
                    status = "Verification email sent! Please check your inbox."
                    HapticFeedbackManager.shared.generateNotification(.success)
                    print("📧 Verification email resent successfully")
                }
            }
        }
    }

    private func signOutAndReturnToLogin() {
        do {
            try Auth.auth().signOut()
            HapticFeedbackManager.shared.generateNotification(.success)
            print("✅ User signed out successfully from email verification")

            // Dismiss the onboarding flow to return to login
            presentationMode.wrappedValue.dismiss()
        } catch let signOutError as NSError {
            print("❌ Error signing out: \(signOutError.localizedDescription)")
            status = "Error signing out: \(signOutError.localizedDescription)"
        }
    }
}

// MARK: - Onboarding Steps Enum
enum OnboardingStep: Int, CaseIterable {
    case emailVerification = 0
    case welcome = 1
    case personalInfo = 2
    case physicalInfo = 3
    case academicInfo = 4
    case housingPreferences = 5
    case lifestylePreferences = 6
    case interests = 7
    case goingOutQuiz = 8
    case weekendsQuiz = 9
    case phoneQuiz = 10
    case profilePhotos = 11
    case completion = 12

    var stepNumber: Int {
        return rawValue + 1
    }

    static var totalSteps: Int {
        return allCases.count
    }

    func next() -> OnboardingStep {
        let nextIndex = min(rawValue + 1, OnboardingStep.allCases.count - 1)
        return OnboardingStep(rawValue: nextIndex) ?? self
    }

    func previous() -> OnboardingStep {
        let previousIndex = max(rawValue - 1, 0)
        return OnboardingStep(rawValue: previousIndex) ?? self
    }
}

// MARK: - Preview
struct EnhancedOnboardingFlow_Previews: PreviewProvider {
    static var previews: some View {
        EnhancedOnboardingFlow()
            .preferredColorScheme(.dark)
    }
}
