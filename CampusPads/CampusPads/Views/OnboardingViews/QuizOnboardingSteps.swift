import SwiftUI

// MARK: - Interests Step View
struct InterestsStepView: View {
    @Binding var selectedInterests: [String]
    
    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            // Header with modern styling
            VStack(spacing: AppTheme.spacing16) {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 80, height: 80)
                        .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)
                    
                    Image(systemName: "heart.text.square.fill")
                        .font(.system(size: 36, weight: .medium))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                }
                
                VStack(spacing: AppTheme.spacing8) {
                    Text("Your Interests")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)
                        .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)
                    
                    Text("Tell us what you're passionate about")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Enhanced interests dropdown selection
            VStack(spacing: AppTheme.spacing16) {
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Text("What are you interested in?")
                            .font(.custom("AvenirNext-Semibold", size: 18))
                            .foregroundStyle(AppTheme.sexyGradient)

                        Spacer()

                        Text("\(selectedInterests.count)/10")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(selectedInterests.count >= 10 ? .red : AppTheme.textSecondary)
                    }

                    Text("Select up to 10 interests from our curated list")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(AppTheme.textSecondary)
                }

                // Interests dropdown selection (matching MyProfileView)
                InterestsDropdownSelection(
                    selectedInterests: $selectedInterests,
                    placeholder: "Select Interests",
                    label: nil,
                    maxSelections: 10
                )
            }

            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing24)
    }
}

// MARK: - Going Out Quiz Step View
struct GoingOutQuizStepView: View {
    @Binding var answers: [String]
    @State private var currentQuestionIndex = 0
    
    private let questions = [
        QuizQuestion(question: "You can find me...", options: ["Dancing 💃", "Socializing 🗣️"]),
        QuizQuestion(question: "I like to...", options: ["Dress Up 👗", "Dress Down 👕"]),
        QuizQuestion(question: "I tend to arrive...", options: ["Early ⏰", "Fashionably Late 🕒"]),
        QuizQuestion(question: "My exit strategy looks like...", options: ["Say Bye First 👋", "Disappear 🕶️"])
    ]
    
    var body: some View {
        OnboardingQuizView(
            title: "Going Out Quiz",
            subtitle: "How do you like to party?",
            icon: "party.popper.fill",
            questions: questions,
            answers: $answers,
            currentQuestionIndex: $currentQuestionIndex
        )
    }
}

// MARK: - Weekends Quiz Step View
struct WeekendsQuizStepView: View {
    @Binding var answers: [String]
    @State private var currentQuestionIndex = 0
    
    private let questions = [
        QuizQuestion(question: "Perfect weekend morning...", options: ["Sleep In 😴", "Early Rise 🌅"]),
        QuizQuestion(question: "Weekend plans are...", options: ["Spontaneous 🎲", "Planned 📅"]),
        QuizQuestion(question: "I prefer to...", options: ["Stay In 🏠", "Go Out 🌟"]),
        QuizQuestion(question: "Weekend energy...", options: ["Chill Vibes 😌", "Adventure Time 🚀"])
    ]
    
    var body: some View {
        OnboardingQuizView(
            title: "Weekends Quiz",
            subtitle: "How do you spend your free time?",
            icon: "calendar.badge.clock",
            questions: questions,
            answers: $answers,
            currentQuestionIndex: $currentQuestionIndex
        )
    }
}

// MARK: - Phone Quiz Step View
struct PhoneQuizStepView: View {
    @Binding var answers: [String]
    @State private var currentQuestionIndex = 0
    
    private let questions = [
        QuizQuestion(question: "My phone usage...", options: ["Always On 📱", "Minimal Use 📵"]),
        QuizQuestion(question: "Social media...", options: ["Love It 💕", "Not My Thing 🤷"]),
        QuizQuestion(question: "Text response time...", options: ["Instant ⚡", "When I Can ⏰"]),
        QuizQuestion(question: "Phone calls...", options: ["Love Them 📞", "Text Please 💬"])
    ]
    
    var body: some View {
        OnboardingQuizView(
            title: "Phone & Social Quiz",
            subtitle: "How connected are you?",
            icon: "iphone",
            questions: questions,
            answers: $answers,
            currentQuestionIndex: $currentQuestionIndex
        )
    }
}

// MARK: - Reusable Quiz View Component
struct OnboardingQuizView: View {
    let title: String
    let subtitle: String
    let icon: String
    let questions: [QuizQuestion]
    @Binding var answers: [String]
    @Binding var currentQuestionIndex: Int
    
    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            // Header
            VStack(spacing: AppTheme.spacing16) {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 80, height: 80)
                        .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)
                    
                    Image(systemName: icon)
                        .font(.system(size: 36, weight: .medium))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                }
                
                VStack(spacing: AppTheme.spacing8) {
                    Text(title)
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)
                        .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)
                    
                    Text(subtitle)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Progress indicator
            HStack(spacing: AppTheme.spacing8) {
                ForEach(0..<questions.count, id: \.self) { index in
                    Circle()
                        .fill(index < answers.count ? AppTheme.primaryColor : AppTheme.primaryColor.opacity(0.3))
                        .frame(width: 12, height: 12)
                        .scaleEffect(index == currentQuestionIndex ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: currentQuestionIndex)
                }
            }
            
            // Current question
            if currentQuestionIndex < questions.count {
                let question = questions[currentQuestionIndex]
                
                VStack(spacing: AppTheme.spacing24) {
                    Text(question.question)
                        .font(.custom("AvenirNext-Bold", size: 22))
                        .foregroundStyle(AppTheme.sexyGradient)
                        .multilineTextAlignment(.center)
                    
                    VStack(spacing: AppTheme.spacing16) {
                        ForEach(question.options, id: \.self) { option in
                            Button(action: {
                                selectAnswer(option)
                            }) {
                                HStack {
                                    Text(option)
                                        .font(.custom("AvenirNext-Semibold", size: 18))
                                        .foregroundColor(AppTheme.textPrimary)
                                    
                                    Spacer()
                                }
                                .padding(.horizontal, AppTheme.spacing20)
                                .padding(.vertical, AppTheme.spacing16)
                                .background(
                                    ZStack {
                                        AppTheme.glassEffect
                                        
                                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                                            .stroke(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [
                                                        Color.white.opacity(0.4),
                                                        Color.white.opacity(0.1),
                                                        Color.clear
                                                    ]),
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                ),
                                                lineWidth: 1
                                            )
                                    }
                                )
                                .cornerRadius(AppTheme.radiusLarge)
                                .shadow(color: AppTheme.primaryColor.opacity(0.1), radius: 8, x: 0, y: 4)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing24)
    }
    
    private func selectAnswer(_ answer: String) {
        // Ensure answers array has enough capacity
        while answers.count <= currentQuestionIndex {
            answers.append("")
        }
        
        // Set the answer for current question
        answers[currentQuestionIndex] = answer
        
        // Move to next question with animation
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if currentQuestionIndex < questions.count - 1 {
                currentQuestionIndex += 1
            }
        }
        
        // Haptic feedback
        HapticFeedbackManager.shared.generateImpact(style: .light)
    }
}
