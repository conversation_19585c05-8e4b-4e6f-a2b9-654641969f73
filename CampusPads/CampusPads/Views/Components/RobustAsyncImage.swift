import SwiftUI

/// SIMPLE: AsyncImage with basic error handling
struct RobustAsyncImage: View {
    let url: URL?
    let contentMode: ContentMode

    @State private var loadedImage: UIImage?
    @State private var isLoading = true
    @State private var hasError = false

    @ObservedObject private var imageLoader = SimpleImageLoader.shared

    init(url: URL?, contentMode: ContentMode = .fill) {
        self.url = url
        self.contentMode = contentMode
    }

    var body: some View {
        Group {
            if let url = url {
                if let image = loadedImage {
                    // Simple image display
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFill()
                        .clipped()
                        .onAppear {
                            print("🖼️ RobustAsyncImage: Displaying loaded image for \(url.absoluteString)")
                        }
                } else if isLoading {
                    loadingView
                        .onAppear {
                            print("⏳ RobustAsyncImage: Showing loading view for \(url.absoluteString)")
                        }
                } else {
                    fallbackView
                        .onAppear {
                            print("❌ RobustAsyncImage: Showing fallback view for \(url.absoluteString)")
                        }
                }
            } else {
                fallbackView
                    .onAppear {
                        print("⚠️ RobustAsyncImage: Showing fallback view - URL is nil")
                    }
            }
        }
        .onAppear {
            loadImageIfNeeded()
        }
    }

    private func loadImageIfNeeded() {
        guard let url = url, loadedImage == nil else {
            if url == nil {
                print("⚠️ RobustAsyncImage: URL is nil")
            } else if loadedImage != nil {
                print("✅ RobustAsyncImage: Image already loaded for \(url?.absoluteString ?? "unknown")")
            }
            return
        }

        print("🔄 RobustAsyncImage: Starting load for \(url.absoluteString)")
        isLoading = true
        hasError = false

        // Simple image loading
        imageLoader.loadImage(from: url.absoluteString) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let image):
                    print("✅ RobustAsyncImage: Successfully loaded image (\(image.size.width)x\(image.size.height)) for \(url.absoluteString)")
                    self.loadedImage = image
                    self.isLoading = false
                    self.hasError = false

                case .failure(let error):
                    print("❌ RobustAsyncImage: Failed to load image for \(url.absoluteString): \(error.localizedDescription)")
                    self.isLoading = false
                    self.hasError = true
                }
            }
        }
    }

    private var loadingView: some View {
        ZStack {
            AppTheme.sexyGradient.opacity(0.7)

            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
        }
    }

    private var fallbackView: some View {
        ZStack {
            AppTheme.sexyGradient.opacity(0.5)

            VStack(spacing: 8) {
                Image(systemName: "photo")
                    .font(.system(size: 32, weight: .light))
                    .foregroundColor(.white.opacity(0.8))

                Text("Photo")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
    }

}

#Preview {
    RobustAsyncImage(url: URL(string: "https://picsum.photos/400/600"))
        .frame(width: 200, height: 300)
        .cornerRadius(12)
        .padding()
}
