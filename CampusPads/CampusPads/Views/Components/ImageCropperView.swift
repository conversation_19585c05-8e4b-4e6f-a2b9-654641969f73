import SwiftUI
import UIKit

/// Advanced image cropping tool optimized for Tinder-style 1.25 aspect ratio cards
struct ImageCropperView: View {
    let originalImage: UIImage
    let targetAspectRatio: CGFloat = 1.25 // Tinder-style aspect ratio
    @Binding var croppedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode

    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastScale: CGFloat = 1.0
    @State private var lastOffset: CGSize = .zero
    @State private var isProcessing = false

    var body: some View {
        NavigationView {
            ZStack {
                // Sexy gradient background
                AppTheme.backgroundGradient
                    .ignoresSafeArea()

                VStack(spacing: AppTheme.spacing20) {
                    // Header with instructions
                    VStack(spacing: AppTheme.spacing8) {
                        Text("Crop Your Image")
                            .font(.system(size: 28, weight: .bold, design: .rounded))
                            .foregroundStyle(AppTheme.sexyGradient)

                        Text("Adjust to fit the card perfectly")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                    .padding(.top, AppTheme.spacing16)

                    // Cropping area with preview
                    GeometryReader { geometry in
                        let cropSize = calculateCropSize(in: geometry.size)

                        ZStack {
                            // Background overlay
                            Color.black.opacity(0.7)

                            // Crop preview area
                            VStack {
                                Spacer()

                                ZStack {
                                    // Crop frame
                                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                                        .stroke(Color.white, lineWidth: 3)
                                        .frame(width: cropSize.width, height: cropSize.height)
                                        .shadow(color: AppTheme.primaryColor.opacity(0.6), radius: 8, x: 0, y: 0)

                                    // Image with gestures
                                    Image(uiImage: originalImage)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: cropSize.width, height: cropSize.height)
                                        .scaleEffect(scale)
                                        .offset(offset)
                                        .clipped()
                                        .clipShape(RoundedRectangle(cornerRadius: AppTheme.radiusXLarge))
                                        .gesture(
                                            SimultaneousGesture(
                                                MagnificationGesture()
                                                    .onChanged { value in
                                                        scale = lastScale * value
                                                    }
                                                    .onEnded { _ in
                                                        lastScale = scale
                                                        // Ensure minimum scale
                                                        if scale < 0.5 {
                                                            withAnimation(.spring()) {
                                                                scale = 0.5
                                                                lastScale = 0.5
                                                            }
                                                        }
                                                    },
                                                DragGesture()
                                                    .onChanged { value in
                                                        offset = CGSize(
                                                            width: lastOffset.width + value.translation.width,
                                                            height: lastOffset.height + value.translation.height
                                                        )
                                                    }
                                                    .onEnded { _ in
                                                        lastOffset = offset
                                                    }
                                            )
                                        )
                                }

                                Spacer()
                            }
                        }
                    }
                    .frame(maxHeight: 500)

                    // Control buttons
                    VStack(spacing: AppTheme.spacing16) {
                        // Reset and fit buttons
                        HStack(spacing: AppTheme.spacing16) {
                            Button("Reset") {
                                withAnimation(.spring()) {
                                    scale = 1.0
                                    offset = .zero
                                    lastScale = 1.0
                                    lastOffset = .zero
                                }
                            }
                            .buttonStyle(CropperSecondaryButtonStyle())

                            Button("Fit to Card") {
                                withAnimation(.spring()) {
                                    fitImageToCard()
                                }
                            }
                            .buttonStyle(CropperSecondaryButtonStyle())
                        }

                        // Main action buttons
                        HStack(spacing: AppTheme.spacing16) {
                            Button("Cancel") {
                                presentationMode.wrappedValue.dismiss()
                            }
                            .buttonStyle(CropperSecondaryButtonStyle())

                            Button(action: cropImage) {
                                HStack(spacing: 8) {
                                    if isProcessing {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    } else {
                                        Image(systemName: "crop")
                                            .font(.system(size: 16, weight: .semibold))
                                    }

                                    Text(isProcessing ? "Processing..." : "Crop Image")
                                        .font(.system(size: 16, weight: .semibold))
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 14)
                                .background(
                                    LinearGradient(
                                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(AppTheme.radiusLarge)
                                .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 12, x: 0, y: 6)
                            }
                            .disabled(isProcessing)
                        }
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing20)
                }
            }
            .navigationBarHidden(true)
        }
    }

    private func calculateCropSize(in containerSize: CGSize) -> CGSize {
        let maxWidth = containerSize.width * 0.9
        let maxHeight = containerSize.height * 0.7

        let targetWidth = min(maxWidth, maxHeight * targetAspectRatio)
        let targetHeight = targetWidth / targetAspectRatio

        return CGSize(width: targetWidth, height: targetHeight)
    }

    private func fitImageToCard() {
        let imageAspectRatio = originalImage.size.width / originalImage.size.height

        if imageAspectRatio > targetAspectRatio {
            // Image is wider than target - scale to fit height
            scale = 1.0
            offset = .zero
        } else {
            // Image is taller than target - scale to fit width
            let scaleNeeded = targetAspectRatio / imageAspectRatio
            scale = scaleNeeded
            offset = .zero
        }

        lastScale = scale
        lastOffset = offset
    }

    private func cropImage() {
        isProcessing = true

        DispatchQueue.global(qos: .userInitiated).async {
            let croppedUIImage = performCrop()

            DispatchQueue.main.async {
                isProcessing = false
                croppedImage = croppedUIImage
                presentationMode.wrappedValue.dismiss()
            }
        }
    }

    private func performCrop() -> UIImage? {
        let imageSize = originalImage.size
        let scaleFactor = originalImage.scale

        // Calculate crop rect in image coordinates
        let cropWidth = imageSize.width / scale
        let cropHeight = cropWidth / targetAspectRatio

        let cropX = (imageSize.width - cropWidth) / 2 - (offset.width / scale)
        let cropY = (imageSize.height - cropHeight) / 2 - (offset.height / scale)

        let cropRect = CGRect(
            x: max(0, cropX * scaleFactor),
            y: max(0, cropY * scaleFactor),
            width: min(cropWidth * scaleFactor, imageSize.width * scaleFactor),
            height: min(cropHeight * scaleFactor, imageSize.height * scaleFactor)
        )

        guard let cgImage = originalImage.cgImage?.cropping(to: cropRect) else {
            return nil
        }

        return UIImage(cgImage: cgImage, scale: scaleFactor, orientation: originalImage.imageOrientation)
    }
}

// MARK: - Cropper Button Style
struct CropperSecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .semibold))
            .foregroundColor(AppTheme.primaryColor)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 1)
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
    }
}

#Preview {
    ImageCropperView(
        originalImage: UIImage(systemName: "photo") ?? UIImage(),
        croppedImage: .constant(nil)
    )
}
