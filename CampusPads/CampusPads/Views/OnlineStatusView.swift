import SwiftUI
import FirebaseFirestore
import FirebaseFirestoreCombineSwift
import Combine

struct OnlineStatusView: View {
    let userID: String

    @State private var isOnline: Bool = false
    @State private var errorMessage: String?
    @State private var refreshTimer: Timer?

    private var db = Firestore.firestore()
    @State private var cancellables = Set<AnyCancellable>()

    // Explicit initializer.
    init(userID: String) {
        self.userID = userID
    }

    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(isOnline ? Color.green : Color.gray.opacity(0.6))
                .frame(width: 10, height: 10)
                .animation(.easeInOut(duration: 0.3), value: isOnline)

            Text(isOnline ? "Online" : "Offline")
                .font(AppTheme.bodyFont)
                .foregroundColor(isOnline ? .green : .secondary)
                .animation(.easeInOut(duration: 0.3), value: isOnline)
        }
        .onAppear {
            observeOnlineStatus()
            startPeriodicRefresh()
        }
        .onDisappear {
            stopPeriodicRefresh()
        }
    }

    private func observeOnlineStatus() {
        // Initial check
        checkOnlineStatus()

        // Real-time listener for immediate updates
        db.collection("users").document(userID)
            .snapshotPublisher()
            .sink { completion in
                if case let .failure(error) = completion {
                    print("❌ OnlineStatusView: Error observing status: \(error)")
                    errorMessage = error.localizedDescription
                }
            } receiveValue: { snapshot in
                if let data = snapshot.data() {
                    // Check both isOnline flag and lastSeen timestamp
                    let onlineFlag = data["isOnline"] as? Bool ?? false

                    if let lastSeenTimestamp = data["lastSeen"] as? Timestamp {
                        let lastSeen = lastSeenTimestamp.dateValue()
                        let timeSinceLastSeen = Date().timeIntervalSince(lastSeen)
                        let isRecentlyActive = timeSinceLastSeen < 60.0 // 60 seconds threshold

                        DispatchQueue.main.async {
                            self.isOnline = onlineFlag && isRecentlyActive
                        }
                    } else {
                        DispatchQueue.main.async {
                            self.isOnline = onlineFlag
                        }
                    }
                }
            }
            .store(in: &cancellables)
    }

    private func checkOnlineStatus() {
        OnlineStatusManager.shared.isUserOnline(userID: userID) { online in
            DispatchQueue.main.async {
                self.isOnline = online
            }
        }
    }

    private func startPeriodicRefresh() {
        // Refresh every 30 seconds to ensure accuracy
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            checkOnlineStatus()
        }
    }

    private func stopPeriodicRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

struct OnlineStatusView_Previews: PreviewProvider {
    static var previews: some View {
        OnlineStatusView(userID: "dummyUserID")
    }
}
