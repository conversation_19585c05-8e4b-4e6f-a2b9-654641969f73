import UIKit

/// Simple image optimization service
class ImageOptimizationService {
    static let shared = ImageOptimizationService()

    private let maxImageSize: CGFloat = 1080
    private let compressionQuality: CGFloat = 0.8

    private init() {}

    /// Simple image optimization
    func optimizeForCard(_ image: UIImage, completion: @escaping (UIImage?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let optimizedImage = self.simpleOptimization(image)
            DispatchQueue.main.async {
                completion(optimizedImage)
            }
        }
    }

    /// Batch optimize multiple images
    func optimizeImages(_ images: [UIImage], progress: @escaping (Double) -> Void, completion: @escaping ([UIImage]) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            var optimizedImages: [UIImage] = []
            let totalImages = Double(images.count)

            for (index, image) in images.enumerated() {
                if let optimized = self.simpleOptimization(image) {
                    optimizedImages.append(optimized)
                } else {
                    optimizedImages.append(image)
                }

                DispatchQueue.main.async {
                    progress(Double(index + 1) / totalImages)
                }
            }

            DispatchQueue.main.async {
                completion(optimizedImages)
            }
        }
    }

    // MARK: - Core Optimization Logic

    private func simpleOptimization(_ image: UIImage) -> UIImage? {
        // Step 1: Resize if needed
        let resizedImage = resizeIfNeeded(image)

        // Step 2: Compress
        return compressImage(resizedImage)
    }

    private func resizeIfNeeded(_ image: UIImage) -> UIImage {
        let size = image.size
        let maxDimension = max(size.width, size.height)

        guard maxDimension > maxImageSize else { return image }

        let scale = maxImageSize / maxDimension
        let newSize = CGSize(
            width: size.width * scale,
            height: size.height * scale
        )

        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return resizedImage ?? image
    }

    private func compressImage(_ image: UIImage) -> UIImage? {
        guard let imageData = image.jpegData(compressionQuality: compressionQuality),
              let compressedImage = UIImage(data: imageData) else {
            return image
        }
        return compressedImage
    }

    // MARK: - Simple Utilities

    /// Check if image needs optimization
    func needsOptimization(_ image: UIImage) -> Bool {
        let size = image.size
        let maxDimension = max(size.width, size.height)
        return maxDimension > maxImageSize
    }
}

// MARK: - Simple Extensions
extension UIImage {
    /// Simple optimization using the shared service
    func optimizedForCard(completion: @escaping (UIImage?) -> Void) {
        ImageOptimizationService.shared.optimizeForCard(self, completion: completion)
    }
}
