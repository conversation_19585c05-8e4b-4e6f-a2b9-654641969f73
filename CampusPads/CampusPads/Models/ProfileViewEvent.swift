//
//  ProfileViewEvent.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import Foundation
import FirebaseFirestore

/// Represents a profile view event for tracking who viewed a user's profile
struct ProfileViewEvent: Codable, Identifiable {
    @DocumentID var id: String?

    /// The ID of the user whose profile was viewed
    let viewedUserID: String

    /// The ID of the user who viewed the profile
    let viewerUserID: String

    /// When the profile was viewed
    let timestamp: Date

    /// The source of the view (search, discovery, etc.)
    let source: ProfileViewSource

    /// Additional metadata about the view
    let metadata: ProfileViewMetadata?

    init(
        viewedUserID: String,
        viewerUserID: String,
        timestamp: Date = Date(),
        source: ProfileViewSource,
        metadata: ProfileViewMetadata? = nil
    ) {
        self.viewedUserID = viewedUserID
        self.viewerUserID = viewerUserID
        self.timestamp = timestamp
        self.source = source
        self.metadata = metadata
    }
}

/// Sources from which a profile can be viewed
enum ProfileViewSource: String, Codable, CaseIterable {
    case search = "search"
    case discovery = "discovery"
    case chat = "chat"
    case matches = "matches"
    case directLink = "direct_link"
    case unknown = "unknown"

    var displayName: String {
        switch self {
        case .search:
            return "Search"
        case .discovery:
            return "Discovery"
        case .chat:
            return "Chat"
        case .matches:
            return "Matches"
        case .directLink:
            return "Direct Link"
        case .unknown:
            return "Unknown"
        }
    }

    var icon: String {
        switch self {
        case .search:
            return "magnifyingglass"
        case .discovery:
            return "heart.circle"
        case .chat:
            return "message"
        case .matches:
            return "heart.fill"
        case .directLink:
            return "link"
        case .unknown:
            return "questionmark.circle"
        }
    }
}

/// Additional metadata for profile views
struct ProfileViewMetadata: Codable {
    /// Search query if the view came from search
    let searchQuery: String?

    /// Duration of the view in seconds
    let viewDuration: TimeInterval?

    /// Whether the viewer interacted with the profile (scrolled, tapped, etc.)
    let hadInteraction: Bool?

    /// Device type (iOS, Android, Web)
    let deviceType: String?

    /// App version when the view occurred
    let appVersion: String?

    init(
        searchQuery: String? = nil,
        viewDuration: TimeInterval? = nil,
        hadInteraction: Bool? = nil,
        deviceType: String? = "iOS",
        appVersion: String? = nil
    ) {
        self.searchQuery = searchQuery
        self.viewDuration = viewDuration
        self.hadInteraction = hadInteraction
        self.deviceType = deviceType
        self.appVersion = appVersion
    }
}

/// Aggregated profile view statistics
struct ProfileViewStats: Codable {
    let totalViews: Int
    let uniqueViewers: Int
    let viewsBySource: [ProfileViewSource: Int]
    let recentViews: [ProfileViewEvent]
    let topViewers: [String] // User IDs of top viewers

    init(
        totalViews: Int = 0,
        uniqueViewers: Int = 0,
        viewsBySource: [ProfileViewSource: Int] = [:],
        recentViews: [ProfileViewEvent] = [],
        topViewers: [String] = []
    ) {
        self.totalViews = totalViews
        self.uniqueViewers = uniqueViewers
        self.viewsBySource = viewsBySource
        self.recentViews = recentViews
        self.topViewers = topViewers
    }
}

/// Summary data for home page profile views display
struct ProfileViewHomeSummary {
    let totalViews: Int
    let recentViews: Int // Last 7 days
    let mostRecentViewer: ProfileViewEvent?
    let isLoading: Bool

    init(
        totalViews: Int = 0,
        recentViews: Int = 0,
        mostRecentViewer: ProfileViewEvent? = nil,
        isLoading: Bool = false
    ) {
        self.totalViews = totalViews
        self.recentViews = recentViews
        self.mostRecentViewer = mostRecentViewer
        self.isLoading = isLoading
    }

    /// Formatted display text for total views
    var totalViewsText: String {
        if totalViews == 0 {
            return "No views yet"
        } else if totalViews == 1 {
            return "1 view"
        } else {
            return "\(totalViews) views"
        }
    }

    /// Formatted display text for recent views
    var recentViewsText: String {
        if recentViews == 0 {
            return "No recent views"
        } else if recentViews == 1 {
            return "1 view this week"
        } else {
            return "\(recentViews) views this week"
        }
    }
}
