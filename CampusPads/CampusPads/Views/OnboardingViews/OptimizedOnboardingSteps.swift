import SwiftUI
import FirebaseAuth

// MARK: - Optimized Onboarding Steps
// Lean, efficient implementations using reusable components

// MARK: - Email Verification (Optimized)
struct OptimizedEmailVerificationView: View {
    @State private var isChecking = false
    @State private var status = "Checking verification..."
    @State private var showResend = false

    var body: some View {
        VStack(spacing: AppTheme.spacing32) {
            Spacer()

            // Icon with status
            VStack(spacing: AppTheme.spacing24) {
                ZStack {
                    Circle()
                        .fill(AppTheme.primaryGradient)
                        .frame(width: 120, height: 120)
                        .blur(radius: 20)
                        .opacity(0.6)

                    Image(systemName: isVerified ? "checkmark.circle.fill" : "envelope.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.white)
                }

                VStack(spacing: AppTheme.spacing12) {
                    Text("Verify Your Email")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text(Auth.auth().currentUser?.email ?? "your email")
                        .font(AppTheme.subtitleFont)
                        .foregroundColor(AppTheme.primaryColor)
                        .fontWeight(.medium)
                }
            }

            // Status and actions
            VStack(spacing: AppTheme.spacing20) {
                Text(status)
                    .font(AppTheme.body)
                    .foregroundColor(isVerified ? .green : AppTheme.textSecondary)
                    .multilineTextAlignment(.center)

                if !isVerified {
                    VStack(spacing: AppTheme.spacing16) {
                        Button("Check Verification") {
                            checkVerification()
                        }
                        .buttonStyle(SecondaryButtonStyle())
                        .disabled(isChecking)

                        if showResend {
                            Button("Resend Email") {
                                resendEmail()
                            }
                            .buttonStyle(TertiaryButtonStyle())
                        }
                    }
                }
            }

            Spacer()
        }
        .onAppear {
            checkVerification()
            DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                showResend = true
            }
        }
    }

    private var isVerified: Bool {
        Auth.auth().currentUser?.isEmailVerified == true
    }

    private func checkVerification() {
        isChecking = true
        status = "Checking..."

        Auth.auth().currentUser?.reload { error in
            DispatchQueue.main.async {
                isChecking = false
                if error != nil {
                    status = "Error checking verification"
                } else if isVerified {
                    status = "✅ Email verified!"
                    HapticFeedbackManager.shared.generateNotification(.success)
                } else {
                    status = "Please check your inbox and verify your email"
                }
            }
        }
    }

    private func resendEmail() {
        Auth.auth().currentUser?.sendEmailVerification { error in
            DispatchQueue.main.async {
                if error != nil {
                    status = "Error sending email"
                } else {
                    status = "Verification email sent!"
                    HapticFeedbackManager.shared.generateNotification(.success)
                }
            }
        }
    }
}

// MARK: - Personal Info (Optimized)
struct OptimizedPersonalInfoView: View {
    @Binding var firstName: String
    @Binding var lastName: String
    @Binding var birthDate: Date
    @Binding var gender: String

    private let genderOptions = ["Male", "Female", "Non-binary", "Other"]

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Personal Information",
                subtitle: "Tell us about yourself"
            )

            VStack(spacing: AppTheme.spacing20) {
                // Name fields
                HStack(spacing: AppTheme.spacing12) {
                    OnboardingTextField(title: "First Name", text: $firstName, placeholder: "John")
                    OnboardingTextField(title: "Last Name", text: $lastName, placeholder: "Doe")
                }

                // Birth date
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Date of Birth")
                    DatePicker("", selection: $birthDate, displayedComponents: .date)
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                }

                // Gender
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Gender")
                    GridSelection(
                        items: genderOptions,
                        selectedItem: Binding(
                            get: { gender.isEmpty ? nil : gender },
                            set: { gender = $0 ?? "" }
                        ),
                        columns: 2,
                        itemText: { $0 }
                    )
                }
            }
        }
    }
}

// MARK: - Physical Info (Optimized)
struct OptimizedPhysicalInfoView: View {
    @Binding var height: String
    @Binding var aboutMe: String

    private let heightOptions = [
        "4'10\"", "4'11\"", "5'0\"", "5'1\"", "5'2\"", "5'3\"", "5'4\"", "5'5\"",
        "5'6\"", "5'7\"", "5'8\"", "5'9\"", "5'10\"", "5'11\"", "6'0\"", "6'1\"",
        "6'2\"", "6'3\"", "6'4\"", "6'5\"", "6'6\"+"
    ]

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Physical Details",
                subtitle: "Help others get to know you better"
            )

            VStack(spacing: AppTheme.spacing20) {
                // Height
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Height")
                    HorizontalSelection(
                        items: heightOptions,
                        selectedItem: Binding(
                            get: { height.isEmpty ? nil : height },
                            set: { height = $0 ?? "" }
                        ),
                        itemText: { $0 }
                    )
                }

                // About me
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "About Me")
                    Text("Tell potential roommates about yourself")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)

                    TextEditor(text: $aboutMe)
                        .font(AppTheme.body)
                        .padding()
                        .frame(minHeight: 120)
                        .background(AppTheme.cardBackground)
                        .cornerRadius(AppTheme.radiusMedium)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                                .stroke(AppTheme.primaryColor.opacity(0.3), lineWidth: 1)
                        )
                }
            }
        }
    }
}

// MARK: - Academic Info (Optimized)
struct OptimizedAcademicInfoView: View {
    @Binding var gradeLevel: String
    @Binding var major: String
    @Binding var collegeName: String

    private let gradeLevels = ["Freshman", "Sophomore", "Junior", "Senior", "Graduate"]

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Academic Information",
                subtitle: "Help us match you with students at your level"
            )

            VStack(spacing: AppTheme.spacing20) {
                // Grade level
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Grade Level")
                    GridSelection(
                        items: gradeLevels,
                        selectedItem: Binding(
                            get: { gradeLevel.isEmpty ? nil : gradeLevel },
                            set: { gradeLevel = $0 ?? "" }
                        ),
                        columns: 2,
                        itemText: { $0 }
                    )
                }

                // Major dropdown selection (matching MyProfileView)
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Major")
                    MajorDropdownSelection(
                        selectedMajor: $major,
                        placeholder: "Select Major",
                        label: nil
                    )
                }

                // College/University
                OnboardingTextField(title: "College/University", text: $collegeName, placeholder: "University of California")
            }
        }
    }
}

// MARK: - Housing Preferences (Optimized)
struct OptimizedHousingPreferencesView: View {
    @Binding var housingStatus: String
    @Binding var dormType: String

    private let housingOptions = ["On-Campus", "Off-Campus", "Either"]
    private let dormTypes = ["Single", "Double", "Triple", "Suite", "Apartment"]

    var body: some View {
        VStack(spacing: AppTheme.spacing24) {
            OnboardingHeader(
                title: "Housing Preferences",
                subtitle: "Tell us about your ideal living situation"
            )

            VStack(spacing: AppTheme.spacing20) {
                // Housing preference
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Housing Preference")
                    HStack(spacing: AppTheme.spacing12) {
                        ForEach(housingOptions, id: \.self) { option in
                            SelectionButton(
                                text: option,
                                isSelected: housingStatus == option,
                                action: {
                                    housingStatus = option
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }
                            )
                        }
                    }
                }

                // Room type
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    SectionHeader(title: "Room Type")
                    GridSelection(
                        items: dormTypes,
                        selectedItem: Binding(
                            get: { dormType.isEmpty ? nil : dormType },
                            set: { dormType = $0 ?? "" }
                        ),
                        columns: 2,
                        itemText: { $0 }
                    )
                }
            }
        }
    }
}
