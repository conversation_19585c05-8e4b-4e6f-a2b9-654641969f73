import SwiftUI

struct AppTheme {
    // MARK: - Colors
    static let primaryColor = Color("AppPrimaryColor")         // e.g., deep blue
    static let secondaryColor = Color("AppSecondaryColor")     // e.g., vibrant teal
    static let accentColor = Color("AccentColor")           // e.g., bright orange
    static let backgroundStart = Color("BackgroundStart")   // e.g., soft light gray
    static let backgroundEnd = Color("BackgroundEnd")       // e.g., slightly darker gray
    static let cardBackground = Color("CardBackground")     // e.g., light off-white (#F8F8F8 in light mode, #2C2C2E in dark mode)

    // New properties for reaction feedback overlays:
    static let likeColor = Color("likeColor")               // e.g., a pleasant green
    static let nopeColor = Color("nopeColor")               // e.g., a noticeable red

    // MARK: - Semantic Colors
    static let successColor = Color.green
    static let warningColor = Color.orange
    static let errorColor = Color.red
    static let infoColor = primaryColor

    // Surface colors for better hierarchy
    static let surfacePrimary = cardBackground
    static let surfaceSecondary = cardBackground.opacity(0.6)
    static let surfaceTertiary = cardBackground.opacity(0.3)

    // Text colors for better contrast
    static let textPrimary = Color.primary
    static let textSecondary = Color.secondary
    static let textTertiary = Color.secondary.opacity(0.6)

    // MARK: - Stunning Dynamic Backgrounds

    /// Enhanced dynamic background that perfectly adapts to light/dark mode
    static var dynamicBackgroundGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark
                        ? UIColor(red: 0.02, green: 0.05, blue: 0.12, alpha: 1.0) // Deep Space Blue
                        : UIColor(red: 0.97, green: 0.98, blue: 1.0, alpha: 1.0)   // Soft Lavender White
                }),
                Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark
                        ? UIColor(red: 0.05, green: 0.02, blue: 0.08, alpha: 1.0) // Rich Dark Purple
                        : UIColor(red: 0.92, green: 0.96, blue: 1.0, alpha: 1.0)   // Light Sky Blue
                }),
                Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark
                        ? UIColor(red: 0.08, green: 0.02, blue: 0.05, alpha: 1.0) // Deep Burgundy
                        : UIColor(red: 0.95, green: 0.92, blue: 1.0, alpha: 1.0)   // Light Lavender
                }),
                Color(UIColor { traitCollection in
                    traitCollection.userInterfaceStyle == .dark
                        ? UIColor(red: 0.03, green: 0.06, blue: 0.10, alpha: 1.0) // Deep Navy
                        : UIColor(red: 0.94, green: 0.97, blue: 0.99, alpha: 1.0)   // Soft Blue-White
                })
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Light mode specific gradient - Enhanced Beauty
    static var lightModeGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.97, green: 0.98, blue: 1.0),  // Soft Lavender White
                Color(red: 0.92, green: 0.96, blue: 1.0),  // Light Sky Blue
                Color(red: 0.95, green: 0.92, blue: 1.0),  // Light Lavender
                Color(red: 0.94, green: 0.97, blue: 0.99)  // Soft Blue-White
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Dark mode specific gradient
    static var darkModeGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.02, green: 0.05, blue: 0.12),  // Deep Space Blue
                Color(red: 0.05, green: 0.02, blue: 0.08),  // Rich Dark Purple
                Color(red: 0.08, green: 0.02, blue: 0.05)   // Deep Burgundy
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Legacy background gradient for compatibility
    static var backgroundGradient: LinearGradient {
        dynamicBackgroundGradient
    }

    /// Glass morphism effect for modern cards
    static var glassEffect: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.white.opacity(0.25),
                Color.white.opacity(0.1),
                Color.white.opacity(0.05)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Enhanced card gradient with glass effect
    static var modernCardGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                cardBackground.opacity(0.9),
                cardBackground.opacity(0.7),
                cardBackground.opacity(0.5)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Legacy card gradient for compatibility
    static var cardGradient: LinearGradient {
        modernCardGradient
    }

    /// Enhanced primary gradient with multiple colors
    static var primaryGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                primaryColor,
                accentColor,
                primaryColor.opacity(0.8)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    /// Sexy gradient for special elements
    static var sexyGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 1.0, green: 0.3, blue: 0.6), // Hot Pink
                Color(red: 0.6, green: 0.3, blue: 1.0), // Electric Purple
                Color(red: 0.2, green: 0.6, blue: 1.0)  // Electric Blue
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // MARK: - Responsive Typography Scale
    static var largeTitle: Font { ResponsiveTypography.largeTitle }
    static var title1: Font { ResponsiveTypography.title1 }
    static var title2: Font { ResponsiveTypography.title2 }
    static var title3: Font { ResponsiveTypography.title3 }
    static var headline: Font { ResponsiveTypography.headline }
    static var body: Font { ResponsiveTypography.body }
    static var callout: Font { ResponsiveTypography.callout }
    static var subheadline: Font { ResponsiveTypography.subheadline }
    static var footnote: Font { ResponsiveTypography.footnote }
    static var caption: Font { ResponsiveTypography.caption }

    // Legacy font names for backward compatibility
    static var titleFont: Font { title2 }
    static var subtitleFont: Font { headline }
    static var bodyFont: Font { body }

    // MARK: - Responsive Spacing System
    static var spacing2: CGFloat { ResponsiveSpacing.spacing(2) }
    static var spacing4: CGFloat { ResponsiveSpacing.xs }
    static var spacing6: CGFloat { ResponsiveSpacing.spacing(6) }
    static var spacing8: CGFloat { ResponsiveSpacing.sm }
    static var spacing12: CGFloat { ResponsiveSpacing.spacing(12) }
    static var spacing16: CGFloat { ResponsiveSpacing.md }
    static var spacing20: CGFloat { ResponsiveSpacing.spacing(20) }
    static var spacing24: CGFloat { ResponsiveSpacing.lg }
    static var spacing32: CGFloat { ResponsiveSpacing.xl }
    static var spacing40: CGFloat { ResponsiveSpacing.spacing(40) }
    static var spacing48: CGFloat { ResponsiveSpacing.xxl }

    // Legacy spacing for backward compatibility
    static var defaultPadding: CGFloat { spacing16 }

    // MARK: - Responsive Corner Radius System
    static var radiusSmall: CGFloat { ResponsiveRadius.small }
    static var radiusMedium: CGFloat { ResponsiveRadius.medium }
    static var radiusLarge: CGFloat { ResponsiveRadius.large }
    static var radiusXLarge: CGFloat { ResponsiveRadius.xlarge }

    // Legacy radius for backward compatibility
    static var defaultCornerRadius: CGFloat { radiusMedium }

    // MARK: - Shadow System
    static let shadowLight = Shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    static let shadowMedium = Shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    static let shadowHeavy = Shadow(color: .black.opacity(0.15), radius: 16, x: 0, y: 8)

    // MARK: - Animation Constants
    static let animationFast: Double = 0.2
    static let animationMedium: Double = 0.3
    static let animationSlow: Double = 0.5

    // Spring animations
    static let springResponse: Double = 0.6
    static let springDamping: Double = 0.8
}

// MARK: - Shadow Helper
struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - Modern View Extensions for Sexy Styling
extension View {
    /// Modern glass morphism card style
    func modernCardStyle() -> some View {
        self
            .background(
                ZStack {
                    AppTheme.modernCardGradient
                    AppTheme.glassEffect
                }
            )
            .cornerRadius(AppTheme.radiusLarge)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }

    /// Legacy card style for compatibility
    func cardStyle() -> some View {
        modernCardStyle()
    }

    /// Sexy gradient button style
    func sexyButtonStyle() -> some View {
        self
            .background(AppTheme.sexyGradient)
            .cornerRadius(AppTheme.radiusXLarge)
            .shadow(color: Color.black.opacity(0.2), radius: 15, x: 0, y: 8)
            .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 10, x: 0, y: 5)
    }

    /// Floating card effect
    func floatingCard() -> some View {
        self
            .background(
                ZStack {
                    AppTheme.cardBackground
                    AppTheme.glassEffect
                }
            )
            .cornerRadius(AppTheme.radiusLarge)
            .shadow(color: Color.black.opacity(0.08), radius: 25, x: 0, y: 15)
            .shadow(color: Color.black.opacity(0.04), radius: 8, x: 0, y: 4)
    }

    /// Enhanced neon glow effect with pulsing animation
    func neonGlow(color: Color = AppTheme.primaryColor, intensity: Double = 1.0) -> some View {
        self
            .shadow(color: color.opacity(0.8 * intensity), radius: 8, x: 0, y: 0)
            .shadow(color: color.opacity(0.6 * intensity), radius: 16, x: 0, y: 0)
            .shadow(color: color.opacity(0.4 * intensity), radius: 24, x: 0, y: 0)
            .shadow(color: color.opacity(0.2 * intensity), radius: 32, x: 0, y: 0)
    }

    /// Pulsing neon glow effect
    func pulsingNeonGlow(color: Color = AppTheme.primaryColor) -> some View {
        PulsingNeonGlowView(content: self, color: color)
    }

    /// Advanced glassmorphism effect with backdrop blur
    func advancedGlassmorphism() -> some View {
        self
            .background(
                ZStack {
                    // Backdrop blur effect
                    Rectangle()
                        .fill(.ultraThinMaterial)
                        .opacity(0.8)

                    // Enhanced glass gradient
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.3),
                            Color.white.opacity(0.1),
                            Color.white.opacity(0.05),
                            Color.clear
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )

                    // Subtle border highlight
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.6),
                                    Color.white.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                }
            )
            .cornerRadius(AppTheme.radiusLarge)
    }

    /// Shimmer loading effect
    func shimmerEffect() -> some View {
        self
            .overlay(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.clear,
                        Color.white.opacity(0.4),
                        Color.clear
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .rotationEffect(.degrees(30))
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: false), value: UUID())
            )
            .clipped()
    }

    func lightShadow() -> some View {
        self.shadow(color: AppTheme.shadowLight.color,
                   radius: AppTheme.shadowLight.radius,
                   x: AppTheme.shadowLight.x,
                   y: AppTheme.shadowLight.y)
    }

    func mediumShadow() -> some View {
        self.shadow(color: AppTheme.shadowMedium.color,
                   radius: AppTheme.shadowMedium.radius,
                   x: AppTheme.shadowMedium.x,
                   y: AppTheme.shadowMedium.y)
    }

    func heavyShadow() -> some View {
        self.shadow(color: AppTheme.shadowHeavy.color,
                   radius: AppTheme.shadowHeavy.radius,
                   x: AppTheme.shadowHeavy.x,
                   y: AppTheme.shadowHeavy.y)
    }

    /// Enhanced button press effect with haptic feedback
    func enhancedButtonPress() -> some View {
        ButtonPressEffectView(content: self)
    }

    /// Morphing gradient background
    func morphingGradient(colors: [Color], duration: Double = 3.0) -> some View {
        MorphingGradientView(content: self, colors: colors, duration: duration)
    }

    /// 3D card transform effect
    func cardTransform3D(isPressed: Bool = false) -> some View {
        self
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .rotation3DEffect(
                .degrees(isPressed ? 2 : 0),
                axis: (x: 1, y: 1, z: 0)
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
    }

    /// Floating animation effect
    func floatingAnimation(amplitude: CGFloat = 10, duration: Double = 2.0) -> some View {
        FloatingAnimationView(content: self, amplitude: amplitude, duration: duration)
    }
}

// MARK: - Advanced Effect Components

struct PulsingNeonGlowView<Content: View>: View {
    let content: Content
    let color: Color
    @State private var intensity: Double = 0.5

    var body: some View {
        content
            .neonGlow(color: color, intensity: intensity)
            .onAppear {
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    intensity = 1.0
                }
            }
    }
}

struct ButtonPressEffectView<Content: View>: View {
    let content: Content
    @State private var isPressed = false

    var body: some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .brightness(isPressed ? -0.1 : 0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onTapGesture {
                HapticFeedbackManager.shared.generateImpact(style: .medium)
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = true
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = false
                    }
                }
            }
    }
}

struct MorphingGradientView<Content: View>: View {
    let content: Content
    let colors: [Color]
    let duration: Double
    @State private var currentIndex = 0

    var body: some View {
        content
            .background(
                LinearGradient(
                    colors: [
                        colors[currentIndex % colors.count],
                        colors[(currentIndex + 1) % colors.count]
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .animation(.easeInOut(duration: duration), value: currentIndex)
            )
            .onAppear {
                Timer.scheduledTimer(withTimeInterval: duration, repeats: true) { _ in
                    currentIndex += 1
                }
            }
    }
}

struct FloatingAnimationView<Content: View>: View {
    let content: Content
    let amplitude: CGFloat
    let duration: Double
    @State private var offset: CGFloat = 0

    var body: some View {
        content
            .offset(y: offset)
            .onAppear {
                withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
                    offset = amplitude
                }
            }
    }
}