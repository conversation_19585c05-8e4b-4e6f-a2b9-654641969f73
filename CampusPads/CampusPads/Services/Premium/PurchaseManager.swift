import Foundation
import StoreKit
import FirebaseFirestore
import FirebaseAuth
import Combine

// MARK: - Purchase Manager
@MainActor
class PurchaseManager: ObservableObject {
    static let shared = PurchaseManager()
    
    // MARK: - Published Properties
    @Published var products: [Product] = []
    @Published var purchasedProductIDs: Set<String> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var isPurchasing = false
    @Published var isRestoring = false
    
    // MARK: - Private Properties
    private let db = Firestore.firestore()
    private var updateListenerTask: Task<Void, Error>?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Product Identifiers
    enum ProductIdentifier: String, CaseIterable {
        case lifetimePremium = "com.CollegePads.CollegePads.lifetime_premium"
        
        var displayName: String {
            switch self {
            case .lifetimePremium:
                return "Lifetime Premium"
            }
        }
        
        var description: String {
            switch self {
            case .lifetimePremium:
                return "Unlock all premium features forever with a one-time purchase"
            }
        }
    }
    
    // MARK: - Initialization
    private init() {
        // Start listening for transaction updates
        updateListenerTask = listenForTransactions()
        
        Task {
            await loadProducts()
            await updatePurchasedProducts()
        }
    }
    
    deinit {
        updateListenerTask?.cancel()
    }
    
    // MARK: - Product Loading
    func loadProducts() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let productIdentifiers = ProductIdentifier.allCases.map { $0.rawValue }
            let loadedProducts = try await Product.products(for: productIdentifiers)
            
            DispatchQueue.main.async {
                self.products = loadedProducts.sorted { $0.price < $1.price }
                self.isLoading = false
            }
            
            print("✅ PurchaseManager: Loaded \(loadedProducts.count) products")
            
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to load products: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("❌ PurchaseManager: Failed to load products - \(error)")
        }
    }
    
    // MARK: - Purchase Methods
    func purchase(_ product: Product) async -> Bool {
        guard !isPurchasing else {
            print("⚠️ PurchaseManager: Purchase already in progress")
            return false
        }
        
        isPurchasing = true
        errorMessage = nil
        
        do {
            let result = try await product.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try Self.checkVerified(verification)
                
                // Update premium status in Firebase
                let success = await updatePremiumStatusInFirebase(for: transaction)
                
                if success {
                    // Finish the transaction
                    await transaction.finish()
                    
                    DispatchQueue.main.async {
                        self.purchasedProductIDs.insert(transaction.productID)
                        self.isPurchasing = false
                    }
                    
                    print("✅ PurchaseManager: Purchase successful for \(product.id)")
                    return true
                } else {
                    DispatchQueue.main.async {
                        self.errorMessage = "Failed to update premium status"
                        self.isPurchasing = false
                    }
                    return false
                }
                
            case .userCancelled:
                DispatchQueue.main.async {
                    self.isPurchasing = false
                }
                print("ℹ️ PurchaseManager: User cancelled purchase")
                return false
                
            case .pending:
                DispatchQueue.main.async {
                    self.isPurchasing = false
                    self.errorMessage = "Purchase is pending approval"
                }
                print("⏳ PurchaseManager: Purchase pending")
                return false
                
            @unknown default:
                DispatchQueue.main.async {
                    self.errorMessage = "Unknown purchase result"
                    self.isPurchasing = false
                }
                print("❌ PurchaseManager: Unknown purchase result")
                return false
            }
            
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Purchase failed: \(error.localizedDescription)"
                self.isPurchasing = false
            }
            print("❌ PurchaseManager: Purchase failed - \(error)")
            return false
        }
    }
    
    // MARK: - Restore Purchases
    func restorePurchases() async -> Bool {
        guard !isRestoring else {
            print("⚠️ PurchaseManager: Restore already in progress")
            return false
        }
        
        isRestoring = true
        errorMessage = nil
        
        do {
            try await AppStore.sync()
            
            var restoredAny = false
            
            for await result in StoreKit.Transaction.currentEntitlements {
                let transaction = try Self.checkVerified(result)
                
                if ProductIdentifier.allCases.map({ $0.rawValue }).contains(transaction.productID) {
                    let success = await updatePremiumStatusInFirebase(for: transaction)
                    
                    if success {
                        DispatchQueue.main.async {
                            self.purchasedProductIDs.insert(transaction.productID)
                        }
                        restoredAny = true
                        print("✅ PurchaseManager: Restored purchase for \(transaction.productID)")
                    }
                }
            }
            
            DispatchQueue.main.async {
                self.isRestoring = false
                if !restoredAny {
                    self.errorMessage = "No purchases found to restore"
                }
            }
            
            return restoredAny
            
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "Failed to restore purchases: \(error.localizedDescription)"
                self.isRestoring = false
            }
            print("❌ PurchaseManager: Restore failed - \(error)")
            return false
        }
    }
    
    // MARK: - Helper Methods
    func isPremiumUser() -> Bool {
        return purchasedProductIDs.contains(ProductIdentifier.lifetimePremium.rawValue)
    }

    func getLifetimePremiumProduct() -> Product? {
        return products.first { $0.id == ProductIdentifier.lifetimePremium.rawValue }
    }

    // MARK: - Private Methods
    private func listenForTransactions() -> Task<Void, Error> {
        return Task {
            // Listen for transaction updates
            for await result in StoreKit.Transaction.updates {
                do {
                    let transaction = try Self.checkVerified(result)

                    // Update premium status for new transactions
                    let success = await updatePremiumStatusInFirebase(for: transaction)

                    if success {
                        // Update local state
                        purchasedProductIDs.insert(transaction.productID)

                        // Finish the transaction
                        await transaction.finish()
                    }

                } catch {
                    print("❌ PurchaseManager: Transaction verification failed - \(error)")
                }
            }
        }
    }

    private static func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw PurchaseError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    private func updatePurchasedProducts() async {
        var purchasedIDs: Set<String> = []

        for await result in StoreKit.Transaction.currentEntitlements {
            do {
                let transaction = try Self.checkVerified(result)
                purchasedIDs.insert(transaction.productID)
            } catch {
                print("❌ PurchaseManager: Failed to verify entitlement - \(error)")
            }
        }

        DispatchQueue.main.async {
            self.purchasedProductIDs = purchasedIDs
        }
    }

    private func updatePremiumStatusInFirebase(for transaction: StoreKit.Transaction) async -> Bool {
        guard let userID = Auth.auth().currentUser?.uid else {
            print("❌ PurchaseManager: No authenticated user for premium update")
            return false
        }

        print("🎯 PurchaseManager: Updating premium status for user: \(userID), product: \(transaction.productID)")

        do {
            let updateData: [String: Any] = [
                "isPremium": true,
                "premiumSince": FieldValue.serverTimestamp(),
                "premiumPlan": "lifetime",
                "purchaseTransactionID": transaction.id,
                "purchaseDate": transaction.purchaseDate,
                "productID": transaction.productID
            ]

            try await db.collection("users").document(userID).updateData(updateData)

            // Also update ProfileViewModel if available
            await MainActor.run {
                if let profileVM = ProfileViewModel.shared.userProfile {
                    ProfileViewModel.shared.userProfile?.isPremium = true
                    ProfileViewModel.shared.userProfile?.premiumSince = Date()
                    ProfileViewModel.shared.userProfile?.premiumPlan = "lifetime"
                }
            }

            print("✅ PurchaseManager: Successfully updated premium status in Firebase")
            return true

        } catch {
            print("❌ PurchaseManager: Failed to update premium status in Firebase - \(error)")
            return false
        }
    }
}

// MARK: - Purchase Errors
enum PurchaseError: Error, LocalizedError {
    case failedVerification
    case noProductsFound
    case purchaseInProgress
    case restoreInProgress

    var errorDescription: String? {
        switch self {
        case .failedVerification:
            return "Purchase verification failed"
        case .noProductsFound:
            return "No products available for purchase"
        case .purchaseInProgress:
            return "A purchase is already in progress"
        case .restoreInProgress:
            return "Purchase restoration is already in progress"
        }
    }
}
