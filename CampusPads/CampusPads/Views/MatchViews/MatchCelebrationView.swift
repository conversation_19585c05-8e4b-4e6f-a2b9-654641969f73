//
//  MatchCelebrationView.swift
//  CampusPads
//
//  Created by Augment Agent on [Date].
//

import SwiftUI

/// Tinder-style match celebration popup with animations
struct MatchCelebrationView: View {
    let currentUser: UserModel
    let matchedUser: UserModel
    let matchType: MatchType
    let onKeepSwiping: () -> Void
    let onSendMessage: () -> Void

    @State private var showAnimation = false
    @State private var showContent = false
    @State private var pulseAnimation = false

    var body: some View {
        ZStack {
            // Background with gradient
            LinearGradient(
                colors: [
                    Color.pink.opacity(0.8),
                    Color.purple.opacity(0.8),
                    Color.blue.opacity(0.8)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            .opacity(showAnimation ? 1.0 : 0.0)

            // Animated hearts background
            if showAnimation {
                ForEach(0..<15, id: \.self) { index in
                    HeartParticle(delay: Double(index) * 0.1)
                }
            }

            // Main content
            VStack(spacing: 40) {
                // Match type indicator
                VStack(spacing: 16) {
                    Text(matchType.emoji)
                        .font(.system(size: 80))
                        .scaleEffect(showContent ? 1.0 : 0.1)
                        .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.3), value: showContent)

                    Text(matchType.displayName.uppercased())
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .opacity(showContent ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.6).delay(0.5), value: showContent)
                }

                // User photos
                HStack(spacing: 20) {
                    // Current user photo
                    ProfileImageView(
                        imageUrl: currentUser.profileImageUrl,
                        size: 120
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 4)
                    )
                    .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                    .scaleEffect(showContent ? 1.0 : 0.1)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.7), value: showContent)

                    // Heart icon between photos
                    Image(systemName: "heart.fill")
                        .font(.system(size: 40, weight: .bold))
                        .foregroundColor(.white)
                        .scaleEffect(pulseAnimation ? 1.3 : 1.0)
                        .opacity(showContent ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: pulseAnimation)
                        .animation(.easeInOut(duration: 0.6).delay(0.9), value: showContent)

                    // Matched user photo
                    ProfileImageView(
                        imageUrl: matchedUser.profileImageUrl,
                        size: 120
                    )
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 4)
                    )
                    .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                    .scaleEffect(showContent ? 1.0 : 0.1)
                    .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(1.1), value: showContent)
                }

                // User names
                VStack(spacing: 8) {
                    Text("You and \(matchedUser.firstName ?? "Someone") liked each other!")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .opacity(showContent ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.6).delay(1.3), value: showContent)

                    if matchType == .superLike {
                        Text("This is a Super Match!")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.yellow)
                            .opacity(showContent ? 1.0 : 0.0)
                            .animation(.easeInOut(duration: 0.6).delay(1.5), value: showContent)
                    }
                }

                Spacer()

                // Action buttons
                VStack(spacing: 16) {
                    // Send Message button
                    Button(action: {
                        HapticFeedbackManager.shared.generateNotification(.success)
                        onSendMessage()
                    }) {
                        HStack {
                            Image(systemName: "message.fill")
                                .font(.system(size: 18, weight: .semibold))
                            Text("SEND MESSAGE")
                                .font(.system(size: 18, weight: .bold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(
                            LinearGradient(
                                colors: [Color.pink, Color.red],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(28)
                        .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                    }
                    .scaleEffect(showContent ? 1.0 : 0.8)
                    .opacity(showContent ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(1.7), value: showContent)

                    // Keep Swiping button
                    Button(action: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        onKeepSwiping()
                    }) {
                        Text("KEEP SWIPING")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white.opacity(0.8))
                            .frame(maxWidth: .infinity)
                            .frame(height: 48)
                            .background(Color.clear)
                            .overlay(
                                RoundedRectangle(cornerRadius: 24)
                                    .stroke(Color.white.opacity(0.5), lineWidth: 2)
                            )
                    }
                    .scaleEffect(showContent ? 1.0 : 0.8)
                    .opacity(showContent ? 1.0 : 0.0)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(1.9), value: showContent)
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 40)
            }
            .padding(.horizontal, 20)
        }
        .onAppear {
            // Trigger haptic feedback
            HapticFeedbackManager.shared.generateNotification(.success)

            // Start animations
            withAnimation(.easeInOut(duration: 0.5)) {
                showAnimation = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation {
                    showContent = true
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                pulseAnimation = true
            }
        }
    }
}

/// Animated heart particle for background effect
struct HeartParticle: View {
    let delay: Double
    @State private var isAnimating = false
    @State private var opacity: Double = 0

    var body: some View {
        Image(systemName: "heart.fill")
            .font(.system(size: CGFloat.random(in: 20...40)))
            .foregroundColor(.white.opacity(0.6))
            .opacity(opacity)
            .offset(
                x: CGFloat.random(in: -200...200),
                y: isAnimating ? -UIScreen.main.bounds.height : UIScreen.main.bounds.height
            )
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(.linear(duration: 3.0)) {
                        isAnimating = true
                    }

                    withAnimation(.easeInOut(duration: 0.5)) {
                        opacity = 1.0
                    }

                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                        withAnimation(.easeInOut(duration: 0.5)) {
                            opacity = 0.0
                        }
                    }
                }
            }
    }
}

// ProfileImageView is now imported from UIComponents.swift - no duplicate needed

#Preview {
    MatchCelebrationView(
        currentUser: UserModel(email: "<EMAIL>", isEmailVerified: true, firstName: "You"),
        matchedUser: UserModel(email: "<EMAIL>", isEmailVerified: true, firstName: "Alex"),
        matchType: .regular,
        onKeepSwiping: {},
        onSendMessage: {}
    )
}
