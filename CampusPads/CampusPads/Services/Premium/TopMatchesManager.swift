import Foundation
import FirebaseFirestore
import Combine
import UIKit

// MARK: - Top Match Data Models
struct TopMatchData: Codable, Identifiable {
    let id: String
    let userID: String
    let compatibilityScore: Double
    let generatedAt: Date
    let expiresAt: Date

    init(userID: String, compatibilityScore: Double) {
        self.id = UUID().uuidString
        self.userID = userID
        self.compatibilityScore = compatibilityScore
        self.generatedAt = Date()
        self.expiresAt = Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
    }
}

struct TopMatchesCollection: Codable {
    let userID: String
    let matches: [TopMatchData]
    let generatedAt: Date
    let expiresAt: Date

    init(userID: String, matches: [TopMatchData]) {
        self.userID = userID
        self.matches = matches
        self.generatedAt = Date()
        self.expiresAt = Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()
    }
}

// MARK: - Top Matches Manager
@MainActor
class TopMatchesManager: ObservableObject {
    static let shared = TopMatchesManager()
    private let db = Firestore.firestore()

    @Published var topMatches: [UserModel] = []
    @Published var viewedCount: Int = 0
    @Published var likedCount: Int = 0
    @Published var canViewMore: Bool = true
    @Published var canLikeMore: Bool = true
    @Published var isLoading: Bool = false
    @Published var lastResetTime: Date?

    let maxDailyViews = 8  // Can view all 8
    let maxDailyLikes = 2  // Can only like 2

    private init() {
        setupDailyResetTimer()
    }

    // MARK: - Daily Reset & Generation
    func generateDailyTopMatches(for userID: String) async {
        print("🎯 Generating daily top matches for user: \(userID)")

        do {
            // 1. Get all potential matches
            let allMatches = try await loadAllPotentialMatches(for: userID)

            // 2. Run compatibility algorithm
            guard let currentUser = await getCurrentUser(userID) else {
                print("❌ Could not load current user for top matches generation")
                return
            }

            let scoredMatches = SmartMatchingEngine.generateSortedMatches(
                from: allMatches,
                currentUser: currentUser
            )

            // 3. Extract top 8 matches
            let topEight = Array(scoredMatches.prefix(8))

            // 4. Store in Firestore
            try await storeTopMatches(topEight, for: userID)

            // 5. Remove from regular discovery pool
            await markAsTopMatches(topEight.map { $0.id ?? "" })

            print("✅ Generated \(topEight.count) top matches for user \(userID)")

        } catch {
            print("❌ Failed to generate top matches: \(error)")
        }
    }

    private func loadAllPotentialMatches(for userID: String) async throws -> [UserModel] {
        // Get all users and filter client-side to avoid composite index requirement
        let snapshot = try await db.collection("users").getDocuments()

        // Get users already interacted with (swiped on or matched)
        let interactedUserIDs = await getInteractedUserIDs(for: userID)

        var potentialMatches: [UserModel] = []

        for document in snapshot.documents {
            do {
                let user = try document.data(as: UserModel.self)

                // Filter out:
                // 1. Current user
                // 2. Users already in top matches
                // 3. Users already interacted with (swiped/matched)
                let isCurrentUser = user.id == userID
                let isInTopMatches = user.isInTopMatches ?? false
                let isAlreadyInteracted = interactedUserIDs.contains(user.id ?? "")

                if !isCurrentUser && !isInTopMatches && !isAlreadyInteracted {
                    potentialMatches.append(user)
                } else {
                    // Debug why user was excluded
                    let reasons = [
                        isCurrentUser ? "current user" : nil,
                        isInTopMatches ? "already in top matches" : nil,
                        isAlreadyInteracted ? "already interacted" : nil
                    ].compactMap { $0 }.joined(separator: ", ")

                    if !isCurrentUser { // Don't log current user exclusion
                        print("   ⚠️ Excluding \(user.firstName ?? "Unknown") (\(user.id ?? "no-id")): \(reasons)")
                    }
                }
            } catch {
                print("❌ Failed to decode user: \(error)")
            }
        }

        print("✅ Loaded \(potentialMatches.count) potential matches for top matches generation")
        print("   - Excluded \(interactedUserIDs.count) already interacted users")
        return potentialMatches
    }

    private func getInteractedUserIDs(for userID: String) async -> Set<String> {
        var interactedIDs = Set<String>()

        do {
            // Get all swipes by this user (both likes and passes)
            let swipesSnapshot = try await db.collection("swipes")
                .whereField("from", isEqualTo: userID)
                .getDocuments()

            for document in swipesSnapshot.documents {
                let data = document.data()
                if let swipedOnID = data["to"] as? String {
                    interactedIDs.insert(swipedOnID)
                }
            }
            print("🔍 Found \(swipesSnapshot.documents.count) swipe interactions")

            // Get all matches involving this user (mutual likes)
            let matchesSnapshot = try await db.collection("matches")
                .whereField("participants", arrayContains: userID)
                .getDocuments()

            for document in matchesSnapshot.documents {
                let data = document.data()
                if let participants = data["participants"] as? [String] {
                    for participantID in participants {
                        if participantID != userID {
                            interactedIDs.insert(participantID)
                        }
                    }
                }
            }
            print("🔍 Found \(matchesSnapshot.documents.count) match interactions")

            // ADDITIONAL: Get users who have been viewed in top matches (to prevent re-showing)
            let topMatchViewsSnapshot = try await db.collection("premium_analytics")
                .document(userID)
                .collection("top_match_views")
                .getDocuments()

            for document in topMatchViewsSnapshot.documents {
                let data = document.data()
                if let viewedUserID = data["viewedUserID"] as? String {
                    interactedIDs.insert(viewedUserID)
                }
            }
            print("🔍 Found \(topMatchViewsSnapshot.documents.count) top match view interactions")

            print("✅ Found \(interactedIDs.count) total already interacted users for filtering")
            print("   - Sample interacted user IDs: \(Array(interactedIDs).prefix(5))") // Show first 5 for debugging

        } catch {
            print("❌ Failed to load interacted users: \(error)")
        }

        return interactedIDs
    }

    private func getCurrentUser(_ userID: String) async -> UserModel? {
        do {
            let document = try await db.collection("users").document(userID).getDocument()
            return try document.data(as: UserModel.self)
        } catch {
            print("❌ Failed to load current user: \(error)")
            return nil
        }
    }

    private func storeTopMatches(_ matches: [UserModel], for userID: String) async throws {
        let topMatchData = matches.map { match in
            TopMatchData(userID: match.id ?? "", compatibilityScore: match.compatibilityScore ?? 0.0)
        }

        let collection = TopMatchesCollection(userID: userID, matches: topMatchData)

        let docRef = db.collection("top_matches").document(userID)
        try await docRef.setData(from: collection)

        print("✅ Stored \(matches.count) top matches for user \(userID)")
    }

    private func markAsTopMatches(_ userIDs: [String]) async {
        let batch = db.batch()
        let expirationDate = Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date()

        for userID in userIDs {
            let userRef = db.collection("users").document(userID)
            batch.updateData([
                "isInTopMatches": true,
                "topMatchesUntil": expirationDate
            ], forDocument: userRef)
        }

        do {
            try await batch.commit()
            print("✅ Marked \(userIDs.count) users as top matches")
        } catch {
            print("❌ Failed to mark users as top matches: \(error)")
        }
    }

    // MARK: - Premium Access Control

    /// Force refresh top matches by regenerating them (clears already matched users)
    func forceRefreshTopMatches(for userID: String) async {
        print("🔄 TopMatches: Force refreshing top matches for user: \(userID)")

        // Clear current matches
        DispatchQueue.main.async {
            self.topMatches = []
        }

        // Generate fresh top matches
        await generateDailyTopMatches(for: userID)

        // Load the new matches (start with recursion depth 0)
        await loadTopMatches(for: userID, recursionDepth: 0)

        print("✅ TopMatches: Force refresh completed")
    }

    func loadTopMatches(for userID: String, recursionDepth: Int = 0) async {
        // Prevent infinite recursion
        guard recursionDepth < 3 else {
            print("⚠️ TopMatches: Maximum recursion depth reached, stopping to prevent infinite loop")
            DispatchQueue.main.async {
                self.topMatches = []
                self.isLoading = false
            }
            return
        }

        DispatchQueue.main.async {
            self.isLoading = true
        }

        do {
            // Load today's top matches
            let doc = try await db.collection("top_matches").document(userID).getDocument()

            if doc.exists {
                let collection = try doc.data(as: TopMatchesCollection.self)

                // Check if matches are still valid (not expired)
                if collection.expiresAt > Date() {
                    // CRITICAL FIX: Re-filter to exclude users already matched with
                    let currentInteractedUsers = await getInteractedUserIDs(for: userID)
                    print("🔍 TopMatches: Re-filtering stored matches to exclude \(currentInteractedUsers.count) already interacted users")

                    // Load full user profiles and filter out already matched users
                    var loadedMatches: [UserModel] = []

                    for matchData in collection.matches {
                        // Skip if user has been matched/swiped on since top matches were generated
                        if currentInteractedUsers.contains(matchData.userID) {
                            print("   ⚠️ Excluding \(matchData.userID) from top matches - already interacted")
                            continue
                        }

                        if let userModel = await loadUserProfile(matchData.userID) {
                            var user = userModel
                            user.compatibilityScore = matchData.compatibilityScore
                            loadedMatches.append(user)
                        }
                    }

                    print("✅ TopMatches: Filtered down to \(loadedMatches.count) valid top matches (from \(collection.matches.count) stored)")

                    // If too few matches remain, regenerate fresh top matches (but only if we haven't recursed too much)
                    if loadedMatches.count < 3 && recursionDepth < 2 {
                        print("🔄 TopMatches: Only \(loadedMatches.count) valid matches remaining - regenerating fresh top matches (depth: \(recursionDepth))")
                        await generateDailyTopMatches(for: userID)
                        await loadTopMatches(for: userID, recursionDepth: recursionDepth + 1) // Recursive call with depth tracking
                        return
                    } else if loadedMatches.count < 3 {
                        print("⚠️ TopMatches: Only \(loadedMatches.count) valid matches but max recursion reached - using what we have")
                    }

                    DispatchQueue.main.async {
                        self.topMatches = loadedMatches
                        self.lastResetTime = collection.generatedAt
                    }
                } else {
                    // Matches expired, generate new ones (but only if we haven't recursed too much)
                    if recursionDepth < 2 {
                        print("🔄 TopMatches: Matches expired - generating new ones (depth: \(recursionDepth))")
                        await generateDailyTopMatches(for: userID)
                        await loadTopMatches(for: userID, recursionDepth: recursionDepth + 1) // Recursive call with depth tracking
                        return
                    } else {
                        print("⚠️ TopMatches: Matches expired but max recursion reached - using empty state")
                        DispatchQueue.main.async {
                            self.topMatches = []
                        }
                    }
                }
            } else {
                // No top matches exist, generate them (but only if we haven't recursed too much)
                if recursionDepth < 2 {
                    print("🔄 TopMatches: No matches exist - generating new ones (depth: \(recursionDepth))")
                    await generateDailyTopMatches(for: userID)
                    await loadTopMatches(for: userID, recursionDepth: recursionDepth + 1) // Recursive call with depth tracking
                    return
                } else {
                    print("⚠️ TopMatches: No matches exist but max recursion reached - using empty state")
                    DispatchQueue.main.async {
                        self.topMatches = []
                    }
                }
            }

            // Load user's daily usage
            await loadDailyUsage(for: userID)

        } catch {
            print("❌ Failed to load top matches: \(error)")
        }

        DispatchQueue.main.async {
            self.isLoading = false
        }
    }

    private func loadUserProfile(_ userID: String) async -> UserModel? {
        do {
            let document = try await db.collection("users").document(userID).getDocument()
            return try document.data(as: UserModel.self)
        } catch {
            print("❌ Failed to load user profile \(userID): \(error)")
            return nil
        }
    }

    private func loadDailyUsage(for userID: String) async {
        do {
            let doc = try await db.collection("premium_analytics").document(userID).getDocument()

            if let data = doc.data() {
                let viewed = data["topMatchesViewedToday"] as? Int ?? 0
                let liked = data["topMatchesLikedToday"] as? Int ?? 0

                DispatchQueue.main.async {
                    self.viewedCount = viewed
                    self.likedCount = liked
                    self.canViewMore = viewed < self.maxDailyViews
                    self.canLikeMore = liked < self.maxDailyLikes
                }
            } else {
                // Initialize premium analytics for new user
                try await db.collection("premium_analytics").document(userID).setData([
                    "userID": userID,
                    "topMatchesViewedToday": 0,
                    "topMatchesLikedToday": 0,
                    "lastTopMatchReset": FieldValue.serverTimestamp(),
                    "totalPremiumValue": 0.0,
                    "conversionEvents": []
                ])

                DispatchQueue.main.async {
                    self.viewedCount = 0
                    self.likedCount = 0
                    self.canViewMore = true
                    self.canLikeMore = true
                }
            }
        } catch {
            print("❌ Failed to load daily usage: \(error)")
        }
    }

    // MARK: - User Interaction Methods
    func canViewProfile(at index: Int) -> Bool {
        return index < viewedCount || canViewMore
    }

    func canLikeProfile() -> Bool {
        return canLikeMore
    }

    func viewProfile(at index: Int, userID: String, viewedUserID: String) {
        guard canViewProfile(at: index) else { return }

        if index >= viewedCount {
            viewedCount += 1
            canViewMore = viewedCount < maxDailyViews

            // Track in analytics
            AnalyticsManager.shared.trackTopMatchView(userID: userID)

            // Track that this user was viewed to prevent re-showing
            Task {
                await trackTopMatchView(currentUserID: userID, viewedUserID: viewedUserID)
            }
        }
    }

    private func trackTopMatchView(currentUserID: String, viewedUserID: String) async {
        do {
            let viewData: [String: Any] = [
                "viewedUserID": viewedUserID,
                "viewedAt": FieldValue.serverTimestamp(),
                "viewType": "top_match"
            ]

            try await db.collection("premium_analytics")
                .document(currentUserID)
                .collection("top_match_views")
                .document(viewedUserID)
                .setData(viewData)

            print("✅ Tracked top match view: \(currentUserID) viewed \(viewedUserID)")
        } catch {
            print("❌ Failed to track top match view: \(error)")
        }
    }

    func likeProfile(userID: String) -> Bool {
        guard canLikeProfile() else { return false }

        likedCount += 1
        canLikeMore = likedCount < maxDailyLikes

        // Track in analytics
        AnalyticsManager.shared.trackTopMatchLike(userID: userID)

        return true
    }

    // MARK: - Daily Reset Timer
    private func setupDailyResetTimer() {
        // Calculate time until next midnight
        let calendar = Calendar.current
        let now = Date()
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: now)!
        let midnight = calendar.startOfDay(for: tomorrow)

        let timeUntilMidnight = midnight.timeIntervalSince(now)

        // Schedule timer for midnight
        Timer.scheduledTimer(withTimeInterval: timeUntilMidnight, repeats: false) { _ in
            Task {
                await self.performDailyReset()
            }
        }
    }

    private func performDailyReset() async {
        print("🌅 Performing daily top matches reset...")

        // Reset analytics
        await AnalyticsManager.shared.resetDailyPremiumLimits()

        // Generate new top matches for all premium users
        await generateTopMatchesForAllPremiumUsers()

        // Release expired top matches back to discovery pool
        await releaseExpiredTopMatches()

        // Reset local state
        DispatchQueue.main.async {
            self.viewedCount = 0
            self.likedCount = 0
            self.canViewMore = true
            self.canLikeMore = true
            self.lastResetTime = Date()
        }

        // Schedule next reset
        setupDailyResetTimer()

        print("✅ Daily top matches reset completed")
    }

    private func generateTopMatchesForAllPremiumUsers() async {
        do {
            let snapshot = try await db.collection("users")
                .whereField("isPremium", isEqualTo: true)
                .getDocuments()

            for document in snapshot.documents {
                await generateDailyTopMatches(for: document.documentID)
            }

            print("✅ Generated top matches for \(snapshot.documents.count) premium users")
        } catch {
            print("❌ Failed to generate top matches for premium users: \(error)")
        }
    }

    private func releaseExpiredTopMatches() async {
        do {
            // Get all users marked as in top matches and filter client-side
            let snapshot = try await db.collection("users")
                .whereField("isInTopMatches", isEqualTo: true)
                .getDocuments()

            let batch = db.batch()
            let currentDate = Date()
            var expiredCount = 0

            for document in snapshot.documents {
                let data = document.data()
                if let expirationDate = data["topMatchesUntil"] as? Timestamp,
                   expirationDate.dateValue() < currentDate {

                    batch.updateData([
                        "isInTopMatches": false,
                        "topMatchesUntil": FieldValue.delete()
                    ], forDocument: document.reference)

                    expiredCount += 1
                }
            }

            if expiredCount > 0 {
                try await batch.commit()
                print("✅ Released \(expiredCount) users back to discovery pool")
            } else {
                print("✅ No expired top matches to release")
            }

        } catch {
            print("❌ Failed to release expired top matches: \(error)")
        }
    }
}
