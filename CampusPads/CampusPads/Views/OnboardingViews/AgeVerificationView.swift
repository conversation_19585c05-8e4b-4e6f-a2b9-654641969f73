import SwiftUI
import FirebaseAuth

struct AgeVerificationView: View {
    @StateObject private var viewModel = AgeVerificationViewModel()
    @State private var dateOfBirth = Date()
    @State private var showAgeError = false
    @State private var isSubmitting = false
    
    let onAgeVerified: (Date) -> Void
    let onAgeRejected: () -> Void
    
    // Calculate minimum date (18 years ago from today)
    private var minimumDate: Date {
        Calendar.current.date(byAdding: .year, value: -100, to: Date()) ?? Date()
    }
    
    // Calculate maximum date (18 years ago from today)
    private var maximumDate: Date {
        Calendar.current.date(byAdding: .year, value: -18, to: Date()) ?? Date()
    }
    
    // Check if user is 18 or older
    private var isAgeValid: Bool {
        viewModel.validateAge(dateOfBirth: dateOfBirth)
    }
    
    var body: some View {
        ZStack {
            AppTheme.backgroundGradient.ignoresSafeArea()
            
            VStack(spacing: AppTheme.spacing32) {
                // Header Section
                headerSection
                
                // Age Verification Section
                ageVerificationSection
                
                // Continue Button
                continueButton
                
                Spacer()
            }
            .padding(AppTheme.spacing24)
        }
        .alert("Age Requirement Not Met", isPresented: $showAgeError) {
            Button("I Understand") {
                onAgeRejected()
            }
        } message: {
            Text("You must be 18 or older to use CampusPads. This helps us maintain a safe environment for college students.")
        }
        .alert(item: Binding(
            get: {
                if let errorMessage = viewModel.errorMessage {
                    return GenericAlertError(message: errorMessage)
                }
                return nil
            },
            set: { _ in viewModel.errorMessage = nil }
        )) { alertError in
            Alert(title: Text("Error"), message: Text(alertError.message), dismissButton: .default(Text("OK")))
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)
                
                Image(systemName: "calendar.badge.checkmark")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            
            VStack(spacing: AppTheme.spacing8) {
                Text("Age Verification")
                    .font(.custom("AvenirNext-Bold", size: 28))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)
                
                Text("Please confirm your date of birth to continue")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var ageVerificationSection: some View {
        VStack(spacing: AppTheme.spacing20) {
            VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                Text("Date of Birth")
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(AppTheme.textPrimary)
                
                Text("You must be 18 or older to use CampusPads")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(AppTheme.textSecondary)
            }
            
            // Date Picker
            DatePicker(
                "Select your date of birth",
                selection: $dateOfBirth,
                in: minimumDate...maximumDate,
                displayedComponents: .date
            )
            .datePickerStyle(.wheel)
            .labelsHidden()
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .padding(AppTheme.spacing16)
            
            // Age Display
            if isAgeValid {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Age: \(viewModel.calculateAge(from: dateOfBirth)) years old")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(.green)
                }
                .padding(.top, AppTheme.spacing8)
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private var continueButton: some View {
        Button(action: verifyAge) {
            HStack(spacing: AppTheme.spacing12) {
                if isSubmitting {
                    ProgressView()
                        .tint(.white)
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.system(size: 18, weight: .bold))
                }
                
                Text(isSubmitting ? "Verifying..." : "Continue")
                    .font(.custom("AvenirNext-Bold", size: 18))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                    .fill(isAgeValid ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.gray))
            )
            .shadow(color: isAgeValid ? AppTheme.primaryColor.opacity(0.3) : Color.clear, radius: 8, x: 0, y: 4)
        }
        .disabled(!isAgeValid || isSubmitting)
        .scaleEffect(isSubmitting ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSubmitting)
    }
    
    // MARK: - Actions
    
    private func verifyAge() {
        isSubmitting = true
        // HapticFeedbackManager.shared.generateImpact(style: .medium)
        
        if viewModel.validateAge(dateOfBirth: dateOfBirth) {
            // Age is valid, proceed
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isSubmitting = false
                // HapticFeedbackManager.shared.generateNotification(.success)
                onAgeVerified(dateOfBirth)
            }
        } else {
            // Age is invalid, show error
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isSubmitting = false
                // HapticFeedbackManager.shared.generateNotification(.error)
                showAgeError = true
            }
        }
    }
}

// MARK: - Age Verification ViewModel

class AgeVerificationViewModel: ObservableObject {
    @Published var errorMessage: String?
    
    /// Validates if the user is 18 or older
    func validateAge(dateOfBirth: Date) -> Bool {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: Date())
        let age = ageComponents.year ?? 0
        return age >= 18
    }
    
    /// Calculates age from date of birth
    func calculateAge(from dateOfBirth: Date) -> Int {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: Date())
        return ageComponents.year ?? 0
    }
}

// MARK: - Preview

struct AgeVerificationView_Previews: PreviewProvider {
    static var previews: some View {
        AgeVerificationView(
            onAgeVerified: { _ in print("Age verified") },
            onAgeRejected: { print("Age rejected") }
        )
    }
}
