import Foundation
import FirebaseAuth
import FirebaseFirestore
import FirebaseStorage

/// Comprehensive data deletion service for GDPR compliance
/// Handles complete removal of user data across all Firebase services
class DataDeletionService {
    static let shared = DataDeletionService()
    
    private let db = Firestore.firestore()
    private let storage = Storage.storage()
    
    private init() {}
    
    /// Completely deletes all user data from Firebase
    /// - Parameter completion: Completion handler with success or error
    func deleteAllUserData(completion: @escaping (Result<Void, Error>) -> Void) {
        guard let currentUser = Auth.auth().currentUser else {
            completion(.failure(DataDeletionError.userNotAuthenticated))
            return
        }
        
        let userID = currentUser.uid
        Logger.shared.info("Starting complete data deletion for user: \(userID)")
        
        Task {
            do {
                // Step 1: Delete user data from Firestore collections
                try await deleteUserFromFirestore(userID: userID)
                
                // Step 2: Delete user images from Storage
                try await deleteUserImages(userID: userID)
                
                // Step 3: Remove user from matches and chats
                try await removeFromMatchesAndChats(userID: userID)
                
                // Step 4: Delete swipe history
                try await deleteSwipeHistory(userID: userID)
                
                // Step 5: Delete reports involving this user
                try await deleteUserReports(userID: userID)
                
                // Step 6: Clear user from other users' blocked lists
                try await removeFromBlockedLists(userID: userID)
                
                // Step 7: Delete Firebase Auth account (must be last)
                try await deleteFirebaseAuthAccount(user: currentUser)
                
                // Step 8: Clear local data
                clearLocalUserData()
                
                Logger.shared.info("Complete data deletion successful")
                DispatchQueue.main.async {
                    completion(.success(()))
                }

            } catch {
                Logger.shared.error("Data deletion failed: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // MARK: - Firestore Data Deletion
    
    /// Deletes user document from Firestore
    private func deleteUserFromFirestore(userID: String) async throws {
        Logger.shared.debug("Deleting user document from Firestore")

        try await db.collection("users").document(userID).delete()
        Logger.shared.debug("User document deleted from Firestore")
    }
    
    /// Deletes all user images from Firebase Storage
    private func deleteUserImages(userID: String) async throws {
        print("🗑️ DataDeletionService: Deleting user images from Storage")
        
        let storageRef = storage.reference()
        let userImagesRef = storageRef.child("user_images/\(userID)")
        
        do {
            // List all items in the user's image folder
            let listResult = try await userImagesRef.listAll()
            
            // Delete each image
            for item in listResult.items {
                try await item.delete()
                print("🗑️ DataDeletionService: Deleted image: \(item.name)")
            }
            
            print("✅ DataDeletionService: All user images deleted from Storage")
        } catch {
            // If folder doesn't exist, that's fine
            if (error as NSError).code == StorageErrorCode.objectNotFound.rawValue {
                print("ℹ️ DataDeletionService: No user images found to delete")
            } else {
                throw error
            }
        }
    }
    
    /// Removes user from all matches and chats
    private func removeFromMatchesAndChats(userID: String) async throws {
        print("🗑️ DataDeletionService: Removing user from matches and chats")
        
        // Delete matches where user is involved
        let matchesQuery = db.collection("matches")
            .whereField("userIDs", arrayContains: userID)
        
        let matchesSnapshot = try await matchesQuery.getDocuments()
        
        for document in matchesSnapshot.documents {
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted match: \(document.documentID)")
        }
        
        // Delete chats where user is involved
        let chatsQuery = db.collection("chats")
            .whereField("participants", arrayContains: userID)
        
        let chatsSnapshot = try await chatsQuery.getDocuments()
        
        for document in chatsSnapshot.documents {
            let chatID = document.documentID
            
            // Delete all messages in this chat
            let messagesQuery = db.collection("chats").document(chatID).collection("messages")
            let messagesSnapshot = try await messagesQuery.getDocuments()
            
            for messageDoc in messagesSnapshot.documents {
                try await messageDoc.reference.delete()
            }
            
            // Delete the chat document
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted chat and messages: \(chatID)")
        }
        
        print("✅ DataDeletionService: User removed from all matches and chats")
    }
    
    /// Deletes user's swipe history
    private func deleteSwipeHistory(userID: String) async throws {
        print("🗑️ DataDeletionService: Deleting swipe history")
        
        let swipesQuery = db.collection("swipes")
            .whereField("swiperId", isEqualTo: userID)
        
        let swipesSnapshot = try await swipesQuery.getDocuments()
        
        for document in swipesSnapshot.documents {
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted swipe: \(document.documentID)")
        }
        
        // Also delete swipes where this user was swiped on
        let swipedOnQuery = db.collection("swipes")
            .whereField("swipedUserId", isEqualTo: userID)
        
        let swipedOnSnapshot = try await swipedOnQuery.getDocuments()
        
        for document in swipedOnSnapshot.documents {
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted swipe on user: \(document.documentID)")
        }
        
        print("✅ DataDeletionService: Swipe history deleted")
    }
    
    /// Deletes reports involving this user
    private func deleteUserReports(userID: String) async throws {
        print("🗑️ DataDeletionService: Deleting user reports")
        
        // Delete reports made by this user
        let reportsByUserQuery = db.collection("reports")
            .whereField("reporterID", isEqualTo: userID)
        
        let reportsByUserSnapshot = try await reportsByUserQuery.getDocuments()
        
        for document in reportsByUserSnapshot.documents {
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted report by user: \(document.documentID)")
        }
        
        // Delete reports against this user
        let reportsAgainstUserQuery = db.collection("reports")
            .whereField("reportedUserID", isEqualTo: userID)
        
        let reportsAgainstUserSnapshot = try await reportsAgainstUserQuery.getDocuments()
        
        for document in reportsAgainstUserSnapshot.documents {
            try await document.reference.delete()
            print("🗑️ DataDeletionService: Deleted report against user: \(document.documentID)")
        }
        
        print("✅ DataDeletionService: User reports deleted")
    }
    
    /// Removes user from other users' blocked lists
    private func removeFromBlockedLists(userID: String) async throws {
        print("🗑️ DataDeletionService: Removing user from blocked lists")
        
        let usersQuery = db.collection("users")
            .whereField("blockedUserIDs", arrayContains: userID)
        
        let usersSnapshot = try await usersQuery.getDocuments()
        
        for document in usersSnapshot.documents {
            try await document.reference.updateData([
                "blockedUserIDs": FieldValue.arrayRemove([userID])
            ])
            print("🗑️ DataDeletionService: Removed user from blocked list: \(document.documentID)")
        }
        
        print("✅ DataDeletionService: User removed from all blocked lists")
    }
    
    /// Deletes Firebase Auth account
    private func deleteFirebaseAuthAccount(user: User) async throws {
        print("🗑️ DataDeletionService: Deleting Firebase Auth account")
        
        try await user.delete()
        print("✅ DataDeletionService: Firebase Auth account deleted")
    }
    
    /// Clears local user data
    private func clearLocalUserData() {
        print("🗑️ DataDeletionService: Clearing local user data")

        // Clear all user-specific UserDefaults keys
        let keysToRemove = [
            // Onboarding states
            "onboardingCompleted",
            "hasCompletedFilterOnboarding",
            "safetyTipsShownAfterOnboarding",

            // User preferences
            "isDarkMode",

            // Search data
            "RecentSearches",

            // Network operations
            "pendingOperations",

            // Any other user-specific data
            "userPreferences",
            "lastSyncTimestamp"
        ]

        for key in keysToRemove {
            UserDefaults.standard.removeObject(forKey: key)
        }

        UserDefaults.standard.synchronize()

        // Clear any cached data
        URLCache.shared.removeAllCachedResponses()

        // Clear any temporary files
        clearTemporaryFiles()

        print("✅ DataDeletionService: Local user data cleared")
    }

    /// Clears temporary files and caches
    private func clearTemporaryFiles() {
        let fileManager = FileManager.default

        // Clear temporary directory
        let tempDirectory = fileManager.temporaryDirectory
        do {
            let tempContents = try fileManager.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
            for url in tempContents {
                try fileManager.removeItem(at: url)
            }
            print("🗑️ DataDeletionService: Temporary files cleared")
        } catch {
            print("⚠️ DataDeletionService: Failed to clear temporary files: \(error)")
        }

        // Clear app's caches directory
        if let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first {
            do {
                let cacheContents = try fileManager.contentsOfDirectory(at: cachesDirectory, includingPropertiesForKeys: nil)
                for url in cacheContents {
                    try fileManager.removeItem(at: url)
                }
                print("🗑️ DataDeletionService: Cache files cleared")
            } catch {
                print("⚠️ DataDeletionService: Failed to clear cache files: \(error)")
            }
        }
    }
}

// MARK: - Error Types

enum DataDeletionError: LocalizedError {
    case userNotAuthenticated
    case firestoreError(String)
    case storageError(String)
    case authError(String)
    
    var errorDescription: String? {
        switch self {
        case .userNotAuthenticated:
            return "User not authenticated"
        case .firestoreError(let message):
            return "Database error: \(message)"
        case .storageError(let message):
            return "Storage error: \(message)"
        case .authError(let message):
            return "Authentication error: \(message)"
        }
    }
}
