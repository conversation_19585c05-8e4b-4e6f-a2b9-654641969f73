//
//  SimpleImageLoader.swift
//  CampusPads
//
//  SIMPLE: Basic image loader that just works
//

import Foundation
import UIKit

/// SIMPLE: Basic image loader with caching
class SimpleImageLoader: ObservableObject {
    static let shared = SimpleImageLoader()

    private let cache = NSCache<NSString, UIImage>()
    let session: URLSession  // Make public for FirebaseStorageService

    private init() {
        // Enable Firebase URL interception for QUIC prevention globally
        FirebaseInterceptorManager.shared.enableInterception()

        let config = NetworkConfigurationManager.shared.createBasicConfiguration()

        // Register our interceptor with this session (for our requests)
        config.protocolClasses = [FirebaseURLInterceptor.self]

        self.session = URLSession(configuration: config)

        // Enhanced cache setup for better performance
        self.cache.countLimit = 200 // Increased for more cards
        self.cache.totalCostLimit = 100 * 1024 * 1024 // 100MB for better caching

        // Enable automatic cache eviction
        self.cache.evictsObjectsWithDiscardedContent = true

        print("✅ SimpleImageLoader: Initialized with enhanced caching and Firebase URL interception")
    }

    /// SIMPLE: Load image from URL with comprehensive debugging
    func loadImage(from urlString: String, completion: @escaping (Result<UIImage, Error>) -> Void) {
        let cleanURL = urlString.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🔄 SimpleImageLoader: Starting load for URL: \(cleanURL)")

        // Check cache first
        if let cachedImage = cache.object(forKey: cleanURL as NSString) {
            print("✅ SimpleImageLoader: Found cached image for: \(cleanURL)")
            completion(.success(cachedImage))
            return
        }

        guard let url = URL(string: cleanURL) else {
            print("❌ SimpleImageLoader: Invalid URL: \(cleanURL)")
            completion(.failure(SimpleImageError.invalidURL))
            return
        }

        print("🌐 SimpleImageLoader: Making network request to: \(url.host ?? "unknown host")")

        // Simple request
        let task = session.dataTask(with: url) { [weak self] data, response, error in
            if let error = error {
                print("❌ SimpleImageLoader: Network error for \(cleanURL): \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }

            if let httpResponse = response as? HTTPURLResponse {
                print("📡 SimpleImageLoader: HTTP response \(httpResponse.statusCode) for \(cleanURL)")
                print("📡 SimpleImageLoader: Content-Type: \(httpResponse.value(forHTTPHeaderField: "Content-Type") ?? "unknown")")
                print("📡 SimpleImageLoader: Content-Length: \(httpResponse.value(forHTTPHeaderField: "Content-Length") ?? "unknown")")
            }

            guard let data = data else {
                print("❌ SimpleImageLoader: No data received for \(cleanURL)")
                DispatchQueue.main.async {
                    completion(.failure(SimpleImageError.invalidData))
                }
                return
            }

            print("📦 SimpleImageLoader: Received \(data.count) bytes for \(cleanURL)")

            guard let image = UIImage(data: data) else {
                print("❌ SimpleImageLoader: Failed to create UIImage from \(data.count) bytes for \(cleanURL)")

                // DEBUG: If it's a small response, it might be an error message
                if data.count < 1000, let errorString = String(data: data, encoding: .utf8) {
                    print("🔍 SimpleImageLoader: Response content (likely error): \(errorString)")
                }

                DispatchQueue.main.async {
                    completion(.failure(SimpleImageError.invalidData))
                }
                return
            }

            print("✅ SimpleImageLoader: Successfully created UIImage (\(image.size.width)x\(image.size.height)) for \(cleanURL)")

            // Cache and return
            self?.cache.setObject(image, forKey: cleanURL as NSString)
            print("💾 SimpleImageLoader: Cached image for \(cleanURL)")

            DispatchQueue.main.async {
                completion(.success(image))
            }
        }

        print("🚀 SimpleImageLoader: Starting network task for \(cleanURL)")
        task.resume()
    }

    /// Clear cache
    func clearCache() {
        cache.removeAllObjects()
    }
}

// MARK: - Simple Error Types

enum SimpleImageError: Error, LocalizedError {
    case invalidURL
    case invalidData

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid image URL"
        case .invalidData:
            return "Invalid image data"
        }
    }
}