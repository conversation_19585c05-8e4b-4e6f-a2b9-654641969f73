import SwiftUI
import FirebaseFirestore

struct TopMatchDetailView: View {
    let match: UserModel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var topMatchesManager = TopMatchesManager.shared
    @StateObject private var profileVM = ProfileViewModel.shared
    @State private var showLikeAnimation = false
    @State private var currentImageIndex = 0

    var body: some View {
        NavigationView {
            ZStack {
                // Premium background
                premiumBackground

                ScrollView {
                    VStack(spacing: AppTheme.spacing24) {
                        // Image carousel
                        imageCarousel

                        // Profile info
                        profileInfoSection

                        // Compatibility section
                        compatibilitySection

                        // Action buttons
                        actionButtonsSection

                        Spacer(minLength: AppTheme.spacing32)
                    }
                    .padding(AppTheme.spacing20)
                }
            }
            .navigationTitle("Top Match")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .foregroundStyle(AppTheme.sexyGradient)
                    .font(.custom("AvenirNext-Semibold", size: 16))
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    premiumBadge
                }
            }
        }
    }

    // MARK: - Premium Background
    private var premiumBackground: some View {
        ZStack {
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()

            // Premium overlay
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.yellow.opacity(0.1),
                    Color.orange.opacity(0.05),
                    Color.clear
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
        }
    }

    // MARK: - Image Carousel
    private var imageCarousel: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Main image
            ZStack {
                if let imageUrls = match.profileImageUrls, !imageUrls.isEmpty {
                    TabView(selection: $currentImageIndex) {
                        ForEach(Array(imageUrls.enumerated()), id: \.offset) { index, imageUrl in
                            AsyncImage(url: URL(string: imageUrl)) { image in
                                image
                                    .resizable()
                                    .scaledToFill()
                            } placeholder: {
                                Rectangle()
                                    .fill(AppTheme.sexyGradient)
                                    .overlay(
                                        ProgressView()
                                            .tint(.white)
                                            .scaleEffect(1.2)
                                    )
                            }
                            .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                } else {
                    Rectangle()
                        .fill(AppTheme.sexyGradient)
                        .overlay(
                            VStack(spacing: AppTheme.spacing8) {
                                Image(systemName: "person.fill")
                                    .font(.system(size: 40, weight: .light))
                                    .foregroundColor(.white)

                                Text("No Photos")
                                    .font(.custom("AvenirNext-Medium", size: 14))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                        )
                }

                // Premium overlay
                VStack {
                    HStack {
                        premiumImageBadge
                        Spacer()
                    }
                    Spacer()
                }
                .padding(AppTheme.spacing16)
            }
            .frame(height: 400)
            .clipShape(RoundedRectangle(cornerRadius: AppTheme.radiusLarge))
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .stroke(
                        LinearGradient(
                            colors: [.yellow.opacity(0.6), .orange.opacity(0.4)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
            )
            .shadow(color: Color.yellow.opacity(0.3), radius: 12, x: 0, y: 6)

            // Image indicators
            if let imageUrls = match.profileImageUrls, imageUrls.count > 1 {
                HStack(spacing: AppTheme.spacing8) {
                    ForEach(0..<imageUrls.count, id: \.self) { index in
                        Circle()
                            .fill(index == currentImageIndex ? Color.yellow : Color.white.opacity(0.5))
                            .frame(width: 8, height: 8)
                            .scaleEffect(index == currentImageIndex ? 1.2 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: currentImageIndex)
                    }
                }
            }
        }
    }

    // MARK: - Profile Info Section
    private var profileInfoSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            // Name and age
            HStack {
                VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                    Text(match.firstName ?? "Unknown")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)

                    if let age = match.age {
                        Text("\(age) years old")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                }

                Spacer()

                // Verification badge
                if match.isEmailVerified {
                    HStack(spacing: AppTheme.spacing4) {
                        Image(systemName: "checkmark.seal.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.blue)

                        Text("Verified")
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(.blue)
                    }
                    .padding(.horizontal, AppTheme.spacing8)
                    .padding(.vertical, AppTheme.spacing4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(AppTheme.radiusSmall)
                }
            }

            // Bio
            if let bio = match.bio, !bio.isEmpty {
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("About")
                        .font(.custom("AvenirNext-Bold", size: 18))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text(bio)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textPrimary)
                        .lineLimit(nil)
                }
            }

            // College info
            if let college = match.college, !college.isEmpty {
                HStack {
                    Image(systemName: "graduationcap.fill")
                        .font(.system(size: 16))
                        .foregroundColor(AppTheme.primaryColor)

                    Text(college)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textPrimary)

                    Spacer()
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(AppTheme.modernCardGradient)
        .cornerRadius(AppTheme.radiusLarge)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .stroke(Color.white.opacity(0.2), lineWidth: 1)
        )
    }

    // MARK: - Compatibility Section
    private var compatibilitySection: some View {
        VStack(spacing: AppTheme.spacing16) {
            HStack {
                Text("Compatibility")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundStyle(AppTheme.sexyGradient)

                Spacer()

                Text("\(Int((match.compatibilityScore ?? 0.0) * 100))%")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundColor(.green)
            }

            // Compatibility bar
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 12)

                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            colors: [.green, .yellow, .orange],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: CGFloat(match.compatibilityScore ?? 0.0) * 300, height: 12)
                    .animation(.spring(response: 0.8, dampingFraction: 0.7), value: match.compatibilityScore)
            }
            .frame(width: 300)

            Text("This match was selected as one of your top 8 daily matches based on compatibility!")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(AppTheme.spacing20)
        .background(AppTheme.modernCardGradient)
        .cornerRadius(AppTheme.radiusLarge)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                .stroke(Color.green.opacity(0.3), lineWidth: 1)
        )
    }

    // MARK: - Action Buttons
    private var actionButtonsSection: some View {
        HStack(spacing: AppTheme.spacing20) {
            // Pass button
            Button(action: {
                // Handle pass
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(Color.red)
                    .clipShape(Circle())
                    .shadow(color: .red.opacity(0.4), radius: 8, x: 0, y: 4)
            }

            Spacer()

            // Like button
            Button(action: {
                handleLike()
            }) {
                Image(systemName: "heart.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(topMatchesManager.canLikeProfile() ? Color.green : Color.gray)
                    .clipShape(Circle())
                    .shadow(color: .green.opacity(0.4), radius: 8, x: 0, y: 4)
                    .scaleEffect(showLikeAnimation ? 1.2 : 1.0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.6), value: showLikeAnimation)
            }
            .disabled(!topMatchesManager.canLikeProfile())
        }
        .padding(.horizontal, AppTheme.spacing40)
    }

    // MARK: - UI Components
    private var premiumBadge: some View {
        HStack(spacing: AppTheme.spacing4) {
            Image(systemName: "crown.fill")
                .font(.system(size: 12))
                .foregroundColor(.yellow)

            Text("TOP MATCH")
                .font(.custom("AvenirNext-Bold", size: 10))
                .foregroundColor(.yellow)
        }
        .padding(.horizontal, AppTheme.spacing8)
        .padding(.vertical, AppTheme.spacing4)
        .background(Color.yellow.opacity(0.2))
        .cornerRadius(AppTheme.radiusSmall)
    }

    private var premiumImageBadge: some View {
        HStack(spacing: AppTheme.spacing4) {
            Image(systemName: "star.fill")
                .font(.system(size: 12))
                .foregroundColor(.white)

            Text("TOP MATCH")
                .font(.custom("AvenirNext-Bold", size: 10))
                .foregroundColor(.white)
        }
        .padding(.horizontal, AppTheme.spacing8)
        .padding(.vertical, AppTheme.spacing4)
        .background(Color.yellow)
        .cornerRadius(AppTheme.radiusSmall)
        .shadow(color: .yellow.opacity(0.4), radius: 4, x: 0, y: 2)
    }

    // MARK: - Actions
    private func handleLike() {
        guard let userID = profileVM.userProfile?.id else { return }

        if topMatchesManager.likeProfile(userID: userID) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                showLikeAnimation = true
            }

            // Create premium match
            createPremiumMatch()

            // Reset animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                showLikeAnimation = false
                dismiss()
            }
        }
    }

    private func createPremiumMatch() {
        Task {
            do {
                try await MatchingService.shared.createMatch(
                    user1ID: profileVM.userProfile?.id ?? "",
                    user2ID: match.id ?? "",
                    isPremiumMatch: true
                )

                // Track premium match creation
                AnalyticsManager.shared.trackEvent("premium_match_created_from_detail", parameters: [
                    "match_user_id": match.id ?? "",
                    "compatibility_score": match.compatibilityScore ?? 0.0
                ])

            } catch {
                print("❌ Failed to create premium match: \(error)")
            }
        }
    }
}

// MARK: - Preview
struct TopMatchDetailView_Previews: PreviewProvider {
    static var previews: some View {
        TopMatchDetailView(match: UserModel.mockUser())
    }
}
