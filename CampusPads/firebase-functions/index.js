const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// 🔔 PUSH NOTIFICATION CLOUD FUNCTIONS

/**
 * Send push notification when a new match is created
 */
exports.sendMatchNotification = functions.firestore
    .document('matches/{matchId}')
    .onCreate(async (snap, context) => {
        try {
            const matchData = snap.data();
            const participants = matchData.participants || [];
            
            console.log('🎉 New match created:', context.params.matchId);
            console.log('Participants:', participants);
            
            if (participants.length !== 2) {
                console.error('Invalid participants count:', participants.length);
                return;
            }
            
            // Get user profiles for both participants
            const userPromises = participants.map(userId => 
                admin.firestore().collection('users').doc(userId).get()
            );
            
            const userDocs = await Promise.all(userPromises);
            const users = userDocs.map(doc => ({ id: doc.id, ...doc.data() }));
            
            // Send notification to both users
            const notificationPromises = users.map(async (user, index) => {
                const otherUser = users[1 - index]; // Get the other user
                
                if (!user.fcmToken || !user.notificationsEnabled) {
                    console.log(`User ${user.id} has no FCM token or notifications disabled`);
                    return;
                }
                
                const message = {
                    token: user.fcmToken,
                    notification: {
                        title: '🎉 New Match!',
                        body: `You matched with ${otherUser.firstName || 'someone'}! Start chatting now.`,
                    },
                    data: {
                        type: 'new_match',
                        matchId: context.params.matchId,
                        matchedUserId: otherUser.id,
                        action: 'open_matches'
                    },
                    apns: {
                        payload: {
                            aps: {
                                badge: 1,
                                sound: 'default'
                            }
                        }
                    }
                };
                
                return admin.messaging().send(message);
            });
            
            await Promise.all(notificationPromises);
            console.log('✅ Match notifications sent successfully');
            
        } catch (error) {
            console.error('❌ Error sending match notification:', error);
        }
    });

/**
 * Send push notification when a new message is sent
 */
exports.sendMessageNotification = functions.firestore
    .document('chats/{chatId}/messages/{messageId}')
    .onCreate(async (snap, context) => {
        try {
            const messageData = snap.data();
            const chatId = context.params.chatId;
            
            console.log('💬 New message in chat:', chatId);
            
            // Get chat document to find participants
            const chatDoc = await admin.firestore().collection('chats').doc(chatId).get();
            if (!chatDoc.exists) {
                console.error('Chat document not found:', chatId);
                return;
            }
            
            const chatData = chatDoc.data();
            const participants = chatData.participants || [];
            
            // Find recipient (not the sender)
            const recipientId = participants.find(id => id !== messageData.senderID);
            if (!recipientId) {
                console.error('Could not find message recipient');
                return;
            }
            
            // Get recipient's profile
            const recipientDoc = await admin.firestore().collection('users').doc(recipientId).get();
            if (!recipientDoc.exists) {
                console.error('Recipient profile not found:', recipientId);
                return;
            }
            
            const recipient = recipientDoc.data();
            if (!recipient.fcmToken || !recipient.notificationsEnabled) {
                console.log(`Recipient ${recipientId} has no FCM token or notifications disabled`);
                return;
            }
            
            // Get sender's name
            const senderDoc = await admin.firestore().collection('users').doc(messageData.senderID).get();
            const senderName = senderDoc.exists ? senderDoc.data().firstName || 'Someone' : 'Someone';
            
            // Create short message preview
            const messagePreview = createMessagePreview(messageData.text || 'Sent a message');
            
            const message = {
                token: recipient.fcmToken,
                notification: {
                    title: senderName,
                    body: messagePreview,
                },
                data: {
                    type: 'new_message',
                    chatId: chatId,
                    senderId: messageData.senderID,
                    action: 'open_chat'
                },
                apns: {
                    payload: {
                        aps: {
                            badge: 1,
                            sound: 'default'
                        }
                    }
                }
            };
            
            await admin.messaging().send(message);
            console.log('✅ Message notification sent successfully');
            
        } catch (error) {
            console.error('❌ Error sending message notification:', error);
        }
    });

/**
 * Process queued notifications from the notifications collection
 */
exports.processQueuedNotifications = functions.firestore
    .document('notifications/{notificationId}')
    .onCreate(async (snap, context) => {
        try {
            const notificationData = snap.data();
            console.log('📤 Processing queued notification:', context.params.notificationId);
            
            // Send the notification
            await admin.messaging().send(notificationData);
            
            // Delete the processed notification
            await snap.ref.delete();
            
            console.log('✅ Queued notification processed and deleted');
            
        } catch (error) {
            console.error('❌ Error processing queued notification:', error);
            
            // Mark as failed instead of deleting
            await snap.ref.update({
                failed: true,
                error: error.message,
                failedAt: admin.firestore.FieldValue.serverTimestamp()
            });
        }
    });

/**
 * Clean up old failed notifications (runs daily)
 */
exports.cleanupFailedNotifications = functions.pubsub
    .schedule('0 2 * * *') // Run at 2 AM daily
    .timeZone('America/New_York')
    .onRun(async (context) => {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 7); // 7 days ago
            
            const query = admin.firestore()
                .collection('notifications')
                .where('failed', '==', true)
                .where('failedAt', '<', cutoffDate);
            
            const snapshot = await query.get();
            const batch = admin.firestore().batch();
            
            snapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            
            await batch.commit();
            console.log(`🧹 Cleaned up ${snapshot.size} old failed notifications`);
            
        } catch (error) {
            console.error('❌ Error cleaning up failed notifications:', error);
        }
    });

// Helper function to create message preview
function createMessagePreview(fullMessage) {
    const maxLength = 50;
    
    if (fullMessage.length <= maxLength) {
        return fullMessage;
    }
    
    // Smart truncation - try to end at word boundary
    const truncated = fullMessage.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.7) { // Only use word boundary if it's not too short
        return truncated.substring(0, lastSpace) + '...';
    }
    
    return truncated + '...';
}

console.log('🚀 CampusPads Firebase Cloud Functions loaded successfully');
