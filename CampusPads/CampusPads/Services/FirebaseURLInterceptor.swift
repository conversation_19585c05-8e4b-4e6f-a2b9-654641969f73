//
//  FirebaseURLInterceptor.swift
//  CampusPads
//
//  SIMPLE: System-level QUIC prevention for Firebase Storage
//

import Foundation

/// SIMPLE: URL interceptor that forces HTTP/1.1 for Firebase Storage
class FirebaseURLInterceptor: URLProtocol {

    private static let handledKey = "FirebaseURLInterceptorHandled"

    override class func canInit(with request: URLRequest) -> <PERSON><PERSON> {
        // Only handle Firebase Storage URLs that haven't been processed yet
        guard let url = request.url,
              property(forKey: handledKey, in: request) == nil else {
            return false
        }

        return isFirebaseURL(url)
    }

    override class func canonicalRequest(for request: URLRequest) -> URLRequest {
        return request
    }

    override func startLoading() {
        guard let url = request.url else {
            client?.urlProtocol(self, didFailWithError: URLError(.badURL))
            return
        }

        // Create modified request with HTTP/1.1 enforcement
        let modifiedRequest = createHTTP1Request(from: request)

        print("🔧 FirebaseURLInterceptor: Forcing HTTP/1.1 for \(url.host ?? "unknown")")

        // Create session with HTTP/1.1-only configuration
        let config = URLSessionConfiguration.ephemeral
        config.httpMaximumConnectionsPerHost = 1
        config.httpShouldUsePipelining = false
        config.timeoutIntervalForRequest = 10.0
        config.timeoutIntervalForResource = 30.0
        config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        config.urlCache = nil

        // Force HTTP/1.1 headers
        config.httpAdditionalHeaders = [
            "Connection": "close",
            "HTTP-Version": "HTTP/1.1",
            "User-Agent": "CampusPads/1.0 HTTP1-Only"
        ]

        let session = URLSession(configuration: config)

        let task = session.dataTask(with: modifiedRequest) { [weak self] data, response, error in
            guard let self = self else { return }

            if let error = error {
                print("❌ FirebaseURLInterceptor: Request failed for \(url.host ?? "unknown"): \(error.localizedDescription)")
                self.client?.urlProtocol(self, didFailWithError: error)
            } else {
                if let httpResponse = response as? HTTPURLResponse {
                    print("✅ FirebaseURLInterceptor: Success with HTTP/1.1 - Status: \(httpResponse.statusCode)")
                    print("📡 FirebaseURLInterceptor: Content-Type: \(httpResponse.value(forHTTPHeaderField: "Content-Type") ?? "unknown")")
                    if let data = data {
                        print("📦 FirebaseURLInterceptor: Received \(data.count) bytes")
                    }
                    self.client?.urlProtocol(self, didReceive: httpResponse, cacheStoragePolicy: .notAllowed)
                } else if let response = response {
                    print("✅ FirebaseURLInterceptor: Success with HTTP/1.1 (non-HTTP response)")
                    self.client?.urlProtocol(self, didReceive: response, cacheStoragePolicy: .notAllowed)
                }

                if let data = data {
                    self.client?.urlProtocol(self, didLoad: data)
                }

                self.client?.urlProtocolDidFinishLoading(self)
            }
        }

        task.resume()
    }

    override func stopLoading() {
        // Nothing to stop - task handles its own lifecycle
    }

    // MARK: - Helper Methods

    private static func isFirebaseURL(_ url: URL) -> Bool {
        guard let host = url.host else { return false }

        // COMPREHENSIVE: Handle ALL Firebase and Google API services
        return host.contains("firebasestorage.googleapis.com") ||
               host.contains("appspot.com") ||
               host.contains("firebasestorage.app") ||
               host.contains("firebaselogging-pa.googleapis.com") ||  // Analytics logging
               host.contains("firebase.googleapis.com") ||
               host.contains("firestore.googleapis.com") ||
               host.contains("firebaseremoteconfig.googleapis.com") ||  // Remote Config
               host.contains("firebaseinstallations.googleapis.com") ||  // Installations
               host.contains("fcmregistrations.googleapis.com") ||  // FCM
               host.contains("identitytoolkit.googleapis.com") ||  // Auth
               host.contains("securetoken.googleapis.com") ||  // Auth tokens
               host.contains("www.googleapis.com") ||  // General Google APIs
               host.hasSuffix(".googleapis.com")  // Catch-all for Google APIs
    }

    private func createHTTP1Request(from original: URLRequest) -> URLRequest {
        guard let originalURL = original.url else {
            return original // Return original if URL is nil (shouldn't happen)
        }

        let modified = NSMutableURLRequest(url: originalURL, cachePolicy: original.cachePolicy, timeoutInterval: original.timeoutInterval)

        // Copy original headers
        if let headers = original.allHTTPHeaderFields {
            for (key, value) in headers {
                modified.setValue(value, forHTTPHeaderField: key)
            }
        }

        // Copy HTTP method and body
        modified.httpMethod = original.httpMethod ?? "GET"
        modified.httpBody = original.httpBody

        // Mark as handled to prevent infinite loops
        URLProtocol.setProperty(true, forKey: Self.handledKey, in: modified)

        // CRITICAL FIX: Don't remove port 443 for Firebase Storage - it's required!
        // Firebase Storage URLs need the port to work correctly
        print("🔧 FirebaseURLInterceptor: Keeping original URL with port (Firebase Storage requirement)")

        // Force HTTP/1.1 headers
        modified.setValue("close", forHTTPHeaderField: "Connection")
        modified.setValue("HTTP/1.1", forHTTPHeaderField: "HTTP-Version")
        modified.setValue("identity", forHTTPHeaderField: "Accept-Encoding")
        modified.setValue("CampusPads/1.0 HTTP1-Only", forHTTPHeaderField: "User-Agent")

        return modified as URLRequest
    }
}

/// SIMPLE: Manager to register/unregister the interceptor
class FirebaseInterceptorManager {
    static let shared = FirebaseInterceptorManager()

    private var isRegistered = false

    private init() {}

    func enableInterception() {
        guard !isRegistered else { return }

        URLProtocol.registerClass(FirebaseURLInterceptor.self)
        isRegistered = true
        print("✅ FirebaseURLInterceptor: Enabled system-level QUIC prevention")
    }

    func disableInterception() {
        guard isRegistered else { return }

        URLProtocol.unregisterClass(FirebaseURLInterceptor.self)
        isRegistered = false
        print("🔧 FirebaseURLInterceptor: Disabled")
    }
}
