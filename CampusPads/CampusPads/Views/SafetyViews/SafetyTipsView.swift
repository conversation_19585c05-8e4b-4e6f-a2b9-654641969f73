import SwiftUI

struct SafetyTipsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var currentTipIndex = 0
    @State private var showingDismissConfirmation = false
    
    let onComplete: () -> Void
    
    private let safetyTips = [
        SafetyTip(
            icon: "person.2.fill",
            title: "Meet in Public Places",
            description: "Always meet potential roommates in public locations like campus cafes, libraries, or student centers. Never meet at private residences for first meetings.",
            color: .blue
        ),
        SafetyTip(
            icon: "person.badge.shield.checkmark",
            title: "Verify Their Identity",
            description: "Confirm they actually attend your college. Ask to see their student ID or check their college email address. Be cautious of profiles with limited information.",
            color: .green
        ),
        SafetyTip(
            icon: "house.fill",
            title: "Verify Housing Information",
            description: "Independently verify any housing or lease information. Contact landlords directly and never send money without seeing the property in person.",
            color: .orange
        ),
        SafetyTip(
            icon: "person.3.fill",
            title: "Bring a Friend",
            description: "Consider bringing a trusted friend to initial meetings. Let someone know where you're going and when you expect to return.",
            color: .purple
        ),
        SafetyTip(
            icon: "exclamationmark.triangle.fill",
            title: "Trust Your Instincts",
            description: "If something feels off or too good to be true, trust your gut. You can always block and report users who make you uncomfortable.",
            color: .red
        ),
        SafetyTip(
            icon: "video.fill",
            title: "Video Chat First",
            description: "Consider having a video call before meeting in person. This helps verify their identity and gives you a better sense of their personality.",
            color: .teal
        )
    ]
    
    var body: some View {
        ZStack {
            AppTheme.backgroundGradient.ignoresSafeArea()
            
            VStack(spacing: AppTheme.spacing24) {
                // Header
                headerSection
                
                // Safety Tip Card
                safetyTipCard
                
                // Progress Indicator
                progressIndicator
                
                // Navigation Buttons
                navigationButtons
                
                Spacer()
            }
            .padding(AppTheme.spacing20)
        }
        .alert("Skip Safety Tips?", isPresented: $showingDismissConfirmation) {
            Button("Skip", role: .destructive) {
                onComplete()
            }
            Button("Continue Reading", role: .cancel) { }
        } message: {
            Text("These safety tips help protect you when meeting potential roommates. We recommend reading all tips before continuing.")
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 12, x: 0, y: 6)
                
                Image(systemName: "shield.checkered")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            
            VStack(spacing: AppTheme.spacing8) {
                Text("Safety First")
                    .font(.custom("AvenirNext-Bold", size: 28))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 2, x: 0, y: 1)
                
                Text("Important tips for meeting potential roommates safely")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var safetyTipCard: some View {
        let currentTip = safetyTips[currentTipIndex]
        
        return VStack(spacing: AppTheme.spacing20) {
            // Tip Icon
            ZStack {
                Circle()
                    .fill(currentTip.color.opacity(0.2))
                    .frame(width: 80, height: 80)
                
                Image(systemName: currentTip.icon)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(currentTip.color)
            }
            
            // Tip Content
            VStack(spacing: AppTheme.spacing12) {
                Text(currentTip.title)
                    .font(.custom("AvenirNext-Bold", size: 22))
                    .foregroundColor(AppTheme.textPrimary)
                    .multilineTextAlignment(.center)
                
                Text(currentTip.description)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
            }
        }
        .padding(AppTheme.spacing24)
        .background(
            RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
    
    private var progressIndicator: some View {
        HStack(spacing: AppTheme.spacing8) {
            ForEach(0..<safetyTips.count, id: \.self) { index in
                Circle()
                    .fill(index <= currentTipIndex ? AppTheme.primaryColor : AppTheme.primaryColor.opacity(0.3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(index == currentTipIndex ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: currentTipIndex)
            }
        }
    }
    
    private var navigationButtons: some View {
        HStack(spacing: AppTheme.spacing16) {
            // Previous Button
            if currentTipIndex > 0 {
                Button(action: previousTip) {
                    HStack(spacing: AppTheme.spacing8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .bold))
                        Text("Previous")
                            .font(.custom("AvenirNext-Bold", size: 16))
                    }
                    .foregroundColor(AppTheme.primaryColor)
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.vertical, AppTheme.spacing12)
                    .background(
                        RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                            .stroke(AppTheme.primaryColor, lineWidth: 2)
                    )
                }
            } else {
                Button("Skip") {
                    showingDismissConfirmation = true
                }
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(AppTheme.textSecondary)
                .padding(.horizontal, AppTheme.spacing20)
                .padding(.vertical, AppTheme.spacing12)
            }
            
            Spacer()
            
            // Next/Complete Button
            Button(action: nextTip) {
                HStack(spacing: AppTheme.spacing8) {
                    Text(currentTipIndex == safetyTips.count - 1 ? "Got It!" : "Next")
                        .font(.custom("AvenirNext-Bold", size: 16))
                    
                    if currentTipIndex < safetyTips.count - 1 {
                        Image(systemName: "chevron.right")
                            .font(.system(size: 16, weight: .bold))
                    } else {
                        Image(systemName: "checkmark")
                            .font(.system(size: 16, weight: .bold))
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing20)
                .padding(.vertical, AppTheme.spacing12)
                .background(
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.sexyGradient)
                )
                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
            }
        }
    }
    
    // MARK: - Actions
    
    private func previousTip() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            if currentTipIndex > 0 {
                currentTipIndex -= 1
            }
        }
        // HapticFeedbackManager.shared.generateImpact(style: .light)
    }
    
    private func nextTip() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            if currentTipIndex < safetyTips.count - 1 {
                currentTipIndex += 1
            } else {
                onComplete()
            }
         }
       // HapticFeedbackManager.shared.generateImpact(style: .medium)
    }
}

// MARK: - Supporting Types

struct SafetyTip {
    let icon: String
    let title: String
    let description: String
    let color: Color
}

// MARK: - Preview

struct SafetyTipsView_Previews: PreviewProvider {
    static var previews: some View {
        SafetyTipsView(onComplete: {
            print("Safety tips completed")
        })
    }
}
