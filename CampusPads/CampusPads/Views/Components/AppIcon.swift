//
//  AppIcon.swift
//  CampusPads
//
//  Beautiful, modern app icon component for CampusPads
//

import SwiftUI

/// Stunning app icon that represents roommate matching and college housing
struct AppIcon: View {
    let size: CGFloat
    let showGlow: Bool
    let animated: Bool

    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var heartBeat: CGFloat = 1.0

    init(size: CGFloat = 120, showGlow: Bool = true, animated: Bool = true) {
        self.size = size
        self.showGlow = showGlow
        self.animated = animated
    }

    var body: some View {
        ZStack {
            // Outer glow ring (optional)
            if showGlow {
                Circle()
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                AppTheme.primaryColor.opacity(0.6),
                                AppTheme.accentColor.opacity(0.4),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: size * 0.025
                    )
                    .frame(width: size * 1.3, height: size * 1.3)
                    .rotationEffect(.degrees(animated ? rotationAngle : 0))
                    .scaleEffect(animated ? pulseScale : 1.0)
            }

            // Main icon background
            Circle()
                .fill(AppTheme.sexyGradient)
                .frame(width: size, height: size)
                .shadow(
                    color: AppTheme.primaryColor.opacity(showGlow ? 0.4 : 0.2),
                    radius: size * 0.15,
                    x: 0,
                    y: size * 0.08
                )

            // Icon content
            iconContent
                .scaleEffect(size / 120) // Scale relative to default size
        }
        .onAppear {
            if animated {
                startAnimations()
            }
        }
    }

    // MARK: - Icon Content
    private var iconContent: some View {
        // Modern house with heart - clean, centered design
        ZStack {
            // House structure
            houseStructure

            // Connection heart in center
            Image(systemName: "heart.fill")
                .font(.system(size: size * 0.117, weight: .bold)) // Slightly smaller heart (14 points at default size)
                .foregroundColor(AppTheme.accentColor)
                .scaleEffect(animated ? heartBeat : 1.0)
        }
    }

    // MARK: - House Structure
    private var houseStructure: some View {
        ZStack {
            // House base - perfectly centered
            RoundedRectangle(cornerRadius: size * 0.05) // 6 points at default size (matches renderer)
                .fill(Color.white)
                .frame(width: size * 0.333, height: size * 0.267) // 40x32 at default size

            // House roof - positioned above base for perfect centering
            Triangle()
                .fill(Color.white)
                .frame(width: size * 0.4, height: size * 0.167) // 48x20 at default size
                .offset(y: -size * 0.133) // -16 points at default size

            // Windows - positioned on house base
            HStack(spacing: size * 0.05) {
                RoundedRectangle(cornerRadius: size * 0.025)
                    .fill(AppTheme.primaryColor.opacity(0.3))
                    .frame(width: size * 0.067, height: size * 0.067) // 8x8 at default size

                RoundedRectangle(cornerRadius: size * 0.025)
                    .fill(AppTheme.primaryColor.opacity(0.3))
                    .frame(width: size * 0.067, height: size * 0.067) // 8x8 at default size
            }
            .offset(y: -size * 0.033) // -4 points at default size
        }
    }



    // MARK: - Animations
    private func startAnimations() {
        // Continuous rotation for glow ring
        withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }

        // Pulse effect for glow ring
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            pulseScale = 1.05
        }

        // Heart beat animation
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            heartBeat = 1.2
        }
    }
}

// MARK: - App Icon Variants
extension AppIcon {
    /// Large icon for splash screen (animated)
    static func splash() -> some View {
        AppIcon(size: 120, showGlow: true, animated: true)
    }

    /// Large icon for authentication pages (static, no animation loop)
    static func `static`() -> some View {
        AppIcon(size: 120, showGlow: true, animated: false)
    }

    /// Medium icon for settings or about pages
    static func medium() -> some View {
        AppIcon(size: 80, showGlow: true, animated: false)
    }

    /// Small icon for navigation or list items
    static func small() -> some View {
        AppIcon(size: 40, showGlow: false, animated: false)
    }

    /// Tiny icon for badges or indicators
    static func tiny() -> some View {
        AppIcon(size: 24, showGlow: false, animated: false)
    }
}







// MARK: - Preview
struct AppIcon_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            AppIcon.splash()
            AppIcon.medium()
            AppIcon.small()
        }
        .padding()
        .background(Color.black)
    }
}
