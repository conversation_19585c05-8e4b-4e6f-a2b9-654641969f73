import Foundation
import FirebaseStorage
import UIKit
import AVFoundation
import Combine

@MainActor
class MediaUploadService: ObservableObject {
    static let shared = MediaUploadService()
    
    private let storage = Storage.storage()
    private var uploadTasks: [String: StorageUploadTask] = [:]
    
    @Published var uploadProgress: [String: Double] = [:]
    @Published var isUploading: Bool = false
    
    private init() {}
    
    // MARK: - Image Upload
    func uploadImage(_ image: UIImage, chatID: String, messageID: String) async throws -> (url: String, thumbnailUrl: String?) {
        print("📸 Starting image upload for message: \(messageID)")
        
        // Compress image
        guard let compressedImageData = compressImage(image) else {
            throw MediaUploadError.compressionFailed
        }
        
        // Create storage reference
        let imagePath = "chat_media/\(chatID)/\(messageID)/image.jpg"
        let imageRef = storage.reference().child(imagePath)
        
        // Upload with progress tracking
        return try await withCheckedThrowingContinuation { continuation in
            let uploadTask = imageRef.putData(compressedImageData, metadata: createImageMetadata()) { [weak self] metadata, error in
                if let error = error {
                    print("❌ Image upload failed: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                    return
                }
                
                // Get download URL
                imageRef.downloadURL { url, error in
                    if let error = error {
                        print("❌ Failed to get download URL: \(error.localizedDescription)")
                        continuation.resume(throwing: error)
                        return
                    }
                    
                    guard let downloadURL = url else {
                        continuation.resume(throwing: MediaUploadError.noDownloadURL)
                        return
                    }
                    
                    print("✅ Image uploaded successfully: \(downloadURL.absoluteString)")
                    continuation.resume(returning: (url: downloadURL.absoluteString, thumbnailUrl: nil))
                    
                    // Clean up
                    DispatchQueue.main.async {
                        self?.uploadTasks.removeValue(forKey: messageID)
                        self?.uploadProgress.removeValue(forKey: messageID)
                        self?.updateUploadingStatus()
                    }
                }
            }
            
            // Track upload progress
            uploadTask.observe(.progress) { [weak self] snapshot in
                guard let progress = snapshot.progress else { return }
                let progressValue = Double(progress.completedUnitCount) / Double(progress.totalUnitCount)
                
                DispatchQueue.main.async {
                    self?.uploadProgress[messageID] = progressValue
                    print("📸 Upload progress: \(Int(progressValue * 100))%")
                }
            }
            
            // Store upload task for potential cancellation
            uploadTasks[messageID] = uploadTask
            isUploading = true
        }
    }
    
    // MARK: - Video Upload
    func uploadVideo(from url: URL, chatID: String, messageID: String) async throws -> (url: String, thumbnailUrl: String?) {
        print("🎥 Starting video upload for message: \(messageID)")
        
        // Compress video
        let compressedVideoURL = try await compressVideo(url)
        
        // Generate thumbnail
        let thumbnailImage = try await generateVideoThumbnail(from: compressedVideoURL)
        
        // Upload video
        let videoPath = "chat_media/\(chatID)/\(messageID)/video.mp4"
        let videoRef = storage.reference().child(videoPath)
        
        let videoData = try Data(contentsOf: compressedVideoURL)
        
        return try await withCheckedThrowingContinuation { continuation in
            let uploadTask = videoRef.putData(videoData, metadata: createVideoMetadata()) { [weak self] metadata, error in
                if let error = error {
                    print("❌ Video upload failed: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                    return
                }
                
                // Get video download URL
                videoRef.downloadURL { videoURL, error in
                    if let error = error {
                        print("❌ Failed to get video download URL: \(error.localizedDescription)")
                        continuation.resume(throwing: error)
                        return
                    }
                    
                    guard let videoDownloadURL = videoURL else {
                        continuation.resume(throwing: MediaUploadError.noDownloadURL)
                        return
                    }
                    
                    // Upload thumbnail
                    Task {
                        do {
                            let thumbnailURL = try await self?.uploadThumbnail(thumbnailImage, chatID: chatID, messageID: messageID)
                            print("✅ Video and thumbnail uploaded successfully")
                            continuation.resume(returning: (url: videoDownloadURL.absoluteString, thumbnailUrl: thumbnailURL))
                        } catch {
                            print("⚠️ Video uploaded but thumbnail failed: \(error.localizedDescription)")
                            continuation.resume(returning: (url: videoDownloadURL.absoluteString, thumbnailUrl: nil))
                        }
                        
                        // Clean up
                        DispatchQueue.main.async {
                            self?.uploadTasks.removeValue(forKey: messageID)
                            self?.uploadProgress.removeValue(forKey: messageID)
                            self?.updateUploadingStatus()
                        }
                    }
                }
            }
            
            // Track upload progress
            uploadTask.observe(.progress) { [weak self] snapshot in
                guard let progress = snapshot.progress else { return }
                let progressValue = Double(progress.completedUnitCount) / Double(progress.totalUnitCount)
                
                DispatchQueue.main.async {
                    self?.uploadProgress[messageID] = progressValue
                    print("🎥 Upload progress: \(Int(progressValue * 100))%")
                }
            }
            
            uploadTasks[messageID] = uploadTask
            isUploading = true
        }
    }
    
    // MARK: - Helper Methods
    private func compressImage(_ image: UIImage, quality: CGFloat = 0.7) -> Data? {
        // Resize image if too large
        let maxDimension: CGFloat = 1920
        let resizedImage: UIImage
        
        if max(image.size.width, image.size.height) > maxDimension {
            let scale = maxDimension / max(image.size.width, image.size.height)
            let newSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
            image.draw(in: CGRect(origin: .zero, size: newSize))
            resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
            UIGraphicsEndImageContext()
        } else {
            resizedImage = image
        }
        
        return resizedImage.jpegData(compressionQuality: quality)
    }
    
    private func compressVideo(_ inputURL: URL) async throws -> URL {
        let outputURL = FileManager.default.temporaryDirectory
            .appendingPathComponent(UUID().uuidString)
            .appendingPathExtension("mp4")
        
        return try await withCheckedThrowingContinuation { continuation in
            let asset = AVAsset(url: inputURL)
            
            guard let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetMediumQuality) else {
                continuation.resume(throwing: MediaUploadError.compressionFailed)
                return
            }
            
            exportSession.outputURL = outputURL
            exportSession.outputFileType = .mp4
            exportSession.shouldOptimizeForNetworkUse = true
            
            exportSession.exportAsynchronously {
                switch exportSession.status {
                case .completed:
                    continuation.resume(returning: outputURL)
                case .failed:
                    continuation.resume(throwing: exportSession.error ?? MediaUploadError.compressionFailed)
                case .cancelled:
                    continuation.resume(throwing: MediaUploadError.uploadCancelled)
                default:
                    continuation.resume(throwing: MediaUploadError.compressionFailed)
                }
            }
        }
    }
    
    private func generateVideoThumbnail(from url: URL) async throws -> UIImage {
        return try await withCheckedThrowingContinuation { continuation in
            let asset = AVAsset(url: url)
            let imageGenerator = AVAssetImageGenerator(asset: asset)
            imageGenerator.appliesPreferredTrackTransform = true
            
            let time = CMTime(seconds: 1, preferredTimescale: 60)
            
            imageGenerator.generateCGImagesAsynchronously(forTimes: [NSValue(time: time)]) { _, cgImage, _, _, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let cgImage = cgImage else {
                    continuation.resume(throwing: MediaUploadError.thumbnailGenerationFailed)
                    return
                }
                
                let thumbnail = UIImage(cgImage: cgImage)
                continuation.resume(returning: thumbnail)
            }
        }
    }
    
    private func uploadThumbnail(_ image: UIImage, chatID: String, messageID: String) async throws -> String {
        guard let thumbnailData = compressImage(image, quality: 0.5) else {
            throw MediaUploadError.compressionFailed
        }
        
        let thumbnailPath = "chat_media/\(chatID)/\(messageID)/thumbnail.jpg"
        let thumbnailRef = storage.reference().child(thumbnailPath)
        
        return try await withCheckedThrowingContinuation { continuation in
            thumbnailRef.putData(thumbnailData, metadata: createImageMetadata()) { metadata, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                thumbnailRef.downloadURL { url, error in
                    if let error = error {
                        continuation.resume(throwing: error)
                        return
                    }
                    
                    guard let downloadURL = url else {
                        continuation.resume(throwing: MediaUploadError.noDownloadURL)
                        return
                    }
                    
                    continuation.resume(returning: downloadURL.absoluteString)
                }
            }
        }
    }
    
    private func createImageMetadata() -> StorageMetadata {
        let metadata = StorageMetadata()
        metadata.contentType = "image/jpeg"
        metadata.cacheControl = "public, max-age=31536000"
        return metadata
    }
    
    private func createVideoMetadata() -> StorageMetadata {
        let metadata = StorageMetadata()
        metadata.contentType = "video/mp4"
        metadata.cacheControl = "public, max-age=31536000"
        return metadata
    }
    
    private func updateUploadingStatus() {
        isUploading = !uploadTasks.isEmpty
    }
    
    // MARK: - Cancel Upload
    func cancelUpload(messageID: String) {
        uploadTasks[messageID]?.cancel()
        uploadTasks.removeValue(forKey: messageID)
        uploadProgress.removeValue(forKey: messageID)
        updateUploadingStatus()
    }
}

// MARK: - Error Types
enum MediaUploadError: LocalizedError {
    case compressionFailed
    case noDownloadURL
    case uploadCancelled
    case thumbnailGenerationFailed
    
    var errorDescription: String? {
        switch self {
        case .compressionFailed:
            return "Failed to compress media file"
        case .noDownloadURL:
            return "Failed to get download URL"
        case .uploadCancelled:
            return "Upload was cancelled"
        case .thumbnailGenerationFailed:
            return "Failed to generate video thumbnail"
        }
    }
}
