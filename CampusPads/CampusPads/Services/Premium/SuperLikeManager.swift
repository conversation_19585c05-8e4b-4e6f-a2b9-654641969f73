import Foundation
import FirebaseFirestore
import Combine

@MainActor
class SuperLikeManager: ObservableObject {
    static let shared = SuperLikeManager()
    private let db = Firestore.firestore()
    
    @Published var weeklySuperLikeCount: Int = 0
    @Published var hasReachedLimit: Bool = false
    @Published var isLoading: Bool = false
    
    // Constants
    let maxWeeklySuperLikes = 3 // Premium users get 3 super likes per week
    private var currentUserID: String?
    
    private init() {
        setupWeeklyResetTimer()
    }
    
    // MARK: - Public Methods
    
    func initialize(for userID: String) async {
        currentUserID = userID
        await loadWeeklySuperLikeCount()
    }
    
    func canSuperLike(isPremium: Bool) -> Bool {
        guard isPremium else {
            return false // Non-premium users cannot super like
        }
        return weeklySuperLikeCount < maxWeeklySuperLikes
    }
    
    func recordSuperLike(userID: String, isPremium: Bool) async {
        guard isPremium else {
            print("⚠️ Non-premium user attempted to super like")
            return
        }
        
        guard weeklySuperLikeCount < maxWeeklySuperLikes else {
            print("⚠️ User has reached weekly super like limit")
            return
        }
        
        // Increment local count
        weeklySuperLikeCount += 1
        hasReachedLimit = weeklySuperLikeCount >= maxWeeklySuperLikes
        
        // Update Firebase
        await updateFirebaseSuperLikeCount(userID: userID)
        
        print("⭐ Weekly super likes: \(weeklySuperLikeCount)/\(maxWeeklySuperLikes)")
    }
    
    func getRemainingSuperLikes(isPremium: Bool) -> Int {
        guard isPremium else {
            return 0 // Non-premium users get 0
        }
        return max(0, maxWeeklySuperLikes - weeklySuperLikeCount)
    }
    
    func getSuperLikeCountText(isPremium: Bool) -> String {
        guard isPremium else {
            return "Premium Feature"
        }
        
        let remaining = getRemainingSuperLikes(isPremium: isPremium)
        if remaining == 0 {
            return "No Super Likes Left"
        } else {
            return "\(remaining) Super Like\(remaining == 1 ? "" : "s") Left"
        }
    }
    
    // MARK: - Private Methods
    
    private func loadWeeklySuperLikeCount() async {
        guard let userID = currentUserID else { return }
        
        isLoading = true
        
        do {
            let weekKey = getCurrentWeekKey()
            let doc = try await db.collection("weekly_super_like_limits")
                .document(userID)
                .collection("weekly_counts")
                .document(weekKey)
                .getDocument()
            
            if let data = doc.data() {
                let count = data["superLikeCount"] as? Int ?? 0
                
                DispatchQueue.main.async {
                    self.weeklySuperLikeCount = count
                    self.hasReachedLimit = count >= self.maxWeeklySuperLikes
                }
                
                print("✅ Loaded weekly super like count: \(count)")
            } else {
                // No document exists, user hasn't super liked this week
                DispatchQueue.main.async {
                    self.weeklySuperLikeCount = 0
                    self.hasReachedLimit = false
                }
                
                print("✅ No super likes recorded for this week")
            }
            
        } catch {
            print("❌ Failed to load weekly super like count: \(error)")
        }
        
        isLoading = false
    }
    
    private func updateFirebaseSuperLikeCount(userID: String) async {
        do {
            let weekKey = getCurrentWeekKey()
            let docRef = db.collection("weekly_super_like_limits")
                .document(userID)
                .collection("weekly_counts")
                .document(weekKey)
            
            let updateData: [String: Any] = [
                "superLikeCount": weeklySuperLikeCount,
                "lastUpdated": FieldValue.serverTimestamp(),
                "weekKey": weekKey,
                "userID": userID
            ]
            
            try await docRef.setData(updateData, merge: true)
            print("✅ Updated Firebase super like count: \(weeklySuperLikeCount)")
            
        } catch {
            print("❌ Failed to update Firebase super like count: \(error)")
        }
    }
    
    private func getCurrentWeekKey() -> String {
        let calendar = Calendar.current
        let now = Date()
        
        // Get the start of the current week (Monday)
        let weekday = calendar.component(.weekday, from: now)
        let daysFromMonday = (weekday + 5) % 7 // Convert Sunday=1 to Monday=0
        let startOfWeek = calendar.date(byAdding: .day, value: -daysFromMonday, to: now)!
        
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-'W'ww" // e.g., "2024-W01"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: startOfWeek)
    }
    
    // MARK: - Weekly Reset Timer
    
    private func setupWeeklyResetTimer() {
        let calendar = Calendar.current
        let now = Date()
        
        // Calculate next Monday at midnight
        let weekday = calendar.component(.weekday, from: now)
        let daysUntilNextMonday = (9 - weekday) % 7
        let nextMonday = calendar.date(byAdding: .day, value: daysUntilNextMonday == 0 ? 7 : daysUntilNextMonday, to: now)!
        let nextMondayMidnight = calendar.startOfDay(for: nextMonday)
        
        let timeInterval = nextMondayMidnight.timeIntervalSince(now)
        
        print("⏰ SuperLikeManager: Next weekly reset in \(timeInterval/3600) hours")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + timeInterval) {
            Task {
                await self.performWeeklyReset()
            }
        }
    }
    
    private func performWeeklyReset() async {
        print("🌅 Performing weekly super like reset...")
        
        // Reset local state
        DispatchQueue.main.async {
            self.weeklySuperLikeCount = 0
            self.hasReachedLimit = false
        }
        
        // Schedule next reset
        setupWeeklyResetTimer()
        
        print("✅ Weekly super like reset completed")
    }
    
    // MARK: - Analytics Integration
    
    func trackSuperLikeLimitReached(userID: String) {
        AnalyticsManager.shared.trackEvent("weekly_super_like_limit_reached", parameters: [
            "user_id": userID,
            "super_like_count": weeklySuperLikeCount,
            "limit": maxWeeklySuperLikes,
            "week": getCurrentWeekKey()
        ])
        
        print("📊 Tracked super like limit reached for user: \(userID)")
    }
    
    func trackSuperLikeAttemptWithoutPremium(userID: String) {
        AnalyticsManager.shared.trackEvent("super_like_attempt_without_premium", parameters: [
            "user_id": userID,
            "feature": "super_like"
        ])
        
        print("📊 Tracked super like attempt without premium for user: \(userID)")
    }
}
