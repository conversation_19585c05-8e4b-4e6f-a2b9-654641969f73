import SwiftUI
import CoreLocation
import FirebaseAuth

// MARK: - Preview Mode Definition
enum PreviewMode: String, CaseIterable, Identifiable {
    case personal = "Personal"
    case lease = "Lease"
    var id: String { self.rawValue }
}

// MARK: - ProfilePreviewView
struct ProfilePreviewView: View {
    let user: UserModel

    // Tracks the current image index for the page-based swipe.
    @State private var currentIndex: Int = 0

    // Persist the preview mode in storage so it remains selected across view launches.
    @AppStorage("profilePreviewMode") private var previewModeRaw: String = PreviewMode.personal.rawValue
    private var previewMode: PreviewMode {
        get { PreviewMode(rawValue: previewModeRaw) ?? .personal }
        set { previewModeRaw = newValue.rawValue }
    }

    // Reporting/blocking state.
    @State private var showReportSheet = false
    @State private var showBlockAlert = false
    @State private var showSuperLikePaywall = false
    @State private var errorMessage: String?

    // Premium action bar state
    @State private var showActionBar = false
    @State private var isLiking = false
    @State private var isSuperLiking = false
    @State private var hasLiked = false
    @State private var hasSuperLiked = false
    @State private var showMatchCelebration = false

    // Access to matching view model for swipe actions
    @StateObject private var matchingViewModel = MatchingViewModel.shared
    @StateObject private var superLikeManager = SuperLikeManager.shared

    // Animation control for stable viewing
    private var shouldUseStableAnimations: Bool {
        // Use stable animations when accessed from search to prevent bouncing
        return showActionBar
    }

    // Animation states for modern entrance effects
    @State private var animateCards = false

    // Tinder-style full-screen immersive cards that maximize entire screen space.
    private let cardAspectRatio: CGFloat = 1.25

    // Computed property to check if the user is in roommate mode.
    private var isRoommateMode: Bool {
        return user.housingStatus == PrimaryHousingPreference.lookingForRoommate.rawValue
    }

    // Convenience: full name.
    private var fullName: String {
        [user.firstName, user.lastName].compactMap { $0 }.joined(separator: " ")
    }

    // MARK: - Initializer
    init(user: UserModel, forcePreviewMode: PreviewMode? = nil, showPremiumActions: Bool = false) {
        self.user = user
        if let forced = forcePreviewMode {
            _previewModeRaw.wrappedValue = forced.rawValue
        } else {
            // Always ensure we have a valid selection
            _previewModeRaw.wrappedValue = PreviewMode.personal.rawValue
        }
        _showActionBar = State(initialValue: showPremiumActions)
    }
    // MARK: - Enhanced Overlay Lists for Personal Previews
    private var personalOverlays: [[String]] {
        return [
            [
                "🎓 \(user.collegeName ?? "College Not Specified")",
                "🎂 Age \(user.dateOfBirth != nil ? Calendar.current.dateComponents([.year], from: user.dateOfBirth!, to: Date()).year ?? 0 : 0)",
                "📚 \(user.major ?? "Major Not Specified")",
                "📖 \(user.gradeLevel ?? "Grade Level Not Specified")"
            ],
            [ user.aboutMe?.isEmpty == false ? "\(user.aboutMe!)" : "No bio available" ],
            [ "💪 Workout: \(user.workout ?? "Not specified")" ],
            [
                "🍺 Drinking: \(user.drinking ?? "Not specified")",
                "🚬 Smoking: \((user.smoker ?? false) ? "Yes" : "No")",
                "🌿 420 Friendly: \(user.cannabis ?? "Not specified")"
            ],
            [ "Going Out Quiz: \(user.goingOutQuizAnswers?.joined(separator: ", ") ?? "N/A")" ],
            [ "Weekends Quiz: \(user.weekendQuizAnswers?.joined(separator: ", ") ?? "N/A")" ],
            [
                "Pets: \((user.petFriendly ?? false) ? "Yes" : "No")",
                "Selected Pets: \(user.pets?.joined(separator: ", ") ?? "N/A")"
            ],
            [ "Interests: \(user.interests?.joined(separator: ", ") ?? "N/A")" ],
            [
                "MyPhone Quiz: \(user.phoneQuizAnswers?.joined(separator: ", ") ?? "N/A")",
                "Social Media: \(user.socialMedia ?? "N/A")"
            ]
        ]
    }

    // MARK: - Privacy & Security Helpers

    /// Sanitizes address for privacy by showing only general area information
    private func sanitizeAddressForPrivacy(_ address: String) -> String {
        let components = address.components(separatedBy: ",")

        // Extract city and state/country, hide specific street address
        if components.count >= 2 {
            let cityAndBeyond = components.dropFirst().joined(separator: ",").trimmingCharacters(in: .whitespaces)

            // If we have city info, show "Near [City, State]"
            if !cityAndBeyond.isEmpty {
                return "Near \(cityAndBeyond)"
            }
        }

        // Fallback: show "General Area" if we can't parse properly
        return "General Area"
    }

    // MARK: - New Lease Overlay Helper
    /// Returns the overlay lines for each lease preview slide.
    private func leaseOverlay(for slideIndex: Int) -> [String] {
        switch slideIndex {
        case 0:
            // Slide 0: Display housing type selected (from desiredLeaseHousingType), room type, and monthly rent.
            let housing = user.desiredLeaseHousingType?.isEmpty == false ? user.desiredLeaseHousingType! : "Not specified"
            let room = user.roomType?.isEmpty == false ? user.roomType! : "Not specified"

            // Enhanced rent range calculation with better formatting
            let rentRange: String
            if let minRent = user.monthlyRentMin, let maxRent = user.monthlyRentMax {
                if minRent == maxRent {
                    rentRange = "$\(Int(minRent))/month"
                } else {
                    rentRange = "$\(Int(minRent))–$\(Int(maxRent))/month"
                }
            } else if let minRent = user.monthlyRentMin {
                rentRange = "$\(Int(minRent))+/month"
            } else if let maxRent = user.monthlyRentMax {
                rentRange = "Up to $\(Int(maxRent))/month"
            } else {
                rentRange = "Not specified"
            }

            return ["Housing: \(housing)", "Room: \(room)", "Rent: \(rentRange)"]
        case 1:
            // Slide 1: No overlay.
            return []
        case 2:
            // Slide 2: Location, lease start date and lease duration.
            let formatter = DateFormatter()
            formatter.dateStyle = .medium

            let leaseStart: String
            if let startDate = user.leaseStartDate {
                leaseStart = formatter.string(from: startDate)
            } else {
                leaseStart = "Not specified"
            }

            let duration = user.leaseDuration?.isEmpty == false ? user.leaseDuration! : "Not specified"

            // Add location information with privacy protection
            let location: String
            if let address = user.propertyAddress, !address.isEmpty {
                // SECURITY FIX: Hide specific street addresses for privacy
                location = sanitizeAddressForPrivacy(address)
            } else {
                location = "Location not specified"
            }

            return ["📍 \(location)", "Start Date: \(leaseStart)", "Duration: \(duration)"]
        case 3:
            // Slide 3: Roommates already had and roommates needed.
            let already = user.roommateCountExisting ?? 0
            let needed = user.roommateCountNeeded ?? 0

            return ["Current Roommates: \(already)", "Looking for: \(needed) more"]
        case 4:
            // Slide 4: Property details bio.
            return [ (user.propertyDetails ?? "").isEmpty ? "No Property Details" : (user.propertyDetails ?? "No Property Details") ]
        case 5:
            // Slide 5: First 5 special lease conditions.
            if let conditions = user.specialLeaseConditions, !conditions.isEmpty {
                let slice = conditions.prefix(5)
                return Array(slice)
            } else {
                return ["No special lease conditions"]
            }
        case 6:
            // Slide 6: Next 5 special lease conditions.
            if let conditions = user.specialLeaseConditions, conditions.count > 5 {
                let remainder = conditions.dropFirst(5).prefix(5)
                return Array(remainder)
            } else {
                return ["No special lease conditions"]
            }
        case 7:
            // Slide 7: First 5 amenities.
            if let amens = user.amenities, !amens.isEmpty {
                let slice = amens.prefix(5)
                return Array(slice)
            } else {
                return ["No amenities"]
            }
        case 8:
            // Slide 8: Next 5 amenities.
            if let amens = user.amenities, amens.count > 5 {
                let remainder = amens.dropFirst(5).prefix(5)
                return Array(remainder)
            } else {
                return ["No amenities"]
            }
        case 9:
            // Slide 9: Cleanliness and sleeping habits.
            let cleanText = (user.cleanliness != nil) ? "Cleanliness: \(user.cleanliness!)/5" : "Cleanliness: N/A"
            let sleepText = (user.sleepSchedule ?? "").isEmpty ? "Sleeping Habits: N/A" : "Sleeping Habits: \(user.sleepSchedule!)"
            return [cleanText, sleepText]
        default:
            return []
        }
    }

    // MARK: - Body
    var body: some View {
        ZStack {
            // Sexy dynamic background
            AppTheme.dynamicBackgroundGradient.ignoresSafeArea()

            VStack(spacing: AppTheme.spacing16) {
                if user.housingStatus == PrimaryHousingPreference.lookingForRoommate.rawValue {
                    // Modern segmented picker for roommate mode
                    modernSegmentedPicker
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : -20)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
                } else {
                    // For non-roommate users, force previewMode to personal.
                    EmptyView()
                        .onAppear {
                            previewModeRaw = PreviewMode.personal.rawValue
                        }
                }

                // Card content with modern styling and animations
                ZStack {
                    if user.housingStatus == PrimaryHousingPreference.lookingForRoommate.rawValue {
                        // Roommate mode: toggle between Personal and Lease preview.
                        if previewMode == .personal, let images = user.profileImageUrls, !images.isEmpty {
                            modernCardView(with: images, overlays: personalOverlays)
                        } else if previewMode == .lease, let images = user.propertyImageUrls, !images.isEmpty {
                            modernLeaseCardView(with: images)
                        } else {
                            modernEmptyStateView()
                        }
                    } else {
                        // Non-roommate: always show the personal preview.
                        if let images = user.profileImageUrls, !images.isEmpty {
                            modernCardView(with: images, overlays: personalOverlays)
                        } else {
                            modernEmptyStateView()
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, AppTheme.spacing4) // Minimal padding for perfect centering
                .opacity(animateCards ? 1.0 : 0.0)
                .offset(y: animateCards ? 0 : 30)
                .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2), value: animateCards)

                Spacer()
            }
            .padding(.top, AppTheme.spacing8)

            // Premium Action Bar Overlay
            if showActionBar {
                VStack {
                    Spacer()
                    premiumActionBar
                }
                .ignoresSafeArea(.keyboard)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // Trigger entrance animations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                animateCards = true
            }
        }
        .alert(item: Binding(
            get: {
                if let err = errorMessage {
                    return GenericAlertError(message: err)
                }
                return nil
            },
            set: { _ in errorMessage = nil }
        )) { (alertError: GenericAlertError) in
            Alert(title: Text("Error"),
                  message: Text(alertError.message),
                  dismissButton: .default(Text("OK")))
        }
        .actionSheet(isPresented: $showBlockAlert) { blockActionSheet }
        .sheet(isPresented: $showReportSheet) {
            if let userID = user.id {
                ReportUserView(reportedUserID: userID)
            } else {
                Text("No user ID found.")
            }
        }
        .overlay(
            // Super like paywall overlay
            Group {
                if showSuperLikePaywall {
                    PremiumPaywallOverlay(
                        title: "Premium Super Likes",
                        subtitle: "Get 3 weekly super likes to stand out and get noticed by your top matches",
                        onUpgrade: {
                            showSuperLikePaywall = false
                            // Navigate to premium upgrade
                        },
                        onDismiss: {
                            showSuperLikePaywall = false
                        }
                    )
                }
            }
        )
    }

    // MARK: - Modern UI Components

    private var modernSegmentedPicker: some View {
        HStack(spacing: AppTheme.spacing4) {
            ForEach(PreviewMode.allCases, id: \.rawValue) { mode in
                let isSelected = previewMode == mode

                Button(action: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    previewModeRaw = mode.rawValue
                }) {
                    Text(mode.rawValue)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                        .padding(.vertical, AppTheme.spacing12)
                        .padding(.horizontal, AppTheme.spacing16)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .scaleEffect(isSelected ? 1.02 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
            }
        }
        .padding(AppTheme.spacing4)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial.opacity(0.6))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, AppTheme.spacing16)
    }

    // MARK: - Modern Card View Builders

    private func modernEmptyStateView() -> some View {
        VStack(spacing: AppTheme.spacing20) {
            // Animated icon with glow effect
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 100, height: 100)
                    .blur(radius: 20)
                    .opacity(0.6)

                Image(systemName: "photo.on.rectangle.angled")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.white)
            }
            .scaleEffect(animateCards ? 1.0 : 0.8)
            .animation(.spring(response: 0.8, dampingFraction: 0.6).delay(0.3), value: animateCards)

            VStack(spacing: AppTheme.spacing12) {
                Text("No Images Available")
                    .font(.custom("AvenirNext-Bold", size: 24))
                    .foregroundColor(.white)

                Text("Add some photos to make your profile shine!")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .opacity(animateCards ? 1.0 : 0.0)
            .offset(y: animateCards ? 0 : 20)
            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.4), value: animateCards)
        }
        .padding(AppTheme.spacing40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
        .modernCardStyle()
    }

    private func modernCardView(with images: [String], overlays: [[String]]) -> some View {
        Group {
            if !images.isEmpty {
                ZStack {
                    TabView(selection: $currentIndex) {
                        ForEach(0..<min(images.count, overlays.count), id: \.self) { idx in
                            ZStack {
                                modernBackgroundImage(for: images[safe: idx])
                                modernBottomOverlay(for: overlays[idx])
                            }
                            .tag(idx)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .overlay(
                        modernPageIndicator(for: min(images.count, overlays.count))
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 16),
                        alignment: .top
                    )
                    .overlay(modernTopRightIcons, alignment: .topTrailing)
                }
                // Removed aspect ratio constraint to allow vertical expansion
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .cornerRadius(AppTheme.radiusXLarge)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.6),
                                    Color.white.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
                .shadow(color: AppTheme.primaryColor.opacity(0.2), radius: 25, x: 0, y: 12)
                .shadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 6)
                .shadow(color: AppTheme.accentColor.opacity(0.1), radius: 35, x: 0, y: 18)
                .clipped()
                // Conditionally apply floating animation only when not accessed from search
                .modifier(ConditionalFloatingModifier(shouldAnimate: !shouldUseStableAnimations))
            } else {
                fallbackImageTabView(images: images)
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    // Removed aspect ratio constraint to allow vertical expansion
                    .cornerRadius(20)
                    .shadow(color: .black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .shadow(color: .black.opacity(0.05), radius: 3, x: 0, y: 2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
            }
        }
    }

    private func leaseCardView(with images: [String]) -> some View {
        var slides: [(AnyView, [String])] = []
        // Slide 0: Floorplan background and overlay.
        if let floorplan = images.first, !floorplan.isEmpty {
            slides.append((AnyView(backgroundImage(for: floorplan)), leaseOverlay(for: 0)))
        }
        // Slide 1: Map view if available.
        if user.location != nil {
            slides.append((AnyView(
                UserLocationMapView(
                    coordinate: CLLocationCoordinate2D(latitude: user.location!.latitude, longitude: user.location!.longitude)
                )
            ), leaseOverlay(for: 1)))
        }
        // For slides 2 to 9, use additional images.
        let additional = Array(images.dropFirst())
        for index in 2...9 {
            if let bg = additional[safe: index - 2], !bg.isEmpty {
                slides.append((AnyView(backgroundImage(for: bg)), leaseOverlay(for: index)))
            }
        }

        return ZStack {
            if !slides.isEmpty {
                TabView(selection: $currentIndex) {
                    ForEach(0..<slides.count, id: \.self) { idx in
                        ZStack {
                            slides[idx].0
                            let overlay = slides[idx].1
                            if !overlay.isEmpty {
                                bottomOverlay(for: overlay)
                            }
                        }
                        .tag(idx)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .overlay(
                    pageIndicator(for: slides.count)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.top, 12),
                    alignment: .top
                )
                .overlay(topRightIcons, alignment: .topTrailing)
            } else {
                EmptyView()
            }
        }
        // Removed aspect ratio constraint to allow vertical expansion
        .cornerRadius(15)
        .shadow(radius: 5)
        .padding()
    }

    // MARK: - Modern Helper Methods

    private func modernBackgroundImage(for imageUrl: String?) -> some View {
        Group {
            if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
                GeometryReader { geometry in
                    RobustAsyncImage(url: url, contentMode: .fill)
                        .scaledToFill()
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            } else {
                // Beautiful fallback with enhanced styling
                ZStack {
                    // Multi-layer gradient for depth
                    LinearGradient(
                        gradient: Gradient(colors: [
                            AppTheme.primaryColor.opacity(0.8),
                            AppTheme.accentColor.opacity(0.6),
                            AppTheme.primaryColor.opacity(0.4)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )

                    // Subtle pattern overlay
                    Rectangle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ]),
                                center: .topLeading,
                                startRadius: 0,
                                endRadius: 300
                            )
                        )

                    // Enhanced icon
                    VStack(spacing: 16) {
                        Image(systemName: "photo.artframe")
                            .font(.system(size: 80, weight: .ultraLight))
                            .foregroundColor(.white.opacity(0.8))

                        Text("No Photo")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .clipped()
    }

    private func modernBottomOverlay(for overlayTexts: [String]) -> some View {
        VStack {
            Spacer()

            if !overlayTexts.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    if !fullName.isEmpty {
                        Text(fullName)
                            .font(.system(size: 28, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.9), radius: 4, x: 0, y: 2)
                            .shadow(color: .black.opacity(0.6), radius: 8, x: 0, y: 4)
                            .lineLimit(1)
                            .minimumScaleFactor(0.7)
                    }

                    ForEach(overlayTexts, id: \.self) { text in
                        Text(text)
                            .font(.system(size: 16, weight: .semibold, design: .rounded))
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.9), radius: 3, x: 0, y: 1)
                            .shadow(color: .black.opacity(0.5), radius: 6, x: 0, y: 3)
                            .lineLimit(text.contains("Bio:") || text.hasPrefix("No bio") || (!text.contains(":") && text.count > 30) ? 4 : 2)
                            .multilineTextAlignment(.leading)
                            .fixedSize(horizontal: false, vertical: true)
                            .minimumScaleFactor(0.9)
                            .padding(.vertical, 1)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    LinearGradient(
                        gradient: Gradient(stops: [
                            .init(color: Color.clear, location: 0.0),
                            .init(color: Color.black.opacity(0.1), location: 0.3),
                            .init(color: Color.black.opacity(0.5), location: 0.7),
                            .init(color: Color.black.opacity(0.85), location: 1.0)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
            }
        }
    }

    private func modernPageIndicator(for count: Int) -> some View {
        HStack(spacing: AppTheme.spacing6) {
            ForEach(0..<count, id: \.self) { index in
                Capsule()
                    .fill(
                        index == currentIndex ?
                        LinearGradient(
                            colors: [Color.white, Color.white.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        ) :
                        LinearGradient(colors: [Color.white.opacity(0.4)], startPoint: .leading, endPoint: .trailing)
                    )
                    .frame(width: index == currentIndex ? 16 : 6, height: 6)
                    .scaleEffect(index == currentIndex ? 1.1 : 1.0)
                    .shadow(color: index == currentIndex ? Color.white.opacity(0.5) : .clear, radius: 3, x: 0, y: 0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.7), value: currentIndex)
            }
        }
        .padding(.horizontal, AppTheme.spacing12)
        .padding(.vertical, AppTheme.spacing6)
        .background(
            Capsule()
                .fill(.ultraThinMaterial)
                .overlay(
                    Capsule()
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
        )
    }

    private var modernTopRightIcons: some View {
        HStack(spacing: AppTheme.spacing8) {
            // Simplified verification check
            if user.isEmailVerified {
                Image(systemName: "checkmark.seal.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }

            Button(action: { showReportSheet = true }) {
                Image(systemName: "shield.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
                    .padding(8)
                    .background(Color.black.opacity(0.3))
                    .clipShape(Circle())
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            }
            .contextMenu {
                Button("Report User") { showReportSheet = true }
                Button("Block User", role: .destructive) { showBlockAlert = true }
            }
        }
        .padding(AppTheme.spacing8)
    }

    private func modernLeaseCardView(with images: [String]) -> some View {
        // Build slides with proper content and overlays
        var slides: [(AnyView, [String])] = []

        // Slide 0: First property image (usually floorplan) with overlay
        if let firstImage = images.first, !firstImage.isEmpty {
            slides.append((AnyView(modernBackgroundImage(for: firstImage)), leaseOverlay(for: 0)))
        }

        // Slide 1: Map view if location is available
        if user.location != nil {
            slides.append((AnyView(
                UserLocationMapView(
                    coordinate: CLLocationCoordinate2D(
                        latitude: user.location!.latitude,
                        longitude: user.location!.longitude
                    )
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .clipped()
            ), leaseOverlay(for: 1)))
        }

        // Slides 2+: Additional property images with overlays
        let additionalImages = Array(images.dropFirst())
        for index in 2..<(additionalImages.count + 2) {
            if let imageUrl = additionalImages[safe: index - 2], !imageUrl.isEmpty {
                slides.append((AnyView(modernBackgroundImage(for: imageUrl)), leaseOverlay(for: index)))
            }
        }

        return Group {
            if !slides.isEmpty {
                ZStack {
                    TabView(selection: $currentIndex) {
                        ForEach(0..<slides.count, id: \.self) { idx in
                            ZStack {
                                slides[idx].0
                                modernBottomOverlay(for: slides[idx].1)
                            }
                            .tag(idx)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .overlay(
                        modernPageIndicator(for: slides.count)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 12),
                        alignment: .top
                    )
                    .overlay(modernTopRightIcons, alignment: .topTrailing)
                }
                // Removed aspect ratio constraint to allow vertical expansion
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .cornerRadius(AppTheme.radiusXLarge)
                .overlay(
                    RoundedRectangle(cornerRadius: AppTheme.radiusXLarge)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: AppTheme.primaryColor.opacity(0.1), radius: 20, x: 0, y: 10)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                .clipped()
            } else {
                modernEmptyStateView()
            }
        }
    }

    // MARK: - Overlay & Utility Views
    private func bottomOverlay(for snippet: [String]) -> some View {
        ZStack(alignment: .bottomLeading) {
            // Enhanced gradient overlay
            LinearGradient(
                gradient: Gradient(stops: [
                    .init(color: Color.clear, location: 0.0),
                    .init(color: Color.black.opacity(0.3), location: 0.6),
                    .init(color: Color.black.opacity(0.8), location: 1.0)
                ]),
                startPoint: .center,
                endPoint: .bottom
            )
            .allowsHitTesting(false)

            VStack(alignment: .leading, spacing: 12) {
                if !fullName.isEmpty {
                    Text(fullName)
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.9), radius: 3, x: 0, y: 2)
                        .shadow(color: .black.opacity(0.5), radius: 8, x: 0, y: 4)
                        .lineLimit(1)
                        .minimumScaleFactor(0.8)
                }

                ForEach(snippet, id: \.self) { line in
                    Text(line)
                        .font(.system(size: 18, weight: .semibold, design: .rounded))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.9), radius: 2, x: 0, y: 1)
                        .shadow(color: .black.opacity(0.4), radius: 6, x: 0, y: 3)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)
                        .fixedSize(horizontal: false, vertical: true)
                        .padding(.vertical, 2)
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 50)
            .frame(maxWidth: .infinity, alignment: .leading)
        }
    }

    private func pageIndicator(for count: Int) -> some View {
        HStack(spacing: 6) {
            ForEach(0..<count, id: \.self) { index in
                Capsule()
                    .fill(index == currentIndex ? Color.white : Color.white.opacity(0.4))
                    .frame(width: index == currentIndex ? 20 : 8, height: 8)
                    .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                    .animation(.easeInOut(duration: 0.3), value: currentIndex)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            Capsule()
                .fill(Color.black.opacity(0.2))
                .blur(radius: 10)
        )
    }

    private var topRightIcons: some View {
        HStack(spacing: 12) {
            // Simplified verification check - only use isEmailVerified
            if user.isEmailVerified {
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.9))
                        .frame(width: 32, height: 32)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)

                    Image(systemName: "checkmark.seal.fill")
                        .foregroundColor(.green)
                        .font(.system(size: 18, weight: .semibold))
                }
            }

            Button(action: { showReportSheet = true }) {
                ZStack {
                    Circle()
                        .fill(Color.white.opacity(0.9))
                        .frame(width: 32, height: 32)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)

                    Image(systemName: "shield")
                        .foregroundColor(.gray)
                        .font(.system(size: 16, weight: .semibold))
                }
            }
            .contextMenu {
                Button("Report User") { showReportSheet = true }
                Button("Block User", role: .destructive) { showBlockAlert = true }
            }
        }
        .padding([.top, .trailing], 16)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
    }

    private func backgroundImage(for urlString: String?) -> some View {
        if let urlStr = urlString, let url = URL(string: urlStr) {
            return AnyView(
                AsyncImage(url: url) { phase in
                    switch phase {
                    case .empty:
                        return AnyView(ProgressView())
                    case .success(let image):
                        return AnyView(
                            GeometryReader { geo in
                                image
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: geo.size.width, height: geo.size.height)
                                    .clipped()
                            }
                        )
                    case .failure:
                        return AnyView(Color.gray)
                    @unknown default:
                        return AnyView(Color.gray)
                    }
                }
            )
        } else {
            return AnyView(Color.gray)
        }
    }

    private func fallbackImageTabView(images: [String]) -> some View {
        TabView {
            ForEach(0..<images.count, id: \.self) { index in
                if let url = URL(string: images[index]) {
                    AsyncImage(url: url) { phase in
                        switch phase {
                        case .empty:
                            ProgressView()
                        case .success(let image):
                            GeometryReader { geometry in
                                image
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: geometry.size.width, height: geometry.size.height)
                                    .clipped()
                            }
                        case .failure:
                            Color.gray
                        @unknown default:
                            Color.gray
                        }
                    }
                } else {
                    Color.gray
                }
            }
        }
    }

    private var blockActionSheet: ActionSheet {
        ActionSheet(
            title: Text("Block User"),
            message: Text("Are you sure you want to block this user? They will no longer appear in your matches."),
            buttons: [
                .destructive(Text("Block")) { blockUser() },
                .cancel()
            ]
        )
    }

    private func blockUser() {
        guard let userID = user.id else {
            errorMessage = "No user ID found to block."
            return
        }
        let blockVM = BlockUserViewModel()
        blockVM.blockUser(candidateID: userID) { result in
            switch result {
            case .success:
                ProfileViewModel.shared.removeBlockedUser(with: userID)
            case .failure(let error):
                errorMessage = error.localizedDescription
            }
        }
    }

    private func calculateAge(from dob: String?) -> Int? {
        guard let dob = dob, !dob.isEmpty else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        if let birthDate = formatter.date(from: dob) {
            let now = Date()
            let comps = Calendar.current.dateComponents([.year], from: birthDate, to: now)
            return comps.year
        }
        return nil
    }
}

// MARK: - Array Safe Subscript
fileprivate extension Array {
    subscript(safe index: Int) -> Element? {
        indices.contains(index) ? self[index] : nil
    }
}

struct ProfilePreviewView_Previews: PreviewProvider {
    static var previews: some View {
        let sampleUser = UserModel(
            email: "test@edu",
            isEmailVerified: true,
            aboutMe: "I love coding, coffee, and late-night debugging!",
            firstName: "Chase",
            lastName: "Vazquez",
            dateOfBirth: {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"
                return dateFormatter.date(from: "2004-09-09")
            }(),
            gender: nil,
            height: nil,
            gradeLevel: "Freshman",
            major: "Computer Science",
            collegeName: "CLA",
            housingStatus: PrimaryHousingPreference.lookingForLease.rawValue,
            dormType: "On-Campus",
            preferredDorm: nil,
            desiredLeaseHousingType: "Apartment",
            roommateCountNeeded: 2,
            roommateCountExisting: 1,
            propertyDetails: "Spacious apartment close to campus. Recently renovated, modern fixtures.",
            propertyAddress: "123 Main St, Springfield, USA",
            propertyImageUrls: [
                "https://picsum.photos/id/200/400/600",
                "https://picsum.photos/id/201/400/600",
                "https://picsum.photos/id/202/400/600",
                "https://picsum.photos/id/203/400/600",
                "https://picsum.photos/id/204/400/600",
                "https://picsum.photos/id/205/400/600",
                "https://picsum.photos/id/206/400/600",
                "https://picsum.photos/id/207/400/600"
            ],
            floorplanUrls: nil,
            documentUrls: nil,
            roomType: "Single",
            leaseStartDate: Date(),
            leaseDuration: "12 months",
            monthlyRentMin: 950,
            monthlyRentMax: 950,
            specialLeaseConditions: ["No pets", "No smoking"],
            amenities: ["Pool", "Gym", "Parking", "High-Speed Internet", "Furnished"],
            budgetMin: 500,
            budgetMax: 1000,
            cleanliness: 5,
            sleepSchedule: "Flexible",
            smoker: false,
            petFriendly: true,
            livingStyle: nil,
            socialLevel: nil,
            studyHabits: nil,
            interests: ["Photography", "Music", "Sports"],
            profileImageUrl: nil,
            profileImageUrls: [
                "https://picsum.photos/id/1025/400/600",
                "https://picsum.photos/id/1035/400/600",
                "https://picsum.photos/id/1037/400/600",
                "https://picsum.photos/id/1040/400/600",
                "https://picsum.photos/id/1041/400/600",
                "https://picsum.photos/id/1042/400/600",
                "https://picsum.photos/id/1043/400/600",
                "https://picsum.photos/id/1044/400/600",
                "https://picsum.photos/id/1045/400/600"
            ],
            location: nil,
            blockedUserIDs: nil,
            filterSettings: nil,
            pets: ["Dog", "Cat"],
            drinking: "Socially on weekends",
            smoking: "Non-smoker",
            cannabis: "Never",
            workout: "Often",
            dietaryPreferences: ["Vegetarian"],
            socialMedia: "Socially active",
            sleepingHabits: "Night owl",
            goingOutQuizAnswers: ["Dancing 💃"],
            weekendQuizAnswers: ["Cozy nights in 🏡"],
            phoneQuizAnswers: ["Replies quickly ⚡"]
        )
        return NavigationView {
            ProfilePreviewView(user: sampleUser, showPremiumActions: true)
        }
    }
}

// MARK: - ProfilePreviewView Extension for Premium Action Bar
extension ProfilePreviewView {

    // MARK: - Premium Action Bar

    private var premiumActionBar: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Main action buttons row - Tinder-like layout (excluding dislike)
            HStack(spacing: AppTheme.spacing20) {
                // Enhanced Revert Button with modern effects
                Button(action: revertAction) {
                    Image(systemName: "arrow.uturn.backward")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 50, height: 50)
                        .background(revertButtonBackground)
                        .shadow(color: AppTheme.primaryColor.opacity(0.4), radius: 12, x: 0, y: 6)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                }
                .disabled(hasLiked || hasSuperLiked)
                .scaleEffect((hasLiked || hasSuperLiked) ? 0.9 : 1.0)
                .modifier(ConditionalCardTransformModifier(shouldAnimate: !shouldUseStableAnimations, isPressed: false))
                .animation(shouldUseStableAnimations ? .easeInOut(duration: 0.2) : .spring(response: 0.4, dampingFraction: 0.6), value: hasLiked || hasSuperLiked)

                // Enhanced Super Like Button (Star) with modern effects
                Button(action: superLikeAction) {
                    Image(systemName: hasSuperLiked ? "star.fill" : "star.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 50, height: 50)
                        .background(superLikeButtonBackground)
                        .shadow(color: .yellow.opacity(0.6), radius: 12, x: 0, y: 6)
                        .shadow(color: .black.opacity(0.2), radius: 6, x: 0, y: 3)
                }
                .disabled(isSuperLiking || hasSuperLiked || hasLiked)
                .scaleEffect(isSuperLiking ? 0.95 : 1.0)
                .modifier(ConditionalCardTransformModifier(shouldAnimate: !shouldUseStableAnimations, isPressed: isSuperLiking))
                .modifier(ConditionalGlowModifier(shouldAnimate: !shouldUseStableAnimations, color: hasSuperLiked ? .yellow : .clear))
                .animation(shouldUseStableAnimations ? .easeInOut(duration: 0.2) : .spring(response: 0.4, dampingFraction: 0.6), value: isSuperLiking)

                // Enhanced Like Button (Heart) with modern effects
                Button(action: likeAction) {
                    Image(systemName: hasLiked ? "heart.fill" : "heart.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                        .frame(width: 60, height: 60)
                        .background(likeButtonBackground)
                        .shadow(color: .green.opacity(0.4), radius: 15, x: 0, y: 8)
                        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                }
                .disabled(isLiking || hasLiked || hasSuperLiked)
                .scaleEffect(isLiking ? 0.95 : 1.0)
                .modifier(ConditionalCardTransformModifier(shouldAnimate: !shouldUseStableAnimations, isPressed: isLiking))
                .modifier(ConditionalGlowModifier(shouldAnimate: !shouldUseStableAnimations, color: hasLiked ? .green : .clear))
                .animation(shouldUseStableAnimations ? .easeInOut(duration: 0.2) : .spring(response: 0.4, dampingFraction: 0.6), value: isLiking)
            }
        }
        .padding(.bottom, AppTheme.spacing40) // Optimized bottom padding for action bar
        .padding(.horizontal, AppTheme.spacing16) // Add horizontal padding for better layout
    }

    // MARK: - Action Bar Button Backgrounds

    private var revertButtonBackground: some View {
        ZStack {
            Circle()
                .fill(
                    (hasLiked || hasSuperLiked) ?
                    LinearGradient(colors: [AppTheme.textTertiary.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    LinearGradient(colors: [AppTheme.primaryColor, AppTheme.primaryColor.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

            // Subtle inner glow
            if !(hasLiked || hasSuperLiked) {
                Circle()
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    .blur(radius: 1)
            }
        }
    }

    private var superLikeButtonBackground: some View {
        ZStack {
            Circle()
                .fill(
                    hasSuperLiked ?
                    LinearGradient(colors: [.yellow.opacity(0.8), .orange.opacity(0.6)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    (isSuperLiking || hasLiked) ?
                    LinearGradient(colors: [Color.gray.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    LinearGradient(colors: [.yellow, .orange, .yellow.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

            // Subtle inner glow
            Circle()
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                .blur(radius: 1)
        }
    }

    private var likeButtonBackground: some View {
        ZStack {
            Circle()
                .fill(
                    hasLiked ?
                    LinearGradient(colors: [.green.opacity(0.8), .mint.opacity(0.6)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    (isLiking || hasSuperLiked) ?
                    LinearGradient(colors: [Color.gray.opacity(0.3)], startPoint: .topLeading, endPoint: .bottomTrailing) :
                    LinearGradient(colors: [.green, .mint, .green.opacity(0.8)], startPoint: .topLeading, endPoint: .bottomTrailing)
                )

            // Subtle inner glow
            Circle()
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                .blur(radius: 1)
        }
    }

    // MARK: - Action Bar Actions

    private func revertAction() {
        guard !hasLiked && !hasSuperLiked else { return }

        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            // Reset any previous actions (placeholder for future undo functionality)
            print("🔄 ProfilePreviewView: Revert action triggered")
        }

        // Haptic feedback
        HapticFeedbackManager.shared.generateNotification(.warning)
    }

    private func superLikeAction() {
        guard !isSuperLiking && !hasSuperLiked && !hasLiked else { return }
        guard let userID = user.id else { return }

        let isPremium = ProfileViewModel.shared.userProfile?.isPremium == true

        // Check if user is premium first
        guard isPremium else {
            showSuperLikePaywall = true
            if let currentUserID = ProfileViewModel.shared.userProfile?.id {
                superLikeManager.trackSuperLikeAttemptWithoutPremium(userID: currentUserID)
            }
            return
        }

        // Check super like limit for premium users
        guard superLikeManager.canSuperLike(isPremium: isPremium) else {
            showSuperLikePaywall = true
            if let currentUserID = ProfileViewModel.shared.userProfile?.id {
                superLikeManager.trackSuperLikeLimitReached(userID: currentUserID)
            }
            return
        }

        isSuperLiking = true

        // Record super like through MatchingViewModel
        matchingViewModel.superLike(on: user)

        // Record super like for tracking
        if let currentUserID = ProfileViewModel.shared.userProfile?.id {
            Task {
                await superLikeManager.recordSuperLike(userID: currentUserID, isPremium: isPremium)
            }
        }

        let animation: Animation = shouldUseStableAnimations ?
            .easeInOut(duration: 0.3) :
            .spring(response: 0.5, dampingFraction: 0.7)

        withAnimation(animation) {
            hasSuperLiked = true
            isSuperLiking = false
        }

        // Enhanced haptic feedback for super like
        HapticFeedbackManager.shared.generateNotification(.success)

        print("⭐ ProfilePreviewView: Super liked user \(user.firstName ?? "Unknown")")

        // Check for mutual match after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            checkForMutualMatch()
        }
    }

    private func likeAction() {
        guard !isLiking && !hasLiked && !hasSuperLiked else { return }
        guard let userID = user.id else { return }

        isLiking = true

        // Record like through MatchingViewModel
        matchingViewModel.swipeRight(on: user)

        let animation: Animation = shouldUseStableAnimations ?
            .easeInOut(duration: 0.3) :
            .spring(response: 0.5, dampingFraction: 0.7)

        withAnimation(animation) {
            hasLiked = true
            isLiking = false
        }

        // Success haptic feedback
        HapticFeedbackManager.shared.generateNotification(.success)

        print("💚 ProfilePreviewView: Liked user \(user.firstName ?? "Unknown")")

        // Check for mutual match after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            checkForMutualMatch()
        }
    }

    private func checkForMutualMatch() {
        // Check if this resulted in a mutual match
        if let userID = user.id, matchingViewModel.matchedUserIDs.contains(userID) {
            let animation: Animation = shouldUseStableAnimations ?
                .easeInOut(duration: 0.4) :
                .spring(response: 0.6, dampingFraction: 0.8)

            withAnimation(animation) {
                showMatchCelebration = true
            }

            // Enhanced haptic for match
            HapticFeedbackManager.shared.generateNotification(.success)

            print("🎉 ProfilePreviewView: Mutual match detected with \(user.firstName ?? "Unknown")!")
        }
    }
}

// MARK: - Conditional Animation Modifiers
struct ConditionalFloatingModifier: ViewModifier {
    let shouldAnimate: Bool

    func body(content: Content) -> some View {
        if shouldAnimate {
            content.floatingAnimation(amplitude: 5, duration: 3.0)
        } else {
            content
        }
    }
}

struct ConditionalCardTransformModifier: ViewModifier {
    let shouldAnimate: Bool
    let isPressed: Bool

    func body(content: Content) -> some View {
        if shouldAnimate {
            content.cardTransform3D(isPressed: isPressed)
        } else {
            content
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
    }
}

struct ConditionalGlowModifier: ViewModifier {
    let shouldAnimate: Bool
    let color: Color

    func body(content: Content) -> some View {
        if shouldAnimate {
            content.pulsingNeonGlow(color: color)
        } else {
            content
        }
    }
}