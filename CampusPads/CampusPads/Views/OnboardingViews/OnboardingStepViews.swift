import SwiftUI
import FirebaseAuth
import FirebaseFirestore

// MARK: - Email Verification Step View
struct EmailVerificationStepView: View {
    @State private var isCheckingVerification = false
    @State private var verificationStatus: String = "Checking verification status..."
    @State private var showResendButton = false

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            VStack(spacing: AppTheme.spacing32) {
                Spacer()

                // Sexy email verification icon with enhanced glow
                VStack(spacing: AppTheme.spacing24) {
                    ZStack {
                        // Multiple glow layers for extra sexy effect
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 140, height: 140)
                            .blur(radius: 30)
                            .opacity(0.8)

                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 100, height: 100)
                            .blur(radius: 15)
                            .opacity(0.6)

                        Image(systemName: Auth.auth().currentUser?.isEmailVerified == true ? "checkmark.circle.fill" : "envelope.fill")
                            .font(.system(size: 60, weight: .bold))
                            .foregroundColor(.white)
                            .shadow(color: AppTheme.primaryColor, radius: 10)
                    }

                    VStack(spacing: AppTheme.spacing16) {
                        Text("Verify Your Email")
                            .font(.custom("AvenirNext-Bold", size: 36))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .shadow(color: .black.opacity(0.3), radius: 2)

                        VStack(spacing: AppTheme.spacing8) {
                            Text("We've sent a verification email to:")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)

                            Text(Auth.auth().currentUser?.email ?? "your email")
                                .font(.custom("AvenirNext-Bold", size: 18))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, AppTheme.spacing20)
                                .padding(.vertical, AppTheme.spacing8)
                                .background(.ultraThinMaterial)
                                .cornerRadius(12)
                        }
                    }
                }

                // Status and actions with sexy styling
                VStack(spacing: AppTheme.spacing20) {
                    Text(verificationStatus)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(Auth.auth().currentUser?.isEmailVerified == true ? .green : .white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(AppTheme.spacing16)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)

                    if Auth.auth().currentUser?.isEmailVerified != true {
                        VStack(spacing: AppTheme.spacing16) {
                            Text("Please check your email and click the verification link, then tap 'Check Verification' below.")
                                .font(.custom("AvenirNext-Medium", size: 14))
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, AppTheme.spacing20)

                            // Sexy check verification button
                            Button("Check Verification") {
                                checkEmailVerification()
                            }
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(AppTheme.spacing16)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(.ultraThinMaterial)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                            .disabled(isCheckingVerification)
                            .opacity(isCheckingVerification ? 0.6 : 1.0)

                            if showResendButton {
                                Button("Resend Verification Email") {
                                    resendVerificationEmail()
                                }
                                .font(.custom("AvenirNext-Medium", size: 14))
                                .foregroundColor(.white.opacity(0.8))
                                .padding(.vertical, AppTheme.spacing12)
                            }
                        }
                    }
                }
                .padding(.horizontal, AppTheme.spacing20)

                Spacer()
            }
        }
        .onAppear {
            checkEmailVerification()
            // Show resend button after 30 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                showResendButton = true
            }
        }
    }

    private func checkEmailVerification() {
        isCheckingVerification = true
        verificationStatus = "Checking verification status..."

        Auth.auth().currentUser?.reload { error in
            DispatchQueue.main.async {
                isCheckingVerification = false

                if let error = error {
                    verificationStatus = "Error checking verification: \(error.localizedDescription)"
                } else if Auth.auth().currentUser?.isEmailVerified == true {
                    verificationStatus = "✅ Email verified! You can continue."
                    HapticFeedbackManager.shared.generateNotification(.success)

                    // CRITICAL: Immediately update Firebase with verified status
                    self.updateFirebaseEmailVerificationStatus()
                } else {
                    verificationStatus = "Email not yet verified. Please check your inbox."
                }
            }
        }
    }

    private func updateFirebaseEmailVerificationStatus() {
        guard let currentUser = Auth.auth().currentUser,
              currentUser.isEmailVerified else { return }

        print("🔥 EmailVerification: Updating Firebase with verified status")

        let db = Firestore.firestore()
        db.collection("users").document(currentUser.uid).updateData([
            "isEmailVerified": true,
            "emailVerifiedAt": FieldValue.serverTimestamp()
        ]) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ EmailVerification: Failed to update Firebase: \(error.localizedDescription)")
                } else {
                    print("✅ EmailVerification: Successfully updated Firebase verification status")
                    // Note: ProfileViewModel will be updated when user completes onboarding
                }
            }
        }
    }

    private func resendVerificationEmail() {
        Auth.auth().currentUser?.sendEmailVerification { error in
            DispatchQueue.main.async {
                if let error = error {
                    verificationStatus = "Error sending email: \(error.localizedDescription)"
                } else {
                    verificationStatus = "Verification email sent! Please check your inbox."
                    HapticFeedbackManager.shared.generateNotification(.success)
                }
            }
        }
    }
}

// MARK: - Welcome Step View
struct WelcomeStepView: View {
    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            VStack(spacing: AppTheme.spacing32) {
                Spacer()

                // Sexy app icon with enhanced glow
                VStack(spacing: AppTheme.spacing24) {
                    ZStack {
                        // Multiple glow layers for extra sexy effect
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 160, height: 160)
                            .blur(radius: 40)
                            .opacity(0.8)

                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 120, height: 120)
                            .blur(radius: 20)
                            .opacity(0.6)

                        AppIcon.splash()
                            .frame(width: 100, height: 100)
                            .shadow(color: AppTheme.primaryColor, radius: 15)
                    }

                    VStack(spacing: AppTheme.spacing16) {
                        Text("Welcome to CampusPads")
                            .font(.custom("AvenirNext-Bold", size: 36))
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                            .shadow(color: .black.opacity(0.3), radius: 2)

                        Text("Let's create your perfect profile to find your ideal roommate")
                            .font(.custom("AvenirNext-Medium", size: 18))
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, AppTheme.spacing20)
                            .lineSpacing(4)
                    }
                }

                // Sexy feature highlights
                VStack(spacing: AppTheme.spacing16) {
                    SexyFeatureHighlight(
                        icon: "person.2.fill",
                        title: "Smart Matching",
                        description: "AI-powered compatibility matching"
                    )

                    SexyFeatureHighlight(
                        icon: "heart.fill",
                        title: "Swipe to Connect",
                        description: "Tinder-style interface you already know"
                    )

                    SexyFeatureHighlight(
                        icon: "message.fill",
                        title: "Instant Chat",
                        description: "Start conversations with your matches"
                    )
                }
                .padding(.horizontal, AppTheme.spacing20)

                Spacer()
            }
        }
    }
}

// MARK: - Personal Info Step View
struct PersonalInfoStepView: View {
    @Binding var firstName: String
    @Binding var lastName: String
    @Binding var birthDate: Date
    @Binding var gender: String

    private let genderOptions = ["Male", "Female", "Non-binary", "Other"]

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            ScrollView(showsIndicators: false) {
                VStack(spacing: AppTheme.spacing32) {
                    // Sexy modern header with glow effect
                    VStack(spacing: AppTheme.spacing16) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.sexyGradient)
                                .frame(width: 80, height: 80)
                                .blur(radius: 20)
                                .opacity(0.6)

                            Image(systemName: "person.fill")
                                .font(.system(size: 40, weight: .bold))
                                .foregroundColor(.white)
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            Text("Tell us about yourself")
                                .font(.custom("AvenirNext-Bold", size: 32))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)

                            Text("This information helps us create your profile")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top, AppTheme.spacing20)

                    VStack(spacing: AppTheme.spacing24) {
                        // Name fields with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "textformat.abc")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Your Name")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            HStack(spacing: AppTheme.spacing12) {
                                SexyTextField(
                                    title: "First Name",
                                    text: $firstName,
                                    placeholder: "John"
                                )

                                SexyTextField(
                                    title: "Last Name",
                                    text: $lastName,
                                    placeholder: "Doe"
                                )
                            }
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // Birth date with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "calendar.circle.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Date of Birth")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            DatePicker("", selection: $birthDate, displayedComponents: .date)
                                .datePickerStyle(CompactDatePickerStyle())
                                .padding(AppTheme.spacing16)
                                .background(.ultraThinMaterial)
                                .cornerRadius(16)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // Gender selection with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "person.2.circle.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Gender")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            VStack(spacing: AppTheme.spacing12) {
                                ForEach(genderOptions, id: \.self) { option in
                                    SexyGenderCard(
                                        title: option,
                                        isSelected: gender == option,
                                        icon: iconForGender(option)
                                    ) {
                                        gender = option
                                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                                    }
                                }
                            }
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing32)
                }
            }
        }
    }

    private func iconForGender(_ gender: String) -> String {
        switch gender {
        case "Male": return "person.fill"
        case "Female": return "person.fill"
        case "Non-binary": return "person.2.fill"
        case "Other": return "person.3.fill"
        default: return "person.fill"
        }
    }
}

// MARK: - Physical Info Step View
struct PhysicalInfoStepView: View {
    @Binding var height: String
    @Binding var aboutMe: String

    private let heightOptions = [
        "4'10\"", "4'11\"", "5'0\"", "5'1\"", "5'2\"", "5'3\"", "5'4\"", "5'5\"",
        "5'6\"", "5'7\"", "5'8\"", "5'9\"", "5'10\"", "5'11\"", "6'0\"", "6'1\"",
        "6'2\"", "6'3\"", "6'4\"", "6'5\"", "6'6\"+"
    ]

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            ScrollView(showsIndicators: false) {
                VStack(spacing: AppTheme.spacing32) {
                    headerSection
                    contentSection
                }
            }
        }
    }

    private var headerSection: some View {
        VStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.sexyGradient)
                    .frame(width: 80, height: 80)
                    .blur(radius: 20)
                    .opacity(0.6)

                Image(systemName: "ruler.fill")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.white)
            }

            VStack(spacing: AppTheme.spacing8) {
                Text("Physical Details")
                    .font(.custom("AvenirNext-Bold", size: 32))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)

                Text("Help others get to know you better")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.top, AppTheme.spacing20)
    }

    private var contentSection: some View {
        VStack(spacing: AppTheme.spacing24) {
            heightSelectionSection
            aboutMeSection
        }
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.bottom, AppTheme.spacing32)
    }

    private var heightSelectionSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "ruler.circle.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)

                Text("Height")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()

                if !height.isEmpty {
                    Text(height)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white.opacity(0.8))
                }
            }

            heightScrollView
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var heightScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppTheme.spacing8) {
                ForEach(heightOptions, id: \.self) { option in
                    heightOptionButton(option)
                }
            }
            .padding(.horizontal, AppTheme.spacing4)
        }
    }

    private func heightOptionButton(_ option: String) -> some View {
        Button(action: {
            height = option
            HapticFeedbackManager.shared.generateImpact(style: .medium)
        }) {
            let isSelected = height == option
            let backgroundFill = isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.6))
            let strokeColor = isSelected ? Color.clear : Color.white.opacity(0.3)
            let textColor = isSelected ? Color.white : Color.white.opacity(0.7)
            let scale = isSelected ? 1.05 : 1.0

            Text(option)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(textColor)
                .padding(.horizontal, AppTheme.spacing16)
                .padding(.vertical, AppTheme.spacing8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(backgroundFill)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(strokeColor, lineWidth: 1)
                        )
                )
                .scaleEffect(scale)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: height == option)
    }

    private var aboutMeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "text.bubble.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)

                Text("About Me")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()

                Text("\(aboutMe.count)/500")
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(.white.opacity(0.6))
            }

            Text("Tell potential roommates about yourself, your interests, and what you're looking for")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.8))

            aboutMeEditor
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var aboutMeEditor: some View {
        ZStack(alignment: .topLeading) {
            // Custom background to override TextEditor's default
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
                .frame(minHeight: 120)

            TextEditor(text: $aboutMe)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .padding(AppTheme.spacing16)
                .frame(minHeight: 120)
                .background(Color.clear) // Make TextEditor background transparent
                .scrollContentBackground(.hidden) // Hide default background
                .cornerRadius(16)

            if aboutMe.isEmpty {
                Text("I'm a friendly student looking for a compatible roommate. I enjoy studying, hanging out with friends, and exploring new places...")
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white.opacity(0.5))
                    .padding(.leading, AppTheme.spacing20)
                    .padding(.top, AppTheme.spacing24)
                    .allowsHitTesting(false)
                    .multilineTextAlignment(.leading)
            }
        }
    }
}

// MARK: - Feature Highlight Component
struct FeatureHighlight: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(AppTheme.primaryGradient)
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(title)
                    .font(AppTheme.subtitleFont)
                    .foregroundColor(AppTheme.textPrimary)

                Text(description)
                    .font(AppTheme.caption)
                    .foregroundColor(AppTheme.textSecondary)
            }

            Spacer()
        }
        .padding(.horizontal, AppTheme.spacing20)
    }
}

// MARK: - Onboarding Header Component
struct OnboardingHeader: View {
    let title: String
    let subtitle: String

    var body: some View {
        VStack(spacing: AppTheme.spacing12) {
            Text(title)
                .font(.custom("AvenirNext-Bold", size: 28))
                .foregroundStyle(AppTheme.sexyGradient)
                .multilineTextAlignment(.center)

            Text(subtitle)
                .font(AppTheme.body)
                .foregroundColor(AppTheme.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppTheme.spacing16)
        }
    }
}

// MARK: - Academic Info Step View
struct AcademicInfoStepView: View {
    @Binding var gradeLevel: String
    @Binding var major: String
    @Binding var collegeName: String

    private let gradeLevels = ["Freshman", "Sophomore", "Junior", "Senior", "Graduate Student"]

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            ScrollView(showsIndicators: false) {
                VStack(spacing: AppTheme.spacing32) {
                    // Sexy modern header with glow effect
                    VStack(spacing: AppTheme.spacing16) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.sexyGradient)
                                .frame(width: 80, height: 80)
                                .blur(radius: 20)
                                .opacity(0.6)

                            Image(systemName: "graduationcap.fill")
                                .font(.system(size: 40, weight: .bold))
                                .foregroundColor(.white)
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            Text("Academic Information")
                                .font(.custom("AvenirNext-Bold", size: 32))
                                .foregroundColor(.white)
                                .multilineTextAlignment(.center)

                            Text("Help us match you with students at your level")
                                .font(.custom("AvenirNext-Medium", size: 16))
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top, AppTheme.spacing20)

                    VStack(spacing: AppTheme.spacing24) {
                        // Grade level with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "chart.bar.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Grade Level")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            VStack(spacing: AppTheme.spacing12) {
                                ForEach(gradeLevels, id: \.self) { level in
                                    SexyGradeLevelCard(
                                        title: level,
                                        isSelected: gradeLevel == level,
                                        icon: iconForGradeLevel(level)
                                    ) {
                                        gradeLevel = level
                                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                                    }
                                }
                            }
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // Major with dropdown selection (matching MyProfileView)
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "book.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("Major")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            MajorDropdownSelection(
                                selectedMajor: $major,
                                placeholder: "Select Major",
                                label: "Field of Study"
                            )
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        // College name with sexy styling
                        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
                            HStack {
                                Image(systemName: "building.columns.fill")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(.white)

                                Text("College/University")
                                    .font(.custom("AvenirNext-Bold", size: 20))
                                    .foregroundColor(.white)

                                Spacer()
                            }

                            SexyCollegeSelector(
                                selectedCollege: $collegeName
                            )
                        }
                        .padding(AppTheme.spacing20)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing32)
                }
            }
        }
    }

    private func iconForGradeLevel(_ level: String) -> String {
        switch level {
        case "Freshman": return "1.circle.fill"
        case "Sophomore": return "2.circle.fill"
        case "Junior": return "3.circle.fill"
        case "Senior": return "4.circle.fill"
        case "Graduate Student": return "graduationcap.fill"
        default: return "person.fill"
        }
    }
}

// MARK: - Enhanced Housing Preferences Step View
struct HousingPreferencesStepView: View {
    @Binding var primaryHousingPreference: PrimaryHousingPreference?
    @Binding var secondaryHousingType: String
    @Binding var roommateCountNeeded: Int
    @Binding var roommateCountExisting: Int
    @Binding var propertyDetails: String
    @Binding var propertyAddress: String
    @Binding var propertyImageUrls: [String]
    @Binding var roomType: String
    @Binding var leaseStartDate: Date
    @Binding var leaseDurationText: String
    @Binding var rentMin: Double
    @Binding var rentMax: Double
    @Binding var selectedSpecialLeaseConditions: [String]
    @Binding var selectedAmenities: [String]
    @Binding var budgetMin: Double
    @Binding var budgetMax: Double

    // Property image management
    @State private var selectedPropertyImages: [UIImage] = []
    @State private var showPropertyImagePicker = false

    // Lease duration slider state
    @State private var leaseDurationMonths: Double = 12

    // Property amenities options (matching MyProfileView)
    private let propertyAmenitiesOptions = [
        "In-Unit Laundry", "On-Site Laundry", "Air Conditioning", "Heating",
        "Furnished", "Unfurnished", "High-Speed Internet", "Utilities Included",
        "Pet Friendly", "Parking Available", "Garage Parking", "Balcony / Patio",
        "Private Bathroom", "Shared Bathroom", "Gym / Fitness Center", "Common Area / Lounge",
        "Pool Access", "Rooftop Access", "Bike Storage", "Dishwasher", "Microwave",
        "Elevator Access", "Wheelchair Accessible", "24/7 Security", "Gated Community",
        "Study Rooms", "Game Room", "Smoke-Free", "Quiet Hours Enforced"
    ]

    // Special lease conditions options (matching MyProfileView)
    private let specialLeaseConditionsOptions: [String] = [
        "Start date negotiable", "Early move-in available", "Late move-out allowed",
        "Rent negotiable", "First month free", "Utilities included", "Partial months prorated",
        "Furnished room", "Unfurnished but furniture available for purchase", "Room includes mattress/desk/chair",
        "Must be approved by landlord", "Temporary sublease only", "Must sign roommate agreement"
    ]

    var body: some View {
        ZStack {
            // Sexy gradient background
            AppTheme.sexyGradient
                .ignoresSafeArea()

            ScrollView(showsIndicators: false) {
                VStack(spacing: AppTheme.spacing32) {
                    // Sexy modern header with glow effect
                    VStack(spacing: AppTheme.spacing16) {
                        ZStack {
                            Circle()
                                .fill(AppTheme.sexyGradient)
                                .frame(width: 80, height: 80)
                                .blur(radius: 20)
                                .opacity(0.6)

                            Image(systemName: "house.fill")
                                .font(.system(size: 40, weight: .bold))
                                .foregroundStyle(AppTheme.sexyGradient)
                        }

                        VStack(spacing: AppTheme.spacing8) {
                            Text("Housing Preferences")
                                .font(.custom("AvenirNext-Bold", size: 32))
                                .foregroundStyle(AppTheme.sexyGradient)
                                .multilineTextAlignment(.center)

                            Text("Tell us about your ideal living situation")
                                .font(AppTheme.body)
                                .foregroundColor(AppTheme.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(.top, AppTheme.spacing20)

                    VStack(spacing: AppTheme.spacing24) {
                        // Primary housing preference with sexy styling
                        primaryHousingSection

                        // Dynamic content based on selection
                        dynamicContent
                    }
                    .padding(.horizontal, AppTheme.spacing20)
                    .padding(.bottom, AppTheme.spacing32)
                }
            }
        }
        .sheet(isPresented: $showPropertyImagePicker) {
            OnboardingImagePicker(
                selectedImages: $selectedPropertyImages,
                sourceType: .photoLibrary
            )
        }
    }

    private var primaryHousingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing20) {
            // Header with improved explanation
            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                HStack {
                    Image(systemName: "house.circle.fill")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text("Housing Situation")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)

                    Spacer()
                }

                Text("Choose the option that best describes your current housing situation:")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }

            // Enhanced option cards with explanations
            VStack(spacing: AppTheme.spacing16) {
                ForEach(PrimaryHousingPreference.allCases) { pref in
                    EnhancedHousingOptionCard(
                        preference: pref,
                        isSelected: primaryHousingPreference == pref,
                        action: {
                            primaryHousingPreference = pref
                            HapticFeedbackManager.shared.generateImpact(style: .medium)
                            // Reset secondary fields when primary changes
                            secondaryHousingType = ""
                        }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.sexyGradient, lineWidth: 1)
                        .opacity(0.5)
                )
        )
    }

    private func iconForPreference(_ pref: PrimaryHousingPreference) -> String {
        switch pref {
        case .lookingForLease: return "key.fill"
        case .lookingForRoommate: return "person.2.fill"
        case .lookingToFindTogether: return "heart.fill"
        }
    }

    @ViewBuilder
    private var dynamicContent: some View {
        if let pref = primaryHousingPreference {
            VStack(spacing: AppTheme.spacing20) {
                switch pref {
                case .lookingForLease:
                    lookingForLeaseContent
                case .lookingForRoommate:
                    lookingForRoommateContent
                case .lookingToFindTogether:
                    findTogetherContent
                }
            }
        }
    }

    private var lookingForLeaseContent: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Room type section
            roomTypeSection

            // Budget section
            budgetSection

            // Amenities section
            amenitiesSection
        }
    }

    private var lookingForRoommateContent: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Property details section
            propertyDetailsSection

            // Room type section
            roomTypeSection

            // Lease pricing section
            leasePricingSection

            // Amenities section
            amenitiesSection
        }
    }

    private var findTogetherContent: some View {
        VStack(spacing: AppTheme.spacing20) {
            // Budget section
            budgetSection
        }
    }

    // MARK: - Section Components (matching MyProfileView)

    private var roomTypeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "bed.double.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Room Type")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()
            }

            VStack(spacing: AppTheme.spacing12) {
                let roomTypes = [
                    ("Private Room", "person.fill"),
                    ("Shared Room", "person.2.fill"),
                    ("Studio", "house.fill")
                ]

                ForEach(roomTypes, id: \.0) { (type, icon) in
                    SexyOptionCard(
                        title: type,
                        isSelected: roomType == type,
                        icon: icon
                    ) {
                        roomType = type
                        HapticFeedbackManager.shared.generateImpact(style: .medium)
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.sexyGradient, lineWidth: 1)
                        .opacity(0.5)
                )
        )
    }

    private var budgetSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "dollarsign.circle.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Budget Range")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()
            }

            VStack(spacing: AppTheme.spacing16) {
                // Budget display
                VStack(spacing: AppTheme.spacing8) {
                    Text("$\(Int(budgetMin)) – $\(Int(budgetMax))")
                        .font(.custom("AvenirNext-Bold", size: 28))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text("Monthly Budget Range")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.8))
                }
                .padding(AppTheme.spacing16)
                .background(.ultraThinMaterial)
                .cornerRadius(16)

                // Sliders
                VStack(spacing: AppTheme.spacing12) {
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        HStack {
                            Text("Minimum: $\(Int(budgetMin))")
                                .font(.custom("AvenirNext-Medium", size: 14))
                                .foregroundColor(.white)
                            Spacer()
                        }

                        Slider(value: $budgetMin, in: 0...5000, step: 50)
                            .accentColor(AppTheme.primaryColor)
                            .onChange(of: budgetMin) { _ in
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                    }

                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        HStack {
                            Text("Maximum: $\(Int(budgetMax))")
                                .font(.custom("AvenirNext-Medium", size: 14))
                                .foregroundColor(.white)
                            Spacer()
                        }

                        Slider(value: $budgetMax, in: 0...5000, step: 50)
                            .accentColor(AppTheme.primaryColor)
                            .onChange(of: budgetMax) { _ in
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                    }
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.sexyGradient, lineWidth: 1)
                        .opacity(0.5)
                )
        )
    }

    private var amenitiesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            HStack {
                Image(systemName: "star.circle.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Amenities")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()

                Text("\(selectedAmenities.count) selected")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.7))
            }

            OnboardingMultiSelectChipView(
                options: propertyAmenitiesOptions,
                selectedItems: $selectedAmenities,
                onSelectionChanged: {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                }
            )
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.sexyGradient, lineWidth: 1)
                        .opacity(0.5)
                )
        )
    }

    private var propertyDetailsSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing20) {
            HStack {
                Image(systemName: "building.2.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)

                Text("Property Details")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Spacer()
            }

            VStack(spacing: AppTheme.spacing16) {
                // Property Address
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Image(systemName: "location.fill")
                            .foregroundStyle(AppTheme.sexyGradient)
                        Text("Property Address")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                    }

                    TextField("Enter property address", text: $propertyAddress)
                        .font(AppTheme.body)
                        .padding(AppTheme.spacing16)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppTheme.sexyGradient, lineWidth: 1)
                                .opacity(0.5)
                        )
                }

                // Property Description
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Image(systemName: "text.alignleft")
                            .foregroundStyle(AppTheme.sexyGradient)
                        Text("Property Description")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                    }

                    TextField("Describe your property...", text: $propertyDetails, axis: .vertical)
                        .font(AppTheme.body)
                        .padding(AppTheme.spacing16)
                        .background(.ultraThinMaterial)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppTheme.sexyGradient, lineWidth: 1)
                                .opacity(0.5)
                        )
                        .lineLimit(3...6)
                }

                // Property Images Grid
                VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                    HStack {
                        Image(systemName: "photo.stack.fill")
                            .foregroundStyle(AppTheme.sexyGradient)
                        Text("Property Images")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                        Spacer()
                        Text("\(selectedPropertyImages.count)/9")
                            .font(.caption)
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    SexyPropertyImageGrid(
                        selectedImages: $selectedPropertyImages,
                        onAddImage: {
                            showPropertyImagePicker = true
                        }
                    )
                }
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.sexyGradient, lineWidth: 1)
                        .opacity(0.5)
                )
        )
    }

    private var leasePricingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            Text("Lease & Pricing Details")
                .font(AppTheme.subtitleFont)
                .foregroundColor(AppTheme.textPrimary)

            VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                DatePicker("Lease Start Date", selection: $leaseStartDate, displayedComponents: .date)
                    .datePickerStyle(.compact)
                    .font(AppTheme.body)

                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("Lease Duration: \(Int(leaseDurationMonths)) months")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textPrimary)

                    Slider(value: $leaseDurationMonths, in: 1...12, step: 1)
                        .onChange(of: leaseDurationMonths) { _ in
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            // Update the text field to match slider
                            leaseDurationText = "\(Int(leaseDurationMonths)) months"
                        }
                        .accentColor(AppTheme.primaryColor)

                    HStack {
                        Text("1 month")
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)

                        Spacer()

                        Text("12 months")
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)
                    }
                }

                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("Monthly Rent: \(Int(rentMin))–\(Int(rentMax)) USD")
                        .font(AppTheme.body)
                        .foregroundColor(AppTheme.textPrimary)

                    HStack {
                        Text("Min: \(Int(rentMin))")
                            .font(AppTheme.caption)
                        Slider(value: $rentMin, in: 0...5000, step: 50)
                            .onChange(of: rentMin) { _ in
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                    }

                    HStack {
                        Text("Max: \(Int(rentMax))")
                            .font(AppTheme.caption)
                        Slider(value: $rentMax, in: 0...5000, step: 50)
                            .onChange(of: rentMax) { _ in
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                            }
                    }
                }

                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("Special Lease Conditions")
                        .font(AppTheme.caption)
                        .foregroundColor(AppTheme.textSecondary)

                    OnboardingMultiSelectChipView(
                        options: specialLeaseConditionsOptions,
                        selectedItems: $selectedSpecialLeaseConditions,
                        onSelectionChanged: {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                        }
                    )
                }
            }
        }
    }
}

// MARK: - Roommate Mode Card Component
struct RoommateModeCard: View {
    let mode: String
    let isSelected: Bool
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: AppTheme.spacing16) {
                VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                    Text(mode)
                        .font(AppTheme.subtitleFont)
                        .foregroundColor(isSelected ? .white : AppTheme.textPrimary)
                        .fontWeight(.semibold)

                    Text(modeDescription)
                        .font(AppTheme.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.9) : AppTheme.textSecondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                Image(systemName: modeIcon)
                    .font(.system(size: 24))
                    .foregroundColor(isSelected ? .white : AppTheme.primaryColor)
            }
            .padding(AppTheme.spacing16)
            .background(
                isSelected ?
                AnyView(AppTheme.primaryGradient) :
                AnyView(AppTheme.cardBackground)
            )
            .cornerRadius(AppTheme.radiusMedium)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusMedium)
                    .stroke(
                        isSelected ? Color.clear : AppTheme.primaryColor.opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var modeDescription: String {
        switch mode {
        case "Find Together":
            return "Look for roommates and a place together"
        case "Looking for Roommate":
            return "Find someone to join your existing place"
        case "Looking for Lease":
            return "Join someone else's existing lease"
        default:
            return ""
        }
    }

    private var modeIcon: String {
        switch mode {
        case "Find Together":
            return "person.2.fill"
        case "Looking for Roommate":
            return "house.fill"
        case "Looking for Lease":
            return "doc.text.fill"
        default:
            return "questionmark.circle.fill"
        }
    }
}

// MARK: - Optimized College Selector
struct OptimizedCollegeSelector: View {
    let title: String
    @Binding var selectedCollege: String
    @State private var searchQuery: String = ""
    @State private var suggestions: [String] = []
    @State private var showSuggestions = false
    @State private var isLoading = false

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(title)
                .font(AppTheme.subtitleFont)
                .foregroundColor(AppTheme.textPrimary)

            CollegeSearchField(
                selectedCollege: $selectedCollege,
                label: "",
                placeholder: "Start typing college name..."
            )
        }
    }




}

// MARK: - Onboarding Multi-Select Chip View (Simplified)
struct OnboardingMultiSelectChipView: View {
    let options: [String]
    @Binding var selectedItems: [String]
    var onSelectionChanged: () -> Void = {}
    var maxSelection: Int? = nil

    var body: some View {
        VStack(spacing: AppTheme.spacing8) {
            ForEach(options, id: \.self) { option in
                chipView(option)
            }
        }
    }

    private func chipView(_ item: String) -> some View {
        let isSelected = selectedItems.contains(item)
        let isDisabled = !isSelected && (maxSelection != nil && selectedItems.count >= maxSelection!)

        return Button(action: {
            if isDisabled { return }
            if isSelected {
                selectedItems.removeAll { $0 == item }
            } else {
                selectedItems.append(item)
            }
            onSelectionChanged()
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            HStack(spacing: AppTheme.spacing8) {
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }

                Text(item)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.6)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .opacity(isDisabled ? 0.5 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Sexy Supporting Components

struct SexyOptionCard: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: AppTheme.spacing12) {
                // Icon with glow effect
                ZStack {
                    Circle()
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                        )

                    if isSelected {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 60, height: 60)
                            .blur(radius: 8)
                            .opacity(0.6)
                    }

                    Image(systemName: icon)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.white)
                }

                // Title with proper spacing
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 14))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .minimumScaleFactor(0.8)
                    .frame(maxWidth: .infinity)
                    .fixedSize(horizontal: false, vertical: true)

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundStyle(AppTheme.sexyGradient)
                } else {
                    // Invisible spacer to maintain consistent height
                    Image(systemName: "circle")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.clear)
                }
            }
            .padding(.vertical, AppTheme.spacing16)
            .padding(.horizontal, AppTheme.spacing12)
            .frame(minHeight: 120)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial.opacity(isSelected ? 1.0 : 0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(isSelected ? AppTheme.primaryColor : Color.white.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct SexyPropertyImageGrid: View {
    @Binding var selectedImages: [UIImage]
    let onAddImage: () -> Void

    private let columns = Array(repeating: GridItem(.flexible(), spacing: 8), count: 3)

    var body: some View {
        LazyVGrid(columns: columns, spacing: 8) {
            ForEach(0..<9, id: \.self) { index in
                ZStack {
                    if index < selectedImages.count {
                        // Show selected image
                        Image(uiImage: selectedImages[index])
                            .resizable()
                            .scaledToFill()
                            .frame(width: 100, height: 100)
                            .clipped()
                            .cornerRadius(16)
                            .overlay(
                                // Remove button
                                Button(action: {
                                    selectedImages.remove(at: index)
                                    HapticFeedbackManager.shared.generateImpact(style: .light)
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.system(size: 20, weight: .bold))
                                        .foregroundColor(.white)
                                        .background(Circle().fill(.black.opacity(0.6)))
                                }
                                .padding(4),
                                alignment: .topTrailing
                            )
                    } else {
                        // Add image placeholder
                        Button(action: {
                            onAddImage()
                            HapticFeedbackManager.shared.generateImpact(style: .medium)
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: index == 0 ? "camera.fill" : "plus")
                                    .font(.system(size: 24, weight: .bold))
                                    .foregroundColor(AppTheme.primaryColor)

                                if index == 0 {
                                    Text("Add Photos")
                                        .font(.caption2)
                                        .foregroundColor(.white.opacity(0.8))
                                }
                            }
                            .frame(width: 100, height: 100)
                            .background(Color.black.opacity(0.3))
                            .cornerRadius(16)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(AppTheme.primaryColor, lineWidth: 1)
                                    .opacity(0.5)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
    }
}

// MARK: - Additional Sexy Components

struct SexyTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String

    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text(title)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.8))

            TextField(placeholder, text: $text)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .padding(AppTheme.spacing16)
                .background(.ultraThinMaterial)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        }
    }
}

struct SexyFeatureHighlight: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: AppTheme.spacing16) {
            ZStack {
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 60, height: 60)
                    .overlay(
                        Circle()
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )

                Image(systemName: icon)
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: AppTheme.spacing4) {
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(.white)

                Text(description)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()
        }
        .padding(AppTheme.spacing16)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(.white.opacity(0.3), lineWidth: 1)
        )
    }
}

struct SexyCollegeSelector: View {
    @Binding var selectedCollege: String

    var body: some View {
        CollegeSearchField(
            selectedCollege: $selectedCollege,
            label: "",
            placeholder: "Start typing college name..."
        )
    }
}


// MARK: - Enhanced Housing Option Card

struct EnhancedHousingOptionCard: View {
    let preference: PrimaryHousingPreference
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: AppTheme.spacing12) {
                // Header with icon and title
                HStack(spacing: AppTheme.spacing12) {
                    Image(systemName: iconForPreference)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(colorForPreference)
                        .frame(width: 28, height: 28)

                    Text(preference.rawValue)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)

                    Spacer()

                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(AppTheme.primaryColor)
                    }
                }

                // Detailed explanation
                Text(explanationForPreference)
                    .font(.custom("AvenirNext-Medium", size: 13))
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
                    .lineLimit(nil)

                // When to choose this option
                Text(whenToChooseText)
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(colorForPreference.opacity(0.9))
                    .multilineTextAlignment(.leading)
                    .lineLimit(nil)
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? colorForPreference.opacity(0.2) : Color.black.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? colorForPreference : .white.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var iconForPreference: String {
        switch preference {
        case .lookingForLease: return "key.fill"
        case .lookingForRoommate: return "person.2.fill"
        case .lookingToFindTogether: return "heart.fill"
        }
    }

    private var colorForPreference: Color {
        switch preference {
        case .lookingForLease: return .blue
        case .lookingForRoommate: return .green
        case .lookingToFindTogether: return .purple
        }
    }

    private var explanationForPreference: String {
        switch preference {
        case .lookingForLease:
            return "You need to find a place to live and are looking for available rooms or apartments to rent."
        case .lookingForRoommate:
            return "You already have a place (lease, room, apartment) and are looking for someone to share it with."
        case .lookingToFindTogether:
            return "You want to team up with someone to search for and secure housing together as partners."
        }
    }

    private var whenToChooseText: String {
        switch preference {
        case .lookingForLease:
            return "💡 Choose this if: You don't have housing secured and need to find a room or place to live"
        case .lookingForRoommate:
            return "💡 Choose this if: You have a lease/room and need someone to share the space and costs"
        case .lookingToFindTogether:
            return "💡 Choose this if: You want to search for housing as a team and apply for places together"
        }
    }
}

// MARK: - Specialized Card Components

struct SexyGenderCard: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.spacing16) {
                // Icon with glow effect
                ZStack {
                    Circle()
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                        )

                    if isSelected {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 50, height: 50)
                            .blur(radius: 6)
                            .opacity(0.6)
                    }

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                }

                // Title with proper spacing
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
            }
            .padding(.vertical, AppTheme.spacing16)
            .padding(.horizontal, AppTheme.spacing20)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(isSelected ? 1.0 : 0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? AppTheme.primaryColor : Color.white.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct SexyGradeLevelCard: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.spacing16) {
                // Icon with glow effect
                ZStack {
                    Circle()
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                        )

                    if isSelected {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 50, height: 50)
                            .blur(radius: 6)
                            .opacity(0.6)
                    }

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                }

                // Title with proper spacing
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 18))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
            }
            .padding(.vertical, AppTheme.spacing16)
            .padding(.horizontal, AppTheme.spacing20)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(isSelected ? 1.0 : 0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? AppTheme.primaryColor : Color.white.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct SexyLifestyleCard: View {
    let title: String
    let isSelected: Bool
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.spacing16) {
                // Icon with glow effect
                ZStack {
                    Circle()
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Circle()
                                .stroke(isSelected ? Color.clear : Color.white.opacity(0.3), lineWidth: 1)
                        )

                    if isSelected {
                        Circle()
                            .fill(AppTheme.sexyGradient)
                            .frame(width: 40, height: 40)
                            .blur(radius: 4)
                            .opacity(0.6)
                    }

                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }

                // Title with proper spacing
                Text(title)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial.opacity(isSelected ? 1.0 : 0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? AppTheme.primaryColor : Color.white.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}
