// MajorDropdownSelection.swift
// Dropdown-only major selection component (no manual text input allowed)

import SwiftUI

struct MajorDropdownSelection: View {
    @Binding var selectedMajor: String
    @StateObject private var databaseService = RealtimeDatabaseService.shared
    
    @State private var searchText = ""
    @State private var isExpanded = false
    @State private var showingDropdown = false
    
    let placeholder: String
    let label: String?
    
    init(selectedMajor: Binding<String>, placeholder: String = "Select Major", label: String? = nil) {
        self._selectedMajor = selectedMajor
        self.placeholder = placeholder
        self.label = label
    }
    
    private var filteredMajors: [String] {
        if searchText.isEmpty {
            return databaseService.majors
        } else {
            return databaseService.searchMajors(query: searchText)
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            // Label
            if let label = label {
                modernFieldLabel(label, icon: "book.closed.fill")
            }
            
            // Main selection button
            Button(action: {
                HapticFeedbackManager.shared.generateImpact(style: .light)
                showingDropdown = true
            }) {
                HStack {
                    Text(selectedMajor.isEmpty ? placeholder : selectedMajor)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(selectedMajor.isEmpty ? .white.opacity(0.6) : .white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                        .rotationEffect(.degrees(showingDropdown ? 180 : 0))
                        .animation(.easeInOut(duration: 0.2), value: showingDropdown)
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .sheet(isPresented: $showingDropdown) {
            majorSelectionSheet
        }
    }
    
    // MARK: - Major Selection Sheet
    
    private var majorSelectionSheet: some View {
        NavigationView {
            ZStack {
                // Background
                AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Search bar
                    searchSection
                    
                    // Majors list
                    if databaseService.isLoadingMajors {
                        loadingView
                    } else if filteredMajors.isEmpty {
                        emptyStateView
                    } else {
                        majorsList
                    }
                }
            }
            .navigationTitle("Select Major")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        showingDropdown = false
                    }
                    .foregroundColor(.white)
                }
                
                if !selectedMajor.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Clear") {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            selectedMajor = ""
                            showingDropdown = false
                        }
                        .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
        }
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        HStack(spacing: AppTheme.spacing12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
            
            TextField("Search majors...", text: $searchText)
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !searchText.isEmpty {
                Button(action: { searchText = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, AppTheme.spacing20)
        .padding(.top, AppTheme.spacing8)
    }
    
    // MARK: - Majors List
    
    private var majorsList: some View {
        ScrollView {
            LazyVStack(spacing: AppTheme.spacing8) {
                ForEach(filteredMajors, id: \.self) { major in
                    majorRow(major)
                }
            }
            .padding(AppTheme.spacing20)
        }
    }
    
    private func majorRow(_ major: String) -> some View {
        let isSelected = selectedMajor == major
        
        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .medium)
            selectedMajor = major
            showingDropdown = false
        }) {
            HStack {
                Text(major)
                    .font(.custom("AvenirNext-Medium", size: 16))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.9))
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
    
    // MARK: - Helper Views
    
    private var loadingView: some View {
        VStack(spacing: AppTheme.spacing16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.2)
            
            Text("Loading majors...")
                .font(.custom("AvenirNext-Medium", size: 16))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.white.opacity(0.6))
            
            Text("No majors found")
                .font(.custom("AvenirNext-Bold", size: 18))
                .foregroundColor(.white)
            
            Text("Try adjusting your search")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, minHeight: 200)
    }
    
    // MARK: - Helper Methods
    
    private func modernFieldLabel(_ text: String, icon: String) -> some View {
        HStack(spacing: AppTheme.spacing8) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
            
            Text(text)
                .font(.custom("AvenirNext-Bold", size: 14))
                .foregroundColor(.white.opacity(0.9))
        }
    }
    
    private var modernInputBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial.opacity(0.4))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.2), lineWidth: 1)
            )
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        AppTheme.dynamicBackgroundGradient.ignoresSafeArea()
        
        VStack(spacing: 20) {
            MajorDropdownSelection(
                selectedMajor: .constant(""),
                placeholder: "Select Major",
                label: "Major"
            )
            
            MajorDropdownSelection(
                selectedMajor: .constant("Computer Science"),
                placeholder: "Select Major",
                label: "Major"
            )
        }
        .padding()
    }
}
