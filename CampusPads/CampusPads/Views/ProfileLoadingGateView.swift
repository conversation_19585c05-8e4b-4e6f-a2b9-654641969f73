import SwiftUI
import FirebaseAuth

/// ProfileLoadingGateView - Critical component that ensures profile is loaded before showing main app
/// This prevents "profile not available" errors by waiting for profile data to be ready
struct ProfileLoadingGateView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var profileViewModel: ProfileViewModel
    @State private var hasTriedLoading = false
    @State private var loadingTimeout = false
    @State private var retryCount = 0
    
    private let maxRetries = 3
    private let loadingTimeoutDuration: TimeInterval = 10.0
    
    var body: some View {
        Group {
            if profileViewModel.userProfile != nil {
                // Profile loaded successfully - show main app
                TabBarView()
                    .environmentObject(authViewModel)
                    .environmentObject(profileViewModel)
                    .onAppear {
                        print("✅ ProfileLoadingGateView: Profile loaded, showing TabBarView")
                    }
                
            } else if profileViewModel.errorMessage != nil || loadingTimeout {
                // Profile loading failed or timed out - show error recovery
                profileErrorRecoveryView
                
            } else {
                // Profile is loading - show loading state
                profileLoadingView
                    .onAppear {
                        handleProfileLoading()
                    }
            }
        }
    }
    
    // MARK: - Profile Loading View
    private var profileLoadingView: some View {
        ZStack {
            // Beautiful animated background
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()
            
            AnimatedBackground()
                .opacity(0.3)
            
            VStack(spacing: AppTheme.spacing24) {
                // Animated loading icon
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)
                        .frame(width: 80, height: 80)
                        .scaleEffect(1.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: UUID())
                    
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 40, weight: .light))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                
                VStack(spacing: AppTheme.spacing8) {
                    Text("Loading Your Profile")
                        .font(.custom("AvenirNext-Bold", size: 24))
                        .foregroundStyle(AppTheme.sexyGradient)
                        .multilineTextAlignment(.center)
                    
                    Text("Setting up your personalized experience...")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppTheme.spacing24)
                }
                
                // Progress indicator
                ProgressView()
                    .tint(AppTheme.primaryColor)
                    .scaleEffect(1.2)
            }
        }
    }
    
    // MARK: - Profile Error Recovery View
    private var profileErrorRecoveryView: some View {
        ZStack {
            AppTheme.dynamicBackgroundGradient
                .ignoresSafeArea()
            
            VStack(spacing: AppTheme.spacing24) {
                // Error icon
                ZStack {
                    Circle()
                        .fill(Color.red.opacity(0.2))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.red)
                }
                
                VStack(spacing: AppTheme.spacing16) {
                    Text("Profile Loading Failed")
                        .font(.custom("AvenirNext-Bold", size: 24))
                        .foregroundColor(AppTheme.textPrimary)
                        .multilineTextAlignment(.center)
                    
                    Text(profileViewModel.errorMessage ?? "Unable to load your profile. Please try again.")
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(AppTheme.textSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppTheme.spacing24)
                }
                
                VStack(spacing: AppTheme.spacing12) {
                    // Retry button
                    Button(action: retryProfileLoading) {
                        HStack(spacing: AppTheme.spacing8) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 16, weight: .medium))
                            Text("Try Again")
                                .font(.custom("AvenirNext-Bold", size: 16))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, AppTheme.spacing24)
                        .padding(.vertical, AppTheme.spacing12)
                        .background(AppTheme.primaryColor)
                        .cornerRadius(AppTheme.radiusMedium)
                    }
                    .disabled(retryCount >= maxRetries)
                    
                    // Sign out button as fallback
                    Button(action: signOut) {
                        Text("Sign Out")
                            .font(.custom("AvenirNext-Medium", size: 14))
                            .foregroundColor(AppTheme.textSecondary)
                    }
                }
                
                if retryCount >= maxRetries {
                    Text("Maximum retry attempts reached. Please sign out and try again.")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, AppTheme.spacing24)
                }
            }
            .padding(AppTheme.spacing24)
        }
    }
    
    // MARK: - Helper Methods
    
    private func handleProfileLoading() {
        guard !hasTriedLoading else { return }
        hasTriedLoading = true
        
        print("🔄 ProfileLoadingGateView: Starting profile loading process")
        
        // Set up loading timeout
        DispatchQueue.main.asyncAfter(deadline: .now() + loadingTimeoutDuration) {
            if profileViewModel.userProfile == nil && profileViewModel.errorMessage == nil {
                print("⏰ ProfileLoadingGateView: Loading timeout reached")
                loadingTimeout = true
            }
        }
        
        // Force profile refresh if needed
        if let uid = authViewModel.userSession?.uid {
            print("🔄 ProfileLoadingGateView: Forcing profile refresh for user: \(uid)")
            // The ProfileViewModel should automatically start loading when auth state changes
            // But we can add additional retry logic here if needed
        }
    }
    
    private func retryProfileLoading() {
        guard retryCount < maxRetries else { return }

        retryCount += 1
        loadingTimeout = false
        hasTriedLoading = false

        print("🔄 ProfileLoadingGateView: Retry attempt \(retryCount) of \(maxRetries)")

        // Use the new force refresh method
        profileViewModel.forceRefreshProfile()

        // Reset timeout timer
        DispatchQueue.main.asyncAfter(deadline: .now() + loadingTimeoutDuration) {
            if profileViewModel.userProfile == nil && profileViewModel.errorMessage == nil {
                print("⏰ ProfileLoadingGateView: Retry timeout reached")
                loadingTimeout = true
            }
        }
    }
    
    private func signOut() {
        print("🔄 ProfileLoadingGateView: User requested sign out due to profile loading failure")
        authViewModel.signOut()
    }
}

#Preview {
    ProfileLoadingGateView()
        .environmentObject(AuthViewModel())
        .environmentObject(ProfileViewModel.shared)
}
