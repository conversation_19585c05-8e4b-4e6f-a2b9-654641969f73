import Foundation
import FirebaseFirestore
import FirebaseFirestoreCombineSwift

struct FilterSettings: Codable {
  // MANDATORY FILTERS - Must be met for inclusion
  var housingStatus: String?        // MANDATORY: Housing compatibility
  var collegeName: String?          // MANDATORY: College match
  var preferredGender: String?      // MANDATORY: Gender preference
  var maxAgeDifference: Double?     // MANDATORY: Age difference limit

  // OPTIONAL FILTERS - Improve ranking but don't exclude
  var dormType: String?
  var budgetMin: Double?
  var budgetMax: Double?
  var rentMin: Double?
  var rentMax: Double?
  var gradeGroup: String?
  var interests: String?
  var maxDistance: Double?          // REMOVED: Distance filtering no longer used
  var roomType: String?
  var amenities: [String]?
  var cleanliness: Int?
  var sleepSchedule: String?
  var petFriendly: Bool?
  var smoker: Bool?
  var drinker: Bool?
  var marijuana: Bool?
  var workout: Bool?
  var mode: String?
  var showAllProfiles: Bool?

  // MARK: - Convenience Initializers
  init(
    // Mandatory filters
    housingStatus: String? = nil,
    collegeName: String? = nil,
    preferredGender: String? = nil,
    maxAgeDifference: Double? = nil,

    // Optional filters
    dormType: String? = nil,
    budgetMin: Double? = nil,
    budgetMax: Double? = nil,
    rentMin: Double? = nil,
    rentMax: Double? = nil,
    gradeGroup: String? = nil,
    interests: String? = nil,
    maxDistance: Double? = nil,
    roomType: String? = nil,
    amenities: [String]? = nil,
    cleanliness: Int? = nil,
    sleepSchedule: String? = nil,
    petFriendly: Bool? = nil,
    smoker: Bool? = nil,
    drinker: Bool? = nil,
    marijuana: Bool? = nil,
    workout: Bool? = nil,
    mode: String? = nil,
    showAllProfiles: Bool? = nil
  ) {
    // Mandatory filters
    self.housingStatus = housingStatus
    self.collegeName = collegeName
    self.preferredGender = preferredGender
    self.maxAgeDifference = maxAgeDifference

    // Optional filters
    self.dormType = dormType
    self.budgetMin = budgetMin
    self.budgetMax = budgetMax
    self.rentMin = rentMin
    self.rentMax = rentMax
    self.gradeGroup = gradeGroup
    self.interests = interests
    self.maxDistance = maxDistance
    self.roomType = roomType
    self.amenities = amenities
    self.cleanliness = cleanliness
    self.sleepSchedule = sleepSchedule
    self.petFriendly = petFriendly
    self.smoker = smoker
    self.drinker = drinker
    self.marijuana = marijuana
    self.workout = workout
    self.mode = mode
    self.showAllProfiles = showAllProfiles
  }
}


struct UserModel: Codable, Identifiable, Equatable {
    @DocumentID var id: String?

    // 1. Basic Info
    var email: String
    var createdAt: Date?           // optional creation date
    var isEmailVerified: Bool
    var lastActiveAt: Date?      // ADDED
    var hideFromDiscovery: Bool? // Controls discovery visibility

    // 2. Personal Info
    var aboutMe: String?
    var firstName: String?
    var lastName: String?
    var dateOfBirth: Date?
    var ageVerified: Bool = false

    // Computed property to get age from date of birth
    var age: Int? {
        guard let dateOfBirth = dateOfBirth else { return nil }
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: Date())
        return ageComponents.year
    }
    var gender: String?            // "Male", "Female", or "Other"
    var height: String?            // e.g., "5\'9"

    // Derived fields for searching - Stored for querying
    var firstName_lowercase: String?
    var lastName_lowercase: String?
    var collegeName_lowercase: String?
    var emailDomain: String?

    // 3. Academic Info
    var gradeLevel: String?
    var major: String?
    var collegeName: String?       // Selected via search

    // 4. Housing & Lease Info (for housing selection)
    var housingStatus: String?
    var dormType: String?          // restored dormType field
    var preferredDorm: String?     // restored preferredDorm field
    var desiredLeaseHousingType: String?
    var roommateCountNeeded: Int?
    var roommateCountExisting: Int?

    // 5. Property Details
    var propertyDetails: String?
    var propertyAddress: String?   // NEW: property address entered by the user
    var propertyImageUrls: [String]?
    var floorplanUrls: [String]?
    var documentUrls: [String]?

    // 6. Room Type Selector (available for both views)
    var roomType: String?

    // 7. Lease & Pricing Details (displayed only for Looking for Roommate view)
    var leaseStartDate: Date?
    var leaseDuration: String?     // reused for lease duration details
    var monthlyRentMin: Double? // ← new
    var monthlyRentMax: Double? // ← new
    var specialLeaseConditions: [String]?

    // 8. Amenities Multi-Select Field
    var amenities: [String]?

    // 9. Additional Housing Fields
    var budgetMin: Double?      // ← new
    var budgetMax: Double?
    var cleanliness: Int?
    var sleepSchedule: String?
    var smoker: Bool?
    var petFriendly: Bool?
    var livingStyle: String?

    // 10. Interests
    var socialLevel: Int?
    var studyHabits: Int?
    var interests: [String]?

    // 11. Media & Location
    var profileImageUrl: String?      // legacy single image
    var profileImageUrls: [String]?   // array for multiple images
    var location: GeoPoint?

    // 12. Blocked Users
    var blockedUserIDs: [String]?

    // 13. Push Notifications
    var fcmToken: String?
    var notificationsEnabled: Bool?

    // 14. Advanced Filter Settings
    var filterSettings: FilterSettings?

    // 15. Lifestyle Fields (matching Tinder's categories)
    var pets: [String]?
    var drinking: String?
    var smoking: String?
    var cannabis: String?
    var workout: String?
    var dietaryPreferences: [String]?
    var socialMedia: String?
    var sleepingHabits: String?

    // 16. Quiz Answers
    var goingOutQuizAnswers: [String]?
    var weekendQuizAnswers: [String]?
    var phoneQuizAnswers: [String]?   // "+ My Phone" quiz answers

    // 17. Premium/Top Matches System
    var isInTopMatches: Bool?         // Whether user is currently in someone's top matches
    var topMatchesUntil: Date?        // When the top matches reservation expires
    var compatibilityScore: Double?  // Calculated compatibility score (0.0 - 1.0)
    var isPremium: Bool?              // Whether user has premium subscription
    var premiumSince: Date?           // When premium subscription started
    var premiumPlan: String?          // "monthly" or "yearly"

    init(
        email: String,
        isEmailVerified: Bool,
        createdAt: Date? = nil,
        lastActiveAt: Date? = nil,        // ADDED
        hideFromDiscovery: Bool? = false, // Controls discovery visibility - defaults to false (visible)
        // 2. Personal Info
        aboutMe: String? = nil,
        firstName: String? = nil,
        lastName: String? = nil,
        dateOfBirth: Date? = nil,
        ageVerified: Bool = false,
        gender: String? = nil,
        height: String? = nil,
        // 3. Academic Info
        gradeLevel: String? = nil,
        major: String? = nil,
        collegeName: String? = nil,
        // 4. Housing & Lease Info
        housingStatus: String? = nil,
        dormType: String? = nil,
        preferredDorm: String? = nil,
        desiredLeaseHousingType: String? = nil,
        roommateCountNeeded: Int? = nil,
        roommateCountExisting: Int? = nil,
        // 5. Property Details
        propertyDetails: String? = nil,
        propertyAddress: String? = nil,   // NEW: address parameter
        propertyImageUrls: [String]? = nil,
        floorplanUrls: [String]? = nil,
        documentUrls: [String]? = nil,
        // 6. Room Type Selector
        roomType: String? = nil,
        // 7. Lease & Pricing Details
        leaseStartDate: Date? = nil,
        leaseDuration: String? = nil,
        monthlyRentMin: Double? = nil,
        monthlyRentMax: Double? = nil,
        specialLeaseConditions: [String]? = nil,
        // 8. Amenities Multi-Select Field
        amenities: [String]? = nil,
        // 9. Additional Housing Fields
        budgetMin: Double? = nil,
        budgetMax: Double? = nil,
        cleanliness: Int? = nil,
        sleepSchedule: String? = nil,
        smoker: Bool? = nil,
        petFriendly: Bool? = nil,
        livingStyle: String? = nil,
        // 10. Interests
        socialLevel: Int? = nil,
        studyHabits: Int? = nil,
        interests: [String]? = nil,
        // 11. Media & Location
        profileImageUrl: String? = nil,
        profileImageUrls: [String]? = nil,
        location: GeoPoint? = nil,
        // 12. Blocked Users
        blockedUserIDs: [String]? = nil,
        // 13. Push Notifications
        fcmToken: String? = nil,
        notificationsEnabled: Bool? = true,
        // 14. Advanced Filter Settings
        filterSettings: FilterSettings? = nil,
        // 15. Lifestyle Fields
        pets: [String]? = nil,
        drinking: String? = nil,
        smoking: String? = nil,
        cannabis: String? = nil,
        workout: String? = nil,
        dietaryPreferences: [String]? = nil,
        socialMedia: String? = nil,
        sleepingHabits: String? = nil,
        // 16. Quiz Answers
        goingOutQuizAnswers: [String]? = nil,
        weekendQuizAnswers: [String]? = nil,
        phoneQuizAnswers: [String]? = nil,
        // 17. Premium/Top Matches System
        isInTopMatches: Bool? = false,
        topMatchesUntil: Date? = nil,
        compatibilityScore: Double? = nil,
        isPremium: Bool? = false,
        premiumSince: Date? = nil,
        premiumPlan: String? = nil
    ) {
        self.email = email
        self.isEmailVerified = isEmailVerified
        self.createdAt = createdAt ?? Date()
        self.lastActiveAt = lastActiveAt          // ADDED
        self.hideFromDiscovery = hideFromDiscovery

        // Personal Info
        self.aboutMe = aboutMe
        self.firstName = firstName
        self.lastName = lastName
        self.dateOfBirth = dateOfBirth
        self.ageVerified = ageVerified
        self.gender = gender
        self.height = height

        // Academic Info
        self.gradeLevel = gradeLevel
        self.major = major
        self.collegeName = collegeName

        // Housing & Lease Info
        self.housingStatus = housingStatus
        self.dormType = dormType
        self.preferredDorm = preferredDorm
        self.desiredLeaseHousingType = desiredLeaseHousingType
        self.roommateCountNeeded = roommateCountNeeded
        self.roommateCountExisting = roommateCountExisting

        // Property Details
        self.propertyDetails = propertyDetails
        self.propertyAddress = propertyAddress   // NEW: assign address
        self.propertyImageUrls = propertyImageUrls
        self.floorplanUrls = floorplanUrls
        self.documentUrls = documentUrls

        // Room Type Selector
        self.roomType = roomType

        // Stored lowercase/derived fields for searching
        self.firstName_lowercase = firstName?.lowercased()
        self.lastName_lowercase = lastName?.lowercased()
        self.collegeName_lowercase = collegeName?.lowercased()
        if let atIndex = email.lastIndex(of: "@") {
            self.emailDomain = String(email.suffix(from: email.index(after: atIndex)))
        } else {
            self.emailDomain = nil
        }

        // Lease & Pricing Details
        self.leaseStartDate = leaseStartDate
        self.leaseDuration = leaseDuration
        self.monthlyRentMin =  monthlyRentMin
        self.monthlyRentMax = monthlyRentMax
        self.specialLeaseConditions = specialLeaseConditions

        // Amenities Multi-Select Field
        self.amenities = amenities

        // Additional Housing Fields
        self.budgetMin = budgetMin
        self.budgetMax = budgetMax
        self.cleanliness = cleanliness
        self.sleepSchedule = sleepSchedule
        self.smoker = smoker
        self.petFriendly = petFriendly
        self.livingStyle = livingStyle

        // Interests
        self.socialLevel = socialLevel
        self.studyHabits = studyHabits
        self.interests = interests

        // Media & Location
        self.profileImageUrl = profileImageUrl
        self.profileImageUrls = profileImageUrls
        self.location = location

        // Blocked Users
        self.blockedUserIDs = blockedUserIDs

        // Push Notifications
        self.fcmToken = fcmToken
        self.notificationsEnabled = notificationsEnabled

        // Advanced Filter Settings
        self.filterSettings = filterSettings

        // Lifestyle Fields
        self.pets = pets
        self.drinking = drinking
        self.smoking = smoking
        self.cannabis = cannabis
        self.workout = workout
        self.dietaryPreferences = dietaryPreferences
        self.socialMedia = socialMedia
        self.sleepingHabits = sleepingHabits

        // Quiz Answers
        self.goingOutQuizAnswers = goingOutQuizAnswers
        self.weekendQuizAnswers = weekendQuizAnswers
        self.phoneQuizAnswers = phoneQuizAnswers

        // Premium/Top Matches System
        self.isInTopMatches = isInTopMatches
        self.topMatchesUntil = topMatchesUntil
        self.compatibilityScore = compatibilityScore
        self.isPremium = isPremium
        self.premiumSince = premiumSince
        self.premiumPlan = premiumPlan
    }

    // MARK: - Custom Decoding for Backward Compatibility
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Decode all standard fields
        email = try container.decode(String.self, forKey: .email)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt)
        isEmailVerified = try container.decode(Bool.self, forKey: .isEmailVerified)
        lastActiveAt = try container.decodeIfPresent(Date.self, forKey: .lastActiveAt)
        hideFromDiscovery = try container.decodeIfPresent(Bool.self, forKey: .hideFromDiscovery)

        // Personal Info
        aboutMe = try container.decodeIfPresent(String.self, forKey: .aboutMe)
        firstName = try container.decodeIfPresent(String.self, forKey: .firstName)
        lastName = try container.decodeIfPresent(String.self, forKey: .lastName)
        // Handle dateOfBirth - can be stored as String or Date in Firestore
        if let dobString = try? container.decodeIfPresent(String.self, forKey: .dateOfBirth) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            dateOfBirth = formatter.date(from: dobString)
        } else {
            dateOfBirth = try container.decodeIfPresent(Date.self, forKey: .dateOfBirth)
        }
        ageVerified = try container.decodeIfPresent(Bool.self, forKey: .ageVerified) ?? false
        gender = try container.decodeIfPresent(String.self, forKey: .gender)
        height = try container.decodeIfPresent(String.self, forKey: .height)

        // Derived fields
        firstName_lowercase = try container.decodeIfPresent(String.self, forKey: .firstName_lowercase)
        lastName_lowercase = try container.decodeIfPresent(String.self, forKey: .lastName_lowercase)
        collegeName_lowercase = try container.decodeIfPresent(String.self, forKey: .collegeName_lowercase)
        emailDomain = try container.decodeIfPresent(String.self, forKey: .emailDomain)

        // Academic Info
        gradeLevel = try container.decodeIfPresent(String.self, forKey: .gradeLevel)
        major = try container.decodeIfPresent(String.self, forKey: .major)
        collegeName = try container.decodeIfPresent(String.self, forKey: .collegeName)

        // Housing & Lease Info
        housingStatus = try container.decodeIfPresent(String.self, forKey: .housingStatus)
        dormType = try container.decodeIfPresent(String.self, forKey: .dormType)
        preferredDorm = try container.decodeIfPresent(String.self, forKey: .preferredDorm)
        desiredLeaseHousingType = try container.decodeIfPresent(String.self, forKey: .desiredLeaseHousingType)
        roommateCountNeeded = try container.decodeIfPresent(Int.self, forKey: .roommateCountNeeded)
        roommateCountExisting = try container.decodeIfPresent(Int.self, forKey: .roommateCountExisting)

        // Property Details
        propertyDetails = try container.decodeIfPresent(String.self, forKey: .propertyDetails)
        propertyAddress = try container.decodeIfPresent(String.self, forKey: .propertyAddress)
        propertyImageUrls = try container.decodeIfPresent([String].self, forKey: .propertyImageUrls)
        floorplanUrls = try container.decodeIfPresent([String].self, forKey: .floorplanUrls)
        documentUrls = try container.decodeIfPresent([String].self, forKey: .documentUrls)

        // Room Type
        roomType = try container.decodeIfPresent(String.self, forKey: .roomType)

        // Lease & Pricing Details
        leaseStartDate = try container.decodeIfPresent(Date.self, forKey: .leaseStartDate)
        leaseDuration = try container.decodeIfPresent(String.self, forKey: .leaseDuration)
        monthlyRentMin = try container.decodeIfPresent(Double.self, forKey: .monthlyRentMin)
        monthlyRentMax = try container.decodeIfPresent(Double.self, forKey: .monthlyRentMax)
        specialLeaseConditions = try container.decodeIfPresent([String].self, forKey: .specialLeaseConditions)

        // Amenities
        amenities = try container.decodeIfPresent([String].self, forKey: .amenities)

        // Additional Housing Fields
        budgetMin = try container.decodeIfPresent(Double.self, forKey: .budgetMin)
        budgetMax = try container.decodeIfPresent(Double.self, forKey: .budgetMax)
        cleanliness = try container.decodeIfPresent(Int.self, forKey: .cleanliness)
        sleepSchedule = try container.decodeIfPresent(String.self, forKey: .sleepSchedule)
        smoker = try container.decodeIfPresent(Bool.self, forKey: .smoker)
        petFriendly = try container.decodeIfPresent(Bool.self, forKey: .petFriendly)
        livingStyle = try container.decodeIfPresent(String.self, forKey: .livingStyle)

        // Interests - Handle both String and Array formats
        socialLevel = try container.decodeIfPresent(Int.self, forKey: .socialLevel)
        studyHabits = try container.decodeIfPresent(Int.self, forKey: .studyHabits)

        // CRITICAL FIX: Handle interests field that can be either String or Array
        if let interestsArray = try? container.decodeIfPresent([String].self, forKey: .interests) {
            // Already an array - use as is
            print("✅ UserModel: Decoded interests as Array: \(interestsArray)")
            interests = interestsArray
        } else if let interestsString = try? container.decodeIfPresent(String.self, forKey: .interests) {
            // String format - convert to array by splitting on commas
            let convertedArray = interestsString.split(separator: ",").map { $0.trimmingCharacters(in: .whitespaces) }
            print("✅ UserModel: Converted interests from String '\(interestsString)' to Array: \(convertedArray)")
            interests = convertedArray
        } else {
            // No interests field or null
            print("✅ UserModel: No interests field found, setting to nil")
            interests = nil
        }

        // Media & Location
        profileImageUrl = try container.decodeIfPresent(String.self, forKey: .profileImageUrl)
        profileImageUrls = try container.decodeIfPresent([String].self, forKey: .profileImageUrls)
        location = try container.decodeIfPresent(GeoPoint.self, forKey: .location)

        // Blocked Users
        blockedUserIDs = try container.decodeIfPresent([String].self, forKey: .blockedUserIDs)

        // Filter Settings
        filterSettings = try container.decodeIfPresent(FilterSettings.self, forKey: .filterSettings)

        // Lifestyle Fields
        pets = try container.decodeIfPresent([String].self, forKey: .pets)
        drinking = try container.decodeIfPresent(String.self, forKey: .drinking)
        smoking = try container.decodeIfPresent(String.self, forKey: .smoking)
        cannabis = try container.decodeIfPresent(String.self, forKey: .cannabis)
        workout = try container.decodeIfPresent(String.self, forKey: .workout)
        dietaryPreferences = try container.decodeIfPresent([String].self, forKey: .dietaryPreferences)
        socialMedia = try container.decodeIfPresent(String.self, forKey: .socialMedia)
        sleepingHabits = try container.decodeIfPresent(String.self, forKey: .sleepingHabits)

        // Quiz Answers
        goingOutQuizAnswers = try container.decodeIfPresent([String].self, forKey: .goingOutQuizAnswers)
        weekendQuizAnswers = try container.decodeIfPresent([String].self, forKey: .weekendQuizAnswers)
        phoneQuizAnswers = try container.decodeIfPresent([String].self, forKey: .phoneQuizAnswers)

        // Premium/Top Matches System
        isInTopMatches = try container.decodeIfPresent(Bool.self, forKey: .isInTopMatches)
        topMatchesUntil = try container.decodeIfPresent(Date.self, forKey: .topMatchesUntil)
        compatibilityScore = try container.decodeIfPresent(Double.self, forKey: .compatibilityScore)
        isPremium = try container.decodeIfPresent(Bool.self, forKey: .isPremium)
        premiumSince = try container.decodeIfPresent(Date.self, forKey: .premiumSince)
        premiumPlan = try container.decodeIfPresent(String.self, forKey: .premiumPlan)
    }

    // MARK: - Coding Keys
    private enum CodingKeys: String, CodingKey {
        case email, createdAt, isEmailVerified, lastActiveAt, hideFromDiscovery
        case aboutMe, firstName, lastName, dateOfBirth, ageVerified, gender, height
        case firstName_lowercase, lastName_lowercase, collegeName_lowercase, emailDomain
        case gradeLevel, major, collegeName
        case housingStatus, dormType, preferredDorm, desiredLeaseHousingType, roommateCountNeeded, roommateCountExisting
        case propertyDetails, propertyAddress, propertyImageUrls, floorplanUrls, documentUrls
        case roomType
        case leaseStartDate, leaseDuration, monthlyRentMin, monthlyRentMax, specialLeaseConditions
        case amenities
        case budgetMin, budgetMax, cleanliness, sleepSchedule, smoker, petFriendly, livingStyle
        case socialLevel, studyHabits, interests
        case profileImageUrl, profileImageUrls, location
        case blockedUserIDs, fcmToken, notificationsEnabled, filterSettings
        case pets, drinking, smoking, cannabis, workout, dietaryPreferences, socialMedia, sleepingHabits
        case goingOutQuizAnswers, weekendQuizAnswers, phoneQuizAnswers
        case isInTopMatches, topMatchesUntil, compatibilityScore, isPremium, premiumSince, premiumPlan
    }

    // MARK: - Equatable Conformance
    static func == (lhs: UserModel, rhs: UserModel) -> Bool {
        // Compare based on unique identifier and email for efficiency
        return lhs.id == rhs.id && lhs.email == rhs.email
    }

    // MARK: - Helper Methods

    /// Computed property for user's display age (uses the existing age property from line 110-115)

    /// Computed property for bio (uses aboutMe)
    var bio: String? {
        return aboutMe
    }

    /// Computed property for college (uses collegeName)
    var college: String? {
        return collegeName
    }

    /// Check if user is currently reserved for top matches
    var isCurrentlyInTopMatches: Bool {
        guard let isInTopMatches = isInTopMatches,
              let expirationDate = topMatchesUntil else {
            return false
        }
        return isInTopMatches && expirationDate > Date()
    }

    /// Get compatibility score as percentage string
    var compatibilityPercentage: String {
        guard let score = compatibilityScore else { return "0%" }
        return "\(Int(score * 100))%"
    }
}

// MARK: - Mock Data for Previews
extension UserModel {
    static func mockUser() -> UserModel {
        return UserModel(
            email: "<EMAIL>",
            isEmailVerified: true,
            aboutMe: "Computer Science major looking for a clean, studious roommate. I enjoy gaming, hiking, and good coffee.",
            firstName: "John",
            lastName: "Doe",
            dateOfBirth: {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd"
                return formatter.date(from: "2002-05-15")
            }(),
            gender: "Male",
            collegeName: "Stanford University",
            housingStatus: "Looking for roommate",
            profileImageUrls: [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            compatibilityScore: 0.92,
            isPremium: false
        )
    }
}


