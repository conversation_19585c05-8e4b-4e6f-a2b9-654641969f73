import SwiftUI

struct TabBarView: View {
    @State private var selectedTab = 0

    var body: some View {
        GeometryReader { geometry in
            if DeviceInfo.isIPad && DeviceInfo.isLandscape {
                // iPad Landscape: Use sidebar navigation
                iPadLandscapeLayout
            } else {
                // iPhone and iPad Portrait: Use tab view
                standardTabLayout
            }
        }
    }

    // MARK: - Standard Tab Layout (iPhone + iPad Portrait)
    private var standardTabLayout: some View {
        TabView(selection: $selectedTab) {
            // Home Tab - moved to first position for better UX
            HomeView()
            .tabItem {
                VStack {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                        .font(.system(size: ResponsiveSizing.iconSize, weight: selectedTab == 0 ? .bold : .medium))
                        .scaleEffect(selectedTab == 0 ? 1.1 : 1.0)
                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                    Text("Home")
                        .font(ResponsiveTypography.caption)
                        .fontWeight(selectedTab == 0 ? .semibold : .regular)
                        .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                }
            }
            .tag(0)

            // Swipe Tab with enhanced flame icon
            SwipeDeckView()
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 1 ? "flame.fill" : "flame")
                            .font(.system(size: ResponsiveSizing.iconSize, weight: selectedTab == 1 ? .bold : .medium))
                            .scaleEffect(selectedTab == 1 ? 1.1 : 1.0)
                            .foregroundStyle(selectedTab == 1 ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.primary))
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                        Text("Discover")
                            .font(ResponsiveTypography.caption)
                            .fontWeight(selectedTab == 1 ? .semibold : .regular)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                    }
                }
                .tag(1)

            // Messages Tab with enhanced bubble icon
            CombinedMatchesChatView(onNavigateToDiscover: {
                selectedTab = 1 // Navigate to Discover tab
            })
                .tabItem {
                    VStack {
                        Image(systemName: selectedTab == 2 ? "bubble.left.and.bubble.right.fill" : "bubble.left.and.bubble.right")
                            .font(.system(size: ResponsiveSizing.iconSize, weight: selectedTab == 2 ? .bold : .medium))
                            .scaleEffect(selectedTab == 2 ? 1.1 : 1.0)
                            .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)
                        Text("Messages")
                            .font(ResponsiveTypography.caption)
                            .fontWeight(selectedTab == 2 ? .semibold : .regular)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 0.5)
                    }
                }
                .tag(2)

            // Global Search Tab with premium gold styling
            GlobalSearchView()
            .tabItem {
                    VStack {
                        ZStack {
                            // ENHANCED: Force premium gold styling with explicit colors
                            Image(systemName: selectedTab == 3 ? "magnifyingglass.circle.fill" : "magnifyingglass")
                                .font(.system(size: 24, weight: selectedTab == 3 ? .bold : .medium))
                                .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                .foregroundColor(Color.yellow) // Force explicit yellow color
                                .overlay(
                                    // Additional gradient overlay to ensure visibility
                                    Image(systemName: selectedTab == 3 ? "magnifyingglass.circle.fill" : "magnifyingglass")
                                        .font(.system(size: 24, weight: selectedTab == 3 ? .bold : .medium))
                                        .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                        .foregroundStyle(
                                            LinearGradient(
                                                colors: [
                                                    Color.yellow,
                                                    Color.orange,
                                                    Color.yellow
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .blendMode(.multiply)
                                )
                                .shadow(color: Color.yellow, radius: 10, x: 0, y: 5)
                                .shadow(color: Color.orange, radius: 15, x: 0, y: 8)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: selectedTab)

                            // ENHANCED: More prominent crown indicator
                            Image(systemName: "crown.fill")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(.yellow)
                                .offset(x: 15, y: -15)
                                .shadow(color: Color.yellow, radius: 4, x: 0, y: 2)
                                .scaleEffect(selectedTab == 3 ? 1.2 : 1.0)

                            // ENHANCED: Always visible sparkles with stronger animation
                            Image(systemName: "sparkles")
                                .font(.system(size: selectedTab == 3 ? 12 : 10, weight: .bold))
                                .foregroundColor(.white)
                                .offset(x: 16, y: -12)
                                .opacity(selectedTab == 3 ? 1.0 : 0.8)
                                .scaleEffect(selectedTab == 3 ? 1.3 : 1.0)
                                .shadow(color: Color.yellow, radius: 3, x: 0, y: 1)
                                .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: selectedTab)
                        }

                        Text("Search")
                            .font(ResponsiveTypography.caption)
                            .fontWeight(selectedTab == 3 ? .bold : .regular)
                            .foregroundColor(Color.yellow) // Force explicit yellow color
                            .shadow(color: Color.yellow, radius: 3, x: 0, y: 1)
                            .shadow(color: Color.orange, radius: 5, x: 0, y: 2)
                            .scaleEffect(selectedTab == 3 ? 1.1 : 1.0)
                    }
                }
                .tag(3)
        }
        .accentColor(AppTheme.primaryColor)
        .onAppear {
            // CRITICAL FIX: Completely disable UITabBar appearance to allow custom styling
            let appearance = UITabBarAppearance()
            appearance.configureWithTransparentBackground()

            // Completely transparent background for modern look
            appearance.backgroundColor = UIColor.clear
            appearance.backgroundEffect = nil
            appearance.shadowColor = UIColor.clear
            appearance.shadowImage = UIImage()

            // Remove default indicator for custom styling
            appearance.selectionIndicatorTintColor = UIColor.clear

            // CRITICAL: Remove ALL icon and text color overrides to allow custom styling
            appearance.stackedLayoutAppearance.normal.iconColor = nil
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [:]
            appearance.stackedLayoutAppearance.selected.iconColor = nil
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [:]

            // Apply minimal appearance
            UITabBar.appearance().standardAppearance = appearance
            UITabBar.appearance().scrollEdgeAppearance = appearance

            // Remove any background styling
            UITabBar.appearance().backgroundColor = UIColor.clear
            UITabBar.appearance().isTranslucent = true
            UITabBar.appearance().barTintColor = UIColor.clear
            UITabBar.appearance().tintColor = UIColor.clear
            UITabBar.appearance().unselectedItemTintColor = UIColor.clear
        }
    }

    // MARK: - iPad Landscape Layout
    private var iPadLandscapeLayout: some View {
        HStack(spacing: 0) {
            // Sidebar Navigation
            VStack(spacing: ResponsiveSpacing.lg) {
                // App Logo/Title
                VStack(spacing: ResponsiveSpacing.sm) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 32))
                        .foregroundStyle(AppTheme.sexyGradient)

                    Text("CampusPads")
                        .font(ResponsiveTypography.headline.weight(.bold))
                        .foregroundStyle(AppTheme.sexyGradient)
                }
                .padding(.top, ResponsiveSpacing.xl)

                // Navigation Items
                VStack(spacing: ResponsiveSpacing.md) {
                    sidebarNavItem(
                        icon: selectedTab == 0 ? "house.fill" : "house",
                        title: "Home",
                        isSelected: selectedTab == 0,
                        tag: 0
                    )

                    sidebarNavItem(
                        icon: selectedTab == 1 ? "flame.fill" : "flame",
                        title: "Discover",
                        isSelected: selectedTab == 1,
                        tag: 1
                    )

                    sidebarNavItem(
                        icon: selectedTab == 2 ? "bubble.left.and.bubble.right.fill" : "bubble.left.and.bubble.right",
                        title: "Messages",
                        isSelected: selectedTab == 2,
                        tag: 2
                    )

                    sidebarNavItem(
                        icon: selectedTab == 3 ? "magnifyingglass.circle.fill" : "magnifyingglass",
                        title: "Search",
                        isSelected: selectedTab == 3,
                        tag: 3,
                        isPremium: true
                    )
                }

                Spacer()
            }
            .frame(width: 200)
            .background(AppTheme.glassEffect)
            .overlay(
                Rectangle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 1),
                alignment: .trailing
            )

            // Main Content
            Group {
                switch selectedTab {
                case 0:
                    HomeView()
                case 1:
                    SwipeDeckView()
                case 2:
                    CombinedMatchesChatView(onNavigateToDiscover: {
                        selectedTab = 1
                    })
                case 3:
                    GlobalSearchView()
                default:
                    HomeView()
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }

    // MARK: - Sidebar Navigation Item
    private func sidebarNavItem(
        icon: String,
        title: String,
        isSelected: Bool,
        tag: Int,
        isPremium: Bool = false
    ) -> some View {
        Button(action: {
            selectedTab = tag
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }) {
            HStack(spacing: ResponsiveSpacing.md) {
                ZStack {
                    Image(systemName: icon)
                        .font(.system(size: 24, weight: isSelected ? .bold : .medium))
                        .foregroundColor(isSelected ? .white : AppTheme.textSecondary)

                    if isPremium {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.yellow)
                            .offset(x: 15, y: -15)
                    }
                }
                .frame(width: 32, height: 32)

                Text(title)
                    .font(ResponsiveTypography.headline.weight(isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? .white : AppTheme.textSecondary)

                Spacer()
            }
            .padding(.horizontal, ResponsiveSpacing.md)
            .padding(.vertical, ResponsiveSpacing.sm)
            .background(
                RoundedRectangle(cornerRadius: ResponsiveRadius.medium)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, ResponsiveSpacing.md)
    }
}

struct TabBarView_Previews: PreviewProvider {
    static var previews: some View {
        TabBarView()
    }
}
