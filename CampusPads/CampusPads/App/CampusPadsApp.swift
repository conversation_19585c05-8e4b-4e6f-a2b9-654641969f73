//
//  CampusPadsApp.swift
//  CampusPads
//
//  Created by <PERSON> on 3/6/25.
//

import SwiftUI
import Firebase

@main
struct CampusPadsApp: App {
    // Use proper AppDelegate for Firebase configuration
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate

    var body: some Scene {
        WindowGroup {
            // RootView determines which UI to display based on auth state
            RootView()
        }
    }
}
