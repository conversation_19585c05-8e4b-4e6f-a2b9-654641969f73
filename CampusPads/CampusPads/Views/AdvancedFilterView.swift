// AdvancedFilterView.swift

import SwiftUI
import CoreLocation

struct AdvancedFilterView: View {
    @StateObject private var viewModel = AdvancedFilterViewModel()
    @StateObject private var locationManager = LocationManager()

    let preferredGenders = ["Any", "Male", "Female", "Other"]
    @State private var selectedGradeLevel: MyProfileView.GradeLevel = .freshman



    // Animation states
    @State private var animateCards = false
    @State private var cardAnimationDelay: Double = 0

    private var alertBinding: Binding<GenericAlertError?> {
        Binding(
            get: { viewModel.errorMessage.map(GenericAlertError.init) },
            set: { _ in viewModel.errorMessage = nil }
        )
    }


    var body: some View {
        ZStack {
            // Sexy dynamic background
            AppTheme.dynamicBackgroundGradient.ignoresSafeArea()

            ScrollView(.vertical, showsIndicators: false) {
                VStack(spacing: AppTheme.spacing20) {
                    // Top spacing for navigation
                    Spacer()
                        .frame(height: 20)

                    // Filter Progress Bar
                    filterProgressSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)

                    // MANDATORY FILTERS SECTION
                    mandatoryFiltersSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: animateCards)

                    // OPTIONAL FILTERS SECTION
                    optionalFiltersSection
                        .opacity(animateCards ? 1.0 : 0.0)
                        .offset(y: animateCards ? 0 : 30)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.3), value: animateCards)

                    // Bottom spacing for safe area
                    Spacer()
                        .frame(height: 100)
                }
                .padding(.horizontal, AppTheme.spacing16)
            }
            .alert(item: alertBinding) { err in
                Alert(title: Text("Error"), message: Text(err.message), dismissButton: .default(Text("OK")))
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("Advanced Search")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)
                }
            }
        }
        .onAppear {
            // 1️⃣ Load saved filters & sync UI state
            viewModel.loadFiltersFromUserDoc {
                selectedGradeLevel = MyProfileView.GradeLevel(
                    rawValue: viewModel.filterGradeGroup
                ) ?? .freshman

                viewModel.applyFilters(
                    currentLocation: locationManager.currentLocation
                )
            }

            // 2️⃣ Trigger entrance animations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                animateCards = true
            }
        }
    }

    private func autoApplyAndSave() {
        viewModel.applyFilters(currentLocation: locationManager.currentLocation)
        viewModel.saveFiltersToUserDoc()
    }

    // MARK: - Filter Progress Calculation

    private var filterCompletion: Double {
        var completed: Double = 0
        var total: Double = 0

        // MANDATORY FILTERS (4 total)
        total += 4

        // Housing Status
        if viewModel.filterHousingPreference != nil || viewModel.showAllProfiles {
            completed += 1
        }

        // College Name
        if !viewModel.filterCollegeName.isEmpty {
            completed += 1
        }

        // Preferred Gender
        if !viewModel.filterPreferredGender.isEmpty {
            completed += 1
        }

        // Max Age Difference
        if viewModel.maxAgeDifference > 0 {
            completed += 1
        }

        // OPTIONAL FILTERS (10 total)
        total += 10

        // Grade Group
        if !viewModel.filterGradeGroup.isEmpty {
            completed += 1
        }

        // Room Type
        if !viewModel.filterRoomType.isEmpty {
            completed += 1
        }

        // Amenities
        if !viewModel.filterAmenities.isEmpty {
            completed += 1
        }

        // Cleanliness
        if viewModel.filterCleanliness != nil {
            completed += 1
        }

        // Sleep Schedule
        if !viewModel.filterSleepSchedule.isEmpty {
            completed += 1
        }

        // Lifestyle preferences (5 total)
        if viewModel.filterPetFriendly != nil { completed += 1 }
        if viewModel.filterSmoker != nil { completed += 1 }
        if viewModel.filterDrinker != nil { completed += 1 }
        if viewModel.filterMarijuana != nil { completed += 1 }
        if viewModel.filterWorkout != nil { completed += 1 }

        return (completed / total) * 100
    }

    // MARK: - Progress Bar Section

    private var filterProgressSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            HStack {
                Image(systemName: "slider.horizontal.3")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundStyle(AppTheme.sexyGradient)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Filter Setup")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)

                    Text("Customize your roommate preferences")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                Text("\(Int(filterCompletion))% Complete")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, AppTheme.spacing12)
                    .padding(.vertical, AppTheme.spacing6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(filterCompletion >= 80 ? Color.green : filterCompletion >= 60 ? Color.orange : Color.red)
                    )
            }

            ProgressView(value: filterCompletion, total: 100)
                .tint(.white)
                .scaleEffect(x: 1, y: 2, anchor: .center)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(.ultraThinMaterial.opacity(0.4))
                )
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    // MARK: - Mandatory Filters Section

    private var mandatoryFiltersSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "exclamationmark.triangle.fill",
                title: "Mandatory Filters",
                subtitle: "Required criteria - users must match these to appear"
            )

            VStack(spacing: AppTheme.spacing16) {
                housingPreferencePicker
                collegeNamePicker
                preferredGenderPicker
                maxAgeDifferencePicker
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.red.opacity(0.3), lineWidth: 2)
        )
    }

    // MARK: - Optional Filters Section

    private var optionalFiltersSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing20) {
            // Section header
            sectionHeader(
                icon: "star.circle.fill",
                title: "Optional Filters",
                subtitle: "Improve ranking but don't exclude users"
            )

            VStack(spacing: AppTheme.spacing16) {
                gradeLevelPicker
                roomTypePicker
                amenitiesSubsection
                lifestyleSubsection
                cleanlinessAndSleepSubsection
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.blue.opacity(0.3), lineWidth: 2)
        )
    }

    // MARK: - Modern Section Views

    private var filterModeSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header with icon
            sectionHeader(
                icon: "graduationcap.fill",
                title: "College Discovery",
                subtitle: "Find roommates at your college"
            )

            // Modern segmented picker
            filterModeButtons
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var filterModeButtons: some View {
        HStack(spacing: 0) {
            ForEach(FilterMode.allCases, id: \.self) { mode in
                filterModeButton(for: mode)
            }
        }
    }

    private func filterModeButton(for mode: FilterMode) -> some View {
        let isSelected = viewModel.filterMode == mode
        let title = "By College" // Only college mode now

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterMode = mode
            autoApplyAndSave()
        }) {
            Text(title)
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                .padding(.vertical, AppTheme.spacing12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var gradeAndHousingSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "graduationcap.fill",
                title: "Academic & Housing",
                subtitle: "Find compatible matches based on your needs"
            )

            VStack(spacing: AppTheme.spacing16) {
                gradeLevelPicker
                housingPreferencePicker
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    // MARK: - Mandatory Filter Components

    private var collegeNamePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Text("College")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Text("REQUIRED")
                    .font(.custom("AvenirNext-Bold", size: 10))
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(4)
            }

            CollegeSearchField(
                selectedCollege: Binding(
                    get: { viewModel.filterCollegeName },
                    set: { newValue in
                        viewModel.filterCollegeName = newValue
                        autoApplyAndSave()
                    }
                ),
                label: "",
                placeholder: "Search for your college"
            )
        }
    }

    private var preferredGenderPicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Text("Preferred Gender")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Text("REQUIRED")
                    .font(.custom("AvenirNext-Bold", size: 10))
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(4)
            }

            VStack(spacing: AppTheme.spacing8) {
                ForEach(preferredGenders, id: \.self) { gender in
                    genderOptionButton(for: gender)
                }
            }
        }
    }

    private func genderOptionButton(for gender: String) -> some View {
        let isSelected = viewModel.filterPreferredGender == gender

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterPreferredGender = gender
            autoApplyAndSave()
        }) {
            HStack {
                Text(gender)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var maxAgeDifferencePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Text("Max Age Difference")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Text("REQUIRED")
                    .font(.custom("AvenirNext-Bold", size: 10))
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(4)

                Spacer()

                Text("\(Int(viewModel.maxAgeDifference)) years")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Slider(value: $viewModel.maxAgeDifference, in: 0...10, step: 1)
                .tint(.white)
                .onChange(of: viewModel.maxAgeDifference) { _ in
                    autoApplyAndSave()
                }
        }
    }

    // MARK: - Optional Filter Components

    private var gradeLevelPicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Grade Level")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            Menu {
                Button("Any") {
                    HapticFeedbackManager.shared.generateImpact(style: .light)
                    viewModel.filterGradeGroup = ""
                    autoApplyAndSave()
                }

                ForEach(MyProfileView.GradeLevel.allCases) { level in
                    Button(level.rawValue) {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        viewModel.filterGradeGroup = level.rawValue
                        autoApplyAndSave()
                    }
                }
            } label: {
                HStack {
                    Text(viewModel.filterGradeGroup.isEmpty ? "Any Grade" : viewModel.filterGradeGroup)
                        .font(.custom("AvenirNext-Medium", size: 16))
                        .foregroundColor(.white)

                    Spacer()

                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
        }
    }

    private var roomTypePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Room Type")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            let roomTypes = ["Any", "Single", "Double", "Triple", "Suite"]
            VStack(spacing: AppTheme.spacing8) {
                ForEach(roomTypes, id: \.self) { roomType in
                    roomTypeButton(for: roomType)
                }
            }
        }
    }

    private func roomTypeButton(for roomType: String) -> some View {
        let isSelected = (roomType == "Any" && viewModel.filterRoomType.isEmpty) ||
                       viewModel.filterRoomType == roomType

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterRoomType = roomType == "Any" ? "" : roomType
            autoApplyAndSave()
        }) {
            HStack {
                Text(roomType)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    // MARK: - Optional Filter Subsections

    private var amenitiesSubsection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            Text("Amenities")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            let amenityOptions = ["Gym", "Pool", "Laundry", "Parking", "WiFi", "Kitchen", "Study Room"]
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: AppTheme.spacing8) {
                ForEach(amenityOptions, id: \.self) { amenity in
                    amenityToggleButton(for: amenity)
                }
            }
        }
    }

    private func amenityToggleButton(for amenity: String) -> some View {
        let isSelected = viewModel.filterAmenities.contains(amenity)

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            if isSelected {
                viewModel.filterAmenities.removeAll { $0 == amenity }
            } else {
                viewModel.filterAmenities.append(amenity)
            }
            autoApplyAndSave()
        }) {
            HStack {
                Text(amenity)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 14, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing8)
            .padding(.horizontal, AppTheme.spacing12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var lifestyleSubsection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            Text("Lifestyle Preferences")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            VStack(spacing: AppTheme.spacing8) {
                lifestyleToggle(title: "Pet Friendly", binding: $viewModel.filterPetFriendly)
                lifestyleToggle(title: "Smoker Friendly", binding: $viewModel.filterSmoker)
                lifestyleToggle(title: "Drinking Friendly", binding: $viewModel.filterDrinker)
                lifestyleToggle(title: "Cannabis Friendly", binding: $viewModel.filterMarijuana)
                lifestyleToggle(title: "Workout Partner", binding: $viewModel.filterWorkout)
            }
        }
    }

    private func lifestyleToggle(title: String, binding: Binding<Bool?>) -> some View {
        HStack {
            Text(title)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white)

            Spacer()

            HStack(spacing: AppTheme.spacing8) {
                ForEach(["Any", "Yes", "No"], id: \.self) { option in
                    let isSelected = (option == "Any" && binding.wrappedValue == nil) ||
                                   (option == "Yes" && binding.wrappedValue == true) ||
                                   (option == "No" && binding.wrappedValue == false)

                    Button(option) {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        switch option {
                        case "Any": binding.wrappedValue = nil
                        case "Yes": binding.wrappedValue = true
                        case "No": binding.wrappedValue = false
                        default: break
                        }
                        autoApplyAndSave()
                    }
                    .font(.custom("AvenirNext-Medium", size: 12))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
        }
        .padding(.vertical, AppTheme.spacing8)
        .padding(.horizontal, AppTheme.spacing12)
        .background(modernInputBackground)
    }

    private var cleanlinessAndSleepSubsection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            Text("Living Habits")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            VStack(spacing: AppTheme.spacing12) {
                // Cleanliness Level
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    HStack {
                        Text("Cleanliness Level")
                            .font(.custom("AvenirNext-Medium", size: 14))
                            .foregroundColor(.white)

                        Spacer()

                        if let cleanliness = viewModel.filterCleanliness {
                            Text("\(cleanliness)/10")
                                .font(.custom("AvenirNext-Medium", size: 12))
                                .foregroundColor(.white.opacity(0.8))
                        } else {
                            Text("Any")
                                .font(.custom("AvenirNext-Medium", size: 12))
                                .foregroundColor(.white.opacity(0.8))
                        }
                    }

                    HStack(spacing: AppTheme.spacing8) {
                        Button("Any") {
                            HapticFeedbackManager.shared.generateImpact(style: .light)
                            viewModel.filterCleanliness = nil
                            autoApplyAndSave()
                        }
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(viewModel.filterCleanliness == nil ? .white : .white.opacity(0.7))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(viewModel.filterCleanliness == nil ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(.white.opacity(0.3), lineWidth: 1)
                                )
                        )

                        if viewModel.filterCleanliness != nil {
                            Slider(value: Binding(
                                get: { Double(viewModel.filterCleanliness ?? 5) },
                                set: { viewModel.filterCleanliness = Int($0) }
                            ), in: 1...10, step: 1)
                            .tint(.white)
                            .onChange(of: viewModel.filterCleanliness) { _ in
                                autoApplyAndSave()
                            }
                        }
                    }
                }

                // Sleep Schedule
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    Text("Sleep Schedule")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white)

                    let sleepOptions = ["Any", "Early Bird", "Night Owl", "Flexible"]
                    HStack(spacing: AppTheme.spacing8) {
                        ForEach(sleepOptions, id: \.self) { option in
                            let isSelected = (option == "Any" && viewModel.filterSleepSchedule.isEmpty) ||
                                           viewModel.filterSleepSchedule == option

                            Button(option) {
                                HapticFeedbackManager.shared.generateImpact(style: .light)
                                viewModel.filterSleepSchedule = option == "Any" ? "" : option
                                autoApplyAndSave()
                            }
                            .font(.custom("AvenirNext-Medium", size: 12))
                            .foregroundColor(isSelected ? .white : .white.opacity(0.7))
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 6)
                                            .stroke(.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                    }
                }
            }
        }
        .padding(AppTheme.spacing12)
        .background(modernInputBackground)
    }

    private var housingPreferencePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            HStack {
                Text("Housing Status")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Text("REQUIRED")
                    .font(.custom("AvenirNext-Bold", size: 10))
                    .foregroundColor(.red)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(4)
            }

            // Helpful explanation section
            housingMatchingExplanation

            VStack(spacing: AppTheme.spacing8) {
                ForEach([Optional<PrimaryHousingPreference>(nil)] + PrimaryHousingPreference.allCases.map(Optional.init), id: \.self) { option in
                    housingOptionButton(for: option)
                }
            }
        }
    }

    private func housingOptionButton(for option: PrimaryHousingPreference?) -> some View {
        let isSelected = (option == nil && viewModel.showAllProfiles) ||
                       (!viewModel.showAllProfiles && viewModel.filterHousingPreference == option)
        let title = option?.rawValue ?? "All"

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            if option == nil {
                viewModel.showAllProfiles = true
                viewModel.filterHousingPreference = nil
            } else {
                viewModel.showAllProfiles = false
                viewModel.filterHousingPreference = option
            }
            autoApplyAndSave()
        }) {
            HStack {
                Text(title)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var amenitiesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header with count
            amenitiesSectionHeader

            VStack(spacing: AppTheme.spacing16) {
                // Amenities chips
                EnhancedMultiSelectChipView(
                    options: viewModel.propertyAmenitiesOptions,
                    selectedItems: $viewModel.filterAmenities,
                    onSelectionChanged: {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        autoApplyAndSave()
                    }
                )

                // Lifestyle toggles
                lifestyleToggles
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var amenitiesSectionHeader: some View {
        HStack {
            Image(systemName: "star.circle.fill")
                .font(.system(size: 24, weight: .bold))
                .foregroundStyle(AppTheme.sexyGradient)

            VStack(alignment: .leading, spacing: 4) {
                Text("Amenities & Lifestyle")
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Text("Property features and lifestyle preferences")
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()

            Text("\(viewModel.filterAmenities.count) selected")
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(.white.opacity(0.7))
        }
    }

    private var lifestyleToggles: some View {
        VStack(spacing: AppTheme.spacing12) {
            EnhancedToggleView(
                title: "Pet Friendly",
                subtitle: "Must allow pets",
                icon: "pawprint.fill",
                isOn: Binding(
                    get: { viewModel.filterPetFriendly ?? false },
                    set: {
                        viewModel.filterPetFriendly = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Smoking Friendly",
                subtitle: "Comfortable with smoking environment",
                icon: "smoke.fill",
                isOn: Binding(
                    get: { viewModel.filterSmoker ?? false },
                    set: {
                        viewModel.filterSmoker = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Social Drinking",
                subtitle: "Comfortable with social drinking",
                icon: "wineglass.fill",
                isOn: Binding(
                    get: { viewModel.filterDrinker ?? false },
                    set: {
                        viewModel.filterDrinker = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "420 Friendly",
                subtitle: "Comfortable with cannabis environment",
                icon: "leaf.fill",
                isOn: Binding(
                    get: { viewModel.filterMarijuana ?? false },
                    set: {
                        viewModel.filterMarijuana = $0
                        autoApplyAndSave()
                    }
                )
            )

            EnhancedToggleView(
                title: "Workout Regularly",
                subtitle: "Active fitness lifestyle",
                icon: "dumbbell.fill",
                isOn: Binding(
                    get: { viewModel.filterWorkout ?? false },
                    set: {
                        viewModel.filterWorkout = $0
                        autoApplyAndSave()
                    }
                )
            )
        }
    }

    private var lifestyleSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "moon.stars.fill",
                title: "Lifestyle Preferences",
                subtitle: "Cleanliness and sleep schedule preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                cleanlinessPicker
                sleepSchedulePicker
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var cleanlinessPicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Cleanliness Level")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            Menu {
                ForEach(1..<6) { level in
                    Button("\(level) – \(viewModel.cleanlinessDescriptions[level] ?? "")") {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                        viewModel.filterCleanliness = level
                        autoApplyAndSave()
                    }
                }
            } label: {
                HStack {
                    if let cleanliness = viewModel.filterCleanliness, cleanliness > 0 {
                        Text("\(cleanliness) – \(viewModel.cleanlinessDescriptions[cleanliness] ?? "")")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white)
                    } else {
                        Text("Select Cleanliness Level")
                            .font(.custom("AvenirNext-Medium", size: 16))
                            .foregroundColor(.white.opacity(0.7))
                    }

                    Spacer()

                    Image(systemName: "chevron.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(AppTheme.spacing12)
                .background(modernInputBackground)
            }
        }
    }

    private var sleepSchedulePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Sleep Schedule")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            let sleepOptions = ["All", "Early Bird", "Night Owl", "Flexible"]
            VStack(spacing: AppTheme.spacing8) {
                ForEach(sleepOptions, id: \.self) { option in
                    sleepScheduleButton(for: option)
                }
            }
        }
    }

    private func sleepScheduleButton(for option: String) -> some View {
        let isSelected = (option == "All" && viewModel.filterSleepSchedule.isEmpty) ||
                       viewModel.filterSleepSchedule == option

        return Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            viewModel.filterSleepSchedule = option == "All" ? "" : option
            autoApplyAndSave()
        }) {
            HStack {
                Text(option)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(isSelected ? .white : .white.opacity(0.8))

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                }
            }
            .padding(.vertical, AppTheme.spacing12)
            .padding(.horizontal, AppTheme.spacing16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(Color.clear))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private var locationSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            HStack {
                Image(systemName: "location.circle.fill")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.white)

                VStack(alignment: .leading, spacing: 4) {
                    Text("College & Housing")
                        .font(.custom("AvenirNext-Bold", size: 20))
                        .foregroundColor(.white)

                    Text("College and housing preferences")
                        .font(.custom("AvenirNext-Medium", size: 14))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()
            }

            VStack(spacing: AppTheme.spacing16) {
                // College search (if university mode)
                if viewModel.filterMode == .university {
                    VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                        Text("College")
                            .font(.custom("AvenirNext-Bold", size: 16))
                            .foregroundColor(.white)

                        CollegeSearchField(
                            selectedCollege: Binding(
                                get: { viewModel.filterCollegeName },
                                set: { newValue in
                                    viewModel.filterCollegeName = newValue
                                    autoApplyAndSave()
                                }
                            ),
                            label: "",
                            placeholder: "Search for your college"
                        )
                    }
                }

                // Distance slider REMOVED - college-only filtering now
            }
        }
        .padding(AppTheme.spacing20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
        .modernCardStyle()
    }

    private var preferencesSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing16) {
            // Section header
            sectionHeader(
                icon: "person.2.circle.fill",
                title: "Personal Preferences",
                subtitle: "Gender and age preferences"
            )

            VStack(spacing: AppTheme.spacing16) {
                genderPreferencePicker
                ageDifferenceSlider
            }
        }
        .padding(AppTheme.spacing20)
        .background(modernCardBackground)
        .modernCardStyle()
    }

    private var genderPreferencePicker: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            Text("Preferred Gender")
                .font(.custom("AvenirNext-Bold", size: 16))
                .foregroundColor(.white)

            VStack(spacing: AppTheme.spacing8) {
                ForEach(preferredGenders, id: \.self) { gender in
                    genderOptionButton(for: gender)
                }
            }
        }
    }



    private var ageDifferenceSlider: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack {
                Text("Max Age Difference")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)

                Spacer()

                Text("\(Int(viewModel.maxAgeDifference)) years")
                    .font(.custom("AvenirNext-Bold", size: 16))
                    .foregroundColor(.white)
                    .padding(.horizontal, AppTheme.spacing12)
                    .padding(.vertical, AppTheme.spacing6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(AppTheme.sexyGradient)
                    )
            }

            EnhancedSlider(
                value: $viewModel.maxAgeDifference,
                range: 0...10,
                step: 1,
                onEditingChanged: { _ in
                    autoApplyAndSave()
                }
            )
        }
    }

    // MARK: - Helper Methods

    private func sectionHeader(icon: String, title: String, subtitle: String) -> some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.custom("AvenirNext-Bold", size: 20))
                    .foregroundColor(.white)

                Text(subtitle)
                    .font(.custom("AvenirNext-Medium", size: 14))
                    .foregroundColor(.white.opacity(0.8))
            }

            Spacer()
        }
    }

    private var modernCardBackground: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }

    private var modernInputBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(.ultraThinMaterial.opacity(0.6))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(.white.opacity(0.3), lineWidth: 1)
            )
    }

    // MARK: - Housing Matching Explanation

    private var housingMatchingExplanation: some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing12) {
            // Header with info icon
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: "info.circle.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))

                VStack(alignment: .leading, spacing: 2) {
                    Text("How Matching Works")
                        .font(.custom("AvenirNext-Bold", size: 14))
                        .foregroundColor(.white.opacity(0.9))

                    Text("Filter for people who complement your housing situation")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()
            }

            // Dynamic current selection highlight
            if let currentFilter = viewModel.filterHousingPreference {
                currentSelectionCard(for: currentFilter)
            }

            // Explanation cards
            VStack(spacing: AppTheme.spacing8) {
                housingExplanationCard(
                    yourStatus: "Looking for Lease",
                    filterFor: "Looking for Roommate",
                    explanation: "You need a place → Find people with rooms to offer",
                    icon: "key.fill",
                    color: .blue,
                    isHighlighted: viewModel.filterHousingPreference == .lookingForRoommate
                )

                housingExplanationCard(
                    yourStatus: "Looking for Roommate",
                    filterFor: "Looking for Lease",
                    explanation: "You have a place → Find people who need rooms",
                    icon: "person.2.fill",
                    color: .green,
                    isHighlighted: viewModel.filterHousingPreference == .lookingForLease
                )

                housingExplanationCard(
                    yourStatus: "Looking to Find Together",
                    filterFor: "Looking to Find Together",
                    explanation: "You want to search together → Find like-minded people",
                    icon: "heart.fill",
                    color: .purple,
                    isHighlighted: viewModel.filterHousingPreference == .lookingToFindTogether
                )
            }
        }
        .padding(AppTheme.spacing16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial.opacity(0.4))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.white.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private func currentSelectionCard(for filter: PrimaryHousingPreference) -> some View {
        VStack(alignment: .leading, spacing: AppTheme.spacing8) {
            HStack(spacing: AppTheme.spacing8) {
                Image(systemName: "target")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(.white)

                Text("Currently Filtering For:")
                    .font(.custom("AvenirNext-Bold", size: 14))
                    .foregroundColor(.white)

                Spacer()
            }

            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: iconForFilter(filter))
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(colorForFilter(filter))

                VStack(alignment: .leading, spacing: 2) {
                    Text(filter.rawValue)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)

                    Text(explanationForFilter(filter))
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()
            }
        }
        .padding(AppTheme.spacing12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppTheme.sexyGradient)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.white.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private func iconForFilter(_ filter: PrimaryHousingPreference) -> String {
        switch filter {
        case .lookingForLease: return "key.fill"
        case .lookingForRoommate: return "person.2.fill"
        case .lookingToFindTogether: return "heart.fill"
        }
    }

    private func colorForFilter(_ filter: PrimaryHousingPreference) -> Color {
        switch filter {
        case .lookingForLease: return .blue
        case .lookingForRoommate: return .green
        case .lookingToFindTogether: return .purple
        }
    }

    private func explanationForFilter(_ filter: PrimaryHousingPreference) -> String {
        switch filter {
        case .lookingForLease: return "Finding people who need a place to live"
        case .lookingForRoommate: return "Finding people who have rooms available"
        case .lookingToFindTogether: return "Finding people to search for housing together"
        }
    }

    private func housingExplanationCard(
        yourStatus: String,
        filterFor: String,
        explanation: String,
        icon: String,
        color: Color,
        isHighlighted: Bool = false
    ) -> some View {
        HStack(spacing: AppTheme.spacing12) {
            // Icon
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text("Your Profile:")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))

                    Text(yourStatus)
                        .font(.custom("AvenirNext-Bold", size: 12))
                        .foregroundColor(.white)
                }

                HStack {
                    Text("Filter for:")
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))

                    Text(filterFor)
                        .font(.custom("AvenirNext-Bold", size: 12))
                        .foregroundColor(color)
                }

                Text(explanation)
                    .font(.custom("AvenirNext-Medium", size: 11))
                    .foregroundColor(.white.opacity(0.6))
                    .lineLimit(2)
            }

            Spacer()
        }
        .padding(.vertical, AppTheme.spacing8)
        .padding(.horizontal, AppTheme.spacing12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isHighlighted ? AnyShapeStyle(color.opacity(0.2)) : AnyShapeStyle(.ultraThinMaterial.opacity(0.3)))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isHighlighted ? color : color.opacity(0.3), lineWidth: isHighlighted ? 2 : 1)
                )
        )
        .scaleEffect(isHighlighted ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isHighlighted)
    }
}

// MARK: - Enhanced UI Components

struct EnhancedMultiSelectChipView: View {
    let options: [String]
    @Binding var selectedItems: [String]
    var onSelectionChanged: () -> Void = {}
    var maxSelection: Int? = nil

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 120), spacing: AppTheme.spacing8)
        ], spacing: AppTheme.spacing8) {
            ForEach(options, id: \.self) { option in
                chipView(option)
            }
        }
    }

    private func chipView(_ item: String) -> some View {
        let isSelected = selectedItems.contains(item)
        let isDisabled = !isSelected && (maxSelection != nil && selectedItems.count >= maxSelection!)

        return Button(action: {
            if isDisabled { return }
            HapticFeedbackManager.shared.generateImpact(style: .light)
            if isSelected {
                selectedItems.removeAll { $0 == item }
            } else {
                selectedItems.append(item)
            }
            onSelectionChanged()
        }) {
            Text(item)
                .font(.custom("AvenirNext-Medium", size: 14))
                .foregroundColor(isSelected ? .white : .white.opacity(0.8))
                .padding(.vertical, AppTheme.spacing8)
                .padding(.horizontal, AppTheme.spacing12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(isSelected ? Color.clear : .white.opacity(0.2), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .opacity(isDisabled ? 0.5 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct EnhancedToggleView: View {
    let title: String
    let subtitle: String
    let icon: String
    @Binding var isOn: Bool

    var body: some View {
        Button(action: {
            HapticFeedbackManager.shared.generateImpact(style: .light)
            isOn.toggle()
        }) {
            HStack(spacing: AppTheme.spacing12) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(isOn ? .white : .white.opacity(0.6))
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(isOn ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.custom("AvenirNext-Bold", size: 16))
                        .foregroundColor(.white)

                    Text(subtitle)
                        .font(.custom("AvenirNext-Medium", size: 12))
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isOn ? AnyShapeStyle(AppTheme.sexyGradient) : AnyShapeStyle(.ultraThinMaterial.opacity(0.4)))
                        .frame(width: 50, height: 30)

                    Circle()
                        .fill(.white)
                        .frame(width: 24, height: 24)
                        .offset(x: isOn ? 10 : -10)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isOn)
                }
            }
            .padding(AppTheme.spacing16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial.opacity(0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isOn ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isOn)
    }
}

struct EnhancedSlider: View {
    @Binding var value: Double
    let range: ClosedRange<Double>
    let step: Double
    var onEditingChanged: ((Bool) -> Void)? = nil

    @State private var isDragging = false

    var body: some View {
        VStack(spacing: AppTheme.spacing8) {
            Slider(
                value: $value,
                in: range,
                step: step,
                onEditingChanged: { editing in
                    isDragging = editing
                    onEditingChanged?(editing)
                    if editing {
                        HapticFeedbackManager.shared.generateImpact(style: .light)
                    }
                }
            )
            .accentColor(.white)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial.opacity(0.4))
                    .frame(height: 8)
            )
            .scaleEffect(isDragging ? 1.02 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isDragging)
        }
    }
}
