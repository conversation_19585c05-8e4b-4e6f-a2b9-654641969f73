import SwiftUI

struct ChatsListView: View {
    @StateObject private var viewModel = ChatsListViewModel()
    @ObservedObject private var profileLoader = ProfileLoaderService.shared

    var body: some View {
        NavigationView {
            ZStack {
                AppTheme.backgroundGradient.ignoresSafeArea()

                Group {
                    if viewModel.isLoading {
                        loadingView
                    } else if viewModel.chats.isEmpty {
                        emptyStateView
                    } else {
                        chatListContent
                    }
                }
                .toolbar {
                    ToolbarItem(placement: .principal) {
                        Text("My Chats")
                            .font(AppTheme.titleFont)
                            .foregroundColor(.primary)
                    }
                }
                .onAppear {
                    print("💬 ChatsListView: View appeared, fetching chats")
                    viewModel.fetchChats()
                }
                .alert(item: Binding(
                    get: {
                        if let errorMessage = viewModel.errorMessage {
                            return GenericAlertError(message: errorMessage)
                        }
                        return nil
                    },
                    set: { _ in viewModel.errorMessage = nil }
                )) { alertError in
                    Alert(title: Text("Error"),
                          message: Text(alertError.message),
                          dismissButton: .default(Text("OK")))
                }
            }
        }
    }

    // MARK: - Subviews

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppTheme.primaryColor)

            Text("Loading conversations...")
                .font(AppTheme.bodyFont)
                .foregroundColor(AppTheme.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyStateView: some View {
        VStack(spacing: AppTheme.spacing32) {
            // Enhanced animated message icon
            ZStack {
                Circle()
                    .fill(AppTheme.primaryGradient)
                    .frame(width: 120, height: 120)
                    .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 20, x: 0, y: 10)

                Image(systemName: "message.circle.fill")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.white)
            }
            .scaleEffect(1.0)
            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: UUID())

            VStack(spacing: AppTheme.spacing16) {
                Text("No Conversations Yet")
                    .font(AppTheme.title1)
                    .fontWeight(.bold)
                    .foregroundColor(AppTheme.textPrimary)

                Text("Connect with your matches and start meaningful conversations about roommate compatibility!")
                    .font(AppTheme.body)
                    .foregroundColor(AppTheme.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.spacing32)
            }

            Button(action: {
                // Navigate to swipe view - will be handled by parent navigation
            }) {
                HStack(spacing: AppTheme.spacing12) {
                    Image(systemName: "heart.circle.fill")
                        .font(.system(size: 20, weight: .semibold))
                    Text("Find Matches")
                        .font(.system(size: 18, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, AppTheme.spacing32)
                .padding(.vertical, AppTheme.spacing16)
                .sexyButtonStyle()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(AppTheme.spacing24)
    }

    private var chatListContent: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.chats) { chat in
                    ChatListCard(
                        chat: chat,
                        currentUserID: viewModel.currentUserID ?? "",
                        profileLoader: profileLoader
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
        }
        .refreshable {
            print("💬 ChatsListView: Pull to refresh triggered")
            viewModel.fetchChats()
        }
    }
}

// MARK: - Chat List Card Component

struct ChatListCard: View {
    let chat: ChatListItem
    let currentUserID: String
    let profileLoader: ProfileLoaderService

    @State private var isPressed = false
    @State private var otherUserProfile: UserModel?
    @State private var isLoadingProfile = false

    var body: some View {
        NavigationLink(destination: destinationView) {
            HStack(spacing: AppTheme.spacing16) {
                // Enhanced Profile Image with online indicator
                ZStack {
                    profileImageView

                    // Online status indicator
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Circle()
                                .fill(AppTheme.successColor)
                                .frame(width: 16, height: 16)
                                .overlay(
                                    Circle()
                                        .stroke(AppTheme.cardBackground, lineWidth: 2)
                                )
                        }
                    }
                }

                // Enhanced Chat Content
                VStack(alignment: .leading, spacing: AppTheme.spacing8) {
                    // Name and timestamp with better typography
                    HStack {
                        Text(displayName)
                            .font(AppTheme.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(AppTheme.textPrimary)

                        Spacer()

                        Text(timeAgoString)
                            .font(AppTheme.caption)
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    // Last message preview with enhanced styling
                    HStack {
                        Text(lastMessagePreview)
                            .font(AppTheme.subheadline)
                            .foregroundColor(unreadCount > 0 ? AppTheme.textPrimary : AppTheme.textSecondary)
                            .fontWeight(unreadCount > 0 ? .medium : .regular)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)

                        Spacer()

                        // Enhanced unread indicator
                        if unreadCount > 0 {
                            ZStack {
                                Circle()
                                    .fill(AppTheme.primaryGradient)
                                    .frame(width: 24, height: 24)

                                Text("\(unreadCount)")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 4, x: 0, y: 2)
                        }
                    }
                }
            }
            .padding(AppTheme.spacing20)
            .background(
                ZStack {
                    // Glass morphism background
                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.modernCardGradient)

                    RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                        .fill(AppTheme.glassEffect)
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.radiusLarge)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                unreadCount > 0 ? AppTheme.primaryColor.opacity(0.5) : Color.white.opacity(0.1),
                                Color.clear
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .floatingCard()
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: AppTheme.animationFast), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            HapticFeedbackManager.shared.generateImpact(style: .light)
        }
        .onAppear {
            loadOtherUserProfile()
        }
    }

    // MARK: - Computed Properties

    private var otherUserID: String? {
        chat.getOtherParticipant(currentUserID: currentUserID)
    }

    private var unreadCount: Int {
        chat.getUnreadCount(for: currentUserID)
    }

    private var lastMessagePreview: String {
        if chat.lastMessage.isEmpty {
            return "No messages yet"
        }

        let isFromCurrentUser = chat.lastMessageSenderID == currentUserID
        let prefix = isFromCurrentUser ? "You: " : ""
        return prefix + chat.lastMessage
    }

    private var timeAgoString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: chat.lastMessageAt, relativeTo: Date())
    }

    private var displayName: String {
        if isLoadingProfile {
            return "Loading..."
        }
        return otherUserProfile?.firstName ?? "Unknown"
    }

    private var profileImageView: some View {
        Group {
            if let profileImageUrl = otherUserProfile?.profileImageUrls?.first,
               let url = URL(string: profileImageUrl) {
                AsyncImage(url: url) { image in
                    image
                        .resizable()
                        .scaledToFill()
                } placeholder: {
                    ZStack {
                        Circle()
                            .fill(AppTheme.sexyGradient)

                        ProgressView()
                            .tint(.white)
                            .scaleEffect(0.8)
                    }
                    .shimmerEffect()
                }
            } else {
                ZStack {
                    Circle()
                        .fill(AppTheme.sexyGradient)

                    Image(systemName: "person.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.white)
                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                }
                .shimmerEffect()
            }
        }
        .frame(width: 60, height: 60)
        .clipShape(Circle())
        .overlay(
            Circle()
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white.opacity(0.6),
                            AppTheme.primaryColor.opacity(0.3),
                            Color.white.opacity(0.2)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 2
                )
        )
        .neonGlow(color: AppTheme.primaryColor)
    }

    private var destinationView: some View {
        Group {
            if let otherUserID = otherUserID {
                ChatConversationView(
                    viewModel: ChatConversationViewModel(chatID: chat.id),
                    chatPartnerID: otherUserID
                )
            } else {
                Text("Error: Unable to load chat")
                    .foregroundColor(.red)
            }
        }
    }

    // MARK: - Methods

    private func loadOtherUserProfile() {
        guard let otherUserID = otherUserID else { return }

        // Check if profile is already cached
        if let cachedProfile = ProfileLoaderService.shared.getCachedProfile(userID: otherUserID) {
            print("✅ ChatListCard: Using cached profile for user: \(cachedProfile.firstName ?? "Unknown")")
            DispatchQueue.main.async {
                self.otherUserProfile = cachedProfile
                self.isLoadingProfile = false
            }
        } else {
            print("🔄 ChatListCard: Loading profile for user: \(otherUserID)")
            isLoadingProfile = true

            // Load profile asynchronously using the correct method
            ProfileLoaderService.shared.loadUserProfile(userID: otherUserID) { result in
                DispatchQueue.main.async {
                    self.isLoadingProfile = false
                    switch result {
                    case .success(let profile):
                        print("✅ ChatListCard: Successfully loaded profile for \(profile.firstName ?? "Unknown")")
                        self.otherUserProfile = profile
                    case .failure(let error):
                        print("❌ ChatListCard: Failed to load profile: \(error)")
                        // Keep showing "Unknown" for failed loads
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Types
// GenericAlertError is now imported from GenericAlertError.swift

struct ChatsListView_Previews: PreviewProvider {
    static var previews: some View {
        ChatsListView()
    }
}
