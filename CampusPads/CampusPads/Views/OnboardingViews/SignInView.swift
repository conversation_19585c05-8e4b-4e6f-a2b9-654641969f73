import SwiftUI

struct SignInView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Binding var showSignIn: Bool
    @State private var hasAttemptedSignIn: Bool = false
    @State private var showingPasswordReset = false

    // Computed property for form validity.
    private var isFormValid: Bool {
        return !authViewModel.email.isEmpty && !authViewModel.password.isEmpty
    }

    var body: some View {
        ScrollView {
                VStack(spacing: AppTheme.spacing24) {
                    Spacer(minLength: AppTheme.spacing40)

                    // Header
                    VStack(spacing: AppTheme.spacing8) {
                        Text("Welcome Back")
                            .font(AppTheme.title1)
                            .foregroundColor(AppTheme.textPrimary)

                        Text("Sign in to your account")
                            .font(AppTheme.body)
                            .foregroundColor(AppTheme.textSecondary)
                    }

                    // Form
                    VStack(spacing: AppTheme.spacing20) {
                        EnhancedTextField(
                            title: "Email",
                            text: $authViewModel.email,
                            placeholder: "Enter your email",
                            keyboardType: .emailAddress
                        )

                        EnhancedTextField(
                            title: "Password",
                            text: $authViewModel.password,
                            placeholder: "Enter your password",
                            isSecure: true,
                            errorMessage: hasAttemptedSignIn ? authViewModel.errorMessage : nil
                        )
                    }
                    .padding(.horizontal, AppTheme.spacing20)

                    // Sign In Button
                    Button {
                        hasAttemptedSignIn = true
                        authViewModel.signIn()
                    } label: {
                        if authViewModel.isLoading {
                            HStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                                Text("Signing In...")
                            }
                        } else {
                            Text("Sign In")
                        }
                    }
                    .buttonStyle(PrimaryButtonStyle(
                        backgroundColor: isFormValid ? AppTheme.primaryColor : AppTheme.primaryColor.opacity(0.5),
                        isLoading: authViewModel.isLoading
                    ))
                    .disabled(!isFormValid || authViewModel.isLoading)
                    .padding(.horizontal, AppTheme.spacing20)

                    // Links
                    VStack(spacing: AppTheme.spacing16) {
                        Button("Forgot password?") {
                            authViewModel.errorMessage = nil
                            showingPasswordReset = true
                        }
                        .buttonStyle(TertiaryButtonStyle(textColor: AppTheme.accentColor))

                        Button("Don't have an account? Sign Up") {
                            authViewModel.errorMessage = nil
                            authViewModel.password = ""
                            showSignIn = false
                        }
                        .buttonStyle(TertiaryButtonStyle())
                    }

                    Spacer(minLength: AppTheme.spacing40)
                }
            }
        .sheet(isPresented: $showingPasswordReset) {
            PasswordResetView(
                onDismiss: { showingPasswordReset = false }
            )
            .environmentObject(authViewModel)
        }
    }

    struct SignInView_Previews: PreviewProvider {
        static var previews: some View {
            NavigationView {
                SignInView(showSignIn: .constant(true))
                    .environmentObject(AuthViewModel())
            }
        }
    }
}
