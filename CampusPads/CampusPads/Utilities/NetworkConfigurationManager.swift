//
//  NetworkConfigurationManager.swift
//  CampusPads
//
//  SIMPLE: Basic network configuration
//

import Foundation

/// SIMPLE: Basic network configuration - just works
class NetworkConfigurationManager {
    static let shared = NetworkConfigurationManager()

    private init() {}

    /// SIMPLE: Create basic URLSession configuration optimized for Firebase
    func createBasicConfiguration() -> URLSessionConfiguration {
        let config = URLSessionConfiguration.default

        // CRITICAL: Conservative timeouts to prevent QUIC issues
        config.timeoutIntervalForRequest = 10.0
        config.timeoutIntervalForResource = 30.0

        // CRITICAL: Force HTTP/1.1 and disable HTTP/3
        config.httpMaximumConnectionsPerHost = 1
        config.httpShouldUsePipelining = false
        config.waitsForConnectivity = true

        // CRITICAL: Disable caching to prevent stale data issues
        config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        config.urlCache = nil

        // CRITICAL: Force HTTP/1.1 headers
        config.httpAdditionalHeaders = [
            "Connection": "close",
            "HTTP-Version": "HTTP/1.1",
            "User-Agent": "CampusPads/1.0 HTTP1-Only",
            "Accept-Encoding": "identity"
        ]

        print("✅ NetworkConfigurationManager: Created conservative HTTP/1.1-only configuration")
        return config
    }
}
