//
//  PushNotificationService.swift
//  CampusPads
//
//  Centralized push notification service for matches, messages, and other events
//

import Foundation
import FirebaseFirestore
import FirebaseAuth
import UserNotifications

/// Centralized service for managing push notifications
class PushNotificationService: ObservableObject {
    static let shared = PushNotificationService()
    
    private let db = Firestore.firestore()
    
    private init() {}
    
    // MARK: - FCM Token Management
    
    /// Update FCM token for current user
    func updateFCMToken(_ token: String) {
        guard let userID = Auth.auth().currentUser?.uid else {
            print("❌ PushNotificationService: No authenticated user for FCM token update")
            return
        }
        
        print("🔄 PushNotificationService: Updating FCM token for user: \(userID)")
        
        db.collection("users").document(userID).updateData([
            "fcmToken": token,
            "notificationsEnabled": true,
            "lastTokenUpdate": FieldValue.serverTimestamp()
        ]) { error in
            if let error = error {
                print("❌ PushNotificationService: Failed to update FCM token: \(error.localizedDescription)")
            } else {
                print("✅ PushNotificationService: Successfully updated FCM token")
            }
        }
    }
    
    /// Get FCM token for a specific user
    func getFCMToken(for userID: String, completion: @escaping (String?) -> Void) {
        db.collection("users").document(userID).getDocument { snapshot, error in
            if let error = error {
                print("❌ PushNotificationService: Error getting FCM token: \(error.localizedDescription)")
                completion(nil)
                return
            }
            
            let token = snapshot?.data()?["fcmToken"] as? String
            completion(token)
        }
    }
    
    // MARK: - Match Notifications
    
    /// Send notification when a new match is created
    func sendMatchNotification(to userID: String, matchedUserName: String, matchType: MatchType = .regular) {
        print("💕 PushNotificationService: Sending match notification to \(userID)")
        
        getFCMToken(for: userID) { [weak self] token in
            guard let token = token else {
                print("❌ PushNotificationService: No FCM token found for user \(userID)")
                return
            }
            
            let notificationData: [String: Any] = [
                "to": token,
                "notification": [
                    "title": "🎉 New Match!",
                    "body": "You matched with \(matchedUserName)! Start chatting now.",
                    "sound": "default",
                    "badge": 1
                ],
                "data": [
                    "type": "new_match",
                    "matchedUserID": userID,
                    "matchType": matchType.rawValue,
                    "action": "open_matches"
                ]
            ]
            
            self?.sendNotificationToFirebase(data: notificationData)
        }
    }
    
    // MARK: - Message Notifications
    
    /// Send notification for new message
    func sendMessageNotification(to userID: String, from senderName: String, message: String, chatID: String) {
        print("💬 PushNotificationService: Sending message notification to \(userID)")
        
        getFCMToken(for: userID) { [weak self] token in
            guard let token = token else {
                print("❌ PushNotificationService: No FCM token found for user \(userID)")
                return
            }
            
            // Create short, sexy notification text
            let shortMessage = self?.createShortMessage(message) ?? "New message"
            
            let notificationData: [String: Any] = [
                "to": token,
                "notification": [
                    "title": senderName,
                    "body": shortMessage,
                    "sound": "default",
                    "badge": 1
                ],
                "data": [
                    "type": "new_message",
                    "chatID": chatID,
                    "senderID": senderName,
                    "action": "open_chat"
                ]
            ]
            
            self?.sendNotificationToFirebase(data: notificationData)
        }
    }
    
    // MARK: - Super Like Notifications
    
    /// Send notification when someone super likes you
    func sendSuperLikeNotification(to userID: String, from senderName: String) {
        print("⭐ PushNotificationService: Sending super like notification to \(userID)")
        
        getFCMToken(for: userID) { [weak self] token in
            guard let token = token else {
                print("❌ PushNotificationService: No FCM token found for user \(userID)")
                return
            }
            
            let notificationData: [String: Any] = [
                "to": token,
                "notification": [
                    "title": "⭐ Super Like!",
                    "body": "\(senderName) super liked you! Check them out.",
                    "sound": "default",
                    "badge": 1
                ],
                "data": [
                    "type": "super_like",
                    "senderName": senderName,
                    "action": "open_discover"
                ]
            ]
            
            self?.sendNotificationToFirebase(data: notificationData)
        }
    }
    
    // MARK: - Helper Methods
    
    /// Create short, engaging message preview
    private func createShortMessage(_ fullMessage: String) -> String {
        let maxLength = 50
        
        if fullMessage.count <= maxLength {
            return fullMessage
        }
        
        // Smart truncation - try to end at word boundary
        let truncated = String(fullMessage.prefix(maxLength))
        if let lastSpace = truncated.lastIndex(of: " ") {
            return String(truncated[..<lastSpace]) + "..."
        }
        
        return truncated + "..."
    }
    
    /// Send notification data to Firebase Cloud Messaging
    private func sendNotificationToFirebase(data: [String: Any]) {
        // Store notification in Firestore for Cloud Function to process
        db.collection("notifications").addDocument(data: data) { error in
            if let error = error {
                print("❌ PushNotificationService: Failed to queue notification: \(error.localizedDescription)")
            } else {
                print("✅ PushNotificationService: Notification queued successfully")
            }
        }
    }
    
    // MARK: - Notification Settings
    
    /// Update notification preferences for user
    func updateNotificationSettings(enabled: Bool) {
        guard let userID = Auth.auth().currentUser?.uid else { return }
        
        db.collection("users").document(userID).updateData([
            "notificationsEnabled": enabled
        ]) { error in
            if let error = error {
                print("❌ PushNotificationService: Failed to update notification settings: \(error.localizedDescription)")
            } else {
                print("✅ PushNotificationService: Updated notification settings: \(enabled)")
            }
        }
    }
    
    /// Check if user has notifications enabled
    func areNotificationsEnabled(for userID: String, completion: @escaping (Bool) -> Void) {
        db.collection("users").document(userID).getDocument { snapshot, error in
            if let error = error {
                print("❌ PushNotificationService: Error checking notification settings: \(error.localizedDescription)")
                completion(true) // Default to enabled
                return
            }
            
            let enabled = snapshot?.data()?["notificationsEnabled"] as? Bool ?? true
            completion(enabled)
        }
    }
}

// MARK: - Match Type Extension
extension MatchType {
    var rawValue: String {
        switch self {
        case .regular:
            return "regular"
        case .premium:
            return "premium"
        case .superLike:
            return "super_like"
        }
    }
}
